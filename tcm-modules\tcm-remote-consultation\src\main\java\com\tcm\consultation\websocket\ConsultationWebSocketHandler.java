package com.tcm.consultation.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcm.common.core.domain.AjaxResult;
import com.tcm.consultation.dto.MessageDTO;
import com.tcm.consultation.service.ConsultationMessageService;
import com.tcm.consultation.service.ConsultationParticipantService;
import com.tcm.consultation.service.ConsultationRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 远程会诊WebSocket处理器 处理远程会诊过程中的实时消息通信
 */
@Slf4j
@Component
public class ConsultationWebSocketHandler extends TextWebSocketHandler {

    /**
     * 存储WebSocket会话 key: sessionId, value: WebSocketSession
     */
    private static final Map<String, WebSocketSession> SESSIONS = new ConcurrentHashMap<>();

    /**
     * 会话与会诊房间的映射 key: sessionId, value: roomId
     */
    private static final Map<String, String> SESSION_ROOM_MAP = new ConcurrentHashMap<>();

    /**
     * 会话与用户的映射 key: sessionId, value: userId
     */
    private static final Map<String, Long> SESSION_USER_MAP = new ConcurrentHashMap<>();

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ConsultationMessageService messageService;

    @Autowired
    private ConsultationParticipantService participantService;

    @Autowired
    private ConsultationRecordService consultationRecordService;

    /**
     * 建立连接后的处理
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        log.info("WebSocket连接已建立: {}", session.getId());

        // 从URL查询参数中获取会诊ID和用户ID
        Map<String, String> params = extractUrlParams(session.getUri().getQuery());
        String roomId = params.get("roomId");
        String userIdStr = params.get("userId");

        if (roomId == null || userIdStr == null) {
            log.error("连接参数不完整，断开连接");
            session.close(CloseStatus.BAD_DATA);
            return;
        }

        Long userId = Long.parseLong(userIdStr);

        // 保存会话
        SESSIONS.put(session.getId(), session);
        SESSION_ROOM_MAP.put(session.getId(), roomId);
        SESSION_USER_MAP.put(session.getId(), userId);

        // 更新参与者在线状态
        participantService.updateOnlineStatus(Long.parseLong(roomId), userId, true);

        // 发送用户加入通知
        broadcastSystemMessage(roomId, userId, "加入了会诊");

        log.info("用户[{}]已加入会诊室[{}]", userId, roomId);
    }

    /**
     * 处理接收到的消息
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.debug("收到消息: {}", payload);

        try {
            // 解析消息
            MessageDTO messageDTO = objectMapper.readValue(payload, MessageDTO.class);

            // 获取会话关联的房间ID
            String roomId = SESSION_ROOM_MAP.get(session.getId());
            Long userId = SESSION_USER_MAP.get(session.getId());

            if (roomId == null || userId == null) {
                log.error("无法识别的会话");
                return;
            }

            // 设置发送者ID
            messageDTO.setSenderId(userId);

            // 处理不同类型的消息
            switch (messageDTO.getMessageType()) {
            case 1: // 文本消息
            case 2: // 图片消息
            case 3: // 文件消息
                // 保存消息到数据库
                messageService.saveMessage(messageDTO);
                // 广播消息给所有会诊参与者
                broadcastMessage(roomId, messageDTO);
                break;

            case 5: // 控制消息
                handleControlMessage(roomId, userId, messageDTO);
                break;

            default:
                log.warn("未知的消息类型: {}", messageDTO.getMessageType());
            }
        } catch (Exception e) {
            log.error("处理消息时发生错误", e);
            sendErrorMessage(session, "消息处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理连接关闭
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        log.info("WebSocket连接已关闭: {}, 状态: {}", session.getId(), status);

        // 获取会话关联的房间ID和用户ID
        String roomId = SESSION_ROOM_MAP.get(session.getId());
        Long userId = SESSION_USER_MAP.get(session.getId());

        if (roomId != null && userId != null) {
            // 更新参与者在线状态
            participantService.updateOnlineStatus(Long.parseLong(roomId), userId, false);

            // 发送用户离开通知
            broadcastSystemMessage(roomId, userId, "离开了会诊");

            log.info("用户[{}]已离开会诊室[{}]", userId, roomId);
        }

        // 清理会话
        SESSIONS.remove(session.getId());
        SESSION_ROOM_MAP.remove(session.getId());
        SESSION_USER_MAP.remove(session.getId());
    }

    /**
     * 处理传输错误
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("WebSocket传输错误: {}", session.getId(), exception);
        session.close(CloseStatus.SERVER_ERROR);
    }

    /**
     * 提取URL查询参数
     */
    private Map<String, String> extractUrlParams(String query) {
        Map<String, String> params = new ConcurrentHashMap<>();
        if (query == null || query.isEmpty()) {
            return params;
        }

        String[] pairs = query.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=");
            if (keyValue.length == 2) {
                params.put(keyValue[0], keyValue[1]);
            }
        }

        return params;
    }

    /**
     * 广播消息给指定房间的所有用户
     */
    private void broadcastMessage(String roomId, MessageDTO message) {
        String messageJson;
        try {
            messageJson = objectMapper.writeValueAsString(message);
        } catch (Exception e) {
            log.error("消息序列化失败", e);
            return;
        }

        TextMessage textMessage = new TextMessage(messageJson);

        SESSION_ROOM_MAP.forEach((sessionId, room) -> {
            if (roomId.equals(room)) {
                WebSocketSession targetSession = SESSIONS.get(sessionId);
                if (targetSession != null && targetSession.isOpen()) {
                    try {
                        targetSession.sendMessage(textMessage);
                    } catch (IOException e) {
                        log.error("发送消息失败: {}", sessionId, e);
                    }
                }
            }
        });
    }

    /**
     * 广播系统消息
     */
    private void broadcastSystemMessage(String roomId, Long userId, String content) {
        MessageDTO systemMessage = new MessageDTO();
        systemMessage.setConsultationId(Long.parseLong(roomId));
        systemMessage.setSenderId(0L); // 系统消息发送者ID为0
        systemMessage.setSenderName("系统通知");
        systemMessage.setSenderType(4); // 系统
        systemMessage.setMessageType(4); // 系统通知
        systemMessage.setContent(participantService.getDoctorName(userId) + " " + content);

        // 保存系统消息
        messageService.saveMessage(systemMessage);

        // 广播消息
        broadcastMessage(roomId, systemMessage);
    }

    /**
     * 处理控制消息
     */
    private void handleControlMessage(String roomId, Long userId, MessageDTO message) {
        String action = message.getContent();

        switch (action) {
        case "START_CONSULTATION":
            // 开始会诊
            consultationRecordService.startConsultation(Long.parseLong(roomId));
            broadcastSystemMessage(roomId, userId, "开始了会诊");
            break;

        case "END_CONSULTATION":
            // 结束会诊
            consultationRecordService.endConsultation(Long.parseLong(roomId));
            broadcastSystemMessage(roomId, userId, "结束了会诊");
            break;

        case "PAUSE_CONSULTATION":
            // 暂停会诊
            broadcastSystemMessage(roomId, userId, "暂停了会诊");
            break;

        case "RESUME_CONSULTATION":
            // 恢复会诊
            broadcastSystemMessage(roomId, userId, "恢复了会诊");
            break;

        default:
            log.warn("未知的控制命令: {}", action);
        }
    }

    /**
     * 发送错误消息
     */
    private void sendErrorMessage(WebSocketSession session, String errorMessage) {
        try {
            AjaxResult error = AjaxResult.error(errorMessage);
            session.sendMessage(new TextMessage(objectMapper.writeValueAsString(error)));
        } catch (IOException e) {
            log.error("发送错误消息失败", e);
        }
    }
}
