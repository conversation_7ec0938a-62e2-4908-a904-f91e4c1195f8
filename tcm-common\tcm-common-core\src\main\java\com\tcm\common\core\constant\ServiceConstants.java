package com.tcm.common.core.constant;

/**
 * 服务配置常量 与TcmServiceConfig中的默认值保持一致，用于早期启动阶段使用
 */
public class ServiceConstants {

    /**
     * Nacos服务相关常量
     */
    public static class Nacos {
        /**
         * 服务器地址 - 与TcmServiceConfig.Nacos中的默认值保持一致
         */
        public static final String HOST = "***********";
        public static final int PORT = 8848;
        public static final String SERVER_ADDR = HOST + ":" + PORT;

        /**
         * 认证信息 - 与TcmServiceConfig.Nacos中的默认值保持一致
         */
        public static final String USERNAME = "nacos";
        public static final String PASSWORD = "kaixin207";
    }

    /**
     * RocketMQ服务相关常量
     */
    public static class RocketMq {
        /**
         * 服务器地址 - 与TcmServiceConfig.RocketMq中的默认值保持一致
         */
        public static final String HOST = "**********";
        public static final int PORT = 9876;
        public static final String NAME_SERVER_ADDR = HOST + ":" + PORT;
    }

    /**
     * Redis服务相关常量
     */
    public static class Redis {
        /**
         * 服务器地址 - 与TcmServiceConfig.Redis中的默认值保持一致
         */
        public static final String HOST = "**********";
        public static final int PORT = 6379;
        public static final String PASSWORD = "kaixin207";
    }

    /**
     * MySQL服务相关常量
     */
    public static class Mysql {
        /**
         * 服务器地址 - 与TcmServiceConfig.Mysql中的默认值保持一致
         */
        public static final String HOST = "**********";
        public static final int PORT = 3306;
        public static final String USERNAME = "root";
        public static final String PASSWORD = "Kaixin207@1984";
        public static final String DATABASE = "tcm-system";
    }

    /**
     * JDK相关常量
     */
    public static class Jdk {
        /**
         * 通用JDK路径
         */
        public static final String HOME = "";

        /**
         * Windows系统JDK路径
         */
        public static final String WINDOWS_HOME = "C:\\Users\\<USER>\\jdk\\java";

        /**
         * Linux系统JDK路径
         */
        public static final String LINUX_HOME = "/home/<USER>/jdk/java";

        /**
         * Mac系统JDK路径
         */
        public static final String MAC_HOME = "/Library/Java/JavaVirtualMachines/current";
    }
}
