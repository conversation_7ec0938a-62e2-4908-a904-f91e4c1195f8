package com.tcm.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.inspection.domain.InspectionItem;

import java.util.List;

/**
 * 检查项目服务接口
 */
public interface InspectionItemService extends IService<InspectionItem> {

    /**
     * 查询检查项目列表
     *
     * @param inspectionItem 检查项目信息
     * @return 检查项目集合
     */
    List<InspectionItem> selectInspectionItemList(InspectionItem inspectionItem);

    /**
     * 分页查询检查项目列表
     *
     * @param page           分页信息
     * @param inspectionItem 检查项目信息
     * @return 检查项目分页信息
     */
    IPage<InspectionItem> selectInspectionItemPage(Page<InspectionItem> page, InspectionItem inspectionItem);

    /**
     * 根据ID查询检查项目
     *
     * @param itemId 检查项目ID
     * @return 检查项目信息
     */
    InspectionItem selectInspectionItemById(Long itemId);

    /**
     * 根据分类ID查询检查项目列表
     *
     * @param categoryId 分类ID
     * @return 检查项目集合
     */
    List<InspectionItem> selectInspectionItemByCategoryId(Long categoryId);

    /**
     * 根据科室ID查询检查项目列表
     *
     * @param deptId 科室ID
     * @return 检查项目集合
     */
    List<InspectionItem> selectInspectionItemByDeptId(Long deptId);

    /**
     * 新增检查项目
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    boolean insertInspectionItem(InspectionItem inspectionItem);

    /**
     * 修改检查项目
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    boolean updateInspectionItem(InspectionItem inspectionItem);

    /**
     * 删除检查项目
     *
     * @param itemId 检查项目ID
     * @return 结果
     */
    boolean deleteInspectionItemById(Long itemId);

    /**
     * 批量删除检查项目
     *
     * @param itemIds 需要删除的检查项目ID数组
     * @return 结果
     */
    boolean deleteInspectionItemByIds(Long[] itemIds);

    /**
     * 检查项目名称是否存在
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    boolean checkItemNameUnique(InspectionItem inspectionItem);

    /**
     * 检查项目编码是否存在
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    boolean checkItemCodeUnique(InspectionItem inspectionItem);
} 