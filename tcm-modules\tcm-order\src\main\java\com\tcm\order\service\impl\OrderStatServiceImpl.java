package com.tcm.order.service.impl;

import com.tcm.order.service.IOrderStatService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.Random;

/**
 * 订单统计服务实现类
 */
@Service
public class OrderStatServiceImpl implements IOrderStatService {

    // 注意：这里暂时使用模拟数据，实际应该从数据库中查询
    private Random random = new Random();

    @Override
    public BigDecimal getTotalAmount(Long doctorId, LocalDate startDate, LocalDate endDate) {
        // 模拟从数据库查询医生在指定时间段内的收费总额
        // 在实际项目中，这里应该是查询数据库的逻辑
        
        // 模拟数据：根据医生ID生成一个随机的收费总额（5000-20000之间）
        double baseAmount = 5000 + (doctorId % 10) * 1500;
        double randomFactor = 0.8 + (random.nextDouble() * 0.4); // 0.8-1.2的随机因子
        
        BigDecimal amount = new BigDecimal(baseAmount * randomFactor);
        return amount.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public BigDecimal getRevisitRate(Long doctorId, LocalDate startDate, LocalDate endDate) {
        // 模拟从数据库查询医生在指定时间段内的复诊率
        // 在实际项目中，这里应该是查询数据库的逻辑
        
        // 模拟数据：根据医生ID生成一个随机的复诊率（20%-60%之间）
        double baseRate = 0.2 + (doctorId % 10) * 0.04;
        double randomFactor = 0.9 + (random.nextDouble() * 0.2); // 0.9-1.1的随机因子
        
        BigDecimal revisitRate = new BigDecimal(baseRate * randomFactor);
        return revisitRate.setScale(2, RoundingMode.HALF_UP);
    }
}
