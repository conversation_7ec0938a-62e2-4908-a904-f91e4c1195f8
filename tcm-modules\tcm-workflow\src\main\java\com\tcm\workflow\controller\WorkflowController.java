package com.tcm.workflow.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工作流控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/workflow")
public class WorkflowController {

    @Autowired
    private RuntimeService runtimeService;
    
    @Autowired
    private TaskService taskService;

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Workflow service is running");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 启动工作流实例
     */
    @PostMapping("/start")
    public ResponseEntity<Map<String, Object>> startProcess(@RequestBody Map<String, Object> processRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String processDefinitionKey = (String) processRequest.get("processDefinitionKey");
            String businessKey = (String) processRequest.get("businessKey");
            Map<String, Object> variables = (Map<String, Object>) processRequest.get("variables");
            
            if (variables == null) {
                variables = new HashMap<>();
            }
            
            // 模拟启动流程实例
            // 实际项目中，这里会调用Flowable的API启动流程实例
            // ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
            
            // 模拟返回流程实例ID
            String processInstanceId = "PROC-" + System.currentTimeMillis();
            
            response.put("success", true);
            response.put("message", "流程启动成功");
            response.put("processInstanceId", processInstanceId);
            response.put("processDefinitionKey", processDefinitionKey);
            response.put("businessKey", businessKey);
            response.put("startTime", LocalDateTime.now().toString());
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "流程启动失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取任务列表
     */
    @GetMapping("/tasks")
    public ResponseEntity<Map<String, Object>> getTasks(String assignee) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 模拟获取任务列表
            // 实际项目中，这里会调用Flowable的API获取任务列表
            // List<Task> tasks = taskService.createTaskQuery().taskAssignee(assignee).list();
            
            // 模拟任务列表数据
            List<Map<String, Object>> taskList = new ArrayList<>();
            
            for (int i = 1; i <= 5; i++) {
                Map<String, Object> task = new HashMap<>();
                task.put("taskId", "TASK-" + i);
                task.put("taskName", "审批任务" + i);
                task.put("processInstanceId", "PROC-" + (System.currentTimeMillis() - i * 1000));
                task.put("assignee", assignee);
                task.put("createTime", LocalDateTime.now().minusDays(i).toString());
                
                taskList.add(task);
            }
            
            response.put("success", true);
            response.put("tasks", taskList);
            response.put("total", taskList.size());
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取任务列表失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 完成任务
     */
    @PostMapping("/complete")
    public ResponseEntity<Map<String, Object>> completeTask(@RequestBody Map<String, Object> taskRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String taskId = (String) taskRequest.get("taskId");
            Map<String, Object> variables = (Map<String, Object>) taskRequest.get("variables");
            
            if (variables == null) {
                variables = new HashMap<>();
            }
            
            // 模拟完成任务
            // 实际项目中，这里会调用Flowable的API完成任务
            // taskService.complete(taskId, variables);
            
            response.put("success", true);
            response.put("message", "任务完成成功");
            response.put("taskId", taskId);
            response.put("completeTime", LocalDateTime.now().toString());
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "任务完成失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取流程实例信息
     */
    @GetMapping("/process")
    public ResponseEntity<Map<String, Object>> getProcessInstance(String processInstanceId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 模拟获取流程实例信息
            // 实际项目中，这里会调用Flowable的API获取流程实例信息
            // ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
            
            // 模拟流程实例数据
            Map<String, Object> processInstance = new HashMap<>();
            processInstance.put("processInstanceId", processInstanceId);
            processInstance.put("processDefinitionKey", "approvalProcess");
            processInstance.put("businessKey", "ORDER-" + System.currentTimeMillis());
            processInstance.put("startTime", LocalDateTime.now().minusDays(1).toString());
            processInstance.put("status", "进行中");
            
            response.put("success", true);
            response.put("processInstance", processInstance);
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取流程实例信息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
} 