package com.tcm.common.core.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.regex.Pattern;

/**
 * JDK路径验证工具类 用于验证JDK路径有效性并提供路径处理功能
 */
public class JdkPathValidator {
    private static final Logger logger = LoggerFactory.getLogger(JdkPathValidator.class);

    // 常见的JDK路径模式
    private static final Pattern WINDOWS_JDK_PATTERN = Pattern.compile("(?i).*\\\\(jdk|java|jre|openjdk).*");
    private static final Pattern LINUX_JDK_PATTERN = Pattern.compile("(?i).*/(?:jdk|java|jre|openjdk).*");
    private static final Pattern MAC_JDK_PATTERN = Pattern.compile("(?i).*/(?:JavaVirtualMachines|jdk|java|jre).*");

    /**
     * 验证JDK路径是否有效
     * 
     * @param jdkPath JDK路径
     * @return 是否有效
     */
    public static boolean isValidJdkPath(String jdkPath) {
        if (jdkPath == null || jdkPath.trim().isEmpty()) {
            return false;
        }

        try {
            // 检查路径是否存在
            Path path = Paths.get(jdkPath);
            if (!Files.exists(path)) {
                return false;
            }

            // 检查是否是目录
            if (!Files.isDirectory(path)) {
                return false;
            }

            // 检查是否包含java可执行文件
            String javaExecutable = isWindows() ? "bin\\java.exe" : "bin/java";
            Path javaPath = Paths.get(jdkPath, javaExecutable);

            return Files.exists(javaPath) && Files.isExecutable(javaPath);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断当前操作系统是否是Windows
     *
     * @return 是否是Windows
     */
    public static boolean isWindows() {
        String os = System.getProperty("os.name").toLowerCase();
        return os.contains("win");
    }

    /**
     * 判断当前操作系统是否是Linux
     *
     * @return 是否是Linux
     */
    public static boolean isLinux() {
        String os = System.getProperty("os.name").toLowerCase();
        return os.contains("nix") || os.contains("nux") || os.contains("aix");
    }

    /**
     * 判断当前操作系统是否是Mac
     *
     * @return 是否是Mac
     */
    public static boolean isMac() {
        String os = System.getProperty("os.name").toLowerCase();
        return os.contains("mac");
    }

    /**
     * 获取当前操作系统类型
     *
     * @return 操作系统类型
     */
    public static String getOsType() {
        if (isWindows()) {
            return "windows";
        } else if (isLinux()) {
            return "linux";
        } else if (isMac()) {
            return "mac";
        } else {
            return "unknown";
        }
    }

    /**
     * 规范化JDK路径
     * 
     * @param jdkHome JDK路径
     * @return 规范化后的JDK路径
     */
    public static String normalizePath(String jdkHome) {
        if (jdkHome == null || jdkHome.trim().isEmpty()) {
            return null;
        }

        // 替换路径分隔符
        String normalized = jdkHome.replace('\\', File.separatorChar).replace('/', File.separatorChar);

        // 移除结尾的路径分隔符
        if (normalized.endsWith(File.separator)) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }

        return normalized;
    }

    /**
     * 判断路径是否可能是JDK路径
     * 
     * @param path 路径
     * @return 是否可能是JDK路径
     */
    public static boolean isPossibleJdkPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false;
        }

        // 使用正则表达式进行简单验证
        if (isWindows()) {
            return WINDOWS_JDK_PATTERN.matcher(path).matches();
        } else if (isLinux()) {
            return LINUX_JDK_PATTERN.matcher(path).matches();
        } else if (isMac()) {
            return MAC_JDK_PATTERN.matcher(path).matches();
        }

        return false;
    }

    /**
     * 路径比较（不区分大小写）
     * 
     * @param path1 路径1
     * @param path2 路径2
     * @return 是否相同
     */
    public static boolean pathEquals(String path1, String path2) {
        if (path1 == null && path2 == null) {
            return true;
        }

        if (path1 == null || path2 == null) {
            return false;
        }

        String normalized1 = normalizePath(path1);
        String normalized2 = normalizePath(path2);

        if (normalized1 == null || normalized2 == null) {
            return false;
        }

        // Windows下不区分大小写
        if (isWindows()) {
            return normalized1.equalsIgnoreCase(normalized2);
        } else {
            return normalized1.equals(normalized2);
        }
    }
}