package com.tcm.admin.controller;

import com.tcm.admin.dto.RoleDTO;
import com.tcm.admin.service.RoleService;
import com.tcm.admin.vo.RoleVO;
import com.tcm.common.core.domain.PageVO;
import com.tcm.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色管理控制器
 */
@Tag(name = "角色管理接口")
@RestController
@RequestMapping("/role")
@RequiredArgsConstructor
public class RoleController {

    private final RoleService roleService;

    /**
     * 分页查询角色列表
     */
    @Operation(summary = "分页查询角色列表")
    @GetMapping("/list")
    public Result<PageVO<RoleVO>> list(
            RoleDTO roleDTO,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Integer pageSize) {
        PageVO<RoleVO> pageData = roleService.listByPage(roleDTO, pageNum, pageSize);
        return Result.success(pageData);
    }

    /**
     * 查询角色详情
     */
    @Operation(summary = "查询角色详情")
    @GetMapping("/{id}")
    public Result<RoleVO> getInfo(@Parameter(description = "角色ID") @PathVariable Long id) {
        RoleVO role = roleService.getById(id);
        return Result.success(role);
    }

    /**
     * 创建角色
     */
    @Operation(summary = "创建角色")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody RoleDTO roleDTO) {
        boolean result = roleService.create(roleDTO);
        return Result.success(result);
    }

    /**
     * 更新角色
     */
    @Operation(summary = "更新角色")
    @PutMapping
    public Result<Boolean> update(@Validated @RequestBody RoleDTO roleDTO) {
        boolean result = roleService.update(roleDTO);
        return Result.success(result);
    }

    /**
     * 删除角色
     */
    @Operation(summary = "删除角色")
    @DeleteMapping("/{ids}")
    public Result<Boolean> remove(@Parameter(description = "角色ID，多个以逗号分隔") @PathVariable Long[] ids) {
        boolean result = roleService.delete(ids);
        return Result.success(result);
    }

    /**
     * 修改角色状态
     */
    @Operation(summary = "修改角色状态")
    @PutMapping("/changeStatus")
    public Result<Boolean> changeStatus(
            @Parameter(description = "角色ID") @RequestParam Long id,
            @Parameter(description = "角色状态（0正常 1停用）") @RequestParam Integer status) {
        boolean result = roleService.changeStatus(id, status);
        return Result.success(result);
    }

    /**
     * 分配角色菜单权限
     */
    @Operation(summary = "分配角色菜单权限")
    @PutMapping("/assignMenus")
    public Result<Boolean> assignMenus(
            @Parameter(description = "角色ID") @RequestParam Long roleId,
            @Parameter(description = "菜单ID数组") @RequestBody(required = false) Long[] menuIds) {
        boolean result = roleService.assignMenus(roleId, menuIds);
        return Result.success(result);
    }

    /**
     * 分配角色数据权限
     */
    @Operation(summary = "分配角色数据权限")
    @PutMapping("/dataScope")
    public Result<Boolean> dataScope(@Validated @RequestBody RoleDTO roleDTO) {
        boolean result = roleService.assignDataScope(roleDTO);
        return Result.success(result);
    }

    /**
     * 获取所有可用角色列表
     */
    @Operation(summary = "获取所有可用角色列表")
    @GetMapping("/optionList")
    public Result<List<RoleVO>> optionList() {
        List<RoleVO> roles = roleService.listAllEnabledRoles();
        return Result.success(roles);
    }
}