package com.tcm;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 预约挂号服务启动类
 */
@SpringBootApplication(exclude = {
        // 端点相关自动配置
        org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class,
        // 管理上下文相关
        org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration.class,
        // 健康检查相关
        org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration.class,
        // JMX相关自动配置 - 解决ParameterValueMapper问题
        org.springframework.boot.actuate.autoconfigure.endpoint.jmx.JmxEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.metrics.export.jmx.JmxMetricsExportAutoConfiguration.class,
        // 其他Actuator配置
        org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.metrics.web.servlet.WebMvcMetricsAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.metrics.SystemMetricsAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.scheduling.ScheduledTasksEndpointAutoConfiguration.class,
        // RocketMQ相关
        org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class })
@EnableDiscoveryClient
@ComponentScan(basePackages = { "com.tcm.appointment", "com.tcm.common", "com.tcm.patient.domain",
        "com.tcm.patient.mapper" })
public class TcmAppointmentApplication {

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 彻底禁用PerfMark实现，避免类加载问题
        System.setProperty("io.perfmark.PerfMark.impl", "io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.impl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.startEnabled", "false");

        // 设置一个假的类名，防止通过Class.forName()加载原始类
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.impl.SecretPerfMarkImpl$PerfMarkImpl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");

        // 禁用gRPC通信，强制使用HTTP
        System.setProperty("nacos.client.naming.grpc.enabled", "false");
        System.setProperty("nacos.client.config.grpc.enabled", "false");
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 彻底解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

            // 3. 数据源配置 - 明确指定使用HikariCP
            System.setProperty("spring.datasource.type", "com.zaxxer.hikari.HikariDataSource");

            // 4. RocketMQ配置
            System.setProperty("tcm.service.rocketMq.nameServerAddress", "**********:9876");

            // 5. Nacos配置 - 彻底解决Nacos连接问题
            // 禁用Nacos配置导入检查，以避免启动错误
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");
            // 设置Nacos认证信息
            System.setProperty("nacos.core.auth.enabled", "true");
            System.setProperty("spring.cloud.nacos.discovery.username", "nacos");
            System.setProperty("spring.cloud.nacos.discovery.password", "kaixin207");
            System.setProperty("spring.cloud.nacos.config.username", "nacos");
            System.setProperty("spring.cloud.nacos.config.password", "kaixin207");
            // Nacos网络连接超时设置
            System.setProperty("nacos.client.connectionTimeout", "3000");
            System.setProperty("nacos.client.naming.pushEmptyProtection", "true");
            // 禁用gRPC相关功能，解决Java 9+环境中的类加载问题
            System.setProperty("nacos.client.naming.grpc.enabled", "false");
            System.setProperty("nacos.client.config.grpc.enabled", "false");
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");

            // 解决Java反射和安全限制问题
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.tryReflectionSetAccessible", "true");

            // 在代码中解决反射访问限制问题
            // 禁用使用反射的组件，使用替代方案
            System.setProperty("nacos.client.naming.grpc.use.basic.auth", "true");
            System.setProperty("nacos.client.naming.enable.direct.access", "true");
            System.setProperty("nacos.config.enable.simplify.namespace", "true");
            System.setProperty("nacos.naming.empty.service.clean.enable", "true");
            System.setProperty("nacos.naming.push.enabled", "false");

            // 关闭不必要的功能，减少反射访问
            System.setProperty("nacos.remote.client.grpc.threadpool.enable.shutdown.now", "true");
            System.setProperty("nacos.remote.client.grpc.threadpool.keep.alive.time", "10000");
            System.setProperty("nacos.remote.client.grpc.max.inbound.message.size", "10485760");

            // 禁用特定组件（Epoll, DnsNameResolver等）
            System.setProperty("io.netty.noUnsafe", "true");
            System.setProperty("io.netty.noKeySetOptimization", "true");
            System.setProperty("io.netty.recycler.maxCapacityPerThread", "0");
            System.setProperty("io.netty.allocator.numHeapArenas", "0");
            System.setProperty("com.alibaba.nacos.naming.cache.dir", "/tmp/nacos/naming");

            // 强制使用HTTP协议而非gRPC
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");

            // 防止Context存储相关类加载
            System.setProperty("io.grpc.Context.storage", "io.grpc.ThreadLocalContextStorage");
            System.setProperty("com.alibaba.nacos.shaded.io.grpc.Context.storage", "io.grpc.ThreadLocalContextStorage");

            // 确保Nacos认证信息优先级高于配置文件
            System.setProperty("spring.cloud.nacos.discovery.username", "nacos");
            System.setProperty("spring.cloud.nacos.discovery.password", "kaixin207");
            System.setProperty("spring.cloud.nacos.config.username", "nacos");
            System.setProperty("spring.cloud.nacos.config.password", "kaixin207");

            // 启动应用程序
            SpringApplication.run(TcmAppointmentApplication.class, args);

            // 成功运行提示
            System.out.println("\n=====================================================\n\u0007"
                    + "【TCM-APPOINTMENT】预约挂号服务已成功启动！连接到服务器: " + "**********" + "\n运行端口: "
                    + (System.getProperty("server.port") != null ? System.getProperty("server.port") : "8080")
                    + "\n服务健康状态: 正常运行" + "\nJDK路径: " + System.getProperty("java.home")
                    + "\n=====================================================\n");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}