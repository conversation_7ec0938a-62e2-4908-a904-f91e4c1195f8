<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.auth.mapper.MenuMapper">

    <!-- 根据用户ID查询权限列表 -->
    <select id="selectPermsByUserId" parameterType="Long" resultType="String">
        select distinct m.permission
        from sys_menu m
        inner join sys_role_menu rm on m.id = rm.menu_id
        inner join sys_user_role ur on rm.role_id = ur.role_id
        where ur.user_id = #{userId} 
          and m.permission is not null 
          and m.permission != ''
          and m.status = 1
          and m.del_flag = 0
    </select>
</mapper> 