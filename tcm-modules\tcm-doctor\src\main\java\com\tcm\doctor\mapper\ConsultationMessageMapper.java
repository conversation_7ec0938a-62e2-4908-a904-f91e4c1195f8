package com.tcm.doctor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.doctor.domain.ConsultationMessage;
import com.tcm.doctor.vo.ConsultationMessageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问诊消息Mapper接口
 */
@Mapper
public interface ConsultationMessageMapper extends BaseMapper<ConsultationMessage> {
    
    /**
     * 查询问诊消息列表
     *
     * @param consultationId 问诊ID
     * @return 消息列表
     */
    List<ConsultationMessageVO> selectMessageList(@Param("consultationId") Long consultationId);
    
    /**
     * 根据ID查询消息详情
     *
     * @param messageId 消息ID
     * @return 消息详情
     */
    ConsultationMessageVO selectMessageById(@Param("messageId") Long messageId);
    
    /**
     * 更新消息已读状态
     *
     * @param messageId 消息ID
     * @param readStatus 已读状态
     * @return 结果
     */
    int updateReadStatus(@Param("messageId") Long messageId, @Param("readStatus") Integer readStatus);
    
    /**
     * 批量更新消息已读状态
     *
     * @param consultationId 问诊ID
     * @param receiverId 接收者ID
     * @param readStatus 已读状态
     * @return 结果
     */
    int batchUpdateReadStatus(@Param("consultationId") Long consultationId, 
                             @Param("receiverId") Long receiverId, 
                             @Param("readStatus") Integer readStatus);
    
    /**
     * 撤回消息
     *
     * @param messageId 消息ID
     * @param updateBy 更新人
     * @return 结果
     */
    int recallMessage(@Param("messageId") Long messageId, @Param("updateBy") String updateBy);
    
    /**
     * 统计未读消息数
     *
     * @param consultationId 问诊ID
     * @param receiverId 接收者ID
     * @return 未读消息数
     */
    int countUnreadMessage(@Param("consultationId") Long consultationId, @Param("receiverId") Long receiverId);
} 