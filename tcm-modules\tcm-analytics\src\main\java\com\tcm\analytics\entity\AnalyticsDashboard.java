package com.tcm.analytics.entity;

import java.util.Date;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;

/**
 * 分析仪表盘实体类
 */
@Data
@TableName(value = "analytics_dashboard", autoResultMap = true)
public class AnalyticsDashboard {
    
    /**
     * 仪表盘ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    
    /**
     * 仪表盘名称
     */
    private String name;

    /**
     * 仪表盘描述
     */
    private String description;
    
    /**
     * 仪表盘类型（0:系统预设, 1:用户自定义）
     */
    private Integer type;
    
    /**
     * 仪表盘布局（JSON格式）
     */
    private String layout;
    
    /**
     * 数据源类型（mysql, elastic, file等）
     */
    private String dataSource;
    
    /**
     * 查询配置
     */
    private String queryConfig;
    
    /**
     * 组件配置（JSON格式）
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> componentsConfig;

    /**
     * 组件字符串（JSON格式）- 用于兼容ES存储
     */
    private String components;
    
    /**
     * 刷新间隔（秒）
     */
    private Integer refreshInterval;
    
    /**
     * 是否自动刷新（0:否, 1:是）
     */
    private Integer autoRefresh;
    
    /**
     * 权限范围（0:私有, 1:部门, 2:全局）
     */
    private Integer scope;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 排序号
     */
    private Integer orderNum;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 删除标记（0:未删除, 1:已删除）
     */
    private Integer delFlag;
} 