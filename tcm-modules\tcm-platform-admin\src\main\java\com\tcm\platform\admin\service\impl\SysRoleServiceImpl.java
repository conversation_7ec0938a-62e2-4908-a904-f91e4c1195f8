package com.tcm.platform.admin.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.platform.admin.entity.SysRole;
import com.tcm.platform.admin.entity.SysRoleMenu;
import com.tcm.platform.admin.entity.SysUserRole;
import com.tcm.platform.admin.mapper.SysRoleMapper;
import com.tcm.platform.admin.mapper.SysRoleMenuMapper;
import com.tcm.platform.admin.mapper.SysUserRoleMapper;
import com.tcm.platform.admin.service.SysRoleService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 系统角色服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    /**
     * 分页查询角色列表
     */
    @Override
    public IPage<SysRole> selectRolePage(Integer pageNum, Integer pageSize, SysRole role) {
        Page<SysRole> page = new Page<>(pageNum, pageSize);
        return roleMapper.selectRolePage(page, role);
    }

    /**
     * 根据角色ID查询角色
     */
    @Override
    public SysRole selectRoleById(Long roleId) {
        return roleMapper.selectById(roleId);
    }

    /**
     * 根据用户ID查询角色列表
     */
    @Override
    public List<SysRole> selectRolesByUserId(Long userId) {
        return roleMapper.selectRolesByUserId(userId);
    }

    /**
     * 根据角色编码查询角色
     */
    @Override
    public SysRole selectRoleByCode(String roleCode) {
        return roleMapper.selectByRoleCode(roleCode);
    }

    /**
     * 检查角色名称是否唯一
     */
    @Override
    public boolean checkRoleNameUnique(String roleName, Long roleId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleName, roleName);
        if (roleId != null) {
            wrapper.ne(SysRole::getId, roleId);
        }
        return roleMapper.selectCount(wrapper) > 0;
    }

    /**
     * 检查角色编码是否唯一
     */
    @Override
    public boolean checkRoleCodeUnique(String roleCode, Long roleId) {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getRoleCode, roleCode);
        if (roleId != null) {
            wrapper.ne(SysRole::getId, roleId);
        }
        return roleMapper.selectCount(wrapper) > 0;
    }

    /**
     * 新增角色
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertRole(SysRole role, List<Long> menuIds) {
        // 设置创建时间等字段
        LocalDateTime now = LocalDateTime.now();
        role.setCreateTime(now);
        role.setUpdateTime(now);
        role.setDelFlag(0);
        role.setStatus(1); // 默认启用

        // 插入角色
        int rows = roleMapper.insert(role);
        if (rows <= 0) {
            return false;
        }

        // 插入角色菜单关系
        insertRoleMenus(role.getId(), menuIds);

        return true;
    }

    /**
     * 修改角色
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRole(SysRole role, List<Long> menuIds) {
        role.setUpdateTime(LocalDateTime.now());

        // 更新角色信息
        int rows = roleMapper.updateById(role);
        if (rows <= 0) {
            return false;
        }

        // 更新角色菜单关系
        // 先删除原有关系
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, role.getId());
        roleMenuMapper.delete(wrapper);

        // 再插入新的关系
        insertRoleMenus(role.getId(), menuIds);

        return true;
    }

    /**
     * 删除角色
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleById(Long roleId) {
        // 检查角色是否已分配给用户
        LambdaQueryWrapper<SysUserRole> userRoleWrapper = new LambdaQueryWrapper<>();
        userRoleWrapper.eq(SysUserRole::getRoleId, roleId);
        Long userCount = userRoleMapper.selectCount(userRoleWrapper);
        if (userCount > 0) {
            throw new RuntimeException("该角色已分配给用户，不能删除");
        }

        // 删除角色
        int rows = roleMapper.deleteById(roleId);
        if (rows <= 0) {
            return false;
        }

        // 删除角色菜单关系
        LambdaQueryWrapper<SysRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleMenu::getRoleId, roleId);
        roleMenuMapper.delete(wrapper);

        return true;
    }

    /**
     * 修改角色状态
     */
    @Override
    public boolean updateRoleStatus(Long roleId, Integer status) {
        return roleMapper.updateStatus(roleId, status) > 0;
    }

    /**
     * 获取角色选择框列表
     */
    @Override
    public List<SysRole> selectRoleOptions() {
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getStatus, 1) // 只查询启用的角色
                .orderByAsc(SysRole::getSort);
        return roleMapper.selectList(wrapper);
    }

    /**
     * 插入角色菜单关系
     */
    private void insertRoleMenus(Long roleId, List<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)) {
            return;
        }

        List<SysRoleMenu> roleMenus = new ArrayList<>();
        for (Long menuId : menuIds) {
            SysRoleMenu roleMenu = new SysRoleMenu();
            roleMenu.setRoleId(roleId);
            roleMenu.setMenuId(menuId);
            roleMenu.setCreateTime(LocalDateTime.now());
            roleMenus.add(roleMenu);
        }

        roleMenuMapper.batchInsert(roleMenus);
    }
}