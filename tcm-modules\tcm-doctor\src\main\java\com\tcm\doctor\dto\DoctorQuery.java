package com.tcm.doctor.dto;

import com.tcm.common.core.domain.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医生查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DoctorQuery extends PageQuery {
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生编号
     */
    private String doctorCode;
    
    /**
     * 职称
     */
    private Integer title;
    
    /**
     * 专长
     */
    private String specialty;
    
    /**
     * 所属医院
     */
    private String hospital;
    
    /**
     * 所属科室
     */
    private String department;
    
    /**
     * 状态（0正常 1停用）
     */
    private Integer status;
} 