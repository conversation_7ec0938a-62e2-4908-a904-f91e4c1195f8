package com.tcm.diagnosis.dto;

import com.tcm.common.core.domain.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 诊断记录查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DiagnosisQuery extends PageQuery {
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 诊断编号
     */
    private String diagnosisCode;
    
    /**
     * 诊断开始日期
     */
    private Date beginDate;
    
    /**
     * 诊断结束日期
     */
    private Date endDate;
    
    /**
     * 中医诊断
     */
    private String tcmDiagnosis;
    
    /**
     * 西医诊断
     */
    private String westernDiagnosis;
    
    /**
     * 辨证分型
     */
    private String syndromeType;
    
    /**
     * 状态（0：草稿，1：已完成，2：已取消）
     */
    private Integer status;
} 