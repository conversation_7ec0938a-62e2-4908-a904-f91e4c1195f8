-- 药品表
CREATE TABLE IF NOT EXISTS `tcm_medicine` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '药品ID',
  `medicine_code` varchar(50) NOT NULL COMMENT '药品编码',
  `medicine_name` varchar(100) NOT NULL COMMENT '药品名称',
  `pinyin_code` varchar(50) DEFAULT NULL COMMENT '拼音码',
  `medicine_type` tinyint(4) NOT NULL COMMENT '药品类型（1：中药材，2：中成药，3：西药）',
  `category_id` bigint(20) NOT NULL COMMENT '药品分类ID',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `dosage_form` varchar(50) DEFAULT NULL COMMENT '剂型',
  `unit` varchar(20) NOT NULL COMMENT '单位',
  `price` decimal(10,2) NOT NULL COMMENT '单价',
  `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存',
  `warn_stock` int(11) DEFAULT '10' COMMENT '预警库存',
  `manufacturer` varchar(200) DEFAULT NULL COMMENT '生产厂家',
  `efficacy` text COMMENT '功效',
  `property` varchar(200) DEFAULT NULL COMMENT '性味归经',
  `usage` text COMMENT '用法用量',
  `contraindication` text COMMENT '禁忌',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（0：停用，1：启用）',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_medicine_code` (`medicine_code`),
  UNIQUE KEY `idx_medicine_name` (`medicine_name`),
  KEY `idx_medicine_type` (`medicine_type`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='药品表';

-- 药品分类表
CREATE TABLE IF NOT EXISTS `tcm_medicine_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父级ID',
  `ancestors` varchar(500) DEFAULT NULL COMMENT '祖级列表',
  `order_num` int(11) DEFAULT '0' COMMENT '显示顺序',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（0：停用，1：启用）',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_category_code` (`category_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='药品分类表';

-- 药品库存记录表
CREATE TABLE IF NOT EXISTS `tcm_medicine_stock_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `medicine_id` bigint(20) NOT NULL COMMENT '药品ID',
  `medicine_name` varchar(100) NOT NULL COMMENT '药品名称',
  `operation_type` tinyint(4) NOT NULL COMMENT '操作类型（1：入库，2：出库，3：盘点）',
  `quantity` int(11) NOT NULL COMMENT '操作数量',
  `before_stock` int(11) NOT NULL COMMENT '操作前库存',
  `after_stock` int(11) NOT NULL COMMENT '操作后库存',
  `price` decimal(10,2) NOT NULL COMMENT '操作单价',
  `amount` decimal(10,2) NOT NULL COMMENT '操作总额',
  `batch_no` varchar(50) DEFAULT NULL COMMENT '批次号',
  `production_date` datetime DEFAULT NULL COMMENT '生产日期',
  `expiry_date` datetime DEFAULT NULL COMMENT '有效期至',
  `related_no` varchar(50) DEFAULT NULL COMMENT '相关单号',
  `operate_by` varchar(50) DEFAULT NULL COMMENT '操作人',
  `operate_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_medicine_id` (`medicine_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operate_time` (`operate_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='药品库存记录表';

-- 中药配伍禁忌表
CREATE TABLE IF NOT EXISTS `tcm_medicine_incompatibility` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配伍禁忌ID',
  `medicine_id_a` bigint(20) NOT NULL COMMENT '药品ID A',
  `medicine_name_a` varchar(100) NOT NULL COMMENT '药品名称 A',
  `medicine_id_b` bigint(20) NOT NULL COMMENT '药品ID B',
  `medicine_name_b` varchar(100) NOT NULL COMMENT '药品名称 B',
  `incompatibility_type` tinyint(4) NOT NULL COMMENT '禁忌类型（1：相反，2：相恶，3：相畏，4：相杀）',
  `description` text COMMENT '禁忌描述',
  `theoretical_basis` text COMMENT '理论依据',
  `source` varchar(200) DEFAULT NULL COMMENT '源出处',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（0：无效，1：有效）',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_medicine_pair` (`medicine_id_a`, `medicine_id_b`),
  KEY `idx_medicine_id_a` (`medicine_id_a`),
  KEY `idx_medicine_id_b` (`medicine_id_b`),
  KEY `idx_incompatibility_type` (`incompatibility_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='中药配伍禁忌表'; 