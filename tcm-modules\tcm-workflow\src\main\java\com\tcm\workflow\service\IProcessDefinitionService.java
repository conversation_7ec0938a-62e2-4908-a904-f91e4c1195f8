package com.tcm.workflow.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.workflow.entity.ProcessDefinition;

/**
 * 流程定义服务接口
 * 
 * <AUTHOR>
 */
public interface IProcessDefinitionService extends IService<ProcessDefinition> {

    /**
     * 查询流程定义列表
     *
     * @param processDefinition 流程定义信息
     * @return 流程定义集合
     */
    List<ProcessDefinition> selectProcessDefinitionList(ProcessDefinition processDefinition);

    /**
     * 查询流程定义详细信息
     *
     * @param processDefinitionId 流程定义ID
     * @return 流程定义信息
     */
    ProcessDefinition selectProcessDefinitionById(String processDefinitionId);

    /**
     * 部署流程定义
     *
     * @param processDefinition 流程定义信息
     * @return 结果
     */
    String deployProcessDefinition(ProcessDefinition processDefinition);

    /**
     * 修改流程定义
     *
     * @param processDefinition 流程定义信息
     * @return 结果
     */
    int updateProcessDefinition(ProcessDefinition processDefinition);

    /**
     * 批量删除流程定义
     *
     * @param processDefinitionIds 需要删除的流程定义ID
     * @return 结果
     */
    int deleteProcessDefinitionByIds(String[] processDefinitionIds);

    /**
     * 删除流程定义信息
     *
     * @param processDefinitionId 流程定义ID
     * @return 结果
     */
    int deleteProcessDefinitionById(String processDefinitionId);

    /**
     * 激活或挂起流程定义
     *
     * @param processDefinitionId 流程定义ID
     * @param status              流程状态：1激活，2挂起
     * @return 结果
     */
    int updateProcessDefinitionStatus(String processDefinitionId, Integer status);
}