package com.tcm.consultation.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 远程会诊参与人实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_consultation_participant")
public class ConsultationParticipant extends BaseEntity {

    /**
     * 参与记录ID
     */
    @TableId
    private Long participantId;

    /**
     * 会诊记录ID
     */
    private Long consultationId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 参与类型（1：主诊医生，2：会诊医生，3：专家）
     */
    private Integer type;

    /**
     * 参与状态（0：待响应，1：已接受，2：已拒绝，3：已参与）
     */
    private Integer status;

    /**
     * 是否在线（0：离线，1：在线）
     */
    private Integer online;

    /**
     * 加入时间
     */
    private Date joinTime;

    /**
     * 离开时间
     */
    private Date leaveTime;

    /**
     * 会诊意见
     */
    private String opinion;

    /**
     * 拒绝原因
     */
    private String rejectReason;
}