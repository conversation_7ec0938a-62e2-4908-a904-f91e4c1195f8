package com.tcm.common.core.domain;

import com.tcm.common.core.constant.HttpStatus;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 通用返回结果
 *
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
public class Result<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private T data;

    /**
     * 初始化
     *
     * @param code    状态码
     * @param message 消息
     * @param data    数据
     */
    public Result(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 返回成功结果
     *
     * @param <T> 数据类型
     * @return 成功结果
     */
    public static <T> Result<T> success() {
        return new Result<>(HttpStatus.SUCCESS, "操作成功", null);
    }

    /**
     * 返回成功结果
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return 成功结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(HttpStatus.SUCCESS, "操作成功", data);
    }

    /**
     * 返回成功结果
     *
     * @param message 消息
     * @param data    数据
     * @param <T>     数据类型
     * @return 成功结果
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(HttpStatus.SUCCESS, message, data);
    }

    /**
     * 返回错误结果
     *
     * @param <T> 数据类型
     * @return 错误结果
     */
    public static <T> Result<T> error() {
        return new Result<>(HttpStatus.ERROR, "操作失败", null);
    }

    /**
     * 返回错误结果
     *
     * @param message 消息
     * @param <T>     数据类型
     * @return 错误结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(HttpStatus.ERROR, message, null);
    }

    /**
     * 返回错误结果
     *
     * @param code    状态码
     * @param message 消息
     * @param <T>     数据类型
     * @return 错误结果
     */
    public static <T> Result<T> error(int code, String message) {
        return new Result<>(code, message, null);
    }

    /**
     * 返回成功结果（返回多个数据集）
     *
     * @param data  主数据
     * @param extra 额外数据
     * @param <T>   数据类型
     * @return 成功结果
     */
    public static <T, E> Result<T> success(T data, E extra) {
        Result<T> result = success(data);
        result.put("extra", extra);
        return result;
    }
    
    /**
     * 向结果中添加额外数据
     *
     * @param key   键
     * @param value 值
     */
    @SuppressWarnings("unchecked")
    public Result<T> put(String key, Object value) {
        if (this.data == null) {
            this.data = (T) new java.util.HashMap<String, Object>();
        }
        if (this.data instanceof java.util.Map) {
            ((java.util.Map<String, Object>) this.data).put(key, value);
        }
        return this;
    }
} 