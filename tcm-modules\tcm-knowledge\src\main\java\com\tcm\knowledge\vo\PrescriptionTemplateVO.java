package com.tcm.knowledge.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 处方模板VO
 */
@Data
public class PrescriptionTemplateVO {
    /**
     * 模板ID
     */
    private Long id;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 适用证型
     */
    private String syndrome;
    
    /**
     * 功效
     */
    private String efficacy;
    
    /**
     * 方剂组成
     */
    private List<MedicineVO> medicines;
    
    /**
     * 用法用量
     */
    private String usage;
    
    /**
     * 注意事项
     */
    private String precautions;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 药材VO
     */
    @Data
    public static class MedicineVO {
        /**
         * 药材ID
         */
        private Long id;
        
        /**
         * 药材名称
         */
        private String name;
        
        /**
         * 用量
         */
        private String amount;
        
        /**
         * 药材功效
         */
        private String effect;
    }
}
