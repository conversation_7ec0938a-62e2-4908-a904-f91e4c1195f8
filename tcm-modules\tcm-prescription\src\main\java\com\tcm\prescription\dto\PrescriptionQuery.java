package com.tcm.prescription.dto;

import com.tcm.common.core.domain.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 处方查询条件
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrescriptionQuery extends PageQuery {
    
    /**
     * 处方编号
     */
    private String prescriptionCode;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 处方类型（1：中药方剂，2：中成药，3：西药）
     */
    private Integer type;
    
    /**
     * 处方名称
     */
    private String name;
    
    /**
     * 处方开始日期
     */
    private Date beginDate;
    
    /**
     * 处方结束日期
     */
    private Date endDate;
    
    /**
     * 状态（0：未审核，1：已审核，2：已发药，3：已取消）
     */
    private Integer status;
} 