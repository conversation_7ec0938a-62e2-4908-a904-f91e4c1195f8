package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 辨证结果视图对象
 */
@Data
public class SyndromeIdentificationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 辨证ID
     */
    private Long id;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 主要证型
     */
    private String primaryPattern;

    /**
     * 次要证型
     */
    private String secondaryPattern;

    /**
     * 证型描述
     */
    private String syndromeDesc;

    /**
     * 辨证依据
     */
    private String reasonings;

    /**
     * 关联症状
     */
    private List<Map<String, Object>> relatedSymptoms;

    /**
     * 关联病机
     */
    private List<String> relatedPathomechanisms;

    /**
     * 关联证型
     */
    private List<Map<String, Object>> relatedSyndromes;

    /**
     * 症状列表（JSON格式）
     */
    private String symptoms;
    
    /**
     * 体征列表（JSON格式）
     */
    private String signs;

    /**
     * 病机学说明
     */
    private String pathogenesis;

    /**
     * 分析结果（JSON格式）
     */
    private String analysisResult;

    /**
     * 临床意义
     */
    private String clinicalImplications;

    /**
     * 建议治法
     */
    private String suggestedTreatment;

    /**
     * 辨证方法（规则/AI/混合）
     */
    private String identificationMethod;

    /**
     * 辨证时间
     */
    private Date identificationTime;

    /**
     * 置信度（0-100）
     */
    private Integer confidence;

    /**
     * 是否医生确认（0：未确认，1：已确认）
     */
    private Integer doctorConfirmed;
    
    /**
     * 状态（0：待审核，1：已通过，2：已驳回）
     */
    private Integer status;
    
    /**
     * 确认时间
     */
    private Date confirmTime;
    
    /**
     * 确认人
     */
    private String confirmBy;
    
    /**
     * 疾病类型
     */
    private String diseaseType;
    
    /**
     * 证型类型
     */
    private String syndromeType;
    
    /**
     * 治疗原则
     */
    private String treatmentPrinciple;
    
    /**
     * 创建时间
     */
    private Date createTime;
}