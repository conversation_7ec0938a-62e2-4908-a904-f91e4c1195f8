package com.tcm.doctor.config;

import com.tcm.doctor.constant.ServiceConstants;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类，提供Redis相关Bean配置
 * 根据ServiceConstants中的配置属性设置Redis连接
 * 注意：只在非模拟模式下有效，即tcm.mock.redis=false或未设置时
 */
@Configuration
@ConditionalOnProperty(name = "tcm.mock.redis", havingValue = "false", matchIfMissing = true)
public class RedisConfiguration {

    /**
     * 配置Redis连接工厂
     * 使用ServiceConstants中定义的Redis连接参数
     */
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(ServiceConstants.REDIS_HOST);
        config.setPort(Integer.parseInt(ServiceConstants.REDIS_PORT));
        config.setPassword(ServiceConstants.REDIS_PASSWORD);
        config.setDatabase(Integer.parseInt(ServiceConstants.REDIS_DATABASE));
        
        return new LettuceConnectionFactory(config);
    }

    /**
     * 配置RedisTemplate，使用Object作为值类型
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        
        // 设置key的序列化方式
        template.setKeySerializer(new StringRedisSerializer());
        // 设置value的序列化方式
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        
        // 设置hash key的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        // 设置hash value的序列化方式
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        
        template.afterPropertiesSet();
        
        return template;
    }

    /**
     * 配置StringRedisTemplate，专用于String类型的操作
     */
    @Bean
    @Primary
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }
}
