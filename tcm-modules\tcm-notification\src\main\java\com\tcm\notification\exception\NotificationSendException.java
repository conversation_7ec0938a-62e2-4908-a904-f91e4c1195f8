package com.tcm.notification.exception;

/**
 * 通知发送异常
 * 
 * <AUTHOR>
 */
public class NotificationSendException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public NotificationSendException() {
        super();
    }

    public NotificationSendException(String message) {
        super(message);
    }

    public NotificationSendException(String message, Throwable cause) {
        super(message, cause);
    }

    public NotificationSendException(Throwable cause) {
        super(cause);
    }
}
