package com.tcm.user.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.user.dto.UserDTO;
import com.tcm.user.entity.Role;
import com.tcm.user.entity.User;
import com.tcm.user.exception.BusinessException;
import com.tcm.user.mapper.UserMapper;
import com.tcm.user.service.RoleService;
import com.tcm.user.service.UserRoleService;
import com.tcm.user.service.UserService;
import com.tcm.user.vo.PageVO;
import com.tcm.user.vo.UserVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired(required = false)
    private PasswordEncoder passwordEncoder;

    @Override
    public PageVO<UserVO> listUsers(Integer pageNum, Integer pageSize, String username, String realName) {
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(username)) {
            queryWrapper.like(User::getUsername, username);
        }
        if (StringUtils.hasText(realName)) {
            queryWrapper.like(User::getRealName, realName);
        }
        queryWrapper.orderByDesc(User::getCreateTime);

        // 分页查询
        Page<User> page = page(new Page<>(pageNum, pageSize), queryWrapper);

        // 转换为VO
        List<UserVO> userVOList = page.getRecords().stream().map(this::convertToVO).collect(Collectors.toList());

        return new PageVO<>(userVOList, page.getTotal(), pageNum, pageSize);
    }

    @Override
    public UserVO getUserById(Long id) {
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        UserVO userVO = convertToVO(user);

        // 获取用户角色
        List<Role> roles = roleService.selectRolesByUserId(id);
        if (!roles.isEmpty()) {
            Long[] roleIds = roles.stream().map(Role::getId).toArray(Long[]::new);
            String[] roleNames = roles.stream().map(Role::getRoleName).toArray(String[]::new);
            userVO.setRoleIds(roleIds);
            userVO.setRoleNames(roleNames);
        }

        return userVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserDTO userDTO) {
        // 检查用户名是否已存在
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, userDTO.getUsername());
        if (count(queryWrapper) > 0) {
            throw new BusinessException("用户名已存在");
        }

        // 创建用户
        User user = new User();
        BeanUtils.copyProperties(userDTO, user);

        // 密码加密
        if (user.getPassword() == null) {
            user.setPassword("123456");
        }
        if (passwordEncoder != null) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }

        // 保存用户
        save(user);

        // 分配角色
        if (userDTO.getRoleIds() != null && userDTO.getRoleIds().length > 0) {
            userRoleService.assignRolesToUser(user.getId(), List.of(userDTO.getRoleIds()));
        }

        return user.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(Long id, UserDTO userDTO) {
        // 检查用户是否存在
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户名是否重复（排除自身）
        if (!user.getUsername().equals(userDTO.getUsername())) {
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getUsername, userDTO.getUsername());
            if (count(queryWrapper) > 0) {
                throw new BusinessException("用户名已存在");
            }
        }

        // 更新用户信息
        BeanUtils.copyProperties(userDTO, user);
        user.setId(id);

        // 如果密码不为空，则加密
        if (StringUtils.hasText(userDTO.getPassword())) {
            if (passwordEncoder != null) {
                user.setPassword(passwordEncoder.encode(userDTO.getPassword()));
            }
        } else {
            user.setPassword(null); // 密码为空时不更新密码
        }

        updateById(user);

        // 更新用户角色
        if (userDTO.getRoleIds() != null) {
            userRoleService.assignRolesToUser(id, List.of(userDTO.getRoleIds()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        // 检查用户是否存在
        if (!removeById(id)) {
            throw new BusinessException("用户不存在或删除失败");
        }

        // 删除用户角色关联
        userRoleService.deleteUserRoles(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserStatus(Long id, Integer status) {
        // 检查用户是否存在
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 更新状态
        user.setStatus(status);
        updateById(user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPassword(Long id) {
        // 检查用户是否存在
        User user = getById(id);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 重置密码为默认密码（并加密）
        String defaultPassword = "123456";
        if (passwordEncoder != null) {
            user.setPassword(passwordEncoder.encode(defaultPassword));
        } else {
            user.setPassword(defaultPassword);
        }

        updateById(user);
    }

    /**
     * 将User对象转换为UserVO对象
     */
    private UserVO convertToVO(User user) {
        if (user == null) {
            return null;
        }

        UserVO userVO = new UserVO();
        BeanUtils.copyProperties(user, userVO);

        // 设置性别描述
        if (user.getGender() != null) {
            switch (user.getGender()) {
            case 0:
                userVO.setGenderDesc("男");
                break;
            case 1:
                userVO.setGenderDesc("女");
                break;
            default:
                userVO.setGenderDesc("未知");
            }
        }

        // 设置状态描述
        if (user.getStatus() != null) {
            userVO.setStatusDesc(user.getStatus() == 0 ? "正常" : "停用");
        }

        return userVO;
    }

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public User getByUsername(String username) {
        return getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));
    }
}