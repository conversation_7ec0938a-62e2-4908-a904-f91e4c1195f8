# 中医检查模块 (tcm-inspection)

## 模块介绍

中医检查模块是中医智能诊疗系统的核心功能之一，基于中医四诊（望、闻、问、切）理论，提供了全面的中医检查管理功能。本模块支持中医检查项目管理、检查记录管理、检查结果分析等功能，为中医诊断提供数据支持。

## 主要功能

### 1. 检查项目管理

- 中医四诊项目分类管理
- 检查项目基础信息维护
- 检查项目定价管理
- 检查项目执行科室管理

### 2. 检查记录管理

- 患者检查申请与排队
- 检查单生成与打印
- 检查执行与结果录入
- 检查影像与资料上传
- 检查结果查询与浏览

### 3. 检查结果分析

- 中医四诊数据统计
- 检查结果归类分析
- 检查结果趋势分析
- 检查结果与病症关联分析

## 技术架构

- 基于Spring Boot和Spring Cloud微服务架构
- 使用MyBatis Plus进行数据库访问
- 采用RESTful API设计规范
- 使用Nacos作为注册中心和配置中心
- 支持与其他模块通过OpenFeign进行服务调用

## 数据库设计

主要数据表：

1. `inspection_category` - 检查分类表
2. `inspection_item` - 检查项目表
3. `inspection_record` - 检查记录表

详细的数据库脚本见 `src/main/resources/db/inspection.sql` 文件。

## 接口文档

可通过Swagger UI访问API文档：`http://localhost:9308/swagger-ui.html`

## 部署说明

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+

### 构建与运行

1. 构建项目

```bash
mvn clean package
```

2. 运行服务

```bash
java -jar target/tcm-inspection-1.0.0.jar
```

或者使用提供的启动脚本：

```bash
start.bat
```

### 配置说明

- 服务端口: 9308
- 数据库配置: 见 `application.yml`
- 微服务配置: 见 `bootstrap.yml`

## 开发指南

### 代码结构

```
tcm-inspection/
├── src/main/java/
│   └── com/tcm/inspection/
│       ├── config/         # 配置类
│       ├── controller/     # 控制器
│       ├── domain/         # 实体类
│       ├── mapper/         # MyBatis Mapper
│       ├── service/        # 服务接口和实现
│       ├── util/           # 工具类
│       ├── feign/          # Feign客户端
│       └── TcmInspectionApplication.java # 启动类
├── src/main/resources/
│   ├── mapper/            # MyBatis XML文件
│   ├── db/                # 数据库脚本
│   ├── application.yml    # 应用配置
│   └── bootstrap.yml      # 启动配置
└── pom.xml                # Maven配置
```

### 开发规范

- 遵循阿里巴巴Java开发手册规范
- 使用驼峰命名法
- 控制器层返回统一的响应格式
- 合理使用注释，方法必须有JavaDoc

## 版本历史

- v1.0.0 - 初始版本，基础功能实现

## 联系方式

- 负责人: TCM开发团队
- 邮箱: <EMAIL> 