package com.tcm.integration;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 系统集成服务启动类
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient(autoRegister = false)
@SpringBootApplication(exclude = {
    org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration.class,
    org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration.class,
    org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class,
    org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
    org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration.class,
    com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.class
})
@EnableFeignClients
public class TcmIntegrationApplication {
    
    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 彻底禁用PerfMark实现，避免类加载问题
        System.setProperty("io.perfmark.PerfMark.impl", "io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.impl", "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.startEnabled", "false");
        
        // 禁用gRPC通信，强制使用HTTP
        System.setProperty("nacos.client.naming.grpc.enabled", "false");
        System.setProperty("nacos.client.config.grpc.enabled", "false");
    }
    
    public static void main(String[] args) {
        try {
            System.out.println("Starting Integration Service...");
            
            // 1. 日志配置 - 彻底解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅
            System.setProperty("org.springframework.boot.logging.LoggingSystem", "none"); // 禁用Spring Boot日志系统
            System.setProperty("log4j2.statusLogger.level", "OFF");
            System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
            
            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖
            System.setProperty("spring.main.lazy-initialization", "true");
            System.setProperty("spring.main.banner-mode", "OFF");
            
            // 3. Nacos配置 - 彻底解决Nacos连接问题
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");
            System.setProperty("spring.cloud.bootstrap.enabled", "false");
            System.setProperty("nacos.core.auth.enabled", "true");
            System.setProperty("spring.cloud.nacos.discovery.server-addr", "10.67.1.100:8848");
            System.setProperty("spring.cloud.nacos.config.server-addr", "10.67.1.100:8848");
            System.setProperty("spring.cloud.nacos.discovery.username", "nacos");
            System.setProperty("spring.cloud.nacos.discovery.password", "kaixin207");
            System.setProperty("spring.cloud.nacos.config.username", "nacos");
            System.setProperty("spring.cloud.nacos.config.password", "kaixin207");
            
            // 4. 彻底禁用所有netty unsafe访问
            System.setProperty("io.netty.noUnsafe", "true");
            System.setProperty("com.alibaba.nacos.shaded.io.netty.noUnsafe", "true");
            System.setProperty("io.grpc.netty.shaded.io.netty.noUnsafe", "true");
            System.setProperty("com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.noUnsafe", "true");
            
            // 5. 强制使用HTTP而非gRPC
            System.setProperty("nacos.client.config.useRpcProtocol", "false");
            System.setProperty("nacos.client.naming.useRpcProtocol", "false");
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");
            System.setProperty("nacos.remote.client.type", "http");
            
            // 6. 解决SLF4J绑定冲突问题 - 关键配置
            System.setProperty("nacos.logging.default.config.enabled", "false");
            System.setProperty("nacos.logging.config", "classpath:logback-spring.xml");
            System.setProperty("com.alibaba.nacos.logging.log4j.level", "WARN");
            System.setProperty("com.alibaba.nacos.client.logging.level", "WARN");
            System.setProperty("logging.level.com.alibaba.nacos.client.naming", "WARN");
            System.setProperty("logging.level.com.alibaba.nacos.client.config", "WARN");
            System.setProperty("logging.level.com.alibaba.nacos.client.logging", "WARN");
            
            // 7. 禁用Census相关类加载 - 避免类加载冲突
            System.setProperty("grpc.census.enabled", "false");
            System.setProperty("io.grpc.census.enabled", "false");
            System.setProperty("io.grpc.internal.DnsNameResolverProvider.enable", "false");
            System.setProperty("io.grpc.internal.CensusStatsModule.enabled", "false");
            System.setProperty("io.grpc.internal.CensusTracingModule.enabled", "false");
            
            // 8. 网络安全配置
            System.setProperty("com.sun.jndi.ldap.object.trustURLCodebase", "false");
            System.setProperty("com.sun.jndi.rmi.object.trustURLCodebase", "false");
            System.setProperty("com.sun.jndi.cosnaming.object.trustURLCodebase", "false");
            
            // 9. 禁用Spring Cloud配置 - 简化启动过程
            System.setProperty("spring.jta.enabled", "false");
            System.setProperty("spring.cloud.loadbalancer.configurations", "default");
            System.setProperty("spring.cloud.loadbalancer.ribbon.enabled", "false");
            System.setProperty("spring.cloud.refresh.refreshable", "none");
            System.setProperty("spring.cloud.refresh.enabled", "false");
            System.setProperty("spring.cloud.loadbalancer.stats.micrometer.enabled", "false");
            System.setProperty("spring.cloud.refresh.extra-refreshable", "none");
            System.setProperty("spring.cloud.loadbalancer.cache.enabled", "false");
            System.setProperty("spring.cloud.loadbalancer.enabled", "false");
            System.setProperty("spring.cloud.compatibility-verifier.enabled", "false");

            // 启动应用程序 - 直接使用SpringApplication而不是builder
            SpringApplication.run(TcmIntegrationApplication.class, args);
            System.out.println("(♥◠‿◠)ﾉﾞ  系统集成服务启动成功   ლ(´ڡ`ლ)ﾞ");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}