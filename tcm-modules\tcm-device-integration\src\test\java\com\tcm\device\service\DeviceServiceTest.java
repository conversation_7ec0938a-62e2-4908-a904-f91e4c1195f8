package com.tcm.device.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.device.domain.Device;
import com.tcm.device.dto.DeviceDTO;
import com.tcm.device.mapper.DeviceMapper;
import com.tcm.device.service.impl.DeviceServiceImpl;
import com.tcm.device.vo.DeviceVO;

/**
 * 设备服务测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
public class DeviceServiceTest {

    @Mock
    private DeviceMapper deviceMapper;

    @InjectMocks
    private DeviceServiceImpl deviceService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetDeviceDetail() {
        // 准备测试数据
        Long deviceId = 1L;
        Device device = new Device();
        device.setId(deviceId);
        device.setDeviceName("测试设备");
        device.setDeviceCode("TEST001");
        device.setStatus(1);

        // 设置Mock行为
        when(deviceMapper.selectById(deviceId)).thenReturn(device);

        // 调用测试方法
        DeviceVO result = deviceService.getDeviceDetail(deviceId);

        // 验证结果
        assertNotNull(result);
        assertEquals(deviceId, result.getId());
        assertEquals("测试设备", result.getDeviceName());
        assertEquals("TEST001", result.getDeviceCode());
        assertEquals(1, result.getStatus());
        assertEquals("在线", result.getStatusDesc());

        // 验证调用
        verify(deviceMapper, times(1)).selectById(deviceId);
    }

    @Test
    public void testAddDevice() {
        // 准备测试数据
        DeviceDTO deviceDTO = new DeviceDTO();
        deviceDTO.setDeviceName("新设备");
        deviceDTO.setDeviceCode("NEW001");
        deviceDTO.setDeviceType("测试类型");

        // 设置Mock行为 - 成功插入
        when(deviceMapper.insert(any(Device.class))).thenReturn(1);

        // 调用测试方法
        int result = deviceService.addDevice(deviceDTO);

        // 验证结果
        assertEquals(1, result);

        // 验证调用
        verify(deviceMapper, times(1)).insert(any(Device.class));
    }

    @Test
    public void testGetOnlineDevices() {
        // 准备测试数据
        List<Device> deviceList = new ArrayList<>();
        Device device1 = new Device();
        device1.setId(1L);
        device1.setDeviceName("在线设备1");
        device1.setStatus(1);

        Device device2 = new Device();
        device2.setId(2L);
        device2.setDeviceName("在线设备2");
        device2.setStatus(1);

        deviceList.add(device1);
        deviceList.add(device2);

        // 设置Mock行为
        when(deviceMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(deviceList);

        // 调用测试方法
        List<DeviceVO> result = deviceService.getOnlineDevices();

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("在线设备1", result.get(0).getDeviceName());
        assertEquals("在线设备2", result.get(1).getDeviceName());
        assertEquals("在线", result.get(0).getStatusDesc());

        // 验证调用
        verify(deviceMapper, times(1)).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    public void testUpdateDevice() {
        // 准备测试数据
        Long deviceId = 1L;
        DeviceDTO deviceDTO = new DeviceDTO();
        deviceDTO.setId(deviceId);
        deviceDTO.setDeviceName("更新设备");
        deviceDTO.setDeviceCode("UPDATE001");

        Device existingDevice = new Device();
        existingDevice.setId(deviceId);
        existingDevice.setDeviceName("原设备");
        existingDevice.setDeviceCode("OLD001");

        // 设置Mock行为
        when(deviceMapper.selectById(deviceId)).thenReturn(existingDevice);
        when(deviceMapper.updateById(any(Device.class))).thenReturn(1);

        // 调用测试方法
        int result = deviceService.updateDevice(deviceDTO);

        // 验证结果
        assertEquals(1, result);

        // 验证调用
        verify(deviceMapper, times(1)).selectById(deviceId);
        verify(deviceMapper, times(1)).updateById(any(Device.class));
    }
}