package com.tcm.platform.admin.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;

import com.tcm.platform.admin.dto.Result;
import com.tcm.platform.admin.entity.SysMenu;
import com.tcm.platform.admin.service.SysMenuService;

import java.util.List;

/**
 * 系统菜单控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/menu")
public class SysMenuController extends BaseController {

    @Autowired
    private SysMenuService menuService;

    /**
     * 获取菜单列表
     */
    @GetMapping("/list")
    public Result<List<SysMenu>> list(
            @RequestParam(value = "menuName", required = false) String menuName,
            @RequestParam(value = "menuType", required = false) String menuType,
            @RequestParam(value = "status", required = false) Integer status) {

        SysMenu menu = new SysMenu();
        menu.setMenuName(menuName);
        menu.setMenuType(menuType);
        menu.setStatus(status);

        List<SysMenu> menus = menuService.selectMenuList(menu);
        return Result.success(menus);
    }

    /**
     * 获取用户菜单树
     */
    @GetMapping("/tree")
    public Result<List<SysMenu>> getMenuTree() {
        Long userId = getCurrentUserId();
        if (userId == null) {
            return Result.error("获取用户信息失败");
        }

        List<SysMenu> menus = menuService.buildMenuTree(userId);
        return Result.success(menus);
    }

    /**
     * 获取菜单详情
     */
    @GetMapping("/{menuId}")
    public Result<SysMenu> getInfo(@PathVariable Long menuId) {
        SysMenu menu = menuService.selectMenuById(menuId);
        if (menu == null) {
            return Result.error("菜单不存在");
        }
        return Result.success(menu);
    }

    /**
     * 新增菜单
     */
    @PostMapping
    public Result<Void> add(@Validated @RequestBody SysMenu menu) {
        boolean success = menuService.insertMenu(menu);
        return success ? Result.success() : Result.error("新增菜单失败");
    }

    /**
     * 修改菜单
     */
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody SysMenu menu) {
        if (menu.getId() == null) {
            return Result.error("菜单ID不能为空");
        }

        boolean success = menuService.updateMenu(menu);
        return success ? Result.success() : Result.error("修改菜单失败");
    }

    /**
     * 删除菜单
     */
    @DeleteMapping("/{menuId}")
    public Result<Void> remove(@PathVariable Long menuId) {
        try {
            boolean success = menuService.deleteMenuById(menuId);
            return success ? Result.success() : Result.error("删除菜单失败");
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }
}