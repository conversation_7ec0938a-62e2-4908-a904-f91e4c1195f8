package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 问诊消息视图对象
 */
@Data
public class ConsultationMessageVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息ID
     */
    private Long messageId;
    
    /**
     * 问诊ID
     */
    private Long consultationId;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 发送者类型（1-医生 2-患者 3-系统）
     */
    private Integer senderType;
    
    /**
     * 发送者类型名称
     */
    private String senderTypeName;
    
    /**
     * 发送者姓名
     */
    private String senderName;
    
    /**
     * 发送者头像
     */
    private String senderAvatar;
    
    /**
     * 接收者ID
     */
    private Long receiverId;
    
    /**
     * 接收者姓名
     */
    private String receiverName;
    
    /**
     * 消息类型（1-文本 2-图片 3-语音 4-视频 5-处方 6-系统通知）
     */
    private Integer messageType;
    
    /**
     * 消息类型名称
     */
    private String messageTypeName;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 媒体URL（如图片、语音、视频的地址）
     */
    private String mediaUrl;
    
    /**
     * 媒体时长（语音或视频的时长，单位：秒）
     */
    private Integer mediaDuration;
    
    /**
     * 是否已读（0-未读 1-已读）
     */
    private Integer readStatus;
    
    /**
     * 是否撤回（0-否 1-是）
     */
    private Integer recalled;
    
    /**
     * 发送时间
     */
    private Date createTime;
} 