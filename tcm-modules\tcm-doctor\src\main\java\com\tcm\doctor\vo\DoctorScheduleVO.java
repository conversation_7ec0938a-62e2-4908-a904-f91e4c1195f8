package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生排班视图对象
 */
@Data
public class DoctorScheduleVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 排班ID
     */
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生职称
     */
    private String titleName;
    
    /**
     * 所属科室
     */
    private String department;
    
    /**
     * 排班日期
     */
    private Date scheduleDate;
    
    /**
     * 星期几
     */
    private String weekDay;
    
    /**
     * 时段类型（1-上午 2-下午 3-晚上）
     */
    private Integer periodType;
    
    /**
     * 时段名称
     */
    private String periodName;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 限诊人数
     */
    private Integer limitCount;
    
    /**
     * 已预约人数
     */
    private Integer appointmentCount;
    
    /**
     * 剩余名额
     */
    private Integer remainingCount;
    
    /**
     * 出诊状态（0-正常 1-停诊）
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 挂号费
     */
    private Double registrationFee;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 