package com.tcm.pharmacy;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 药房服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        org.redisson.spring.starter.RedissonAutoConfiguration.class,
        com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration.class
})
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.tcm.pharmacy.mapper")
public class TcmPharmacyApplication {

    public static void main(String[] args) {
        SpringApplication.run(TcmPharmacyApplication.class, args);
    }
}
