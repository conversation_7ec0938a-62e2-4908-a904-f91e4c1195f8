package com.tcm.schedule.service.impl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.domain.PageVO;
import com.tcm.schedule.dto.ScheduleDTO;
import com.tcm.schedule.entity.Schedule;
import com.tcm.schedule.mapper.ScheduleMapper;
import com.tcm.schedule.service.ScheduleService;
import com.tcm.schedule.vo.ScheduleVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 排班服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleServiceImpl extends ServiceImpl<ScheduleMapper, Schedule> implements ScheduleService {

    /**
     * 查询排班分页列表
     *
     * @param dto 查询条件
     * @return 分页列表
     */
    @Override
    public PageVO<ScheduleVO> selectSchedulePage(ScheduleDTO dto) {
        Schedule schedule = new Schedule();
        schedule.setDoctorId(dto.getDoctorId());
        schedule.setDoctorName(dto.getDoctorName());
        schedule.setDeptId(dto.getDeptId());
        schedule.setShiftType(dto.getShiftType());
        schedule.setStatus(dto.getStatus());

        Page<Schedule> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<ScheduleVO> pageData = baseMapper.selectSchedulePage(page, schedule);

        return new PageVO<>(pageData.getRecords(), pageData.getTotal(), dto.getPageSize(), dto.getPageNum());
    }

    /**
     * 查询排班详细信息
     *
     * @param id 排班ID
     * @return 排班信息
     */
    @Override
    public ScheduleVO getScheduleById(Long id) {
        return baseMapper.selectScheduleById(id);
    }

    /**
     * 新增排班
     *
     * @param schedule 排班信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addSchedule(Schedule schedule) {
        schedule.setCreateTime(LocalDateTime.now());
        schedule.setAppointmentCount(0);
        return save(schedule);
    }

    /**
     * 修改排班
     *
     * @param schedule 排班信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSchedule(Schedule schedule) {
        schedule.setUpdateTime(LocalDateTime.now());
        return updateById(schedule);
    }

    /**
     * 删除排班
     *
     * @param ids 排班ID数组
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteScheduleByIds(Long[] ids) {
        // 逻辑删除
        return update(new Schedule(), new LambdaQueryWrapper<Schedule>().in(Schedule::getId, Arrays.asList(ids))
                .set(Schedule::getDelFlag, 1));
    }

    /**
     * 查询医生排班列表
     *
     * @param doctorId  医生ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 排班列表
     */
    @Override
    public List<ScheduleVO> getScheduleByDoctorId(Long doctorId, LocalDateTime beginDate, LocalDateTime endDate) {
        return baseMapper.selectScheduleByDoctorId(doctorId, beginDate, endDate);
    }

    /**
     * 查询科室排班列表
     *
     * @param deptId    科室ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 排班列表
     */
    @Override
    public List<ScheduleVO> getScheduleByDeptId(Long deptId, LocalDateTime beginDate, LocalDateTime endDate) {
        return baseMapper.selectScheduleByDeptId(deptId, beginDate, endDate);
    }

    /**
     * 更新排班预约数
     *
     * @param id 排班ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAppointmentCount(Long id) {
        return baseMapper.updateAppointmentCount(id) > 0;
    }
}