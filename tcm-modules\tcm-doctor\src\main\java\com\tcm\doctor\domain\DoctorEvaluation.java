package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医生评价实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_doctor_evaluation")
public class DoctorEvaluation extends BaseEntity {
    
    /**
     * 评价ID
     */
    @TableId
    private Long evaluationId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 评分（1-5分）
     */
    private Integer score;
    
    /**
     * 评价内容
     */
    private String content;
    
    /**
     * 是否匿名（0-否 1-是）
     */
    private Integer anonymous;
    
    /**
     * 医生回复
     */
    private String reply;
    
    /**
     * 状态（0-显示 1-隐藏）
     */
    private Integer status;
} 