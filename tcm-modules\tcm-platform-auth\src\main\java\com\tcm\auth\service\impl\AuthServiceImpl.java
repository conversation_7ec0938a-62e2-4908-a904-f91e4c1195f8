package com.tcm.auth.service.impl;

import com.tcm.auth.domain.Role;
import com.tcm.auth.domain.User;
import com.tcm.auth.dto.LoginDTO;
import com.tcm.auth.mapper.MenuMapper;
import com.tcm.auth.mapper.RoleMapper;
import com.tcm.auth.mapper.UserMapper;
import com.tcm.auth.service.AuthService;
import com.tcm.auth.util.JwtUtils;
import com.tcm.auth.vo.LoginUserVO;
import com.tcm.auth.vo.RoleVO;
import com.tcm.auth.vo.TokenVO;
import com.tcm.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 认证服务实现类
 */
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final UserMapper userMapper;
    private final RoleMapper roleMapper;
    private final MenuMapper menuMapper;
    private final JwtUtils jwtUtils;
    private final RedisTemplate<String, Object> redisTemplate;
    private final BCryptPasswordEncoder passwordEncoder;

    private static final String TOKEN_PREFIX = "auth:token:";
    private static final String USER_PREFIX = "auth:user:";

    /**
     * 用户登录
     */
    @Override
    public TokenVO login(LoginDTO loginDTO) {
        // 验证用户名密码
        User user = userMapper.selectByUsername(loginDTO.getUsername());
        if (user == null) {
            throw new ServiceException("用户名或密码错误");
        }

        if (user.getStatus() == 0) {
            throw new ServiceException("账号已被禁用");
        }

        if (!passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new ServiceException("用户名或密码错误");
        }

        // 更新登录信息
        String ip = getClientIp();
        userMapper.updateLoginInfo(user.getId(), ip);

        // 生成token
        String accessToken = jwtUtils.generateAccessToken(user.getId());
        String refreshToken = jwtUtils.generateRefreshToken(user.getId());
        Long expiresIn = jwtUtils.getExpirationFromToken(accessToken);

        // 缓存用户信息
        LoginUserVO loginUserVO = getUserInfo(user);
        redisTemplate.opsForValue().set(USER_PREFIX + user.getId(), loginUserVO, expiresIn, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(TOKEN_PREFIX + accessToken, user.getId(), expiresIn, TimeUnit.SECONDS);

        // 返回结果
        return TokenVO.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .build();
    }

    /**
     * 刷新令牌
     */
    @Override
    public TokenVO refreshToken(String refreshToken) {
        // 验证刷新令牌
        if (!jwtUtils.validateToken(refreshToken)) {
            throw new ServiceException("刷新令牌已过期");
        }

        // 获取用户信息
        Long userId = jwtUtils.getUserIdFromToken(refreshToken);
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        if (user.getStatus() == 0) {
            throw new ServiceException("账号已被禁用");
        }

        // 生成新token
        String accessToken = jwtUtils.generateAccessToken(userId);
        String newRefreshToken = jwtUtils.generateRefreshToken(userId);
        Long expiresIn = jwtUtils.getExpirationFromToken(accessToken);

        // 缓存用户信息
        LoginUserVO loginUserVO = getUserInfo(user);
        redisTemplate.opsForValue().set(USER_PREFIX + userId, loginUserVO, expiresIn, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(TOKEN_PREFIX + accessToken, userId, expiresIn, TimeUnit.SECONDS);

        // 返回结果
        return TokenVO.builder()
                .accessToken(accessToken)
                .refreshToken(newRefreshToken)
                .tokenType("Bearer")
                .expiresIn(expiresIn)
                .build();
    }

    /**
     * 获取用户信息
     */
    @Override
    public LoginUserVO getUserInfo(String token) {
        // 验证令牌
        if (!jwtUtils.validateToken(token)) {
            throw new ServiceException("令牌已过期");
        }

        // 获取用户信息
        Long userId = jwtUtils.getUserIdFromToken(token);
        Object cacheUser = redisTemplate.opsForValue().get(USER_PREFIX + userId);

        if (cacheUser != null) {
            return (LoginUserVO) cacheUser;
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("用户不存在");
        }

        return getUserInfo(user);
    }

    /**
     * 退出登录
     */
    @Override
    public void logout(String token) {
        if (!jwtUtils.validateToken(token)) {
            return;
        }

        Long userId = jwtUtils.getUserIdFromToken(token);
        redisTemplate.delete(USER_PREFIX + userId);
        redisTemplate.delete(TOKEN_PREFIX + token);
    }

    /**
     * 获取用户信息
     */
    private LoginUserVO getUserInfo(User user) {
        LoginUserVO loginUserVO = new LoginUserVO();
        loginUserVO.setUserId(user.getId());
        loginUserVO.setUsername(user.getUsername());
        loginUserVO.setRealName(user.getRealName());
        loginUserVO.setAvatar(user.getAvatar());
        loginUserVO.setDeptId(user.getDeptId());

        // 查询用户角色
        List<Role> roles = roleMapper.selectRolesByUserId(user.getId());
        if (roles != null && !roles.isEmpty()) {
            List<RoleVO> roleVOList = roles.stream().map(role -> {
                RoleVO roleVO = new RoleVO();
                roleVO.setRoleId(role.getId());
                roleVO.setRoleName(role.getRoleName());
                roleVO.setRoleCode(role.getRoleCode());
                return roleVO;
            }).collect(Collectors.toList());
            loginUserVO.setRoles(roleVOList);
        }

        // 查询用户权限
        List<String> permissions = menuMapper.selectPermsByUserId(user.getId());
        loginUserVO.setPermissions(permissions);

        return loginUserVO;
    }

    /**
     * 获取客户端IP
     */
    private String getClientIp() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            String ip = attributes.getRequest().getHeader("X-Forwarded-For");
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = attributes.getRequest().getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = attributes.getRequest().getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = attributes.getRequest().getHeader("HTTP_CLIENT_IP");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = attributes.getRequest().getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                ip = attributes.getRequest().getRemoteAddr();
            }
            return ip;
        }
        return "unknown";
    }
}