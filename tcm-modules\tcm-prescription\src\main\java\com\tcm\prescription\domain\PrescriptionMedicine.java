package com.tcm.prescription.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 处方药品实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_prescription_medicine")
public class PrescriptionMedicine extends BaseEntity {
    
    /**
     * 处方药品ID
     */
    @TableId
    private Long prescriptionMedicineId;
    
    /**
     * 处方ID
     */
    private Long prescriptionId;
    
    /**
     * 药品ID
     */
    private Long medicineId;
    
    /**
     * 药品名称
     */
    private String medicineName;
    
    /**
     * 药品类型（1：中药材，2：中成药，3：西药）
     */
    private Integer medicineType;
    
    /**
     * 药品规格
     */
    private String specification;
    
    /**
     * 剂量
     */
    private BigDecimal dosage;
    
    /**
     * 剂量单位
     */
    private String dosageUnit;
    
    /**
     * 单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 小计金额
     */
    private BigDecimal amount;
    
    /**
     * 用法
     */
    private String usage;
    
    /**
     * 特殊要求（如：先煎、后下等）
     */
    private String specialRequirement;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 备注
     */
    private String remark;
} 