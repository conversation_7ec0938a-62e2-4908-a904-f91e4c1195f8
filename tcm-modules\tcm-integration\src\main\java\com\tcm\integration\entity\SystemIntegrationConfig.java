package com.tcm.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统集成配置实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_integration_config")
public class SystemIntegrationConfig {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 系统类型（HIS、LIS、PACS等）
     */
    private String systemType;

    /**
     * 接口地址
     */
    private String apiUrl;

    /**
     * 认证类型（None、Basic、OAuth、Token等）
     */
    private String authType;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码（加密存储）
     */
    private String password;

    /**
     * 令牌
     */
    private String token;

    /**
     * 令牌有效期
     */
    private LocalDateTime tokenExpireTime;

    /**
     * 连接超时时间(毫秒)
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间(毫秒)
     */
    private Integer readTimeout;

    /**
     * 系统描述
     */
    private String description;

    /**
     * 状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}