package com.tcm.knowledge.service;

import com.tcm.knowledge.dto.KnowledgeItemDTO;
import com.tcm.knowledge.vo.KnowledgeItemVO;
import java.util.List;

public interface IKnowledgeItemService {
    Long addKnowledgeItem(KnowledgeItemDTO dto);

    boolean updateKnowledgeItem(Long id, KnowledgeItemDTO dto);

    boolean deleteKnowledgeItem(Long id);

    KnowledgeItemVO getKnowledgeItem(Long id);

    List<KnowledgeItemVO> listKnowledgeItems(String type, String keyword);
}