package com.tcm.diagnosis.service;

import com.tcm.diagnosis.domain.PulseData;
import com.tcm.diagnosis.dto.PulseDataCaptureDTO;
import com.tcm.diagnosis.vo.PulseDataVO;

import java.util.List;

/**
 * 脉诊数据服务接口
 */
public interface IPulseDataService {

    /**
     * 上传并保存脉诊数据
     *
     * @param pulseDataDTO 脉诊数据
     * @return 数据ID
     */
    Long uploadPulseData(PulseDataCaptureDTO pulseDataDTO);

    /**
     * 获取脉诊数据详情
     *
     * @param dataId 数据ID
     * @return 脉诊数据视图对象
     */
    PulseDataVO getPulseData(Long dataId);

    /**
     * 获取诊断记录关联的脉诊数据列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 脉诊数据视图对象列表
     */
    List<PulseDataVO> getPulseDataListByDiagnosisId(Long diagnosisId);

    /**
     * 获取患者的脉诊数据列表
     *
     * @param patientId 患者ID
     * @return 脉诊数据视图对象列表
     */
    List<PulseDataVO> getPulseDataListByPatientId(Long patientId);

    /**
     * 删除脉诊数据
     *
     * @param dataId 数据ID
     * @return 是否成功
     */
    boolean deletePulseData(Long dataId);

    /**
     * 获取最近的脉诊数据列表
     *
     * @param patientId 患者ID
     * @param limit     限制数量
     * @return 脉诊数据视图对象列表
     */
    List<PulseDataVO> getRecentPulseData(Long patientId, Integer limit);
}