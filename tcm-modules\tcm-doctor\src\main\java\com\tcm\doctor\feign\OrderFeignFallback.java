package com.tcm.doctor.feign;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单服务远程调用降级处理
 */
@Component
public class OrderFeignFallback implements OrderFeignClient {
    
    private static final Logger log = LoggerFactory.getLogger(OrderFeignFallback.class);

    @Override
    public BigDecimal getTotalAmount(Long doctorId, LocalDate startDate, LocalDate endDate) {
        log.error("远程调用订单服务获取收费总金额失败，doctorId: {}, 时间范围: {} - {}", doctorId, startDate, endDate);
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getRevisitRate(Long doctorId, LocalDate startDate, LocalDate endDate) {
        log.error("远程调用订单服务获取复诊率失败，doctorId: {}, 时间范围: {} - {}", doctorId, startDate, endDate);
        return BigDecimal.ZERO;
    }
}
