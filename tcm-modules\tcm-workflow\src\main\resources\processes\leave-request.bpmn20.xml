<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" 
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
             xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
             xmlns:flowable="http://flowable.org/bpmn" 
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" 
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" 
             typeLanguage="http://www.w3.org/2001/XMLSchema" 
             expressionLanguage="http://www.w3.org/1999/XPath" 
             targetNamespace="http://www.flowable.org/processdef">
    
    <process id="leave-request" name="请假申请流程" isExecutable="true">
        
        <documentation>员工请假申请流程</documentation>
        
        <startEvent id="startEvent" name="开始" flowable:formKey="leave-request-form">
            <documentation>开始请假流程</documentation>
        </startEvent>
        
        <userTask id="fillForm" name="填写请假申请" flowable:candidateGroups="employees" flowable:formKey="leave-request-form">
            <documentation>填写请假申请表单</documentation>
        </userTask>
        
        <userTask id="managerApproval" name="主管审批" flowable:candidateGroups="managers" flowable:formKey="leave-approval-form">
            <documentation>主管审批请假申请</documentation>
        </userTask>
        
        <userTask id="hrApproval" name="人事审批" flowable:candidateGroups="hr" flowable:formKey="hr-approval-form">
            <documentation>人事部门审批请假申请</documentation>
        </userTask>
        
        <exclusiveGateway id="approvalGateway" name="审批结果" />
        
        <endEvent id="approvedEndEvent" name="批准结束" />
        
        <endEvent id="rejectedEndEvent" name="拒绝结束" />
        
        <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="fillForm" />
        <sequenceFlow id="flow2" sourceRef="fillForm" targetRef="managerApproval" />
        <sequenceFlow id="flow3" sourceRef="managerApproval" targetRef="approvalGateway" />
        <sequenceFlow id="flow4" sourceRef="approvalGateway" targetRef="hrApproval">
            <conditionExpression xsi:type="tFormalExpression">${approved == true}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow5" sourceRef="approvalGateway" targetRef="rejectedEndEvent">
            <conditionExpression xsi:type="tFormalExpression">${approved == false}</conditionExpression>
        </sequenceFlow>
        <sequenceFlow id="flow6" sourceRef="hrApproval" targetRef="approvedEndEvent" />
    </process>
    
    <bpmndi:BPMNDiagram id="BPMNDiagram_leave-request">
        <bpmndi:BPMNPlane bpmnElement="leave-request" id="BPMNPlane_leave-request">
            <bpmndi:BPMNShape id="shape-startEvent" bpmnElement="startEvent">
                <omgdc:Bounds x="100" y="100" width="30" height="30" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="shape-fillForm" bpmnElement="fillForm">
                <omgdc:Bounds x="180" y="80" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="shape-managerApproval" bpmnElement="managerApproval">
                <omgdc:Bounds x="330" y="80" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="shape-approvalGateway" bpmnElement="approvalGateway">
                <omgdc:Bounds x="480" y="100" width="40" height="40" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="shape-hrApproval" bpmnElement="hrApproval">
                <omgdc:Bounds x="570" y="80" width="100" height="80" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="shape-approvedEndEvent" bpmnElement="approvedEndEvent">
                <omgdc:Bounds x="720" y="100" width="30" height="30" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNShape id="shape-rejectedEndEvent" bpmnElement="rejectedEndEvent">
                <omgdc:Bounds x="500" y="190" width="30" height="30" />
            </bpmndi:BPMNShape>
            <bpmndi:BPMNEdge id="edge-flow1" bpmnElement="flow1">
                <omgdi:waypoint x="130" y="115" />
                <omgdi:waypoint x="180" y="115" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="edge-flow2" bpmnElement="flow2">
                <omgdi:waypoint x="280" y="115" />
                <omgdi:waypoint x="330" y="115" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="edge-flow3" bpmnElement="flow3">
                <omgdi:waypoint x="430" y="115" />
                <omgdi:waypoint x="480" y="115" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="edge-flow4" bpmnElement="flow4">
                <omgdi:waypoint x="520" y="115" />
                <omgdi:waypoint x="570" y="115" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="edge-flow5" bpmnElement="flow5">
                <omgdi:waypoint x="500" y="140" />
                <omgdi:waypoint x="500" y="190" />
            </bpmndi:BPMNEdge>
            <bpmndi:BPMNEdge id="edge-flow6" bpmnElement="flow6">
                <omgdi:waypoint x="670" y="115" />
                <omgdi:waypoint x="720" y="115" />
            </bpmndi:BPMNEdge>
        </bpmndi:BPMNPlane>
    </bpmndi:BPMNDiagram>
    
</definitions> 