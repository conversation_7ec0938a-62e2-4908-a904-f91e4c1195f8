package com.tcm.common.core.utils.file;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件类型判断工具类
 */
public class FileTypeUtils {

    private static final Map<String, String> FILE_TYPE_MAP = new HashMap<>();
    
    static {
        // 初始化图片类型
        FILE_TYPE_MAP.put("jpg", "image/jpeg");
        FILE_TYPE_MAP.put("jpeg", "image/jpeg");
        FILE_TYPE_MAP.put("png", "image/png");
        FILE_TYPE_MAP.put("gif", "image/gif");
        FILE_TYPE_MAP.put("bmp", "image/bmp");
        
        // 初始化文档类型
        FILE_TYPE_MAP.put("doc", "application/msword");
        FILE_TYPE_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        FILE_TYPE_MAP.put("xls", "application/vnd.ms-excel");
        FILE_TYPE_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        FILE_TYPE_MAP.put("pdf", "application/pdf");
        FILE_TYPE_MAP.put("txt", "text/plain");
        
        // 初始化音视频类型
        FILE_TYPE_MAP.put("mp3", "audio/mpeg");
        FILE_TYPE_MAP.put("mp4", "video/mp4");
        FILE_TYPE_MAP.put("wav", "audio/wav");
    }
    
    /**
     * 根据文件扩展名获取MIME类型
     *
     * @param extension 文件扩展名
     * @return MIME类型
     */
    public static String getMimeType(String extension) {
        if (extension == null || extension.isEmpty()) {
            return "application/octet-stream";
        }
        String ext = extension.toLowerCase();
        if (ext.startsWith(".")) {
            ext = ext.substring(1);
        }
        String mimeType = FILE_TYPE_MAP.get(ext);
        return mimeType != null ? mimeType : "application/octet-stream";
    }
    
    /**
     * 判断文件是否为图片
     *
     * @param fileName 文件名
     * @return 是否为图片
     */
    public static boolean isImage(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        String extension = getFileExtension(fileName);
        String mimeType = getMimeType(extension);
        return mimeType.startsWith("image/");
    }
    
    /**
     * 判断文件是否为视频
     *
     * @param fileName 文件名
     * @return 是否为视频
     */
    public static boolean isVideo(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        String extension = getFileExtension(fileName);
        String mimeType = getMimeType(extension);
        return mimeType.startsWith("video/");
    }
    
    /**
     * 判断文件是否为音频
     *
     * @param fileName 文件名
     * @return 是否为音频
     */
    public static boolean isAudio(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }
        String extension = getFileExtension(fileName);
        String mimeType = getMimeType(extension);
        return mimeType.startsWith("audio/");
    }
    
    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
    }
    
    /**
     * 判断文件实际类型
     *
     * @param file 文件
     * @return 文件类型
     */
    public static String getFileType(File file) {
        try (InputStream is = Files.newInputStream(file.toPath())) {
            byte[] b = new byte[10];
            is.read(b, 0, b.length);
            String hexString = bytesToHexString(b);
            
            if (hexString.startsWith("FFD8FF")) {
                return "jpg";
            } else if (hexString.startsWith("89504E47")) {
                return "png";
            } else if (hexString.startsWith("47494638")) {
                return "gif";
            } else if (hexString.startsWith("424D")) {
                return "bmp";
            } else if (hexString.startsWith("25504446")) {
                return "pdf";
            } else if (hexString.startsWith("504B0304")) {
                return "docx/xlsx/zip";
            }
            
            return "unknown";
        } catch (IOException e) {
            return "unknown";
        }
    }
    
    private static String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (byte b : src) {
            int v = b & 0xFF;
            String hv = Integer.toHexString(v).toUpperCase();
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }
    
    /**
     * 通过ByteArrayInputStream获取图片文件类型
     *
     * @param inputStream 输入流
     * @return 图片文件类型
     */
    public static String getImageFileType(ByteArrayInputStream inputStream) {
        inputStream.mark(10);
        byte[] b = new byte[10];
        inputStream.read(b, 0, b.length);
        inputStream.reset();
        
        String hexString = bytesToHexString(b);
        
        if (hexString.startsWith("FFD8FF")) {
            return "jpg";
        } else if (hexString.startsWith("89504E47")) {
            return "png";
        } else if (hexString.startsWith("47494638")) {
            return "gif";
        } else if (hexString.startsWith("424D")) {
            return "bmp";
        }
        
        return "unknown";
    }
}
