package com.tcm.admin.service.impl;

import com.tcm.admin.service.MenuCacheService;
import com.tcm.admin.service.MenuService;
import com.tcm.admin.vo.MenuVO;
import com.tcm.admin.vo.TreeSelectVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 菜单权限缓存服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MenuCacheServiceImpl implements MenuCacheService {

    private final MenuService menuService;
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 缓存过期时间（秒）
     */
    private static final long CACHE_EXPIRATION = 3600L;

    /**
     * 用户菜单列表缓存键
     */
    private static final String USER_MENU_KEY = "menu:user:%d:list";

    /**
     * 用户权限集合缓存键
     */
    private static final String USER_PERMS_KEY = "menu:user:%d:perms";

    /**
     * 菜单树选择缓存键
     */
    private static final String MENU_TREE_KEY = "menu:user:%d:tree";

    /**
     * 获取用户菜单列表（从缓存中获取）
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<MenuVO> getUserMenuListCache(Long userId) {
        String cacheKey = String.format(USER_MENU_KEY, userId);
        Object cacheValue = redisTemplate.opsForValue().get(cacheKey);

        if (cacheValue != null) {
            log.debug("从缓存获取用户菜单列表, userId={}", userId);
            return (List<MenuVO>) cacheValue;
        }

        log.debug("缓存未命中，从数据库获取用户菜单列表, userId={}", userId);
        List<MenuVO> menuList = menuService.selectMenuListByUserId(userId);

        // 存入缓存
        if (menuList != null && !menuList.isEmpty()) {
            redisTemplate.opsForValue().set(cacheKey, menuList, CACHE_EXPIRATION, TimeUnit.SECONDS);
        }

        return menuList;
    }

    /**
     * 获取用户权限集合（从缓存中获取）
     *
     * @param userId 用户ID
     * @return 权限集合
     */
    @Override
    @SuppressWarnings("unchecked")
    public Set<String> getUserPermissionsCache(Long userId) {
        String cacheKey = String.format(USER_PERMS_KEY, userId);
        Object cacheValue = redisTemplate.opsForValue().get(cacheKey);

        if (cacheValue != null) {
            log.debug("从缓存获取用户权限集合, userId={}", userId);
            return (Set<String>) cacheValue;
        }

        log.debug("缓存未命中，从数据库获取用户权限集合, userId={}", userId);
        Set<String> permSet = menuService.selectMenuPermsByUserId(userId);

        // 存入缓存
        if (permSet != null && !permSet.isEmpty()) {
            redisTemplate.opsForValue().set(cacheKey, permSet, CACHE_EXPIRATION, TimeUnit.SECONDS);
        }

        return permSet;
    }

    /**
     * 获取菜单树选择列表（从缓存中获取）
     *
     * @param userId 用户ID
     * @return 菜单树选择列表
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<TreeSelectVO> getMenuTreeSelectCache(Long userId) {
        String cacheKey = String.format(MENU_TREE_KEY, userId);
        Object cacheValue = redisTemplate.opsForValue().get(cacheKey);

        if (cacheValue != null) {
            log.debug("从缓存获取菜单树选择列表, userId={}", userId);
            return (List<TreeSelectVO>) cacheValue;
        }

        log.debug("缓存未命中，从数据库获取菜单树选择列表, userId={}", userId);
        List<MenuVO> menuList = menuService.selectMenuListByUserId(userId);
        List<TreeSelectVO> treeSelectList = menuService.buildMenuTreeSelect(menuList);

        // 存入缓存
        if (treeSelectList != null && !treeSelectList.isEmpty()) {
            redisTemplate.opsForValue().set(cacheKey, treeSelectList, CACHE_EXPIRATION, TimeUnit.SECONDS);
        }

        return treeSelectList;
    }

    /**
     * 清除用户菜单缓存
     *
     * @param userId 用户ID
     */
    @Override
    public void clearUserMenuCache(Long userId) {
        String cacheKey = String.format(USER_MENU_KEY, userId);
        redisTemplate.delete(cacheKey);
        log.debug("清除用户菜单缓存, userId={}", userId);
    }

    /**
     * 清除用户权限缓存
     *
     * @param userId 用户ID
     */
    @Override
    public void clearUserPermissionsCache(Long userId) {
        String cacheKey = String.format(USER_PERMS_KEY, userId);
        redisTemplate.delete(cacheKey);
        log.debug("清除用户权限缓存, userId={}", userId);
    }

    /**
     * 清除菜单相关所有缓存
     */
    @Override
    public void clearAllMenuCache() {
        Set<String> keys = redisTemplate.keys("menu:*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
            log.debug("清除所有菜单缓存, keys.size={}", keys.size());
        }
    }
}