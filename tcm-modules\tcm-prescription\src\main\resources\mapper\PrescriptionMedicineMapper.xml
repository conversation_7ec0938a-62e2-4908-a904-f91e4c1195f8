<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.prescription.mapper.PrescriptionMedicineMapper">

    <resultMap id="PrescriptionMedicineResult" type="com.tcm.prescription.domain.PrescriptionMedicine">
        <id property="prescriptionMedicineId" column="prescription_medicine_id"/>
        <result property="prescriptionId" column="prescription_id"/>
        <result property="medicineId" column="medicine_id"/>
        <result property="medicineName" column="medicine_name"/>
        <result property="medicineType" column="medicine_type"/>
        <result property="specification" column="specification"/>
        <result property="dosage" column="dosage"/>
        <result property="dosageUnit" column="dosage_unit"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="usage" column="usage"/>
        <result property="specialRequirement" column="special_requirement"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <sql id="selectPrescriptionMedicineVo">
        SELECT pm.prescription_medicine_id,
               pm.prescription_id,
               pm.medicine_id,
               pm.medicine_name,
               pm.medicine_type,
               pm.specification,
               pm.dosage,
               pm.dosage_unit,
               pm.unit_price,
               pm.amount,
               pm.usage,
               pm.special_requirement,
               pm.sort_order,
               pm.create_by,
               pm.create_time,
               pm.update_by,
               pm.update_time,
               pm.remark,
               pm.del_flag
        FROM tcm_prescription_medicine pm
    </sql>
    
    <select id="selectPrescriptionMedicineList" parameterType="Long" resultMap="PrescriptionMedicineResult">
        <include refid="selectPrescriptionMedicineVo"/>
        WHERE pm.prescription_id = #{prescriptionId} AND pm.del_flag = 0
        ORDER BY pm.sort_order ASC
    </select>
    
</mapper>