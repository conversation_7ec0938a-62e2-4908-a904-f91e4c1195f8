package com.tcm.common.core.utils.uuid;

/**
 * ID生成工具类
 */
public class IdUtils {
    
    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UuidUtils.randomUUID();
    }
    
    /**
     * 获取简化的UUID，不带连接符
     *
     * @return 简化的UUID，不带连接符
     */
    public static String simpleUUID() {
        return UuidUtils.simpleUUID();
    }
    
    /**
     * 获取指定长度的UUID
     *
     * @param length UUID长度
     * @return 指定长度的UUID
     */
    public static String fastUUID(int length) {
        return UuidUtils.randomUUID(length);
    }
    
    /**
     * 获取快速的UUID，不带连接符
     *
     * @return 快速的UUID，不带连接符
     */
    public static String fastUUID() {
        return UuidUtils.simpleUUID();
    }
    
    /**
     * 检验字符串是否为有效的UUID
     * 
     * @param uuid 待检验的字符串
     * @return 是否为有效的UUID
     */
    public static boolean isValidUUID(String uuid) {
        return UuidUtils.isValidUUID(uuid);
    }
}
