package com.tcm.doctor.controller;

import com.tcm.common.core.domain.R;
import com.tcm.doctor.dto.ConsultationMessageDTO;
import com.tcm.doctor.service.IConsultationMessageService;
import com.tcm.doctor.vo.ConsultationMessageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 问诊消息控制器
 */
@RestController
@RequestMapping("/doctor/message")
public class ConsultationMessageController {
    
    @Autowired
    private IConsultationMessageService messageService;
    
    /**
     * 查询问诊消息列表
     */
    @GetMapping("/list/{consultationId}")
    public R<List<ConsultationMessageVO>> list(@PathVariable Long consultationId) {
        List<ConsultationMessageVO> messages = messageService.listMessages(consultationId);
        return R.ok(messages);
    }
    
    /**
     * 获取消息详情
     */
    @GetMapping("/{messageId}")
    public R<ConsultationMessageVO> getInfo(@PathVariable Long messageId) {
        ConsultationMessageVO message = messageService.getMessageById(messageId);
        return R.ok(message);
    }
    
    /**
     * 发送消息
     */
    @PostMapping
    public R<Boolean> send(@RequestBody @Valid ConsultationMessageDTO messageDTO) {
        boolean result = messageService.sendMessage(messageDTO);
        return R.ok(result);
    }
    
    /**
     * 发送系统消息
     */
    @PostMapping("/system/{consultationId}")
    public R<Boolean> sendSystem(@PathVariable Long consultationId, @RequestParam String content) {
        boolean result = messageService.sendSystemMessage(consultationId, content);
        return R.ok(result);
    }
    
    /**
     * 更新消息已读状态
     */
    @PutMapping("/read/{messageId}")
    public R<Boolean> read(@PathVariable Long messageId) {
        boolean result = messageService.readMessage(messageId);
        return R.ok(result);
    }
    
    /**
     * 批量更新消息已读状态
     */
    @PutMapping("/read/all/{consultationId}/{receiverId}")
    public R<Boolean> readAll(@PathVariable Long consultationId, @PathVariable Long receiverId) {
        boolean result = messageService.readAllMessages(consultationId, receiverId);
        return R.ok(result);
    }
    
    /**
     * 撤回消息
     */
    @PutMapping("/recall/{messageId}/{senderId}")
    public R<Boolean> recall(@PathVariable Long messageId, @PathVariable Long senderId) {
        boolean result = messageService.recallMessage(messageId, senderId);
        return R.ok(result);
    }
    
    /**
     * 统计未读消息数
     */
    @GetMapping("/unread/count/{consultationId}/{receiverId}")
    public R<Integer> countUnread(@PathVariable Long consultationId, @PathVariable Long receiverId) {
        int count = messageService.countUnreadMessages(consultationId, receiverId);
        return R.ok(count);
    }
} 