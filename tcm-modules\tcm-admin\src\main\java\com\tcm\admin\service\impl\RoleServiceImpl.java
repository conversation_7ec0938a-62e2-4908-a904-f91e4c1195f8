package com.tcm.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.admin.dto.RoleDTO;
import com.tcm.admin.entity.Role;
import com.tcm.admin.mapper.RoleMapper;
import com.tcm.admin.service.RoleService;
import com.tcm.admin.vo.RoleVO;
import com.tcm.common.core.domain.PageVO;
import com.tcm.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    /**
     * 分页查询角色列表
     *
     * @param roleDTO  查询条件
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @return 角色分页数据
     */
    @Override
    public PageVO<RoleVO> listByPage(RoleDTO roleDTO, Integer pageNum, Integer pageSize) {
        // 构建查询条件
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();

        if (roleDTO != null) {
            // 角色名称模糊查询
            if (StringUtils.hasText(roleDTO.getRoleName())) {
                queryWrapper.like(Role::getRoleName, roleDTO.getRoleName());
            }

            // 角色编码模糊查询
            if (StringUtils.hasText(roleDTO.getRoleCode())) {
                queryWrapper.like(Role::getRoleCode, roleDTO.getRoleCode());
            }

            // 状态精确查询
            if (roleDTO.getStatus() != null) {
                queryWrapper.eq(Role::getStatus, roleDTO.getStatus());
            }
        }

        // 按排序升序、创建时间降序排列
        queryWrapper.orderByAsc(Role::getRoleSort).orderByDesc(Role::getCreateTime);

        // 执行分页查询
        Page<Role> page = new Page<>(pageNum, pageSize);
        page = page(page, queryWrapper);

        // 转换为VO
        List<RoleVO> roleVOList = new ArrayList<>();
        if (page.getRecords() != null && !page.getRecords().isEmpty()) {
            roleVOList = page.getRecords().stream().map(this::convertToVO).collect(Collectors.toList());
        }

        // 返回分页结果
        return new PageVO<>(roleVOList, page.getTotal(), pageNum, pageSize);
    }

    /**
     * 根据ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    @Override
    public RoleVO getById(Long id) {
        Role role = super.getById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        RoleVO roleVO = convertToVO(role);

        // 查询角色关联的菜单ID
        List<Long> menuIds = baseMapper.selectMenuIdsByRoleId(id);
        roleVO.setMenuIds(menuIds.toArray(new Long[0]));

        // 如果是自定义数据权限，则查询角色关联的部门ID
        if (role.getDataScope() != null && role.getDataScope() == 2) {
            List<Long> deptIds = baseMapper.selectDeptIdsByRoleId(id);
            roleVO.setDeptIds(deptIds.toArray(new Long[0]));
        }

        return roleVO;
    }

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(RoleDTO roleDTO) {
        // 检查角色名称是否重复
        if (checkRoleNameExists(roleDTO.getRoleName(), null)) {
            throw new BusinessException("角色名称已存在");
        }

        // 检查角色编码是否重复
        if (checkRoleCodeExists(roleDTO.getRoleCode(), null)) {
            throw new BusinessException("角色编码已存在");
        }

        // 构建角色实体
        Role role = new Role();
        BeanUtils.copyProperties(roleDTO, role);

        // 设置默认值
        if (role.getStatus() == null) {
            role.setStatus(0); // 默认启用
        }
        if (role.getDataScope() == null) {
            role.setDataScope(3); // 默认本部门数据权限
        }
        role.setCreateTime(LocalDateTime.now());

        // 保存角色
        boolean result = save(role);

        // 保存角色菜单关联
        if (result && roleDTO.getMenuIds() != null && roleDTO.getMenuIds().length > 0) {
            saveRoleMenus(role.getId(), roleDTO.getMenuIds());
        }

        // 保存角色部门关联（如果是自定义数据权限）
        if (result && role.getDataScope() == 2 && roleDTO.getDeptIds() != null && roleDTO.getDeptIds().length > 0) {
            saveRoleDepts(role.getId(), roleDTO.getDeptIds());
        }

        return result;
    }

    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(RoleDTO roleDTO) {
        // 检查角色是否存在
        Role existingRole = super.getById(roleDTO.getId());
        if (existingRole == null) {
            throw new BusinessException("角色不存在");
        }

        // 检查角色名称是否重复
        if (checkRoleNameExists(roleDTO.getRoleName(), roleDTO.getId())) {
            throw new BusinessException("角色名称已存在");
        }

        // 检查角色编码是否重复
        if (checkRoleCodeExists(roleDTO.getRoleCode(), roleDTO.getId())) {
            throw new BusinessException("角色编码已存在");
        }

        // 更新角色信息
        Role role = new Role();
        BeanUtils.copyProperties(roleDTO, role);
        role.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(role);

        // 更新角色菜单关联
        if (result && roleDTO.getMenuIds() != null) {
            // 先删除原有关联
            deleteRoleMenus(role.getId());
            // 再保存新的关联
            if (roleDTO.getMenuIds().length > 0) {
                saveRoleMenus(role.getId(), roleDTO.getMenuIds());
            }
        }

        // 更新角色部门关联（如果是自定义数据权限）
        if (result && role.getDataScope() != null && role.getDataScope() == 2) {
            // 先删除原有关联
            deleteRoleDepts(role.getId());
            // 再保存新的关联
            if (roleDTO.getDeptIds() != null && roleDTO.getDeptIds().length > 0) {
                saveRoleDepts(role.getId(), roleDTO.getDeptIds());
            }
        }

        return result;
    }

    /**
     * 删除角色
     *
     * @param ids 角色ID数组
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return false;
        }

        // 检查是否有内置角色
        for (Long id : ids) {
            Role role = super.getById(id);
            if (role != null && "admin".equals(role.getRoleCode())) {
                throw new BusinessException("系统内置角色不能删除");
            }
        }

        // 检查角色是否已分配用户
        // 省略实现，需要增加用户角色表查询逻辑

        // 删除角色菜单关联
        Arrays.stream(ids).forEach(this::deleteRoleMenus);

        // 删除角色部门关联
        Arrays.stream(ids).forEach(this::deleteRoleDepts);

        // 删除角色
        return removeByIds(Arrays.asList(ids));
    }

    /**
     * 修改角色状态
     *
     * @param id     角色ID
     * @param status 角色状态
     * @return 修改结果
     */
    @Override
    public boolean changeStatus(Long id, Integer status) {
        if (id == null || status == null) {
            throw new BusinessException("参数错误");
        }

        // 检查是否为内置角色
        Role role = super.getById(id);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        if ("admin".equals(role.getRoleCode())) {
            throw new BusinessException("系统内置角色不能修改状态");
        }

        // 更新状态
        Role updateRole = new Role();
        updateRole.setId(id);
        updateRole.setStatus(status);
        updateRole.setUpdateTime(LocalDateTime.now());

        return updateById(updateRole);
    }

    /**
     * 分配角色菜单权限
     *
     * @param roleId  角色ID
     * @param menuIds 菜单ID数组
     * @return 分配结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignMenus(Long roleId, Long[] menuIds) {
        // 检查角色是否存在
        Role role = super.getById(roleId);
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 删除原有关联
        deleteRoleMenus(roleId);

        // 保存新的关联
        if (menuIds != null && menuIds.length > 0) {
            return saveRoleMenus(roleId, menuIds);
        }

        return true;
    }

    /**
     * 分配角色数据权限
     *
     * @param roleDTO 角色DTO（包含ID、数据范围、部门ID列表）
     * @return 分配结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignDataScope(RoleDTO roleDTO) {
        // 检查角色是否存在
        Role role = super.getById(roleDTO.getId());
        if (role == null) {
            throw new BusinessException("角色不存在");
        }

        // 更新数据权限范围
        Role updateRole = new Role();
        updateRole.setId(roleDTO.getId());
        updateRole.setDataScope(roleDTO.getDataScope());
        updateRole.setUpdateTime(LocalDateTime.now());

        boolean result = updateById(updateRole);

        // 如果是自定义数据权限，需要更新角色部门关联
        if (result && roleDTO.getDataScope() == 2) {
            // 删除原有关联
            deleteRoleDepts(roleDTO.getId());

            // 保存新的关联
            if (roleDTO.getDeptIds() != null && roleDTO.getDeptIds().length > 0) {
                saveRoleDepts(roleDTO.getId(), roleDTO.getDeptIds());
            }
        }

        return result;
    }

    /**
     * 查询用户拥有的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Override
    public List<RoleVO> getUserRoles(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        List<Role> roles = baseMapper.selectRolesByUserId(userId);
        if (roles == null || roles.isEmpty()) {
            return new ArrayList<>();
        }

        return roles.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 查询所有可用角色列表
     *
     * @return 角色列表
     */
    @Override
    public List<RoleVO> listAllEnabledRoles() {
        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getStatus, 0); // 状态正常
        queryWrapper.orderByAsc(Role::getRoleSort);

        List<Role> roles = list(queryWrapper);
        if (roles == null || roles.isEmpty()) {
            return new ArrayList<>();
        }

        return roles.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 将实体转换为VO
     *
     * @param role 角色实体
     * @return 角色VO
     */
    private RoleVO convertToVO(Role role) {
        if (role == null) {
            return null;
        }

        RoleVO roleVO = new RoleVO();
        BeanUtils.copyProperties(role, roleVO);

        // 设置数据范围描述
        if (role.getDataScope() != null) {
            switch (role.getDataScope()) {
                case 1:
                    roleVO.setDataScopeDesc("全部数据权限");
                    break;
                case 2:
                    roleVO.setDataScopeDesc("自定义数据权限");
                    break;
                case 3:
                    roleVO.setDataScopeDesc("本部门数据权限");
                    break;
                case 4:
                    roleVO.setDataScopeDesc("本部门及以下数据权限");
                    break;
                default:
                    roleVO.setDataScopeDesc("未知");
            }
        }

        // 设置状态描述
        if (role.getStatus() != null) {
            roleVO.setStatusDesc(role.getStatus() == 0 ? "正常" : "停用");
        }

        return roleVO;
    }

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param roleId   角色ID（更新时排除自身）
     * @return 是否存在
     */
    private boolean checkRoleNameExists(String roleName, Long roleId) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }

        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getRoleName, roleName);
        if (roleId != null) {
            queryWrapper.ne(Role::getId, roleId);
        }

        return count(queryWrapper) > 0;
    }

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param roleId   角色ID（更新时排除自身）
     * @return 是否存在
     */
    private boolean checkRoleCodeExists(String roleCode, Long roleId) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }

        LambdaQueryWrapper<Role> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Role::getRoleCode, roleCode);
        if (roleId != null) {
            queryWrapper.ne(Role::getId, roleId);
        }

        return count(queryWrapper) > 0;
    }

    /**
     * 保存角色菜单关联
     *
     * @param roleId  角色ID
     * @param menuIds 菜单ID数组
     * @return 保存结果
     */
    private boolean saveRoleMenus(Long roleId, Long[] menuIds) {
        // 这里需要实现保存角色菜单关联关系的逻辑
        // 由于没有角色菜单关联表的实体类和Mapper，此处省略具体实现
        log.info("保存角色菜单关联，roleId: {}, menuIds: {}", roleId, menuIds);
        return true;
    }

    /**
     * 删除角色菜单关联
     *
     * @param roleId 角色ID
     */
    private void deleteRoleMenus(Long roleId) {
        // 这里需要实现删除角色菜单关联关系的逻辑
        // 由于没有角色菜单关联表的实体类和Mapper，此处省略具体实现
        log.info("删除角色菜单关联，roleId: {}", roleId);
    }

    /**
     * 保存角色部门关联
     *
     * @param roleId  角色ID
     * @param deptIds 部门ID数组
     * @return 保存结果
     */
    private boolean saveRoleDepts(Long roleId, Long[] deptIds) {
        // 这里需要实现保存角色部门关联关系的逻辑
        // 由于没有角色部门关联表的实体类和Mapper，此处省略具体实现
        log.info("保存角色部门关联，roleId: {}, deptIds: {}", roleId, deptIds);
        return true;
    }

    /**
     * 删除角色部门关联
     *
     * @param roleId 角色ID
     */
    private void deleteRoleDepts(Long roleId) {
        // 这里需要实现删除角色部门关联关系的逻辑
        // 由于没有角色部门关联表的实体类和Mapper，此处省略具体实现
        log.info("删除角色部门关联，roleId: {}", roleId);
    }
}