package com.tcm.consultation.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 会诊参与人数据传输对象
 */
@Data
public class ParticipantDTO {

    /**
     * 参与记录ID（更新时需要）
     */
    private Long participantId;

    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    /**
     * 参与类型（1：主诊医生，2：会诊医生，3：专家）
     */
    @NotNull(message = "参与类型不能为空")
    private Integer type;
}