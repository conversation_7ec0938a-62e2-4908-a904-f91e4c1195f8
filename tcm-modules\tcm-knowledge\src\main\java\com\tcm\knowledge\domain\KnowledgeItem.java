package com.tcm.knowledge.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.util.Date;

/**
 * 知识库条目实体
 */
@Data
@TableName("tcm_knowledge_item")
public class KnowledgeItem {
    @TableId
    private Long id;
    private String title;
    private String content;
    private String type; // 例如：方剂、病证、药材、理论等
    private String tags; // 逗号分隔
    private Date createTime;
    private Date updateTime;
}