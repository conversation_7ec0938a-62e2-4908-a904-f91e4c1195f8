@echo off
echo 正在启动TCM检查服务...

set JAVA_OPTS=-Xms256m -Xmx512m -Dfile.encoding=UTF-8
set NACOS_ADDR=localhost:8848
set NACOS_USER=nacos
set NACOS_PASS=nacos
set SERVER_PORT=9308

echo 使用以下配置:
echo Nacos地址: %NACOS_ADDR%
echo 服务端口: %SERVER_PORT%

java %JAVA_OPTS% -jar target\tcm-inspection-1.0.0.jar ^
--spring.cloud.nacos.discovery.server-addr=%NACOS_ADDR% ^
--spring.cloud.nacos.discovery.username=%NACOS_USER% ^
--spring.cloud.nacos.discovery.password=%NACOS_PASS% ^
--spring.cloud.nacos.config.server-addr=%NACOS_ADDR% ^
--spring.cloud.nacos.config.username=%NACOS_USER% ^
--spring.cloud.nacos.config.password=%NACOS_PASS% ^
--server.port=%SERVER_PORT% ^
--spring.profiles.active=dev

pause 