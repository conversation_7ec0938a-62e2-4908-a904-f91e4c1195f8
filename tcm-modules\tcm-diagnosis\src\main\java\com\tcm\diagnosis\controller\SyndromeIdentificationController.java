package com.tcm.diagnosis.controller;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.log.annotation.Log;
import com.tcm.common.log.enums.BusinessType;
import com.tcm.common.security.annotation.RequiresPermissions;
import com.tcm.diagnosis.dto.SyndromeIdentificationDTO;
import com.tcm.diagnosis.service.ISyndromeIdentificationService;
import com.tcm.diagnosis.vo.SimilarCaseVO;
import com.tcm.diagnosis.vo.SyndromeIdentificationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 辨证控制器
 */
@RestController
@RequestMapping("/syndrome")
public class SyndromeIdentificationController extends BaseController {

    @Autowired
    private ISyndromeIdentificationService syndromeIdentificationService;

    /**
     * 进行辨证
     */
    @PostMapping("/identify")
    @RequiresPermissions("diagnosis:syndrome:identify")
    @Log(title = "辨证分析", businessType = BusinessType.INSERT)
    public R<Long> identifySyndrome(@Validated @RequestBody SyndromeIdentificationDTO syndromeDTO) {
        return R.ok(syndromeIdentificationService.identifySyndrome(syndromeDTO));
    }

    /**
     * 获取辨证结果
     */
    @GetMapping("/{syndromeId}")
    @RequiresPermissions("diagnosis:syndrome:query")
    public R<SyndromeIdentificationVO> getSyndromeIdentification(@PathVariable Long syndromeId) {
        return R.ok(syndromeIdentificationService.getSyndromeIdentification(syndromeId));
    }

    /**
     * 根据诊断ID获取辨证结果
     */
    @GetMapping("/diagnosis/{diagnosisId}")
    @RequiresPermissions("diagnosis:syndrome:query")
    public R<SyndromeIdentificationVO> getSyndromeByDiagnosisId(@PathVariable Long diagnosisId) {
        return R.ok(syndromeIdentificationService.getSyndromeByDiagnosisId(diagnosisId));
    }

    /**
     * 获取患者的辨证结果列表
     */
    @GetMapping("/list/patient/{patientId}")
    @RequiresPermissions("diagnosis:syndrome:list")
    public R<List<SyndromeIdentificationVO>> getSyndromeListByPatientId(@PathVariable Long patientId) {
        return R.ok(syndromeIdentificationService.getSyndromeListByPatientId(patientId));
    }

    /**
     * 医生确认辨证结果
     */
    @PutMapping("/confirm/{syndromeId}/{doctorId}")
    @RequiresPermissions("diagnosis:syndrome:confirm")
    @Log(title = "辨证分析", businessType = BusinessType.UPDATE)
    public R<Boolean> confirmSyndrome(@PathVariable Long syndromeId, @PathVariable Long doctorId) {
        return R.ok(syndromeIdentificationService.confirmSyndrome(syndromeId, doctorId));
    }

    /**
     * 修改辨证结果
     */
    @PutMapping("/update")
    @RequiresPermissions("diagnosis:syndrome:edit")
    @Log(title = "辨证分析", businessType = BusinessType.UPDATE)
    public R<Boolean> updateSyndrome(@Validated @RequestBody SyndromeIdentificationDTO syndromeDTO) {
        return R.ok(syndromeIdentificationService.updateSyndrome(syndromeDTO));
    }

    /**
     * 删除辨证结果
     */
    @DeleteMapping("/{syndromeId}")
    @RequiresPermissions("diagnosis:syndrome:remove")
    @Log(title = "辨证分析", businessType = BusinessType.DELETE)
    public R<Boolean> deleteSyndrome(@PathVariable Long syndromeId) {
        return R.ok(syndromeIdentificationService.deleteSyndrome(syndromeId));
    }

    /**
     * 获取相似病例
     */
    @GetMapping("/similar/{syndromeId}/{limit}")
    @RequiresPermissions("diagnosis:syndrome:similar")
    public R<List<SimilarCaseVO>> getSimilarCases(@PathVariable Long syndromeId, @PathVariable Integer limit) {
        return R.ok(syndromeIdentificationService.getSimilarCases(syndromeId, limit));
    }

    /**
     * 导出辨证报告
     */
    @GetMapping("/export/{syndromeId}")
    @RequiresPermissions("diagnosis:syndrome:export")
    @Log(title = "辨证分析", businessType = BusinessType.EXPORT)
    public R<String> exportSyndromeReport(@PathVariable Long syndromeId) {
        return R.ok(syndromeIdentificationService.exportSyndromeReport(syndromeId));
    }
}