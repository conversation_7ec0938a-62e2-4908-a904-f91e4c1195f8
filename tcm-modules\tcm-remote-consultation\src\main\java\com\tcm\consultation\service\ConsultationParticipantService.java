package com.tcm.consultation.service;

/**
 * 会诊参与者服务接口
 */
public interface ConsultationParticipantService {

    /**
     * 更新参与者在线状态
     * 
     * @param consultationId 会诊ID
     * @param userId         用户ID
     * @param isOnline       是否在线
     * @return 更新结果
     */
    boolean updateOnlineStatus(Long consultationId, Long userId, boolean isOnline);

    /**
     * 获取医生姓名
     * 
     * @param userId 用户ID
     * @return 医生姓名
     */
    String getDoctorName(Long userId);
}