package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.diagnosis.domain.PulseData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 脉诊数据Mapper接口
 */
@Mapper
public interface PulseDataMapper extends BaseMapper<PulseData> {

    /**
     * 根据诊断ID查询脉诊数据列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 脉诊数据列表
     */
    List<PulseData> selectPulseDataByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据患者ID查询脉诊数据列表
     *
     * @param patientId 患者ID
     * @return 脉诊数据列表
     */
    List<PulseData> selectPulseDataByPatientId(@Param("patientId") Long patientId);
}