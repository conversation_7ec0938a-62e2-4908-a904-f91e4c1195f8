package com.tcm.diagnosis.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

/**
 * Redis连接工厂配置，直接从Environment获取Redis配置属性
 */
@Configuration
public class DiagnosisRedisConfig {

    @Autowired
    private Environment env;
    
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        // 从统一配置命名空间获取Redis配置
        String host = env.getProperty("tcm.service.redis.host", "localhost");
        int port = env.getProperty("tcm.service.redis.port", Integer.class, 6379);
        String password = env.getProperty("tcm.service.redis.password", "");
        int database = env.getProperty("tcm.service.redis.database", Integer.class, 0);
        
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        if (!password.isEmpty()) {
            config.setPassword(password);
        }
        config.setDatabase(database);
        
        return new LettuceConnectionFactory(config);
    }
}
