package com.tcm.medication.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.medication.dto.MedicineDTO;
import com.tcm.medication.dto.Result;
import com.tcm.medication.entity.Medicine;
import com.tcm.medication.service.MedicineService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 药品控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/medicine")
public class MedicineController {

    @Autowired
    private MedicineService medicineService;

    /**
     * 分页查询药品列表
     *
     * @param page         页码
     * @param pageSize     每页大小
     * @param medicineName 药品名称
     * @param medicineCode 药品编码
     * @param medicineType 药品类型
     * @param categoryId   分类ID
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<IPage<Medicine>> page(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String medicineName,
            @RequestParam(required = false) String medicineCode,
            @RequestParam(required = false) Integer medicineType,
            @RequestParam(required = false) Long categoryId) {
        IPage<Medicine> result = medicineService.page(page, pageSize, medicineName, medicineCode, medicineType,
                categoryId);
        return Result.success(result);
    }

    /**
     * 获取药品详情
     *
     * @param id 药品ID
     * @return 药品详情
     */
    @GetMapping("/{id}")
    public Result<MedicineDTO> getDetail(@PathVariable Long id) {
        MedicineDTO dto = medicineService.getDetail(id);
        if (dto == null) {
            return Result.error("药品不存在");
        }
        return Result.success(dto);
    }

    /**
     * 新增药品
     *
     * @param dto 药品信息
     * @return 操作结果
     */
    @PostMapping
    public Result<Boolean> add(@RequestBody @Valid MedicineDTO dto) {
        try {
            boolean result = medicineService.add(dto);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新药品
     *
     * @param dto 药品信息
     * @return 操作结果
     */
    @PutMapping
    public Result<Boolean> update(@RequestBody @Valid MedicineDTO dto) {
        if (dto.getId() == null) {
            return Result.error("药品ID不能为空");
        }

        try {
            boolean result = medicineService.update(dto);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除药品
     *
     * @param id 药品ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        if (!medicineService.checkExist(id)) {
            return Result.error("药品不存在");
        }

        boolean result = medicineService.delete(id);
        return Result.success(result);
    }

    /**
     * 更新药品库存
     *
     * @param id         药品ID
     * @param stockDelta 库存变化量
     * @return 操作结果
     */
    @PutMapping("/stock/{id}")
    public Result<Boolean> updateStock(@PathVariable Long id, @RequestParam Integer stockDelta) {
        try {
            boolean result = medicineService.updateStock(id, stockDelta);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 启用药品
     *
     * @param id 药品ID
     * @return 操作结果
     */
    @PutMapping("/enable/{id}")
    public Result<Boolean> enable(@PathVariable Long id) {
        try {
            boolean result = medicineService.enable(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 禁用药品
     *
     * @param id 药品ID
     * @return 操作结果
     */
    @PutMapping("/disable/{id}")
    public Result<Boolean> disable(@PathVariable Long id) {
        try {
            boolean result = medicineService.disable(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }
}