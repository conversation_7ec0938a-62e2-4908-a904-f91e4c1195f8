# TCM Workflow Module

基于Flowable的工作流服务模块，提供流程定义、流程部署、流程执行等功能。

## 功能特性

- 流程定义管理：支持BPMN2.0标准的工作流定义、部署和管理
- 流程实例管理：创建、查询、终止流程实例
- 任务管理：任务分配、任务处理、任务查询
- 表单集成：支持表单数据与流程集成
- 高性能：基于Flowable工作流引擎，提供高性能的工作流处理能力
- 可扩展：支持自定义服务任务、监听器等扩展点

## 技术栈

- Spring Boot 2.x
- Flowable 6.8.0
- MyBatis-Plus
- MySQL
- Redis
- Nacos (服务注册与配置中心)

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.6+
- MySQL 5.7+
- Redis 5.0+
- Nacos 2.x

### 配置说明

主要配置文件：

- `application.yml`: 应用主配置
- `bootstrap.yml`: 引导配置
- `logback-spring.xml`: 日志配置

### 数据库配置

需要创建工作流相关数据库：

```sql
CREATE DATABASE IF NOT EXISTS tcm_workflow DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
```

### 启动服务

1. 启动依赖服务：MySQL、Redis、Nacos

2. 使用Maven构建

```bash
mvn clean package -DskipTests
```

3. 运行服务

```bash
java -jar target/tcm-workflow-1.0.0.jar
```

或者使用IDE直接运行`TcmWorkflowApplication`主类。

### API文档

启动服务后，访问Swagger UI查看API文档：

```
http://localhost:9314/swagger-ui/index.html
```

## 接口说明

### 流程定义接口

- `GET /process/definition/list`: 查询流程定义列表
- `GET /process/definition/{processDefinitionId}`: 获取流程定义详情
- `POST /process/definition/deploy`: 部署流程定义
- `PUT /process/definition`: 修改流程定义
- `DELETE /process/definition/{processDefinitionIds}`: 删除流程定义
- `PUT /process/definition/{processDefinitionId}/{status}`: 激活或挂起流程定义

## 开发指南

### 新增流程

1. 设计BPMN流程
2. 部署流程定义
3. 实现相关服务任务或表单处理逻辑
4. 进行流程测试

### 扩展自定义服务任务

1. 实现`JavaDelegate`接口
2. 在BPMN中引用自定义服务任务
3. 部署流程定义

## 2025-06-17 开发记录

### 会话的主要目的
规范化工作流模块配置，根据项目规则和统一配置进行调整，确保符合项目标准。

### 完成的主要任务
1. 更新bootstrap.yml文件，添加正确的Nacos配置中心配置
2. 修改application.yml，去除硬编码，使用变量引用
3. 修复重复配置和配置冲突问题
4. 添加工作流服务端口到统一配置文件
5. 创建适配JDK17的启动脚本

### 关键决策和解决方案
- 配置优先采用环境变量 > 统一配置 > 默认值的方式
- 移除了application.yml中重复的Nacos配置，统一在bootstrap.yml中配置
- 将硬编码的本地地址(localhost)替换为变量引用
- 为敏感配置如密码和用户名添加了环境变量配置

### 使用的技术栈
- Spring Boot
- Spring Cloud Alibaba
- Nacos配置中心
- Flowable工作流

### 修改的文件
1. tcm-modules/tcm-workflow/src/main/resources/bootstrap.yml
2. tcm-modules/tcm-workflow/src/main/resources/application.yml
3. tcm-common/tcm-common-core/src/main/resources/config/tcm-common.yml
4. tcm-modules/tcm-workflow/start.bat
5. tcm-modules/tcm-workflow/README.md

## 2025-06-23 开发记录

### 会话的主要目的
修复工作流模块启动问题，解决编译错误和配置问题。

### 完成的主要任务
1. 修复TcmWorkflowApplication.java中的Spring占位符问题，将占位符替换为直接的数值
2. 移除了无法解析的自定义注解（@EnableCustomConfig, @EnableCustomSwagger2, @EnableTcmFeignClients）
3. 更新启动脚本，使用编译后的class文件而非jar包
4. 解决了编译错误和运行时异常

### 关键决策和解决方案
- 识别出系统属性中不能使用Spring风格的占位符
- 简化了应用程序配置，只保留必要的标准Spring Boot注解
- 修改启动脚本以使用类路径直接运行应用程序，避免JAR包依赖

### 使用的技术栈
- Spring Boot
- JDK 17
- Flowable工作流

### 修改的文件
1. tcm-modules/tcm-workflow/src/main/java/com/tcm/workflow/TcmWorkflowApplication.java
2. tcm-modules/tcm-workflow/start.bat
3. tcm-modules/tcm-workflow/README.md 