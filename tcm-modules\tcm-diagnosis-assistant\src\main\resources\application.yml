server:
  port: ${tcm.service.diagnosis-assistant.port:${TCM_DIAGNOSIS_ASSISTANT_PORT:9301}}

spring:
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  application:
    name: tcm-diagnosis-assistant
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    password: ${tcm.service.redis.password}
    database: ${tcm.service.redis.database}
  liquibase:
    enabled: false
  thymeleaf:
    enabled: false
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.webflux.WebFluxAutoConfiguration,org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        enabled: true
        metadata:
          "[preserved.heart.beat.interval]": 10000
          "[preserved.heart.beat.timeout]": 30000
          "[preserved.ip.delete.timeout]": 30000
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: application-common.yml
            refresh: true
        timeout: 10000
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/${tcm.service.mysql.database}_assistant?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.tcm.assistant.domain
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.tcm.assistant.controller

# Spring Boot Actuator配置
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      enabled: true
      show-details: when-authorized
  server:
    port: ${server.port}

# 日志配置
logging:
  level:
    "[com.tcm]": debug
    "[org.springframework.web]": info
    "[org.springframework.security]": info
    "[com.alibaba.nacos]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc]": ERROR
    "[com.alibaba.nacos.shaded.io.perfmark]": ERROR
    "[com.alibaba.nacos.common.remote.client]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc.netty]": ERROR
    "[com.alibaba.cloud.nacos]": ERROR
    "[com.alibaba.nacos.client.naming]": ERROR
    "[org.springframework.cloud]": debug
    "[org.springframework.boot.autoconfigure]": warn
    org.springframework.boot: warn
    org.springframework: warn

# RocketMQ配置和AI服务配置
tcm:
  service:
    rocketMq:
      nameServerAddress: ${tcm.service.rocketMq.host}:${tcm.service.rocketMq.port}
      producerGroup: ${spring.application.name}-producer-group
      enable-msg-trace: false
      customized-trace-topic: false
      retry-times-when-send-failed: 0
      retry-times-when-send-async-failed: 0
      check-connection: false
    rocket-mq:
      host: ${tcm.service.rocketMq.host}
      port: ${tcm.service.rocketMq.port}
  ai:
    deepseek:
      api-key: ${DEEPSEEK_API_KEY:your_deepseek_api_key}
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com}
      timeout: 30000