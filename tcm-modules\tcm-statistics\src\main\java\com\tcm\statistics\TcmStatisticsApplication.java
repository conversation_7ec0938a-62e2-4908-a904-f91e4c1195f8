package com.tcm.statistics;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 统计分析模块启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        // 排除所有数据源相关配置，避免Seata问题
        DataSourceAutoConfiguration.class, DataSourceTransactionManagerAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class,
        // 仅排除Redis repositories，但保留Redis本身
        RedisRepositoriesAutoConfiguration.class })
@ComponentScan(basePackages = "com.tcm.statistics")
@MapperScan("com.tcm.statistics.mapper")
public class TcmStatisticsApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmStatisticsApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 彻底禁用PerfMark实现，避免类加载问题
        System.setProperty("io.perfmark.PerfMark.impl", "io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.impl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.startEnabled", "false");

        // 设置一个假的类名，防止通过Class.forName()加载原始类
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.impl.SecretPerfMarkImpl$PerfMarkImpl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");

        // 禁用gRPC通信，强制使用HTTP
        System.setProperty("nacos.client.naming.grpc.enabled", "false");
        System.setProperty("nacos.client.config.grpc.enabled", "false");

        // 处理日志冲突问题
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 彻底禁用Spring Boot Actuator
        System.setProperty("management.endpoints.enabled-by-default", "false");
        System.setProperty("management.endpoint.health.enabled", "false");
        System.setProperty("management.endpoint.info.enabled", "false");
        System.setProperty("management.endpoints.web.exposure.include", "");
        System.setProperty("management.endpoints.jmx.exposure.include", "");
        System.setProperty("spring.jmx.enabled", "false");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-statistics");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_STATISTICS_PORT") == null) {
            System.setProperty("server.port", "9205");
        }
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 彻底解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

            // 优先设置固定端口，确保Spring Boot使用这个端口启动
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.statistics.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_STATISTICS_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "9205";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            // 设置配置文件包含
            System.setProperty("spring.config.import", "optional:classpath:/config/tcm-common.yml");

            // 3. Nacos配置 - 彻底解决Nacos连接问题
            // 禁用Nacos配置导入检查，以避免启动错误
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

            // 禁用Sentinel和Nacos
            System.setProperty("spring.cloud.sentinel.enabled", "false");
            System.setProperty("spring.cloud.sentinel.eager", "false");
            System.setProperty("spring.cloud.nacos.config.enabled", "false");
            System.setProperty("spring.cloud.nacos.discovery.enabled", "false");

            // 设置Seata参数
            System.setProperty("seata.enabled", "false");
            System.setProperty("seata.enable-auto-data-source-proxy", "false");
            System.setProperty("seata.registry.type", "file");
            System.setProperty("seata.config.type", "file");

            // 禁用不需要的Druid功能
            System.setProperty("spring.datasource.druid.web-stat-filter.enabled", "false");
            System.setProperty("spring.datasource.druid.stat-view-servlet.enabled", "false");

            // 设置RocketMQ参数
            System.setProperty("rocketmq.producer.group", "tcm-statistics-producer");
            System.setProperty("rocketmq.enabled", "false");

            // 禁用各种服务组件（除非明确启用）
            System.setProperty("spring.elasticsearch.enabled", "false");
            System.setProperty("spring.data.mongodb.enabled", "false");
            System.setProperty("spring.kafka.enabled", "false");
            System.setProperty("spring.rabbitmq.enabled", "false");

            // 禁用Spring Boot Actuator相关功能
            System.setProperty("management.endpoints.enabled-by-default", "false");
            System.setProperty("management.endpoint.health.enabled", "false");
            System.setProperty("management.endpoint.info.enabled", "false");
            System.setProperty("management.endpoints.web.exposure.include", "");
            System.setProperty("management.endpoints.jmx.exposure.include", "");
            System.setProperty("spring.jmx.enabled", "false");

            // 禁用一切监控
            System.setProperty("spring.boot.admin.client.enabled", "false");

            // 解决Java反射和安全限制问题
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.tryReflectionSetAccessible", "true");

            // 强制禁用所有netty unsafe访问
            System.setProperty("io.netty.noUnsafe", "true");

            // 启动应用并记录日志
            LOGGER.info("正在启动统计分析服务...");
            ConfigurableApplicationContext context = SpringApplication.run(TcmStatisticsApplication.class, args);

            // 获取实际使用的端口（包括随机分配的端口）
            String serverPort;

            // 如果PortListener已经捕获到了实际端口，则使用它
            if (actualServerPort > 0) {
                serverPort = String.valueOf(actualServerPort);
            } else {
                // 尝试从环境中获取端口
                serverPort = context.getEnvironment().getProperty("server.port");

                // 如果环境中没有端口配置，则尝试从系统属性和环境变量获取
                if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                    serverPort = System.getProperty("tcm.service.statistics.port");

                    // 如果系统属性中没有端口配置，则尝试从环境变量获取
                    if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                        serverPort = System.getenv("TCM_STATISTICS_PORT");

                        // 如果环境变量中没有端口配置，则使用默认端口
                        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                            serverPort = "9205";
                        }
                    }
                }
            }

            // 确保端口被设置到系统属性中（使之可被其他组件获取）
            System.setProperty("server.port", serverPort);

            LOGGER.info("=====================================================");
            LOGGER.info("      统计分析服务启动成功!                      ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            System.out.println("  _____   _____ __  __    _____ _        _       ");
            System.out.println(" |_   _| / ____|  \\/  |  / ____| |      | |      ");
            System.out.println("   | |  | |    | \\  / | | (___ | |_ __ _| |_ ___ ");
            System.out.println("   | |  | |    | |\\/| |  \\___ \\| __/ _` | __/ __|");
            System.out.println("  _| |_ | |____| |  | |  ____) | || (_| | |_\\__ \\");
            System.out.println(" |_____| \\_____|_|  |_| |_____/ \\__\\__,_|\\__|___/");
            System.out.println("                                                  ");

            System.out.println("  Started successfully: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort);
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web server initialized with port: {}", actualServerPort);
        }
    }
}