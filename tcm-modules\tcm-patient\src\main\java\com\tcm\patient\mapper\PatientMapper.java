package com.tcm.patient.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.patient.domain.Patient;
import com.tcm.patient.dto.PatientQuery;
import com.tcm.patient.vo.PatientVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 患者Mapper接口
 */
@Mapper
public interface PatientMapper extends BaseMapper<Patient> {
    
    /**
     * 查询患者列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 患者列表
     */
    IPage<PatientVO> selectPatientList(Page<Patient> page, @Param("query") PatientQuery query);
    
    /**
     * 根据ID查询患者详情
     *
     * @param patientId 患者ID
     * @return 患者详情
     */
    PatientVO selectPatientById(@Param("patientId") Long patientId);
    
    /**
     * 根据用户ID查询患者信息
     *
     * @param userId 用户ID
     * @return 患者信息
     */
    PatientVO selectPatientByUserId(@Param("userId") Long userId);
    
    /**
     * 根据身份证号查询患者信息
     *
     * @param idCard 身份证号
     * @return 患者信息
     */
    PatientVO selectPatientByIdCard(@Param("idCard") String idCard);
} 