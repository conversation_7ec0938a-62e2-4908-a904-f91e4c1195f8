package com.tcm.medication.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 药品库存记录实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("tcm_medicine_stock_record")
public class MedicineStockRecord {

    /**
     * 记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 药品ID
     */
    private Long medicineId;

    /**
     * 药品名称
     */
    private String medicineName;

    /**
     * 操作类型（1：入库，2：出库，3：盘点）
     */
    private Integer operationType;

    /**
     * 操作数量
     */
    private Integer quantity;

    /**
     * 操作前库存
     */
    private Integer beforeStock;

    /**
     * 操作后库存
     */
    private Integer afterStock;

    /**
     * 操作单价
     */
    private BigDecimal price;

    /**
     * 操作总额
     */
    private BigDecimal amount;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 生产日期
     */
    private LocalDateTime productionDate;

    /**
     * 有效期至
     */
    private LocalDateTime expiryDate;

    /**
     * 相关单号
     */
    private String relatedNo;

    /**
     * 操作人
     */
    private String operateBy;

    /**
     * 操作时间
     */
    private LocalDateTime operateTime;

    /**
     * 备注
     */
    private String remark;
}