package com.tcm.doctor.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.doctor.domain.DoctorSchedule;
import com.tcm.doctor.dto.DoctorScheduleDTO;
import com.tcm.doctor.mapper.DoctorScheduleMapper;
import com.tcm.doctor.service.IDoctorScheduleService;
import com.tcm.doctor.vo.DoctorScheduleVO;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 模拟医生排班服务实现
 * 仅在开发环境下使用，用于解决依赖注入问题
 */
@Service
@ConditionalOnProperty(name = "tcm.mock.redis", havingValue = "true")
public class MockDoctorScheduleServiceImpl extends ServiceImpl<DoctorScheduleMapper, DoctorSchedule> implements IDoctorScheduleService {

    @Override
    public IPage<DoctorScheduleVO> listDoctorSchedules(Long doctorId, Date beginDate, Date endDate, int pageNum, int pageSize) {
        // 返回空的分页对象
        return new Page<>(pageNum, pageSize);
    }

    @Override
    public DoctorScheduleVO getDoctorScheduleById(Long scheduleId) {
        // 返回null
        return null;
    }

    @Override
    public List<DoctorScheduleVO> getDoctorSchedulesByDoctorIdAndDate(Long doctorId, Date scheduleDate) {
        // 返回空列表
        return new ArrayList<>();
    }

    @Override
    public List<DoctorScheduleVO> getDoctorSchedulesByDepartmentAndDate(String department, Date scheduleDate) {
        // 返回空列表
        return new ArrayList<>();
    }

    @Override
    public boolean addDoctorSchedule(DoctorScheduleDTO scheduleDTO) {
        // 模拟添加成功
        return true;
    }

    @Override
    public boolean updateDoctorSchedule(DoctorScheduleDTO scheduleDTO) {
        // 模拟更新成功
        return true;
    }

    @Override
    public boolean deleteDoctorSchedule(Long scheduleId) {
        // 模拟删除成功
        return true;
    }

    @Override
    public boolean batchAddDoctorSchedules(List<DoctorScheduleDTO> scheduleDTOs) {
        // 模拟批量添加成功
        return true;
    }

    @Override
    public boolean updateDoctorScheduleStatus(Long scheduleId, Integer status) {
        // 模拟更新状态成功
        return true;
    }

    @Override
    public boolean updateAppointmentCount(Long scheduleId, int count) {
        // 模拟更新预约人数成功
        return true;
    }
}
