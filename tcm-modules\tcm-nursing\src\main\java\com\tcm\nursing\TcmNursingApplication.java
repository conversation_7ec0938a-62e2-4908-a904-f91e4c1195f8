package com.tcm.nursing;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 护理服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class,
        org.redisson.spring.starter.RedissonAutoConfiguration.class,
        org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
        com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.class
})
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.tcm.nursing.mapper")
public class TcmNursingApplication {

    public static void main(String[] args) {
        SpringApplication.run(TcmNursingApplication.class, args);
    }
}