package com.tcm.appointment.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约挂号数据传输对象
 */
@Data
public class AppointmentDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预约ID
     */
    private Long appointmentId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生排班ID
     */
    private Long scheduleId;
    
    /**
     * 预约日期
     */
    private Date appointmentDate;
    
    /**
     * 时间段
     */
    private String timeSlot;
    
    /**
     * 预约类型（1-初诊 2-复诊）
     */
    private Integer type;
    
    /**
     * 主要症状
     */
    private String symptom;
    
    /**
     * 就诊科室
     */
    private String department;
    
    /**
     * 费用
     */
    private Double fee;
    
    /**
     * 备注
     */
    private String notes;
} 