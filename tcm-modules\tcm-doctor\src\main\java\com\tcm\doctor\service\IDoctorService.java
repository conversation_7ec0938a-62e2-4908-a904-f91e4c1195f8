package com.tcm.doctor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.doctor.domain.Doctor;
import com.tcm.doctor.dto.DoctorDTO;
import com.tcm.doctor.dto.DoctorQuery;
import com.tcm.doctor.vo.DoctorRecommendVO;
import com.tcm.doctor.vo.DoctorScheduleVO;
import com.tcm.doctor.vo.DoctorVO;
import com.tcm.doctor.vo.DoctorWorkloadVO;

import java.time.LocalDate;
import java.util.List;

/**
 * 医生服务接口
 */
public interface IDoctorService extends IService<Doctor> {

    /**
     * 分页查询医生列表
     *
     * @param query 查询条件
     * @return 医生列表
     */
    IPage<DoctorVO> listDoctors(DoctorQuery query);

    /**
     * 根据ID查询医生详情
     *
     * @param doctorId 医生ID
     * @return 医生详情
     */
    DoctorVO getDoctorById(Long doctorId);

    /**
     * 新增医生
     *
     * @param doctorDTO 医生信息
     * @return 结果
     */
    boolean addDoctor(DoctorDTO doctorDTO);

    /**
     * 修改医生
     *
     * @param doctorDTO 医生信息
     * @return 结果
     */
    boolean updateDoctor(DoctorDTO doctorDTO);

    /**
     * 删除医生
     *
     * @param doctorId 医生ID
     * @return 结果
     */
    boolean deleteDoctor(Long doctorId);

    /**
     * 批量删除医生
     *
     * @param doctorIds 医生ID数组
     * @return 结果
     */
    boolean deleteDoctors(Long[] doctorIds);

    /**
     * 根据科室查询医生列表
     *
     * @param department 科室名称
     * @return 医生列表
     */
    List<DoctorVO> getDoctorsByDepartment(String department);

    /**
     * 根据专长查询医生列表
     *
     * @param specialty 专长
     * @return 医生列表
     */
    List<DoctorVO> getDoctorsBySpecialty(String specialty);

    /**
     * 修改医生状态
     *
     * @param doctorId 医生ID
     * @param status   状态
     * @return 结果
     */
    boolean updateDoctorStatus(Long doctorId, Integer status);

    /**
     * 根据疾病症状推荐医生
     *
     * @param symptom    症状描述
     * @param department 科室（可选）
     * @param limit      推荐数量
     * @return 推荐医生列表
     */
    List<DoctorRecommendVO> recommendDoctorsBySymptom(String symptom, String department, int limit);

    /**
     * 获取医生排班
     *
     * @param doctorId 医生ID
     * @return 排班列表
     */
    List<DoctorScheduleVO> getDoctorSchedule(Long doctorId);

    /**
     * 设置医生排班
     *
     * @param doctorId 医生ID
     * @param schedule 排班信息列表
     * @return 结果
     */
    boolean setDoctorSchedule(Long doctorId, List<DoctorScheduleVO> schedule);

    /**
     * 获取医生工作量统计
     *
     * @param doctorId 医生ID
     * @return 工作量统计
     */
    DoctorWorkloadVO getDoctorWorkloadStatistics(Long doctorId);
    
    /**
     * 获取医生工作量统计（带时间范围）
     *
     * @param doctorId  医生ID
     * @param startDate 统计开始日期
     * @param endDate   统计结束日期
     * @return 工作量统计
     */
    DoctorWorkloadVO getDoctorWorkloadStatistics(Long doctorId, LocalDate startDate, LocalDate endDate);
}