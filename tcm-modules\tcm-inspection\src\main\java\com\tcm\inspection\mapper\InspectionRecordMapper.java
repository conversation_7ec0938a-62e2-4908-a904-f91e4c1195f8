package com.tcm.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.inspection.domain.InspectionRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查记录Mapper接口
 */
public interface InspectionRecordMapper extends BaseMapper<InspectionRecord> {

    /**
     * 查询检查记录列表
     *
     * @param inspectionRecord 检查记录信息
     * @return 检查记录集合
     */
    List<InspectionRecord> selectInspectionRecordList(InspectionRecord inspectionRecord);

    /**
     * 分页查询检查记录列表
     *
     * @param page             分页信息
     * @param inspectionRecord 检查记录信息
     * @return 检查记录集合
     */
    IPage<InspectionRecord> selectInspectionRecordPage(Page<InspectionRecord> page, @Param("record") InspectionRecord inspectionRecord);

    /**
     * 根据ID查询检查记录
     *
     * @param recordId 检查记录ID
     * @return 检查记录信息
     */
    InspectionRecord selectInspectionRecordById(Long recordId);

    /**
     * 根据患者ID查询检查记录列表
     *
     * @param patientId 患者ID
     * @return 检查记录集合
     */
    List<InspectionRecord> selectInspectionRecordByPatientId(Long patientId);

    /**
     * 根据医生ID查询检查记录列表
     *
     * @param doctorId 医生ID
     * @return 检查记录集合
     */
    List<InspectionRecord> selectInspectionRecordByDoctorId(Long doctorId);

    /**
     * 根据科室ID查询检查记录列表
     *
     * @param deptId 科室ID
     * @return 检查记录集合
     */
    List<InspectionRecord> selectInspectionRecordByDeptId(Long deptId);

    /**
     * 根据检查单号查询检查记录
     *
     * @param inspectionNo 检查单号
     * @return 检查记录信息
     */
    InspectionRecord selectInspectionRecordByInspectionNo(String inspectionNo);

    /**
     * 更新检查记录状态
     *
     * @param recordId 检查记录ID
     * @param status   状态
     * @return 结果
     */
    int updateInspectionRecordStatus(@Param("recordId") Long recordId, @Param("status") Integer status);
} 