package com.tcm.common.core.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 */
public class JsonUtils {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    static {
        // 注册处理Java 8日期/时间类型的模块
        objectMapper.registerModule(new JavaTimeModule());
        // 配置不将日期输出为时间戳
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }
    
    /**
     * 对象转JSON字符串
     *
     * @param object 对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("转换JSON字符串失败", e);
        }
    }
    
    /**
     * JSON字符串转对象
     *
     * @param json JSON字符串
     * @param clazz 目标类
     * @param <T> 泛型
     * @return 对象
     */
    public static <T> T parseObject(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析JSON字符串失败", e);
        }
    }
    
    /**
     * JSON字符串转列表
     *
     * @param json JSON字符串
     * @param clazz 列表元素类
     * @param <T> 泛型
     * @return 列表
     */
    public static <T> List<T> parseArray(String json, Class<T> clazz) {
        try {
            JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
            return objectMapper.readValue(json, javaType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析JSON数组字符串失败", e);
        }
    }
    
    /**
     * JSON字符串转Map
     *
     * @param json JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> parseMap(String json) {
        try {
            return objectMapper.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("解析JSON为Map失败", e);
        }
    }
    
    /**
     * 对象转换为指定类型
     *
     * @param object 对象
     * @param clazz 目标类
     * @param <T> 泛型
     * @return 转换后的对象
     */
    public static <T> T convertObject(Object object, Class<T> clazz) {
        return objectMapper.convertValue(object, clazz);
    }
}
