<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.consultation.mapper.ConsultationRecordMapper">
    
    <resultMap id="ConsultationRecordResult" type="com.tcm.consultation.domain.ConsultationRecord">
        <id property="consultationId" column="consultation_id"/>
        <result property="consultationCode" column="consultation_code"/>
        <result property="patientId" column="patient_id"/>
        <result property="primaryDoctorId" column="primary_doctor_id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="appointmentTime" column="appointment_time"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="duration" column="duration"/>
        <result property="status" column="status"/>
        <result property="summary" column="summary"/>
        <result property="diagnosisId" column="diagnosis_id"/>
        <result property="roomId" column="room_id"/>
        <result property="password" column="password"/>
        <result property="videoUrl" column="video_url"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <sql id="selectConsultationRecordVo">
        SELECT cr.consultation_id, 
               cr.consultation_code, 
               cr.patient_id, 
               cr.primary_doctor_id, 
               cr.title, 
               cr.type, 
               cr.appointment_time, 
               cr.start_time, 
               cr.end_time, 
               cr.duration, 
               cr.status, 
               cr.summary, 
               cr.diagnosis_id, 
               cr.room_id, 
               cr.password, 
               cr.video_url, 
               cr.create_by, 
               cr.create_time, 
               cr.update_by, 
               cr.update_time, 
               cr.remark, 
               cr.del_flag
        FROM tcm_consultation_record cr
    </sql>
    
</mapper> 