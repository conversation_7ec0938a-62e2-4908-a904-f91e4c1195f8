package com.tcm.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.tcm.device.domain.Device;
import com.tcm.device.dto.DeviceDTO;
import com.tcm.device.mapper.DeviceMapper;
import com.tcm.device.service.IDeviceService;
import com.tcm.device.vo.DeviceVO;

/**
 * 设备服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {

    @Override
    public IPage<DeviceVO> selectDevicePage(IPage<Device> page, DeviceDTO deviceDTO) {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        // 构建查询条件
        if (deviceDTO != null) {
            if (!StringUtils.isEmpty(deviceDTO.getDeviceName())) {
                queryWrapper.like(Device::getDeviceName, deviceDTO.getDeviceName());
            }
            if (!StringUtils.isEmpty(deviceDTO.getDeviceCode())) {
                queryWrapper.like(Device::getDeviceCode, deviceDTO.getDeviceCode());
            }
            if (!StringUtils.isEmpty(deviceDTO.getDeviceType())) {
                queryWrapper.eq(Device::getDeviceType, deviceDTO.getDeviceType());
            }
            if (!StringUtils.isEmpty(deviceDTO.getManufacturer())) {
                queryWrapper.like(Device::getManufacturer, deviceDTO.getManufacturer());
            }
        }

        // 执行查询
        IPage<Device> resultPage = this.page(page, queryWrapper);

        // 转换为VO对象
        IPage<DeviceVO> voPage = resultPage.convert(this::convertToVO);

        return voPage;
    }

    @Override
    public DeviceVO getDeviceDetail(Long id) {
        Device device = this.getById(id);
        return device != null ? convertToVO(device) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int addDevice(DeviceDTO deviceDTO) {
        Device device = new Device();
        BeanUtils.copyProperties(deviceDTO, device);

        // 设置默认值
        device.setStatus(0); // 默认离线
        device.setRegisterTime(new Date());
        device.setCreateTime(new Date());

        return this.save(device) ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDevice(DeviceDTO deviceDTO) {
        Device device = this.getById(deviceDTO.getId());
        if (device == null) {
            return 0;
        }

        BeanUtils.copyProperties(deviceDTO, device);
        device.setUpdateTime(new Date());

        return this.updateById(device) ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeviceByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return 0;
        }

        // 逻辑删除
        List<Device> devices = new ArrayList<>();
        for (Long id : ids) {
            Device device = new Device();
            device.setId(id);
            device.setUpdateTime(new Date());
            devices.add(device);
        }

        return this.updateBatchById(devices) ? ids.length : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean connectDevice(Long id) {
        Device device = this.getById(id);
        if (device == null) {
            return false;
        }

        // 更新设备状态为在线
        device.setStatus(1);
        device.setLastOnlineTime(new Date());
        device.setUpdateTime(new Date());

        // TODO: 实际设备连接逻辑，通过协议与物理设备建立连接

        return this.updateById(device);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disconnectDevice(Long id) {
        Device device = this.getById(id);
        if (device == null) {
            return false;
        }

        // 更新设备状态为离线
        device.setStatus(0);
        device.setUpdateTime(new Date());

        // TODO: 实际设备断开连接逻辑

        return this.updateById(device);
    }

    @Override
    public List<DeviceVO> getOnlineDevices() {
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getStatus, 1); // 状态为在线

        List<Device> devices = this.list(queryWrapper);
        return devices.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    @Override
    public List<DeviceVO> getDevicesByType(String deviceType) {
        if (StringUtils.isEmpty(deviceType)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getDeviceType, deviceType);

        List<Device> devices = this.list(queryWrapper);
        return devices.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 将设备实体转换为VO对象
     */
    private DeviceVO convertToVO(Device device) {
        if (device == null) {
            return null;
        }

        DeviceVO vo = new DeviceVO();
        BeanUtils.copyProperties(device, vo);

        // 设置状态描述
        if (device.getStatus() != null) {
            switch (device.getStatus()) {
                case 0:
                    vo.setStatusDesc("离线");
                    break;
                case 1:
                    vo.setStatusDesc("在线");
                    break;
                case 2:
                    vo.setStatusDesc("维护中");
                    break;
                default:
                    vo.setStatusDesc("未知");
            }
        }

        return vo;
    }
}