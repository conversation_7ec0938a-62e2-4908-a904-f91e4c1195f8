package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 医生排班实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_doctor_schedule")
public class DoctorSchedule extends BaseEntity {
    
    /**
     * 排班ID
     */
    @TableId
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 排班日期
     */
    private Date scheduleDate;
    
    /**
     * 时段类型（1-上午 2-下午 3-晚上）
     */
    private Integer periodType;
    
    /**
     * 开始时间
     */
    private String startTime;
    
    /**
     * 结束时间
     */
    private String endTime;
    
    /**
     * 限诊人数
     */
    private Integer limitCount;
    
    /**
     * 已预约人数
     */
    private Integer appointmentCount;
    
    /**
     * 出诊状态（0-正常 1-停诊）
     */
    private Integer status;
    
    /**
     * 挂号费
     */
    private Double registrationFee;
} 