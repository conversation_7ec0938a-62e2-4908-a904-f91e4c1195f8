package com.tcm.common.security.annotation;

import java.lang.annotation.*;

/**
 * 权限校验注解
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresPermissions {
    /**
     * 需要校验的权限码
     */
    String value() default "";

    /**
     * 验证模式：AND | OR
     * AND: 需要满足所有权限
     * OR: 满足任一权限即可
     */
    Logical logical() default Logical.AND;

    /**
     * 逻辑操作类型
     */
    enum Logical {
        AND,
        OR
    }
}
