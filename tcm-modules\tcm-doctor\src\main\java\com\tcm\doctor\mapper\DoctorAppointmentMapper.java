package com.tcm.doctor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.doctor.domain.DoctorAppointment;
import com.tcm.doctor.dto.AppointmentQuery;
import com.tcm.doctor.vo.DoctorAppointmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 医生预约Mapper接口
 */
@Mapper
public interface DoctorAppointmentMapper extends BaseMapper<DoctorAppointment> {
    
    /**
     * 查询预约列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 预约列表
     */
    IPage<DoctorAppointmentVO> selectAppointmentList(Page<DoctorAppointment> page, @Param("query") AppointmentQuery query);
    
    /**
     * 根据ID查询预约详情
     *
     * @param appointmentId 预约ID
     * @return 预约详情
     */
    DoctorAppointmentVO selectAppointmentById(@Param("appointmentId") Long appointmentId);
    
    /**
     * 根据患者ID查询预约列表
     *
     * @param patientId 患者ID
     * @return 预约列表
     */
    List<DoctorAppointmentVO> selectAppointmentByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 根据医生ID和日期查询预约列表
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 预约列表
     */
    List<DoctorAppointmentVO> selectAppointmentByDoctorIdAndDate(@Param("doctorId") Long doctorId, @Param("date") Date date);
    
    /**
     * 获取指定排班下一个可用的预约号序
     *
     * @param scheduleId 排班ID
     * @return 下一个可用的预约号序
     */
    Integer selectNextSequenceNumber(@Param("scheduleId") Long scheduleId);
    
    /**
     * 更新预约状态
     *
     * @param appointmentId 预约ID
     * @param status        状态
     * @param updateBy      更新人
     * @return 结果
     */
    int updateAppointmentStatus(@Param("appointmentId") Long appointmentId, 
                               @Param("status") Integer status,
                               @Param("updateBy") String updateBy);
    
    /**
     * 取消预约
     *
     * @param appointmentId 预约ID
     * @param cancelReason  取消原因
     * @param cancelTime    取消时间
     * @param updateBy      更新人
     * @return 结果
     */
    int cancelAppointment(@Param("appointmentId") Long appointmentId,
                         @Param("cancelReason") String cancelReason,
                         @Param("cancelTime") Date cancelTime,
                         @Param("updateBy") String updateBy);
    
    /**
     * 更新就诊信息
     *
     * @param appointmentId 预约ID
     * @param diagnosisId   诊断记录ID
     * @param visitTime     就诊时间
     * @param updateBy      更新人
     * @return 结果
     */
    int updateVisitInfo(@Param("appointmentId") Long appointmentId,
                       @Param("diagnosisId") Long diagnosisId,
                       @Param("visitTime") Date visitTime,
                       @Param("updateBy") String updateBy);
}