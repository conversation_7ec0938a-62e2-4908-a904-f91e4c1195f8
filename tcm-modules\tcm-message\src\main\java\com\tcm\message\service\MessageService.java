package com.tcm.message.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.message.entity.Message;

import java.util.List;

/**
 * 消息服务接口
 */
public interface MessageService extends IService<Message> {

    /**
     * 发送消息
     *
     * @param message 消息实体
     * @return 是否发送成功
     */
    boolean sendMessage(Message message);

    /**
     * 批量发送消息
     *
     * @param message     消息实体
     * @param receiverIds 接收者ID列表
     * @return 是否发送成功
     */
    boolean batchSendMessage(Message message, List<Long> receiverIds);

    /**
     * 获取用户消息列表
     *
     * @param receiverId 接收者ID
     * @param page       分页对象
     * @return 分页消息列表
     */
    Page<Message> getMessageList(Long receiverId, Page<Message> page);

    /**
     * 获取用户未读消息数量
     *
     * @param receiverId 接收者ID
     * @return 未读消息数量
     */
    Integer getUnreadMessageCount(Long receiverId);

    /**
     * 标记消息为已读
     *
     * @param id 消息ID
     * @return 是否标记成功
     */
    boolean markAsRead(Long id);

    /**
     * 标记所有消息为已读
     *
     * @param receiverId 接收者ID
     * @return 是否标记成功
     */
    boolean markAllAsRead(Long receiverId);

    /**
     * 删除消息
     *
     * @param id 消息ID
     * @return 是否删除成功
     */
    boolean deleteMessage(Long id);
}