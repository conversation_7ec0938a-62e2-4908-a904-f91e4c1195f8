package com.tcm.prescription.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.prescription.domain.PrescriptionMedicine;
import com.tcm.prescription.vo.PrescriptionMedicineVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 处方药品Mapper接口
 */
@Mapper
public interface PrescriptionMedicineMapper extends BaseMapper<PrescriptionMedicine> {
    
    /**
     * 根据处方ID查询处方药品列表
     *
     * @param prescriptionId 处方ID
     * @return 处方药品列表
     */
    List<PrescriptionMedicineVO> selectPrescriptionMedicineByPrescriptionId(@Param("prescriptionId") Long prescriptionId);
    
    /**
     * 批量插入处方药品
     *
     * @param prescriptionMedicineList 处方药品列表
     * @return 结果
     */
    int batchInsert(@Param("list") List<PrescriptionMedicine> prescriptionMedicineList);
    
    /**
     * 根据处方ID删除处方药品
     *
     * @param prescriptionId 处方ID
     * @return 结果
     */
    int deleteByPrescriptionId(@Param("prescriptionId") Long prescriptionId);
} 