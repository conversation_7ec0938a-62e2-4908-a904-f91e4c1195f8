package com.tcm.diagnosis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Map;

/**
 * 辨证结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_syndrome_identification")
public class SyndromeIdentification extends BaseEntity {

    /**
     * 辨证ID
     */
    @TableId
    private Long syndromeId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 主要证型
     */
    private String primaryPattern;

    /**
     * 次要证型
     */
    private String secondaryPattern;

    /**
     * 证型类型
     */
    private String syndromeType;
    
    /**
     * 疾病类型
     */
    private String diseaseType;
    
    /**
     * 证型描述
     */
    private String syndromeDesc;

    /**
     * 辨证依据
     */
    private String reasonings;

    /**
     * 临床意义
     */
    private String clinicalImplications;

    /**
     * 建议治法
     */
    private String suggestedTreatment;
    
    /**
     * 治疗原则
     */
    private String treatmentPrinciple;
    
    /**
     * 症状列表（JSON格式）
     */
    private String symptoms;
    
    /**
     * 体征列表（JSON格式）
     */
    private String signs;
    
    /**
     * 病机学说明
     */
    private String pathogenesis;
    
    /**
     * 分析结果（JSON格式）
     */
    private String analysisResult;

    /**
     * 关联症状（JSON格式）
     */
    private String relatedSymptoms;

    /**
     * 关联检查结果（JSON格式）
     */
    private String relatedExaminations;

    /**
     * 辨证方法（规则/AI/混合）
     */
    private String identificationMethod;

    /**
     * 辨证时间
     */
    private Date identificationTime;

    /**
     * 置信度（0-100）
     */
    private Integer confidence;

    /**
     * 是否医生确认（0：未确认，1：已确认）
     */
    private Integer doctorConfirmed;
    
    /**
     * 状态（0：待审核，1：已通过，2：已驳回）
     */
    private Integer status;
    
    /**
     * 确认时间
     */
    private Date confirmTime;
    
    /**
     * 确认人
     */
    private String confirmBy;
}