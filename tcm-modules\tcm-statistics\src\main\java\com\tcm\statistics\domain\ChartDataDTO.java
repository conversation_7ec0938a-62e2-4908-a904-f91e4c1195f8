package com.tcm.statistics.domain;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 图表数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChartDataDTO {
    
    /**
     * 图表标题
     */
    private String title;
    
    /**
     * 图表类型（pie, bar, line等）
     */
    private String type;
    
    /**
     * 类别/标签列表
     */
    private List<String> categories;
    
    /**
     * 数据系列列表
     */
    private List<ChartSeriesDTO> series;
} 