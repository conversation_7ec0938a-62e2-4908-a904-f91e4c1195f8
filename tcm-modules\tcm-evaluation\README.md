# TCM评估模块

## 简介
中医评估模块（tcm-evaluation）是中医智能诊疗系统（TCM System）的核心组件之一，负责管理和执行各类中医评估功能，包括体质评估、症状评估、功能评估和疗效评估等。

## 功能特点
- 支持多种评估类型：体质评估、症状评估、功能评估、疗效评估
- 灵活的评估表单配置：支持标准问卷、自定义问卷和临床量表
- 完整的评估流程：创建、填写、计算、报告生成
- 评估结果统计分析：个体数据分析和群体数据统计

## 技术架构
- 基于Spring Boot 2.6.x构建
- 使用MyBatis-Plus进行数据访问
- 采用Nacos进行服务注册和配置管理
- 支持RESTful API接口规范

## 快速开始
1. 确保已安装JDK 17或更高版本
2. 配置application.yml中的数据库连接
3. 启动服务：`java -jar tcm-evaluation.jar`
4. 默认端口：9301，可通过`server.port`或环境变量`TCM_EVALUATION_PORT`配置

## 主要API
- GET `/api/v1/evaluation/forms` - 获取所有评估表单
- GET `/api/v1/evaluation/forms/{id}` - 获取特定表单
- POST `/api/v1/evaluation/forms` - 创建新表单
- GET `/api/v1/evaluation/records` - 获取评估记录
- POST `/api/v1/evaluation/records` - 提交评估记录

## 数据库设计
包含两个核心表：
1. `eval_form` - 评估表单
2. `eval_record` - 评估记录

## 依赖服务
- 患者服务 (tcm-patient)
- 医生服务 (tcm-doctor)

## 开发说明
- 遵循项目通用的代码规范和设计原则
- 新增的评估类型需在EvaluationConstants中定义常量
- 所有评估算法应封装在独立的服务类中 