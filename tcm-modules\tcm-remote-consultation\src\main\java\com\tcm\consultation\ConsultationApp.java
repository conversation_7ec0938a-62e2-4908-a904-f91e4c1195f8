package com.tcm.consultation;

/**
 * 简单的启动类，避免日志冲突问题和服务依赖问题
 */
public class ConsultationApp {
    public static void main(String[] args) {
        // 1. 设置系统属性，解决日志冲突
        System.setProperty("log4j2.configurationFile", "log4j2.xml");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("org.springframework.boot.logging.LoggingSystem",
                "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem");

        // 2. 允许Bean定义覆盖
        System.setProperty("spring.main.allow-bean-definition-overriding", "true");

        // 3. 禁用Redis自动配置
        System.setProperty("spring.data.redis.enabled", "false");

        // 4. 禁用Nacos自动配置
        System.setProperty("spring.cloud.nacos.discovery.enabled", "false");
        System.setProperty("spring.cloud.nacos.config.enabled", "false");

        // 5. 禁用所有自动配置
        System.setProperty("spring.autoconfigure.exclude",
                "org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,"
                        + "org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration,"
                        + "org.redisson.spring.starter.RedissonAutoConfiguration,"
                        + "com.alibaba.cloud.nacos.NacosDiscoveryAutoConfiguration,"
                        + "com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration,"
                        + "com.alibaba.cloud.nacos.NacosConfigAutoConfiguration,"
                        + "com.alibaba.cloud.sentinel.SentinelAutoConfiguration");

        // 6. 启动应用
        String[] newArgs = { "--spring.cloud.nacos.discovery.enabled=false",
                "--spring.cloud.nacos.config.enabled=false" };
        TcmRemoteConsultationApplication.main(newArgs);
    }
}