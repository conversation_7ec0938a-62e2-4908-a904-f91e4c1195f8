server:
  port: ${tcm.service.diagnosis.port:${TCM_DIAGNOSIS_PORT:9202}}

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: tcm-diagnosis
  # 引入公共配置
  profiles:
    active: dev
    include: common
  # 修改配置导入，引入tcm-common.yml
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
      - optional:classpath:/META-INF/tcm-common.yml
  # 数据库配置 - 明确指定使用HikariCP
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${tcm.service.mysql.url}
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 900000
      auto-commit: true
      connection-test-query: SELECT 1
  # Redis配置，使用统一配置中的属性
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    password: ${tcm.service.redis.password}
    database: ${tcm.service.redis.database}
  cloud:
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        # 禁用导入检查
        import-check:
          enabled: false
    # 禁用Sentinel相关功能
    circuitbreaker:
      enabled: false
    sentinel:
      enabled: false
  # 禁用Liquibase自动配置
  liquibase:
    enabled: false
  # Neo4j配置，使用统一配置中的属性
  neo4j:
    uri: bolt://${tcm.service.neo4j.host}:${tcm.service.neo4j.boltPort}
    authentication:
      username: ${tcm.service.neo4j.username}
      password: ${tcm.service.neo4j.password}


# 日志配置
logging:
  level:
    '[com.tcm.diagnosis]': debug
    '[org.springframework]': warn

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.diagnosis.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# Feign配置
feign:
  sentinel:
    enabled: false
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

# RocketMQ配置已移至application-common.yml中统一管理

