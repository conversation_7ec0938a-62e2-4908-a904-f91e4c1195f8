package com.tcm.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.auth.entity.AuthUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 认证用户Mapper接口
 */
public interface AuthUserMapper extends BaseMapper<AuthUser> {

    /**
     * 根据用户ID获取角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.role_code FROM sys_user_role ur " +
            "LEFT JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE ur.user_id = #{userId} AND ur.del_flag = 0 AND r.del_flag = 0 AND r.status = 1")
    List<String> selectRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID获取权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT m.permission FROM sys_user_role ur " +
            "LEFT JOIN sys_role_menu rm ON ur.role_id = rm.role_id " +
            "LEFT JOIN sys_menu m ON rm.menu_id = m.id " +
            "WHERE ur.user_id = #{userId} AND m.permission IS NOT NULL AND m.permission != '' " +
            "AND ur.del_flag = 0 AND rm.del_flag = 0 AND m.del_flag = 0")
    List<String> selectPermissionsByUserId(@Param("userId") Long userId);
} 
