package com.tcm.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.analytics.entity.AnalyticsModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 分析模型Mapper接口
 */
@Mapper
public interface AnalyticsModelMapper extends BaseMapper<AnalyticsModel> {

    /**
     * 查询可用模型
     * 
     * @param modelType 模型类型
     * @return 模型列表
     */
    @Select("SELECT * FROM analytics_model WHERE model_type = #{modelType} AND status = 1 AND del_flag = 0 ORDER BY update_time DESC")
    List<AnalyticsModel> selectAvailableModels(@Param("modelType") Integer modelType);
    
    /**
     * 查询最新版本模型
     * 
     * @param modelName 模型名称
     * @return 模型
     */
    @Select("SELECT * FROM analytics_model WHERE model_name = #{modelName} AND status = 1 AND del_flag = 0 ORDER BY update_time DESC LIMIT 1")
    AnalyticsModel selectLatestModel(@Param("modelName") String modelName);
    
    /**
     * 更新模型状态
     * 
     * @param id 模型ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE analytics_model SET status = #{status} WHERE id = #{id}")
    int updateModelStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 查询指定创建者的模型
     * 
     * @param creator 创建者
     * @return 模型列表
     */
    @Select("SELECT * FROM analytics_model WHERE creator = #{creator} AND del_flag = 0 ORDER BY create_time DESC")
    List<AnalyticsModel> selectByCreator(@Param("creator") String creator);
} 