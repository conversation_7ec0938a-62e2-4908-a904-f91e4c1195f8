package com.tcm.inspection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.inspection.domain.InspectionCategory;
import com.tcm.inspection.domain.InspectionItem;
import com.tcm.inspection.mapper.InspectionCategoryMapper;
import com.tcm.inspection.mapper.InspectionItemMapper;
import com.tcm.inspection.service.InspectionCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 检查分类服务实现类
 */
@Service
public class InspectionCategoryServiceImpl extends ServiceImpl<InspectionCategoryMapper, InspectionCategory> implements InspectionCategoryService {

    @Autowired
    private InspectionCategoryMapper inspectionCategoryMapper;

    @Autowired
    private InspectionItemMapper inspectionItemMapper;

    /**
     * 查询检查分类列表
     *
     * @param inspectionCategory 检查分类信息
     * @return 检查分类集合
     */
    @Override
    public List<InspectionCategory> selectInspectionCategoryList(InspectionCategory inspectionCategory) {
        return inspectionCategoryMapper.selectInspectionCategoryList(inspectionCategory);
    }

    /**
     * 分页查询检查分类列表
     *
     * @param page               分页信息
     * @param inspectionCategory 检查分类信息
     * @return 检查分类分页信息
     */
    @Override
    public IPage<InspectionCategory> selectInspectionCategoryPage(Page<InspectionCategory> page, InspectionCategory inspectionCategory) {
        return inspectionCategoryMapper.selectInspectionCategoryPage(page, inspectionCategory);
    }

    /**
     * 根据ID查询检查分类
     *
     * @param categoryId 检查分类ID
     * @return 检查分类信息
     */
    @Override
    public InspectionCategory selectInspectionCategoryById(Long categoryId) {
        return inspectionCategoryMapper.selectInspectionCategoryById(categoryId);
    }

    /**
     * 构建检查分类树结构
     *
     * @return 检查分类树列表
     */
    @Override
    public List<InspectionCategory> buildCategoryTree() {
        List<InspectionCategory> categoryList = list(new LambdaQueryWrapper<InspectionCategory>()
                .eq(InspectionCategory::getDelFlag, 0)
                .orderByAsc(InspectionCategory::getOrderNum));
        
        // 构建树形结构
        List<InspectionCategory> returnList = new ArrayList<>();
        List<Long> tempList = new ArrayList<>();
        
        for (InspectionCategory category : categoryList) {
            tempList.add(category.getCategoryId());
        }
        
        for (InspectionCategory category : categoryList) {
            // 如果是顶级节点或者父节点不存在
            if (category.getParentId() == null || category.getParentId() == 0L || !tempList.contains(category.getParentId())) {
                recursionFn(categoryList, category);
                returnList.add(category);
            }
        }
        
        if (returnList.isEmpty()) {
            returnList = categoryList;
        }
        
        return returnList;
    }

    /**
     * 递归构建分类树
     */
    private void recursionFn(List<InspectionCategory> list, InspectionCategory category) {
        // 得到子节点列表
        List<InspectionCategory> childList = getChildList(list, category);
        category.setChildren(childList);
        
        for (InspectionCategory child : childList) {
            // 判断是否有子节点
            if (hasChild(list, child)) {
                recursionFn(list, child);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<InspectionCategory> getChildList(List<InspectionCategory> list, InspectionCategory category) {
        List<InspectionCategory> childList = new ArrayList<>();
        
        for (InspectionCategory child : list) {
            if (child.getParentId() != null && child.getParentId().equals(category.getCategoryId())) {
                childList.add(child);
            }
        }
        
        return childList;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<InspectionCategory> list, InspectionCategory category) {
        return getChildList(list, category).size() > 0;
    }

    /**
     * 新增检查分类
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertInspectionCategory(InspectionCategory inspectionCategory) {
        return save(inspectionCategory);
    }

    /**
     * 修改检查分类
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateInspectionCategory(InspectionCategory inspectionCategory) {
        return updateById(inspectionCategory);
    }

    /**
     * 删除检查分类
     *
     * @param categoryId 检查分类ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteInspectionCategoryById(Long categoryId) {
        // 逻辑删除
        InspectionCategory category = new InspectionCategory();
        category.setCategoryId(categoryId);
        category.setDelFlag(1);
        return updateById(category);
    }

    /**
     * 批量删除检查分类
     *
     * @param categoryIds 需要删除的检查分类ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteInspectionCategoryByIds(Long[] categoryIds) {
        // 批量逻辑删除
        return update(new InspectionCategory().setDelFlag(1),
                new LambdaQueryWrapper<InspectionCategory>()
                        .in(InspectionCategory::getCategoryId, Arrays.asList(categoryIds)));
    }

    /**
     * 检查分类名称是否存在
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    @Override
    public boolean checkCategoryNameUnique(InspectionCategory inspectionCategory) {
        Long categoryId = inspectionCategory.getCategoryId() == null ? -1L : inspectionCategory.getCategoryId();
        InspectionCategory info = getOne(new LambdaQueryWrapper<InspectionCategory>()
                .eq(InspectionCategory::getCategoryName, inspectionCategory.getCategoryName())
                .eq(InspectionCategory::getDelFlag, 0)
                .last("limit 1"));
        return info != null && !info.getCategoryId().equals(categoryId);
    }

    /**
     * 检查分类编码是否存在
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    @Override
    public boolean checkCategoryCodeUnique(InspectionCategory inspectionCategory) {
        Long categoryId = inspectionCategory.getCategoryId() == null ? -1L : inspectionCategory.getCategoryId();
        InspectionCategory info = getOne(new LambdaQueryWrapper<InspectionCategory>()
                .eq(InspectionCategory::getCategoryCode, inspectionCategory.getCategoryCode())
                .eq(InspectionCategory::getDelFlag, 0)
                .last("limit 1"));
        return info != null && !info.getCategoryId().equals(categoryId);
    }

    /**
     * 检查是否有子分类
     *
     * @param categoryId 检查分类ID
     * @return 结果
     */
    @Override
    public boolean hasChildCategory(Long categoryId) {
        long count = count(new LambdaQueryWrapper<InspectionCategory>()
                .eq(InspectionCategory::getParentId, categoryId)
                .eq(InspectionCategory::getDelFlag, 0));
        return count > 0;
    }

    /**
     * 检查是否有关联的检查项目
     *
     * @param categoryId 检查分类ID
     * @return 结果
     */
    @Override
    public boolean hasAssociatedItems(Long categoryId) {
        InspectionItem item = new InspectionItem();
        item.setCategoryId(categoryId);
        List<InspectionItem> items = inspectionItemMapper.selectInspectionItemList(item);
        return items != null && items.size() > 0;
    }
} 