<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.order.mapper.OrderMapper">

    <!-- 基础列定义 -->
    <sql id="Base_Column_List">
        order_id, order_no, order_type, patient_id, patient_name, doctor_id, doctor_name,
        prescription_id, total_amount, pay_amount, discount_amount, payment_method, payment_status,
        payment_time, transaction_no, order_status, delivery_method, address_id, receiver_name,
        receiver_phone, receiver_address, express_company, express_no, delivery_time, receive_time,
        remark, cancel_reason, refund_reason, refund_time, refund_amount, refund_transaction_no,
        del_flag, create_by, create_time, update_by, update_time
    </sql>

    <!-- 分页查询订单列表 -->
    <select id="selectOrderPage" resultType="com.tcm.order.entity.Order">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tcm_order
        WHERE del_flag = 0
        <if test="orderType != null">
            AND order_type = #{orderType}
        </if>
        <if test="status != null">
            AND order_status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (order_no LIKE CONCAT('%', #{keyword}, '%')
            OR patient_name LIKE CONCAT('%', #{keyword}, '%')
            OR doctor_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询订单统计数据 -->
    <select id="selectOrderStatistics" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(create_time, '%Y-%m-%d') AS date,
            COUNT(1) AS order_count,
            SUM(total_amount) AS total_amount,
            SUM(pay_amount) AS pay_amount,
            COUNT(CASE WHEN order_status = 0 THEN 1 END) AS unpaid_count,
            COUNT(CASE WHEN order_status = 1 THEN 1 END) AS paid_count,
            COUNT(CASE WHEN order_status = 2 THEN 1 END) AS processing_count,
            COUNT(CASE WHEN order_status = 3 THEN 1 END) AS waiting_count,
            COUNT(CASE WHEN order_status = 4 THEN 1 END) AS completed_count,
            COUNT(CASE WHEN order_status = 5 THEN 1 END) AS canceled_count,
            COUNT(CASE WHEN order_status = 6 THEN 1 END) AS refunded_count
        FROM tcm_order
        WHERE del_flag = 0
        AND create_time BETWEEN #{startTime} AND #{endTime}
        GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d')
        ORDER BY date
    </select>
    
    <!-- 查询患者订单列表 -->
    <select id="selectPatientOrders" resultType="com.tcm.order.entity.Order">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tcm_order
        WHERE del_flag = 0
        AND patient_id = #{patientId}
        <if test="status != null">
            AND order_status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>
    
    <!-- 查询医生订单列表 -->
    <select id="selectDoctorOrders" resultType="com.tcm.order.entity.Order">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tcm_order
        WHERE del_flag = 0
        AND doctor_id = #{doctorId}
        <if test="status != null">
            AND order_status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>
    
    <!-- 更新订单支付状态 -->
    <update id="updateOrderPaymentStatus">
        UPDATE tcm_order
        SET payment_status = #{paymentStatus},
            transaction_no = #{transactionNo},
            payment_time = #{paymentTime},
            update_time = NOW()
        WHERE order_id = #{orderId}
        AND del_flag = 0
    </update>
</mapper>
