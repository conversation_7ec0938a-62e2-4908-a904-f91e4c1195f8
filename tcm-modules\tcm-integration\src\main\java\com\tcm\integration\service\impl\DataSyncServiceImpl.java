package com.tcm.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.integration.dto.DataSyncRecordDTO;
import com.tcm.integration.entity.DataSyncRecord;
import com.tcm.integration.entity.SystemIntegrationConfig;
import com.tcm.integration.mapper.DataSyncRecordMapper;
import com.tcm.integration.service.DataSyncService;
import com.tcm.integration.service.SystemIntegrationConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据同步服务实现类
 *
 * <AUTHOR>
 */
@Service
public class DataSyncServiceImpl extends ServiceImpl<DataSyncRecordMapper, DataSyncRecord> implements DataSyncService {

    @Autowired
    private SystemIntegrationConfigService configService;

    @Override
    public IPage<DataSyncRecord> page(int page, int pageSize, String systemCode, String syncType, Integer status) {
        LambdaQueryWrapper<DataSyncRecord> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(systemCode)) {
            queryWrapper.eq(DataSyncRecord::getSystemCode, systemCode);
        }

        if (StringUtils.hasText(syncType)) {
            queryWrapper.eq(DataSyncRecord::getSyncType, syncType);
        }

        if (status != null) {
            queryWrapper.eq(DataSyncRecord::getStatus, status);
        }

        queryWrapper.orderByDesc(DataSyncRecord::getCreateTime);

        return page(new Page<>(page, pageSize), queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createSyncTask(DataSyncRecordDTO dto) {
        // 检查系统配置是否存在
        SystemIntegrationConfig config = configService.getById(dto.getConfigId());
        if (config == null) {
            throw new RuntimeException("系统配置不存在");
        }

        // 检查系统是否启用
        if (config.getStatus() != 1) {
            throw new RuntimeException("系统未启用");
        }

        DataSyncRecord record = new DataSyncRecord();
        BeanUtils.copyProperties(dto, record);

        // 设置初始状态
        record.setStatus(0); // 0-同步中
        record.setTotalCount(0);
        record.setSuccessCount(0);
        record.setFailCount(0);
        record.setStartTime(LocalDateTime.now());
        record.setCreateTime(LocalDateTime.now());

        save(record);
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean executeSyncTask(Long recordId) {
        DataSyncRecord record = getById(recordId);
        if (record == null) {
            throw new RuntimeException("同步任务不存在");
        }

        // 判断任务状态
        if (record.getStatus() != 0) {
            throw new RuntimeException("任务已完成或已取消，无法执行");
        }

        try {
            // 获取系统配置
            SystemIntegrationConfig config = configService.getById(record.getConfigId());
            if (config == null) {
                throw new RuntimeException("系统配置不存在");
            }

            // 模拟同步过程
            boolean syncResult = performSync(record, config);

            // 更新同步记录
            record.setEndTime(LocalDateTime.now());
            record.setDuration(Duration.between(record.getStartTime(), record.getEndTime()).toMillis());

            if (syncResult) {
                // 同步成功
                record.setStatus(1);
            } else {
                // 同步部分成功
                record.setStatus(2);
                record.setFailReason("部分数据同步失败");
            }

            updateById(record);
            return syncResult;
        } catch (Exception e) {
            // 同步失败
            record.setEndTime(LocalDateTime.now());
            record.setDuration(Duration.between(record.getStartTime(), record.getEndTime()).toMillis());
            record.setStatus(3);
            record.setFailReason(e.getMessage());
            updateById(record);
            return false;
        }
    }

    /**
     * 执行具体的同步操作
     * 
     * @param record 同步记录
     * @param config 系统配置
     * @return 是否成功
     */
    private boolean performSync(DataSyncRecord record, SystemIntegrationConfig config) {
        // 这里实现具体的同步逻辑，可以根据不同的同步类型和系统类型实现不同的同步策略
        // 例如，对接HIS系统同步患者信息、药品信息等

        // 模拟同步过程
        record.setTotalCount(100);
        record.setSuccessCount(95);
        record.setFailCount(5);

        // 返回同步结果，如果成功数量等于总数量，则表示同步成功
        return record.getSuccessCount().equals(record.getTotalCount());
    }

    @Override
    public DataSyncRecord getSyncDetail(Long id) {
        return getById(id);
    }

    @Override
    public Map<String, Object> getSyncStatistics(String systemCode, String syncType) {
        Map<String, Object> statistics = new HashMap<>();

        // 获取同步成功率
        Double successRate = baseMapper.calculateSuccessRate(systemCode, syncType);
        statistics.put("successRate", successRate != null ? successRate : 0);

        // 获取最近一次同步记录
        DataSyncRecord lastRecord = baseMapper.selectLastSyncRecord(systemCode, syncType);
        statistics.put("lastSyncTime", lastRecord != null ? lastRecord.getEndTime() : null);
        statistics.put("lastSyncStatus", lastRecord != null ? lastRecord.getStatus() : null);

        // 统计同步次数
        LambdaQueryWrapper<DataSyncRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DataSyncRecord::getSystemCode, systemCode)
                .eq(DataSyncRecord::getSyncType, syncType);
        long totalCount = count(queryWrapper);
        statistics.put("totalSyncCount", totalCount);

        // 统计成功次数
        LambdaQueryWrapper<DataSyncRecord> successWrapper = new LambdaQueryWrapper<>();
        successWrapper.eq(DataSyncRecord::getSystemCode, systemCode)
                .eq(DataSyncRecord::getSyncType, syncType)
                .eq(DataSyncRecord::getStatus, 1);
        long successCount = count(successWrapper);
        statistics.put("successSyncCount", successCount);

        // 统计失败次数
        LambdaQueryWrapper<DataSyncRecord> failWrapper = new LambdaQueryWrapper<>();
        failWrapper.eq(DataSyncRecord::getSystemCode, systemCode)
                .eq(DataSyncRecord::getSyncType, syncType)
                .eq(DataSyncRecord::getStatus, 3);
        long failCount = count(failWrapper);
        statistics.put("failSyncCount", failCount);

        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelSync(Long id) {
        DataSyncRecord record = getById(id);
        if (record == null) {
            throw new RuntimeException("同步任务不存在");
        }

        // 判断任务状态
        if (record.getStatus() != 0) {
            throw new RuntimeException("只有进行中的任务才能取消");
        }

        record.setStatus(3); // 设置为失败状态
        record.setEndTime(LocalDateTime.now());
        record.setDuration(Duration.between(record.getStartTime(), record.getEndTime()).toMillis());
        record.setFailReason("用户取消同步");

        return updateById(record);
    }

    @Override
    public DataSyncRecord getLastSyncRecord(String systemCode, String syncType) {
        return baseMapper.selectLastSyncRecord(systemCode, syncType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importData(String systemCode, String syncType, Map<String, Object> params) {
        // 查询系统配置
        SystemIntegrationConfig config = configService.getBySystemCode(systemCode);
        if (config == null) {
            throw new RuntimeException("系统配置不存在");
        }

        // 创建同步记录
        DataSyncRecordDTO dto = new DataSyncRecordDTO();
        dto.setConfigId(config.getId());
        dto.setSystemCode(systemCode);
        dto.setSyncType(syncType);
        dto.setSyncDirection("IN");
        dto.setRemark("数据导入: " + syncType);

        Long recordId = createSyncTask(dto);

        // 异步执行导入任务
        // 在实际项目中，应该使用线程池或消息队列来异步执行
        // 这里为了简化，直接调用执行方法
        executeSyncTask(recordId);

        return recordId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long exportData(String systemCode, String syncType, Map<String, Object> params) {
        // 查询系统配置
        SystemIntegrationConfig config = configService.getBySystemCode(systemCode);
        if (config == null) {
            throw new RuntimeException("系统配置不存在");
        }

        // 创建同步记录
        DataSyncRecordDTO dto = new DataSyncRecordDTO();
        dto.setConfigId(config.getId());
        dto.setSystemCode(systemCode);
        dto.setSyncType(syncType);
        dto.setSyncDirection("OUT");
        dto.setRemark("数据导出: " + syncType);

        Long recordId = createSyncTask(dto);

        // 异步执行导出任务
        // 在实际项目中，应该使用线程池或消息队列来异步执行
        // 这里为了简化，直接调用执行方法
        executeSyncTask(recordId);

        return recordId;
    }
}