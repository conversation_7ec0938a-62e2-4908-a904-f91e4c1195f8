<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.medication.mapper.MedicineMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.medication.entity.Medicine">
        <id column="id" property="id" />
        <result column="medicine_code" property="medicineCode" />
        <result column="medicine_name" property="medicineName" />
        <result column="pinyin_code" property="pinyinCode" />
        <result column="medicine_type" property="medicineType" />
        <result column="category_id" property="categoryId" />
        <result column="specification" property="specification" />
        <result column="dosage_form" property="dosageForm" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="stock" property="stock" />
        <result column="warn_stock" property="warnStock" />
        <result column="manufacturer" property="manufacturer" />
        <result column="efficacy" property="efficacy" />
        <result column="property" property="property" />
        <result column="usage" property="usage" />
        <result column="contraindication" property="contraindication" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 分页查询药品列表 -->
    <select id="selectMedicinePage" resultMap="BaseResultMap">
        SELECT *
        FROM tcm_medicine
        WHERE 1 = 1
        <if test="medicineName != null and medicineName != ''">
            AND medicine_name LIKE concat('%', #{medicineName}, '%')
        </if>
        <if test="medicineCode != null and medicineCode != ''">
            AND medicine_code LIKE concat('%', #{medicineCode}, '%')
        </if>
        <if test="medicineType != null">
            AND medicine_type = #{medicineType}
        </if>
        <if test="categoryId != null">
            AND category_id = #{categoryId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据药品名称查询药品 -->
    <select id="selectByName" resultMap="BaseResultMap">
        SELECT *
        FROM tcm_medicine
        WHERE medicine_name = #{medicineName}
        LIMIT 1
    </select>

    <!-- 根据药品编码查询药品 -->
    <select id="selectByCode" resultMap="BaseResultMap">
        SELECT *
        FROM tcm_medicine
        WHERE medicine_code = #{medicineCode}
        LIMIT 1
    </select>

    <!-- 更新药品库存 -->
    <update id="updateStock">
        UPDATE tcm_medicine
        SET stock = #{stock},
            update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper> 