package com.tcm.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备告警实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("device_alarm")
@EqualsAndHashCode(callSuper = false)
public class DeviceAlarm implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 告警级别（1低 2中 3高）
     */
    private Integer alarmLevel;

    /**
     * 告警内容
     */
    private String alarmContent;

    /**
     * 告警时间
     */
    private Date alarmTime;

    /**
     * 处理状态（0未处理 1已处理）
     */
    private Integer handleStatus;

    /**
     * 处理人员
     */
    private String handleUser;

    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer deleted;
}