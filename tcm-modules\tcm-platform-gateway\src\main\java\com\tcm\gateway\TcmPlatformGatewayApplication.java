package com.tcm.gateway;

import com.tcm.gateway.config.CommonConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Import;

/**
 * 平台网关服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableConfigurationProperties(CommonConfig.class)
@Import(CommonConfig.class)
public class TcmPlatformGatewayApplication {

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 彻底解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

            // 3. 解决Java反射和安全限制问题
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.tryReflectionSetAccessible", "false");
            System.setProperty("illegal-access", "permit");

            // 4. 解决Java 9+中的反射访问限制问题
            System.setProperty("jdk.internal.reflect.permitAll", "true");
            System.setProperty("jdk.attach.allowAttachSelf", "true");

            // 5. 设置应用名称
            System.setProperty("spring.application.name", "tcm-platform-gateway");

            // 6. 设置配置文件包含
            System.setProperty("spring.config.import", "optional:classpath:/META-INF/tcm-common.yml");

            SpringApplication.run(TcmPlatformGatewayApplication.class, args);
            System.out.println("(♥◠‿◠)ﾉﾞ  平台网关服务启动成功   ლ(´ڡ`ლ)ﾞ");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}