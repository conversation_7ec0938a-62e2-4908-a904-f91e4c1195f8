package com.tcm.inspection.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 检查项目实体类
 */
@Data
@Accessors(chain = true)
@TableName("inspection_item")
public class InspectionItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检查项目ID
     */
    @TableId(value = "item_id", type = IdType.AUTO)
    private Long itemId;

    /**
     * 检查项目编码
     */
    private String itemCode;

    /**
     * 检查项目名称
     */
    private String itemName;

    /**
     * 检查项目分类ID
     */
    private Long categoryId;

    /**
     * 检查项目分类名称
     */
    @TableField(exist = false)
    private String categoryName;

    /**
     * 检查项目描述
     */
    private String itemDesc;

    /**
     * 检查项目价格
     */
    private BigDecimal price;

    /**
     * 检查项目状态（0:停用 1:启用）
     */
    private Integer status;

    /**
     * 执行科室ID
     */
    private Long deptId;

    /**
     * 执行科室名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 检查方法
     */
    private String inspectionMethod;

    /**
     * 注意事项
     */
    private String precautions;

    /**
     * 参考值范围
     */
    private String referenceRange;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0:未删除 1:已删除）
     */
    private Integer delFlag;
} 