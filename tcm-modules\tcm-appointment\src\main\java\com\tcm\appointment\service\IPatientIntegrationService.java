package com.tcm.appointment.service;

import com.tcm.appointment.vo.PatientVO;

/**
 * 患者模块集成服务接口
 */
public interface IPatientIntegrationService {
    
    /**
     * 获取患者信息
     *
     * @param patientId 患者ID
     * @return 患者信息
     */
    PatientVO getPatientInfo(Long patientId);
    
    /**
     * 验证患者是否可以预约（检查是否在黑名单中）
     *
     * @param patientId 患者ID
     * @return 是否可以预约
     */
    boolean checkPatientAppointmentEligibility(Long patientId);
    
    /**
     * 记录患者爽约
     *
     * @param patientId 患者ID
     * @return 操作结果
     */
    boolean recordPatientNoShow(Long patientId);
    
    /**
     * 获取患者历史就诊记录
     *
     * @param patientId 患者ID
     * @param doctorId  医生ID
     * @return 历史记录
     */
    boolean hasVisitedDoctor(Long patientId, Long doctorId);
} 