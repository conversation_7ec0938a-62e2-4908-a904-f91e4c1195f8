package com.tcm.order;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

// 导入通用服务配置
import com.tcm.common.core.service.CommonServiceConfig;

/**
 * 订单管理服务启动类
 *
 * <AUTHOR> Team
 */
@SpringBootApplication(exclude = {
        // 排除RocketMQ自动配置，避免配置缺失导致启动失败
        org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class })
@EnableDiscoveryClient // 确保启用服务发现
@EnableFeignClients // 移除不存在的包名
@MapperScan("com.tcm.order.mapper")
public class TcmOrderApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmOrderApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    /**
     * 静态初始化块，处理日志冲突
     */
    static {
        // 解决SLF4J绑定冲突
        try {
            // 重置任何现有的SLF4J绑定
            LoggerFactory.getILoggerFactory();
        } catch (Exception e) {
            System.err.println("SLF4J绑定初始化失败: " + e.getMessage());
        }

        // 设置系统属性，确保使用Logback
        System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SLF4JLog");
        System.setProperty("java.util.logging.manager", "org.apache.logging.log4j.jul.LogManager");

        // 禁用Nacos日志
        System.setProperty("nacos.logging.default.config.enabled", "false");

        // 配置数据源类型，明确指定HikariCP
        System.setProperty("spring.datasource.type", "com.zaxxer.hikari.HikariDataSource");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-order");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_ORDER_PORT") == null) {
            System.setProperty("server.port", "9320");
        }
    }

    public static void main(String[] args) {
        try {
            System.out.println("订单服务正在启动...");
            LOGGER.info("Starting order service module...");

            // 1. 调用通用服务配置，完成所有必要的系统设置（包括JDK、Nacos等）
            CommonServiceConfig.setupApplication("tcm-order");

            // 日志配置
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");

            // 优先设置固定端口，确保Spring Boot使用这个端口启动
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.order.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_ORDER_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "9320";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            // 2. 创建SpringApplication并启动
            SpringApplication app = new SpringApplication(TcmOrderApplication.class);
            ConfigurableApplicationContext context = app.run(args);

            // 获取实际使用的端口（包括随机分配的端口）
            String serverPort;

            // 如果PortListener已经捕获到了实际端口，则使用它
            if (actualServerPort > 0) {
                serverPort = String.valueOf(actualServerPort);
            } else {
                // 尝试从环境中获取端口
                serverPort = context.getEnvironment().getProperty("server.port");

                // 如果环境中没有端口配置，则尝试从系统属性和环境变量获取
                if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                    serverPort = System.getProperty("tcm.service.order.port");

                    // 如果系统属性中没有端口配置，则尝试从环境变量获取
                    if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                        serverPort = System.getenv("TCM_ORDER_PORT");

                        // 如果环境变量中没有端口配置，则使用默认端口
                        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                            serverPort = "9320";
                        }
                    }
                }
            }

            // 确保端口被设置到系统属性中（使之可被其他组件获取）
            System.setProperty("server.port", serverPort);

            // 3. 获取环境配置，用于输出信息
            Environment env = context.getEnvironment();
            String mysqlHost = env.getProperty("tcm.service.mysql.host");

            LOGGER.info("=====================================================");
            LOGGER.info("            Order Service Module Started!           ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);
            LOGGER.info("Database Host: {}", mysqlHost);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            System.out.println("  _____   _____ __  __    ____          _           ");
            System.out.println(" |_   _| / ____|  \\/  |  / __ \\        | |          ");
            System.out.println("   | |  | |    | \\  / | | |  | |_ __ __| | ___ _ __ ");
            System.out.println("   | |  | |    | |\\/| | | |  | | '__/ _` |/ _ \\ '__|");
            System.out.println("  _| |_ | |____| |  | | | |__| | | | (_| |  __/ |   ");
            System.out.println(" |_____| \\_____|_|  |_|  \\____/|_|  \\__,_|\\___|_|   ");
            System.out.println("                                                     ");

            System.out.println("  Started successfully: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort);
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("Startup failed: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web server initialized with port: {}", actualServerPort);
        }
    }
}
