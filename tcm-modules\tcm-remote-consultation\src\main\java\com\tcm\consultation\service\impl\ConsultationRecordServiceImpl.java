package com.tcm.consultation.service.impl;

import org.springframework.stereotype.Service;
import com.tcm.consultation.service.ConsultationRecordService;
import com.tcm.consultation.mapper.ConsultationRecordMapper;
import com.tcm.consultation.domain.ConsultationRecord;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import java.util.Date;

/**
 * 会诊记录服务实现类
 */
@Service
@Slf4j
public class ConsultationRecordServiceImpl implements ConsultationRecordService {

    @Autowired
    private ConsultationRecordMapper consultationRecordMapper;

    @Override
    public boolean startConsultation(Long consultationId) {
        try {
            // 查询会诊记录
            ConsultationRecord record = consultationRecordMapper.selectById(consultationId);
            if (record == null) {
                log.error("会诊记录不存在: {}", consultationId);
                return false;
            }

            // 更新会诊状态为进行中
            record.setStatus(1); // 1: 进行中
            record.setStartTime(new Date());
            record.setUpdateTime(new Date());

            // 更新会诊记录
            int result = consultationRecordMapper.updateById(record);

            log.info("开始会诊: 会诊ID={}, 结果={}", consultationId, result > 0);
            return result > 0;
        } catch (Exception e) {
            log.error("开始会诊失败", e);
            return false;
        }
    }

    @Override
    public boolean endConsultation(Long consultationId) {
        try {
            // 查询会诊记录
            ConsultationRecord record = consultationRecordMapper.selectById(consultationId);
            if (record == null) {
                log.error("会诊记录不存在: {}", consultationId);
                return false;
            }

            // 更新会诊状态为已结束
            record.setStatus(2); // 2: 已结束
            record.setEndTime(new Date());
            record.setUpdateTime(new Date());

            // 更新会诊记录
            int result = consultationRecordMapper.updateById(record);

            log.info("结束会诊: 会诊ID={}, 结果={}", consultationId, result > 0);
            return result > 0;
        } catch (Exception e) {
            log.error("结束会诊失败", e);
            return false;
        }
    }
}