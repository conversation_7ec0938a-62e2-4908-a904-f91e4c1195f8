package com.tcm.order.service;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单统计服务接口
 */
public interface IOrderStatService {

    /**
     * 获取医生收费总金额
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收费总金额
     */
    BigDecimal getTotalAmount(Long doctorId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取医生复诊率
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 复诊率（百分比）
     */
    BigDecimal getRevisitRate(Long doctorId, LocalDate startDate, LocalDate endDate);
}
