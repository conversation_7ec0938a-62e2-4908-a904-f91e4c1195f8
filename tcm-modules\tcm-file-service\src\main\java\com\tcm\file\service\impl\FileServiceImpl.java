package com.tcm.file.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.file.config.FileStorageConfig;
import com.tcm.file.domain.FileInfo;
import com.tcm.file.mapper.FileInfoMapper;
import com.tcm.file.service.FileService;
import com.tcm.file.util.FileTypeUtil;
import com.tcm.file.util.FileUtils;
import io.minio.GetObjectArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 文件服务实现类
 */
@Slf4j
@Service
public class FileServiceImpl extends ServiceImpl<FileInfoMapper, FileInfo> implements FileService {

    @Autowired
    private FileStorageConfig fileStorageConfig;

    @Autowired(required = false)
    private MinioClient minioClient;

    @Autowired
    private FileInfoMapper fileInfoMapper;

    /**
     * 上传文件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileInfo upload(MultipartFile file, String module, String businessId) {
        try {
            if (file == null || file.isEmpty()) {
                throw new RuntimeException("上传文件不能为空");
            }

            String originalFilename = file.getOriginalFilename();
            String extension = FilenameUtils.getExtension(originalFilename);
            String mimeType = file.getContentType();
            long fileSize = file.getSize();

            // 使用输入流上传
            return uploadByStream(file.getInputStream(), originalFilename, fileSize, mimeType, module, businessId);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 通过输入流上传文件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileInfo uploadByStream(InputStream inputStream, String originalName, long size, String mimeType, String module, String businessId) {
        try {
            // 生成唯一文件名
            String extension = FilenameUtils.getExtension(originalName);
            String fileName = UUID.randomUUID().toString().replaceAll("-", "") + "." + extension;

            // 如果没有提供MIME类型，使用Tika检测
            if (StringUtils.isBlank(mimeType)) {
                Tika tika = new Tika();
                mimeType = tika.detect(originalName);
            }

            // 确定文件类型
            int fileType = FileTypeUtil.getFileTypeByMimeType(mimeType);

            // 构建文件信息对象
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(fileName);
            fileInfo.setOriginalName(originalName);
            fileInfo.setExtension(extension);
            fileInfo.setFileSize(size);
            fileInfo.setMimeType(mimeType);
            fileInfo.setFileType(fileType);
            fileInfo.setStatus(0); // 临时状态
            fileInfo.setModule(module);
            fileInfo.setBusinessId(businessId);
            fileInfo.setCreateTime(new Date());
            fileInfo.setDelFlag(0);

            // 计算MD5值
            String md5 = FileUtils.calculateMd5(inputStream);
            fileInfo.setMd5(md5);

            // 检查是否已存在相同MD5的文件
            FileInfo existingFile = fileInfoMapper.selectByMd5(md5);
            if (existingFile != null) {
                // 文件已存在，复用信息但创建新记录
                fileInfo.setFilePath(existingFile.getFilePath());
                fileInfo.setFileUrl(existingFile.getFileUrl());
                fileInfo.setStorageType(existingFile.getStorageType());
                save(fileInfo);
                return fileInfo;
            }

            // 根据配置决定存储方式
            if (fileStorageConfig.isMinioEnabled() && minioClient != null) {
                // MinIO存储
                String objectName = module + "/" + fileName;
                
                // 重新获取输入流（因为计算MD5时已经读取过一次）
                inputStream.reset();
                
                // 上传到MinIO
                minioClient.putObject(
                    PutObjectArgs.builder()
                        .bucket(fileStorageConfig.getMinioBucket())
                        .object(objectName)
                        .stream(inputStream, size, -1)
                        .contentType(mimeType)
                        .build()
                );
                
                // 设置文件路径和URL
                fileInfo.setFilePath(objectName);
                fileInfo.setFileUrl("/file/download/" + fileInfo.getFileId());
                fileInfo.setStorageType(1); // MinIO存储
            } else {
                // 本地存储
                String relativePath = module + File.separator + fileName;
                String absolutePath = fileStorageConfig.getBaseStoragePath() + File.separator + relativePath;
                
                // 确保目录存在
                File targetFile = new File(absolutePath);
                if (!targetFile.getParentFile().exists()) {
                    targetFile.getParentFile().mkdirs();
                }
                
                // 保存文件
                try (FileOutputStream outputStream = new FileOutputStream(targetFile)) {
                    // 重新获取输入流
                    inputStream.reset();
                    IOUtils.copy(inputStream, outputStream);
                }
                
                // 设置文件路径和URL
                fileInfo.setFilePath(relativePath);
                fileInfo.setFileUrl("/file/download/" + fileInfo.getFileId());
                fileInfo.setStorageType(0); // 本地存储
            }
            
            // 保存文件信息到数据库
            save(fileInfo);
            
            return fileInfo;
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     */
    @Override
    public InputStream download(Long fileId) {
        try {
            FileInfo fileInfo = getById(fileId);
            if (fileInfo == null) {
                throw new RuntimeException("文件不存在");
            }
            
            // 根据存储类型获取文件流
            if (fileInfo.getStorageType() == 1 && fileStorageConfig.isMinioEnabled() && minioClient != null) {
                // MinIO存储
                return minioClient.getObject(
                    GetObjectArgs.builder()
                        .bucket(fileStorageConfig.getMinioBucket())
                        .object(fileInfo.getFilePath())
                        .build()
                );
            } else {
                // 本地存储
                String filePath = fileStorageConfig.getBaseStoragePath() + File.separator + fileInfo.getFilePath();
                return new FileInputStream(filePath);
            }
        } catch (Exception e) {
            log.error("文件下载失败", e);
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    @Override
    public FileInfo getFileInfo(Long fileId) {
        return getById(fileId);
    }

    /**
     * 根据业务ID获取文件列表
     */
    @Override
    public List<FileInfo> listByBusinessId(String businessId, String module) {
        return fileInfoMapper.selectByBusinessId(businessId, module);
    }

    /**
     * 删除文件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(Long fileId) {
        FileInfo fileInfo = getById(fileId);
        if (fileInfo == null) {
            return false;
        }
        
        // 逻辑删除文件记录
        fileInfo.setDelFlag(1);
        fileInfo.setUpdateTime(new Date());
        updateById(fileInfo);
        
        // 检查是否有其他记录引用同一个物理文件
        LambdaQueryWrapper<FileInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FileInfo::getMd5, fileInfo.getMd5())
                   .eq(FileInfo::getDelFlag, 0);
        long count = count(queryWrapper);
        
        // 如果没有其他引用，删除物理文件
        if (count == 0) {
            try {
                if (fileInfo.getStorageType() == 1 && fileStorageConfig.isMinioEnabled() && minioClient != null) {
                    // MinIO存储，暂不物理删除
                } else {
                    // 本地存储
                    String filePath = fileStorageConfig.getBaseStoragePath() + File.separator + fileInfo.getFilePath();
                    Files.deleteIfExists(Paths.get(filePath));
                }
            } catch (Exception e) {
                log.error("物理文件删除失败", e);
                // 物理删除失败不影响逻辑删除结果
            }
        }
        
        return true;
    }

    /**
     * 批量删除文件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFiles(List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return false;
        }
        
        boolean result = true;
        for (Long fileId : fileIds) {
            result = result && deleteFile(fileId);
        }
        return result;
    }

    /**
     * 根据业务ID删除文件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByBusinessId(String businessId, String module) {
        // 查询所有相关文件
        List<FileInfo> fileInfos = fileInfoMapper.selectByBusinessId(businessId, module);
        
        // 获取文件ID列表
        List<Long> fileIds = new ArrayList<>();
        for (FileInfo fileInfo : fileInfos) {
            fileIds.add(fileInfo.getFileId());
        }
        
        // 批量删除
        return deleteFiles(fileIds);
    }

    /**
     * 更新文件状态（临时->永久）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return false;
        }
        
        int rows = fileInfoMapper.updateStatusBatch(fileIds, 1);
        return rows > 0;
    }
} 