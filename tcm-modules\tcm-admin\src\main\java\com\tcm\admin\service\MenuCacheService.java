package com.tcm.admin.service;

import com.tcm.admin.vo.MenuVO;
import com.tcm.admin.vo.TreeSelectVO;

import java.util.List;
import java.util.Set;

/**
 * 菜单权限缓存服务接口
 */
public interface MenuCacheService {

    /**
     * 获取用户菜单列表（从缓存中获取）
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<MenuVO> getUserMenuListCache(Long userId);

    /**
     * 获取用户权限集合（从缓存中获取）
     *
     * @param userId 用户ID
     * @return 权限集合
     */
    Set<String> getUserPermissionsCache(Long userId);

    /**
     * 获取菜单树选择列表（从缓存中获取）
     *
     * @param userId 用户ID
     * @return 菜单树选择列表
     */
    List<TreeSelectVO> getMenuTreeSelectCache(Long userId);

    /**
     * 清除用户菜单缓存
     *
     * @param userId 用户ID
     */
    void clearUserMenuCache(Long userId);

    /**
     * 清除用户权限缓存
     *
     * @param userId 用户ID
     */
    void clearUserPermissionsCache(Long userId);

    /**
     * 清除菜单相关所有缓存
     */
    void clearAllMenuCache();
}