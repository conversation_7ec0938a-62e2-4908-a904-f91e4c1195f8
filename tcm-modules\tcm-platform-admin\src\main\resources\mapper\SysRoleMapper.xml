<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.platform.admin.mapper.SysRoleMapper">

    <!-- 角色结果映射 -->
    <resultMap id="RoleResultMap" type="com.tcm.platform.admin.entity.SysRole">
        <id property="id" column="id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleCode" column="role_code"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="dataScope" column="data_scope"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 分页查询角色列表 -->
    <select id="selectRolePage" resultMap="RoleResultMap">
        select id, role_name, role_code, sort, status, data_scope, remark, 
        create_by, create_time, update_by, update_time, del_flag
        from sys_role
        where del_flag = 0
        <if test="role.roleName != null and role.roleName != ''">
            AND role_name like concat('%', #{role.roleName}, '%')
        </if>
        <if test="role.roleCode != null and role.roleCode != ''">
            AND role_code like concat('%', #{role.roleCode}, '%')
        </if>
        <if test="role.status != null">
            AND status = #{role.status}
        </if>
        order by sort asc, create_time desc
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" parameterType="Long" resultMap="RoleResultMap">
        select r.id, r.role_name, r.role_code, r.sort, r.status, r.data_scope, r.remark,
        r.create_by, r.create_time, r.update_by, r.update_time, r.del_flag
        from sys_role r
        inner join sys_user_role ur on r.id = ur.role_id
        where ur.user_id = #{userId} and r.del_flag = 0
    </select>

    <!-- 修改角色状态 -->
    <update id="updateStatus">
        update sys_role set status = #{status}, update_time = now() where id = #{roleId}
    </update>

    <!-- 根据角色编码查询角色 -->
    <select id="selectByRoleCode" parameterType="String" resultMap="RoleResultMap">
        select id, role_name, role_code, sort, status, data_scope, remark,
        create_by, create_time, update_by, update_time, del_flag
        from sys_role
        where role_code = #{roleCode} and del_flag = 0
        limit 1
    </select>
</mapper> 