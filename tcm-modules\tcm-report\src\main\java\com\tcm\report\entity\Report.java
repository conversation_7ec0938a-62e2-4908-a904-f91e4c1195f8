package com.tcm.report.entity;

import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

/**
 * 报表实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("tcm_report")
public class Report {
    
    /**
     * 报表ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 报表名称
     */
    @TableField("report_name")
    private String reportName;

    /**
     * 报表类型（1-患者报表，2-医生报表，3-药房报表，4-统计报表）
     */
    @TableField("report_type")
    private Integer reportType;

    /**
     * 报表模板路径
     */
    @TableField("template_path")
    private String templatePath;

    /**
     * 报表描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态（0-禁用，1-启用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;
} 