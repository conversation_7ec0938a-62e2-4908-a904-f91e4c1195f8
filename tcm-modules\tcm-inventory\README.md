# TCM库存管理模块

## 模块介绍
TCM库存管理模块（tcm-inventory）是中医系统的核心模块之一，负责管理中药材、中成药、西药和医疗器械等库存物品的入库、出库、库存查询、库存预警等功能。

## 主要功能
- 库存项目管理：添加、修改、删除库存项目
- 库存查询：按药品编码、名称、类型等条件查询库存
- 库存操作：入库、出库、库存调整等操作
- 库存预警：低库存预警、过期预警等
- 库存统计：库存金额统计、库存周转率等统计分析

## 技术栈
- Spring Boot 2.7.x
- Spring Cloud Alibaba
- MyBatis Plus
- MySQL
- Redis
- Nacos（服务注册与配置中心）
- RocketMQ（消息队列）

## API接口
### 健康检查接口
- GET /inventory/health - 检查服务健康状态

### 库存状态接口
- GET /inventory/status - 获取库存整体状态

### 库存项目接口
- GET /inventory/items - 获取所有库存项目
- GET /inventory/items/{id} - 根据ID获取库存项目
- GET /inventory/items/code/{itemCode} - 根据药品编码获取库存项目
- POST /inventory/items - 添加库存项目
- PUT /inventory/items - 更新库存项目
- DELETE /inventory/items/{id} - 删除库存项目
- PUT /inventory/items/{id}/quantity/{quantity} - 更新库存数量

## 数据库设计
### 库存项目表（tcm_inventory_item）
- id：主键ID
- item_code：药品编码
- item_name：药品名称
- item_type：药品类型（1:中药材, 2:中成药, 3:西药, 4:器械）
- specification：规格
- unit：单位
- manufacturer：生产厂家
- batch_number：批号
- production_date：生产日期
- expiry_date：有效期至
- quantity：库存数量
- cost_price：成本价
- retail_price：零售价
- status：库存状态（0:正常, 1:低库存, 2:缺货, 3:过期）
- storage_location：存储位置
- remark：备注
- create_by：创建者
- create_time：创建时间
- update_by：更新者
- update_time：更新时间
- del_flag：删除标志

### 库存操作记录表（tcm_inventory_record）
- id：主键ID
- item_id：库存项目ID
- operation_type：操作类型（1:入库, 2:出库, 3:调整）
- quantity：操作数量
- before_quantity：操作前数量
- after_quantity：操作后数量
- operation_time：操作时间
- operator：操作人
- remark：备注
- create_time：创建时间

## 启动说明
1. 确保Nacos服务已启动
2. 确保MySQL服务已启动，并已创建tcm数据库
3. 执行sql目录下的初始化SQL脚本
4. 使用以下命令启动服务：
```bash
java -jar tcm-inventory-1.0.0.jar --spring.profiles.active=dev
```

## 配置说明
主要配置项在application-dev.yml中，包括：
- Nacos配置：服务注册与发现
- MySQL配置：数据库连接信息
- Redis配置：缓存配置
- RocketMQ配置：消息队列配置 