package com.tcm.diagnosis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 脉象分析结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_pulse_analysis")
public class PulseAnalysis extends BaseEntity {

    /**
     * 分析ID
     */
    @TableId
    private Long analysisId;

    /**
     * 脉诊数据ID
     */
    private Long dataId;
    
    /**
     * 脉诊数据ID（兼容方法名）
     */
    private Long pulseId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 脉象类型（浮/沉/迟/数/虚/实等）
     */
    private String pulseType;

    /**
     * 脉率（次/分钟）
     */
    private Integer pulseRate;

    /**
     * 脉力（强/中/弱）
     */
    private String pulseStrength;

    /**
     * 脉律（整齐/不整齐）
     */
    private String pulseRhythm;
    
    /**
     * 脉象宽度
     */
    private String pulseWidth;

    /**
     * 脉象特征
     */
    private String pulseCharacteristics;

    /**
     * 分析结果详情（JSON格式）
     */
    private String analysisResult;

    /**
     * 临床意义
     */
    private String clinicalImplications;

    /**
     * 分析时间
     */
    private Date analysisTime;

    /**
     * 分析模型版本
     */
    private String modelVersion;

    /**
     * 分析置信度（0-100）
     */
    private Integer confidence;
}