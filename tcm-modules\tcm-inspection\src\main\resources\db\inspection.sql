-- 检查分类表
CREATE TABLE `inspection_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_code` varchar(50) NOT NULL COMMENT '分类编码',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT NULL COMMENT '父分类ID',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `order_num` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态（0:停用 1:启用）',
  `create_by` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标志（0:未删除 1:已删除）',
  PRIMARY KEY (`category_id`),
  UNIQUE KEY `uk_category_code` (`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='检查分类表';

-- 检查项目表
CREATE TABLE `inspection_item` (
  `item_id` bigint NOT NULL AUTO_INCREMENT COMMENT '检查项目ID',
  `item_code` varchar(50) NOT NULL COMMENT '检查项目编码',
  `item_name` varchar(100) NOT NULL COMMENT '检查项目名称',
  `category_id` bigint DEFAULT NULL COMMENT '检查项目分类ID',
  `item_desc` varchar(255) DEFAULT NULL COMMENT '检查项目描述',
  `price` decimal(10,2) DEFAULT '0.00' COMMENT '检查项目价格',
  `status` tinyint DEFAULT '1' COMMENT '检查项目状态（0:停用 1:启用）',
  `dept_id` bigint DEFAULT NULL COMMENT '执行科室ID',
  `inspection_method` text COMMENT '检查方法',
  `precautions` text COMMENT '注意事项',
  `reference_range` varchar(255) DEFAULT NULL COMMENT '参考值范围',
  `create_by` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标志（0:未删除 1:已删除）',
  PRIMARY KEY (`item_id`),
  UNIQUE KEY `uk_item_code` (`item_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='检查项目表';

-- 检查记录表
CREATE TABLE `inspection_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '检查记录ID',
  `inspection_no` varchar(50) NOT NULL COMMENT '检查单号',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `item_id` bigint NOT NULL COMMENT '检查项目ID',
  `doctor_id` bigint DEFAULT NULL COMMENT '医生ID',
  `dept_id` bigint DEFAULT NULL COMMENT '执行科室ID',
  `apply_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '检查申请时间',
  `execute_time` datetime DEFAULT NULL COMMENT '检查执行时间',
  `result` text COMMENT '检查结果',
  `conclusion` text COMMENT '检查结论',
  `file_paths` varchar(1000) DEFAULT NULL COMMENT '检查图片/文件路径（多个用逗号分隔）',
  `status` tinyint DEFAULT '0' COMMENT '检查状态（0:待执行 1:已执行 2:已出结果 3:已取消）',
  `executor_id` bigint DEFAULT NULL COMMENT '检查执行人ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint DEFAULT '0' COMMENT '删除标志（0:未删除 1:已删除）',
  PRIMARY KEY (`record_id`),
  UNIQUE KEY `uk_inspection_no` (`inspection_no`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='检查记录表';

-- 初始化中医检查分类数据
INSERT INTO `inspection_category` (`category_id`, `category_code`, `category_name`, `parent_id`, `description`, `order_num`, `status`) VALUES
(1, 'TCM_INSPECTION_001', '望诊', NULL, '通过观察患者的神色、形态、动态等进行诊断', 1, 1),
(2, 'TCM_INSPECTION_002', '闻诊', NULL, '通过嗅觉和听觉获取患者的气味和声音信息进行诊断', 2, 1),
(3, 'TCM_INSPECTION_003', '问诊', NULL, '通过询问患者的症状、病史等进行诊断', 3, 1),
(4, 'TCM_INSPECTION_004', '切诊', NULL, '通过触摸患者的脉搏、皮肤等进行诊断', 4, 1),
(5, 'TCM_INSPECTION_005', '舌诊', 1, '通过观察舌头的颜色、形态、苔质等进行诊断', 5, 1),
(6, 'TCM_INSPECTION_006', '面诊', 1, '通过观察面部颜色、神态等进行诊断', 6, 1),
(7, 'TCM_INSPECTION_007', '脉诊', 4, '通过触摸脉搏的位置、频率、强度等进行诊断', 7, 1),
(8, 'TCM_INSPECTION_008', '腹诊', 4, '通过触摸腹部的状态进行诊断', 8, 1);

-- 初始化中医检查项目数据
INSERT INTO `inspection_item` (`item_id`, `item_code`, `item_name`, `category_id`, `item_desc`, `price`, `status`, `dept_id`, `inspection_method`, `precautions`, `reference_range`) VALUES
(1, 'TCM_ITEM_001', '舌象检查', 5, '观察舌质、舌苔颜色和形态', 30.00, 1, 1, '患者伸舌，医生观察舌头的颜色、形态、苔质等', '检查前避免吃有色食物，不要刷舌苔', NULL),
(2, 'TCM_ITEM_002', '面色检查', 6, '观察面部颜色和精神状态', 20.00, 1, 1, '在自然光线下观察患者面部颜色、光泽及表情', '检查前避免化妆', NULL),
(3, 'TCM_ITEM_003', '脉象检查', 7, '触摸脉搏判断脉象类型', 40.00, 1, 1, '医生以食、中、无名三指按在患者的寸、关、尺三部位，轻、中、重三种力度诊脉', '检查前避免剧烈运动，保持情绪稳定', NULL),
(4, 'TCM_ITEM_004', '腹部触诊', 8, '触摸腹部检查脏腑状态', 40.00, 1, 1, '患者仰卧，医生用手触摸腹部，检查有无压痛、胀满等', '检查前排空大小便，避免饱餐', NULL),
(5, 'TCM_ITEM_005', '气味辨识', 2, '通过嗅觉辨别患者的特殊气味', 15.00, 1, 1, '医生通过嗅觉判断患者口气、体味、分泌物气味等', '检查前避免使用香水等掩盖气味的物品', NULL),
(6, 'TCM_ITEM_006', '声音评估', 2, '通过听觉辨别患者声音特点', 15.00, 1, 1, '医生听取患者说话、咳嗽、呼吸等声音，判断其特点', '保持安静的检查环境', NULL),
(7, 'TCM_ITEM_007', '症状询问', 3, '系统询问患者症状和病史', 30.00, 1, 1, '医生通过问诊了解患者的主诉、病史、生活习惯等', '患者应如实回答，不遗漏重要信息', NULL),
(8, 'TCM_ITEM_008', '体质辨识', 4, '通过综合诊断辨别患者体质类型', 60.00, 1, 1, '结合望、闻、问、切四诊方法，综合判断患者属于哪种体质类型', '需要空腹进行某些检查项目', NULL); 