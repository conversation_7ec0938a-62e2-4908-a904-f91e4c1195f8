package com.tcm.patient.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.R;
import com.tcm.patient.dto.PatientDTO;
import com.tcm.patient.dto.PatientQuery;
import com.tcm.patient.service.IPatientService;
import com.tcm.patient.vo.PatientVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 患者管理控制器
 */
@RestController
@RequestMapping("/patient")
public class PatientController {
    
    @Autowired
    private IPatientService patientService;
    
    /**
     * 获取患者列表
     */
    @GetMapping("/list")
    public R<IPage<PatientVO>> list(PatientQuery query) {
        IPage<PatientVO> page = patientService.listPatients(query);
        return R.ok(page);
    }
    
    /**
     * 获取患者详情
     */
    @GetMapping("/{patientId}")
    public R<PatientVO> getInfo(@PathVariable Long patientId) {
        PatientVO patient = patientService.getPatientById(patientId);
        return R.ok(patient);
    }
    
    /**
     * 根据用户ID获取患者信息
     */
    @GetMapping("/user/{userId}")
    public R<PatientVO> getPatientByUserId(@PathVariable Long userId) {
        PatientVO patient = patientService.getPatientByUserId(userId);
        return R.ok(patient);
    }
    
    /**
     * 根据身份证号获取患者信息
     */
    @GetMapping("/idCard")
    public R<PatientVO> getPatientByIdCard(@RequestParam String idCard) {
        PatientVO patient = patientService.getPatientByIdCard(idCard);
        return R.ok(patient);
    }
    
    /**
     * 新增患者
     */
    @PostMapping
    public R<Boolean> add(@RequestBody @Valid PatientDTO patientDTO) {
        boolean result = patientService.addPatient(patientDTO);
        return R.ok(result);
    }
    
    /**
     * 修改患者
     */
    @PutMapping
    public R<Boolean> update(@RequestBody @Valid PatientDTO patientDTO) {
        boolean result = patientService.updatePatient(patientDTO);
        return R.ok(result);
    }
    
    /**
     * 删除患者
     */
    @DeleteMapping("/{patientId}")
    public R<Boolean> remove(@PathVariable Long patientId) {
        boolean result = patientService.deletePatient(patientId);
        return R.ok(result);
    }
    
    /**
     * 批量删除患者
     */
    @DeleteMapping("/batch/{patientIds}")
    public R<Boolean> removeBatch(@PathVariable Long[] patientIds) {
        boolean result = patientService.deletePatients(patientIds);
        return R.ok(result);
    }
    
    /**
     * 修改患者状态
     */
    @PutMapping("/status/{patientId}/{status}")
    public R<Boolean> updateStatus(@PathVariable Long patientId, @PathVariable Integer status) {
        boolean result = patientService.updatePatientStatus(patientId, status);
        return R.ok(result);
    }
} 