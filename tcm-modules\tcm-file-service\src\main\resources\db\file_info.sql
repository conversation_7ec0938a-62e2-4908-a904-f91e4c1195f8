-- ----------------------------
-- 创建文件信息表
-- ----------------------------
DROP TABLE IF EXISTS `file_info`;
CREATE TABLE `file_info` (
  `file_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `extension` varchar(50) DEFAULT NULL COMMENT '文件扩展名',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小(字节)',
  `file_path` varchar(500) NOT NULL COMMENT '文件存储路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL',
  `storage_type` tinyint(1) DEFAULT '0' COMMENT '存储类型(0:本地存储 1:MinIO 2:阿里云OSS)',
  `file_type` tinyint(1) DEFAULT '0' COMMENT '文件类型(0:普通文件 1:图片 2:视频 3:音频 4:文档)',
  `mime_type` varchar(128) DEFAULT NULL COMMENT '文件MIME类型',
  `md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  `status` tinyint(1) DEFAULT '0' COMMENT '文件状态(0:临时 1:永久)',
  `module` varchar(100) DEFAULT 'common' COMMENT '业务模块',
  `business_id` varchar(64) DEFAULT NULL COMMENT '业务ID',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新者ID',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0:未删除 1:已删除)',
  PRIMARY KEY (`file_id`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_module` (`module`),
  KEY `idx_md5` (`md5`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='文件信息表';

-- ----------------------------
-- 创建文件操作日志表
-- ----------------------------
DROP TABLE IF EXISTS `file_operation_log`;
CREATE TABLE `file_operation_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `file_id` bigint(20) NOT NULL COMMENT '文件ID',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型(0:上传 1:下载 2:删除 3:修改)',
  `operation_user` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `operation_ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `operation_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `operation_result` tinyint(1) DEFAULT '0' COMMENT '操作结果(0:失败 1:成功)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`log_id`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='文件操作日志表'; 