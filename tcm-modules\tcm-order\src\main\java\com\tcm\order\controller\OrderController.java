package com.tcm.order.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.order.entity.Order;
import com.tcm.order.service.IOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单控制器
 *
 * <AUTHOR> Team
 */
@Slf4j
@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private IOrderService orderService;

    /**
     * 创建订单
     *
     * @param order 订单信息
     * @return 创建结果
     */
    @PostMapping
    public Map<String, Object> createOrder(@RequestBody Order order) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long orderId = orderService.createOrder(order);
            result.put("code", 200);
            result.put("message", "订单创建成功");
            result.put("data", orderId);
        } catch (Exception e) {
            log.error("创建订单失败", e);
            result.put("code", 500);
            result.put("message", "订单创建失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    @GetMapping("/{orderId}")
    public Map<String, Object> getOrderDetail(@PathVariable Long orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Order order = orderService.getOrderDetail(orderId);
            if (order != null) {
                result.put("code", 200);
                result.put("message", "获取订单详情成功");
                result.put("data", order);
            } else {
                result.put("code", 404);
                result.put("message", "订单不存在");
            }
        } catch (Exception e) {
            log.error("获取订单详情失败", e);
            result.put("code", 500);
            result.put("message", "获取订单详情失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 分页查询订单列表
     *
     * @param pageNum   页码
     * @param pageSize  每页记录数
     * @param orderType 订单类型
     * @param status    订单状态
     * @param keyword   关键字
     * @return 订单分页列表
     */
    @GetMapping("/page")
    public Map<String, Object> getOrderPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) Integer orderType,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) String keyword) {
        Map<String, Object> result = new HashMap<>();
        try {
            Page<Order> page = new Page<>(pageNum, pageSize);
            IPage<Order> orderPage = orderService.getOrderPage(page, orderType, status, keyword);
            
            result.put("code", 200);
            result.put("message", "获取订单列表成功");
            result.put("data", orderPage);
        } catch (Exception e) {
            log.error("获取订单列表失败", e);
            result.put("code", 500);
            result.put("message", "获取订单列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 支付订单
     *
     * @param orderId       订单ID
     * @param paymentMethod 支付方式
     * @param transactionNo 交易号
     * @return 支付结果
     */
    @PostMapping("/{orderId}/pay")
    public Map<String, Object> payOrder(
            @PathVariable Long orderId,
            @RequestParam Integer paymentMethod,
            @RequestParam String transactionNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.payOrder(orderId, paymentMethod, transactionNo);
            if (success) {
                result.put("code", 200);
                result.put("message", "订单支付成功");
            } else {
                result.put("code", 400);
                result.put("message", "订单支付失败，请检查订单状态");
            }
        } catch (Exception e) {
            log.error("订单支付失败", e);
            result.put("code", 500);
            result.put("message", "订单支付失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 取消订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     * @return 取消结果
     */
    @PostMapping("/{orderId}/cancel")
    public Map<String, Object> cancelOrder(
            @PathVariable Long orderId,
            @RequestParam String cancelReason) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.cancelOrder(orderId, cancelReason);
            if (success) {
                result.put("code", 200);
                result.put("message", "订单取消成功");
            } else {
                result.put("code", 400);
                result.put("message", "订单取消失败，请检查订单状态");
            }
        } catch (Exception e) {
            log.error("订单取消失败", e);
            result.put("code", 500);
            result.put("message", "订单取消失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 申请退款
     *
     * @param orderId      订单ID
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款申请结果
     */
    @PostMapping("/{orderId}/refund")
    public Map<String, Object> refundOrder(
            @PathVariable Long orderId,
            @RequestParam String refundAmount,
            @RequestParam String refundReason) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.refundOrder(orderId, refundAmount, refundReason);
            if (success) {
                result.put("code", 200);
                result.put("message", "退款申请成功");
            } else {
                result.put("code", 400);
                result.put("message", "退款申请失败，请检查订单状态");
            }
        } catch (Exception e) {
            log.error("退款申请失败", e);
            result.put("code", 500);
            result.put("message", "退款申请失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 订单发货
     *
     * @param orderId        订单ID
     * @param expressCompany 物流公司
     * @param expressNo      物流单号
     * @return 发货结果
     */
    @PostMapping("/{orderId}/delivery")
    public Map<String, Object> deliveryOrder(
            @PathVariable Long orderId,
            @RequestParam String expressCompany,
            @RequestParam String expressNo) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.deliveryOrder(orderId, expressCompany, expressNo);
            if (success) {
                result.put("code", 200);
                result.put("message", "订单发货成功");
            } else {
                result.put("code", 400);
                result.put("message", "订单发货失败，请检查订单状态");
            }
        } catch (Exception e) {
            log.error("订单发货失败", e);
            result.put("code", 500);
            result.put("message", "订单发货失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 确认收货
     *
     * @param orderId 订单ID
     * @return 确认收货结果
     */
    @PostMapping("/{orderId}/receive")
    public Map<String, Object> receiveOrder(@PathVariable Long orderId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = orderService.receiveOrder(orderId);
            if (success) {
                result.put("code", 200);
                result.put("message", "确认收货成功");
            } else {
                result.put("code", 400);
                result.put("message", "确认收货失败，请检查订单状态");
            }
        } catch (Exception e) {
            log.error("确认收货失败", e);
            result.put("code", 500);
            result.put("message", "确认收货失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询患者订单列表
     *
     * @param patientId 患者ID
     * @param status    订单状态
     * @return 订单列表
     */
    @GetMapping("/patient/{patientId}")
    public Map<String, Object> getPatientOrders(
            @PathVariable Long patientId,
            @RequestParam(required = false) Integer status) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Order> orders = orderService.getPatientOrders(patientId, status);
            result.put("code", 200);
            result.put("message", "获取患者订单列表成功");
            result.put("data", orders);
        } catch (Exception e) {
            log.error("获取患者订单列表失败", e);
            result.put("code", 500);
            result.put("message", "获取患者订单列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询医生订单列表
     *
     * @param doctorId 医生ID
     * @param status   订单状态
     * @return 订单列表
     */
    @GetMapping("/doctor/{doctorId}")
    public Map<String, Object> getDoctorOrders(
            @PathVariable Long doctorId,
            @RequestParam(required = false) Integer status) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Order> orders = orderService.getDoctorOrders(doctorId, status);
            result.put("code", 200);
            result.put("message", "获取医生订单列表成功");
            result.put("data", orders);
        } catch (Exception e) {
            log.error("获取医生订单列表失败", e);
            result.put("code", 500);
            result.put("message", "获取医生订单列表失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询订单统计数据
     *
     * @param startTime 开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @param endTime   结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public Map<String, Object> getOrderStatistics(
            @RequestParam String startTime,
            @RequestParam String endTime) {
        Map<String, Object> result = new HashMap<>();
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime start = LocalDateTime.parse(startTime, formatter);
            LocalDateTime end = LocalDateTime.parse(endTime, formatter);
            
            List<Map<String, Object>> statistics = orderService.getOrderStatistics(start, end);
            result.put("code", 200);
            result.put("message", "获取订单统计数据成功");
            result.put("data", statistics);
        } catch (Exception e) {
            log.error("获取订单统计数据失败", e);
            result.put("code", 500);
            result.put("message", "获取订单统计数据失败：" + e.getMessage());
        }
        return result;
    }
}
