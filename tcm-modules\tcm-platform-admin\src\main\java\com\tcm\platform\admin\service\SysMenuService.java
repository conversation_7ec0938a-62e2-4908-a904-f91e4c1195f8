package com.tcm.platform.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.platform.admin.entity.SysMenu;

import java.util.List;

/**
 * 系统菜单服务接口
 * 
 * <AUTHOR>
 */
public interface SysMenuService extends IService<SysMenu> {

    /**
     * 查询菜单列表
     * 
     * @param menu 查询条件
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList(SysMenu menu);

    /**
     * 构建前端路由所需要的菜单树
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> buildMenuTree(Long userId);

    /**
     * 根据用户ID查询菜单权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据角色ID查询菜单ID列表
     * 
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<Long> selectMenuIdsByRoleId(Long roleId);

    /**
     * 根据菜单ID查询信息
     * 
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuById(Long menuId);

    /**
     * 新增菜单
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    boolean insertMenu(SysMenu menu);

    /**
     * 修改菜单
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    boolean updateMenu(SysMenu menu);

    /**
     * 删除菜单
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    boolean deleteMenuById(Long menuId);

    /**
     * 检查菜单是否有子菜单
     * 
     * @param menuId 菜单ID
     * @return 结果 true存在 false不存在
     */
    boolean hasChildrenMenu(Long menuId);
}