package com.tcm.appointment.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 预约挂号实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_appointment")
public class Appointment extends BaseEntity {
    
    /**
     * 预约ID
     */
    @TableId(type = IdType.AUTO)
    private Long appointmentId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生排班ID
     */
    private Long scheduleId;
    
    /**
     * 预约日期
     */
    private Date appointmentDate;
    
    /**
     * 时间段
     */
    private String timeSlot;
    
    /**
     * 预约类型（1-初诊 2-复诊）
     */
    private Integer type;
    
    /**
     * 主要症状
     */
    private String symptom;
    
    /**
     * 就诊科室
     */
    private String department;
    
    /**
     * 预约状态（0-待支付 1-已预约 2-已到诊 3-已取消 4-已爽约）
     */
    private Integer status;
    
    /**
     * 费用
     */
    private Double fee;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 支付状态（0-未支付 1-已支付 2-已退款）
     */
    private Integer payStatus;
    
    /**
     * 支付时间
     */
    private Date payTime;
    
    /**
     * 到诊时间
     */
    private Date arriveTime;
    
    /**
     * 排序号
     */
    private Integer orderNum;
    
    /**
     * 删除标志（0-正常 1-已删除）
     */
    @TableField("del_flag")
    private Integer delFlag;
    
    /**
     * 获取ID
     * @return 预约ID
     */
    public Long getId() {
        return this.appointmentId;
    }
    
    /**
     * 设置ID
     * @param id 预约ID
     */
    public void setId(Long id) {
        this.appointmentId = id;
    }
} 