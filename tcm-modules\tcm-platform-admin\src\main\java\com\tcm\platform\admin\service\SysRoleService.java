package com.tcm.platform.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.platform.admin.entity.SysRole;

import java.util.List;

/**
 * 系统角色服务接口
 * 
 * <AUTHOR>
 */
public interface SysRoleService extends IService<SysRole> {

    /**
     * 分页查询角色列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param role     查询条件
     * @return 角色分页列表
     */
    IPage<SysRole> selectRolePage(Integer pageNum, Integer pageSize, SysRole role);

    /**
     * 根据角色ID查询角色
     * 
     * @param roleId 角色ID
     * @return 角色信息
     */
    SysRole selectRoleById(Long roleId);

    /**
     * 根据用户ID查询角色列表
     * 
     * @param userId 用户ID
     * @return 角色列表
     */
    List<SysRole> selectRolesByUserId(Long userId);

    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色信息
     */
    SysRole selectRoleByCode(String roleCode);

    /**
     * 检查角色名称是否唯一
     * 
     * @param roleName 角色名称
     * @param roleId   角色ID（更新时校验）
     * @return 结果 true-重复 false-不重复
     */
    boolean checkRoleNameUnique(String roleName, Long roleId);

    /**
     * 检查角色编码是否唯一
     * 
     * @param roleCode 角色编码
     * @param roleId   角色ID（更新时校验）
     * @return 结果 true-重复 false-不重复
     */
    boolean checkRoleCodeUnique(String roleCode, Long roleId);

    /**
     * 新增角色
     * 
     * @param role    角色信息
     * @param menuIds 菜单ID列表
     * @return 结果
     */
    boolean insertRole(SysRole role, List<Long> menuIds);

    /**
     * 修改角色
     * 
     * @param role    角色信息
     * @param menuIds 菜单ID列表
     * @return 结果
     */
    boolean updateRole(SysRole role, List<Long> menuIds);

    /**
     * 删除角色
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    boolean deleteRoleById(Long roleId);

    /**
     * 修改角色状态
     * 
     * @param roleId 角色ID
     * @param status 状态
     * @return 结果
     */
    boolean updateRoleStatus(Long roleId, Integer status);

    /**
     * 获取角色选择框列表
     * 
     * @return 角色列表
     */
    List<SysRole> selectRoleOptions();
}