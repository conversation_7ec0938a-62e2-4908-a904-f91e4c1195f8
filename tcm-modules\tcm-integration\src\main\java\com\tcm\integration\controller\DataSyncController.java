package com.tcm.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.integration.dto.DataSyncRecordDTO;
import com.tcm.integration.dto.Result;
import com.tcm.integration.entity.DataSyncRecord;
import com.tcm.integration.service.DataSyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

/**
 * 数据同步控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/integration/sync")
public class DataSyncController {

    @Autowired
    private DataSyncService dataSyncService;

    /**
     * 分页查询数据同步记录
     *
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @param status     状态
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<IPage<DataSyncRecord>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String systemCode,
            @RequestParam(required = false) String syncType,
            @RequestParam(required = false) Integer status) {

        IPage<DataSyncRecord> page = dataSyncService.page(pageNum, pageSize, systemCode, syncType, status);
        return Result.success(page);
    }

    /**
     * 创建同步任务
     *
     * @param dto 同步任务信息
     * @return 任务ID
     */
    @PostMapping
    public Result<Long> createSyncTask(@RequestBody @Valid DataSyncRecordDTO dto) {
        Long taskId = dataSyncService.createSyncTask(dto);
        return Result.success(taskId);
    }

    /**
     * 执行同步任务
     *
     * @param recordId 同步记录ID
     * @return 是否成功
     */
    @PutMapping("/execute/{recordId}")
    public Result<Boolean> executeSyncTask(@PathVariable Long recordId) {
        boolean result = dataSyncService.executeSyncTask(recordId);
        return Result.success(result);
    }

    /**
     * 获取同步详情
     *
     * @param id 同步记录ID
     * @return 同步详情
     */
    @GetMapping("/{id}")
    public Result<DataSyncRecord> getSyncDetail(@PathVariable Long id) {
        DataSyncRecord record = dataSyncService.getSyncDetail(id);
        return Result.success(record);
    }

    /**
     * 获取同步统计数据
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @return 同步统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getSyncStatistics(
            @RequestParam String systemCode,
            @RequestParam String syncType) {

        Map<String, Object> statistics = dataSyncService.getSyncStatistics(systemCode, syncType);
        return Result.success(statistics);
    }

    /**
     * 取消同步任务
     *
     * @param id 同步记录ID
     * @return 是否成功
     */
    @PutMapping("/cancel/{id}")
    public Result<Boolean> cancelSync(@PathVariable Long id) {
        boolean result = dataSyncService.cancelSync(id);
        return Result.success(result);
    }

    /**
     * 获取最近同步记录
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @return 最近同步记录
     */
    @GetMapping("/last")
    public Result<DataSyncRecord> getLastSyncRecord(
            @RequestParam String systemCode,
            @RequestParam String syncType) {

        DataSyncRecord record = dataSyncService.getLastSyncRecord(systemCode, syncType);
        return Result.success(record);
    }

    /**
     * 数据导入
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @param params     导入参数
     * @return 同步记录ID
     */
    @PostMapping("/import")
    public Result<Long> importData(
            @RequestParam String systemCode,
            @RequestParam String syncType,
            @RequestBody Map<String, Object> params) {

        Long recordId = dataSyncService.importData(systemCode, syncType, params);
        return Result.success(recordId);
    }

    /**
     * 数据导出
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @param params     导出参数
     * @return 同步记录ID
     */
    @PostMapping("/export")
    public Result<Long> exportData(
            @RequestParam String systemCode,
            @RequestParam String syncType,
            @RequestBody Map<String, Object> params) {

        Long recordId = dataSyncService.exportData(systemCode, syncType, params);
        return Result.success(recordId);
    }
}