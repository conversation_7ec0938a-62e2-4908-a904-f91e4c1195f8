package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医生知识库实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_doctor_knowledge")
public class DoctorKnowledge extends BaseEntity {
    
    /**
     * 知识ID
     */
    @TableId
    private Long knowledgeId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 知识标题
     */
    private String title;
    
    /**
     * 知识类型（1-诊疗经验 2-特色疗法 3-医学观点 4-典型案例）
     */
    private Integer type;
    
    /**
     * 知识内容
     */
    private String content;
    
    /**
     * 适用疾病
     */
    private String applicableDiseases;
    
    /**
     * 关键词（多个以逗号分隔）
     */
    private String keywords;
    
    /**
     * 参考文献
     */
    private String references;
    
    /**
     * 附件URL（多个以逗号分隔）
     */
    private String attachments;
    
    /**
     * 分享状态（0-私有 1-公开）
     */
    private Integer shareStatus;
    
    /**
     * 点赞数
     */
    private Integer likeCount;
    
    /**
     * 收藏数
     */
    private Integer favoriteCount;
    
    /**
     * 阅读数
     */
    private Integer viewCount;
    
    /**
     * 引用数
     */
    private Integer citationCount;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 状态（0-正常 1-禁用）
     */
    private Integer status;
} 