package com.tcm.appointment.service.impl;

import com.tcm.appointment.service.IPatientIntegrationService;
import com.tcm.appointment.vo.PatientVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * 患者模块集成服务实现类
 */
@Slf4j
@Service
public class PatientIntegrationServiceImpl implements IPatientIntegrationService {

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取患者信息
     *
     * @param patientId 患者ID
     * @return 患者信息
     */
    @Override
    public PatientVO getPatientInfo(Long patientId) {
        log.info("调用患者服务获取患者信息，patientId: {}", patientId);
        try {
            // 实际项目中应使用微服务调用或Feign客户端
            // 这里为简化实现，使用RestTemplate模拟调用
            String url = "http://tcm-patient/patient/" + patientId;
            return restTemplate.getForObject(url, PatientVO.class);
        } catch (Exception e) {
            log.error("获取患者信息失败", e);
            return null;
        }
    }

    /**
     * 验证患者是否可以预约（检查是否在黑名单中）
     *
     * @param patientId 患者ID
     * @return 是否可以预约
     */
    @Override
    public boolean checkPatientAppointmentEligibility(Long patientId) {
        log.info("检查患者预约资格，patientId: {}", patientId);
        try {
            String url = "http://tcm-patient/patient/check-eligibility/" + patientId;
            return Boolean.TRUE.equals(restTemplate.getForObject(url, Boolean.class));
        } catch (Exception e) {
            log.error("检查患者预约资格失败", e);
            return false;
        }
    }

    /**
     * 记录患者爽约
     *
     * @param patientId 患者ID
     * @return 操作结果
     */
    @Override
    public boolean recordPatientNoShow(Long patientId) {
        log.info("记录患者爽约，patientId: {}", patientId);
        try {
            String url = "http://tcm-patient/patient/record-no-show/" + patientId;
            return Boolean.TRUE.equals(restTemplate.postForObject(url, null, Boolean.class));
        } catch (Exception e) {
            log.error("记录患者爽约失败", e);
            return false;
        }
    }

    /**
     * 获取患者历史就诊记录
     *
     * @param patientId 患者ID
     * @param doctorId  医生ID
     * @return 历史记录
     */
    @Override
    public boolean hasVisitedDoctor(Long patientId, Long doctorId) {
        log.info("查询患者历史就诊记录，patientId: {}, doctorId: {}", patientId, doctorId);
        try {
            String url = "http://tcm-patient/patient/history/check?patientId=" + patientId + "&doctorId=" + doctorId;
            return Boolean.TRUE.equals(restTemplate.getForObject(url, Boolean.class));
        } catch (Exception e) {
            log.error("查询患者历史就诊记录失败", e);
            return false;
        }
    }
} 