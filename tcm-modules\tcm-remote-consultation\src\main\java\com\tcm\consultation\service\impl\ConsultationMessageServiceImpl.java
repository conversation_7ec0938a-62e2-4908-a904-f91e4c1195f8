package com.tcm.consultation.service.impl;

import org.springframework.stereotype.Service;
import com.tcm.consultation.dto.MessageDTO;
import com.tcm.consultation.service.ConsultationMessageService;
import com.tcm.consultation.mapper.ConsultationMessageMapper;
import com.tcm.consultation.domain.ConsultationMessage;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import java.util.Date;

/**
 * 会诊消息服务实现类
 */
@Service
@Slf4j
public class ConsultationMessageServiceImpl implements ConsultationMessageService {

    @Autowired
    private ConsultationMessageMapper messageMapper;

    @Override
    public boolean saveMessage(MessageDTO messageDTO) {
        try {
            // 转换DTO为实体
            ConsultationMessage message = new ConsultationMessage();
            message.setConsultationId(messageDTO.getConsultationId());
            message.setSenderId(messageDTO.getSenderId());
            message.setContent(messageDTO.getContent());
            message.setMessageType(messageDTO.getMessageType());
            message.setCreateTime(new Date());

            // 保存消息
            int result = messageMapper.insert(message);

            log.info("保存会诊消息: {}, 结果={}", messageDTO, result > 0);
            return result > 0;
        } catch (Exception e) {
            log.error("保存会诊消息失败", e);
            return false;
        }
    }
}