server:
  port: ${tcm.service.doctor.port:${TCM_DOCTOR_PORT:9200}}

spring:
  application:
    name: tcm-doctor
  # 引入公共配置
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
    
  # 允许Bean覆盖，解决RedisTemplate等Bean定义冲突问题
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
    
  # 数据库配置 - 将继续使用application-common.yml中的MySQL配置，但增加容错处理
  datasource:
    # 明确指定使用HikariCP，避免Druid依赖问题
    type: com.zaxxer.hikari.HikariDataSource
    # 采用同样的基本配置，确保与application-common.yml兼容
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${tcm.service.mysql.url}
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    # 数据库连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 900000
      auto-commit: true
      connection-test-query: SELECT 1
  
  # 数据库初始化配置 - 替代已废弃的datasource.continue-on-error
  sql:
    init:
      continue-on-error: true
      
  # 暂时禁用Liquibase自动配置
  liquibase:
    enabled: false
      
  cloud:
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.url}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.url}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml


# 日志配置
logging:
  level:
    com.tcm.doctor: debug
    org.springframework: warn

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.doctor.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# Feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

# 管理端点配置
management:
  # 禁用JMX连接，避免RMI问题
  endpoints:
    jmx:
      exposure:
        exclude: "*"
      enabled: false

# TCM系统配置
tcm:
  # JDK配置
  jdk:
    # 默认JDK路径
    home: C:\Users\<USER>\jdk\java
    # Windows系统JDK路径
    windows-home: C:\Users\<USER>\jdk\java
    # Linux系统JDK路径
    linux-home: /home/<USER>/jdk/java