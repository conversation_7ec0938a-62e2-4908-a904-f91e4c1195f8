package com.tcm.common.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * JDK配置属性类 从application.yml中加载tcm.jdk前缀的配置
 */
@Component
@ConfigurationProperties(prefix = "tcm.service.jdk")
public class JdkProperties {

    /**
     * 默认JDK路径
     */
    private String home;

    /**
     * Windows系统JDK路径
     */
    private Windows windows = new Windows();

    /**
     * Linux系统JDK路径
     */
    private Linux linux = new Linux();

    /**
     * Mac系统JDK路径
     */
    private Mac mac = new Mac();

    /**
     * Windows系统特定配置
     */
    public static class Windows {
        private String home;

        public String getHome() {
            return home;
        }

        public void setHome(String home) {
            this.home = home;
        }
    }

    /**
     * Linux系统特定配置
     */
    public static class Linux {
        private String home;

        public String getHome() {
            return home;
        }

        public void setHome(String home) {
            this.home = home;
        }
    }

    /**
     * Mac系统特定配置
     */
    public static class Mac {
        private String home;

        public String getHome() {
            return home;
        }

        public void setHome(String home) {
            this.home = home;
        }
    }

    public String getHome() {
        return home;
    }

    public void setHome(String home) {
        this.home = home;
    }

    public Windows getWindows() {
        return windows;
    }

    public void setWindows(Windows windows) {
        this.windows = windows;
    }

    public Linux getLinux() {
        return linux;
    }

    public void setLinux(Linux linux) {
        this.linux = linux;
    }

    public Mac getMac() {
        return mac;
    }

    public void setMac(Mac mac) {
        this.mac = mac;
    }
}