<?xml version="1.0" encoding="UTF-8"?>
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Generated by Maven Help Plugin                                         -->
<!-- See: https://maven.apache.org/plugins/maven-help-plugin/               -->
<!--                                                                        -->
<!-- ====================================================================== -->
<!-- ====================================================================== -->
<!--                                                                        -->
<!-- Effective POM for project 'com.tcm:tcm-remote-consultation:jar:1.0.0'  -->
<!--                                                                        -->
<!-- ====================================================================== -->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.tcm</groupId>
    <artifactId>tcm-modules</artifactId>
    <version>1.0.0</version>
  </parent>
  <groupId>com.tcm</groupId>
  <artifactId>tcm-remote-consultation</artifactId>
  <version>1.0.0</version>
  <name>tcm-remote-consultation</name>
  <description>远程会诊系统模块</description>
  <properties>
    <commons-lang3.version>3.13.0</commons-lang3.version>
    <druid.version>1.2.20</druid.version>
    <elasticsearch-java.version>8.12.1</elasticsearch-java.version>
    <elasticsearch.version>8.12.1</elasticsearch.version>
    <hibernate-validator.version>6.2.5.Final</hibernate-validator.version>
    <hutool.version>5.8.25</hutool.version>
    <jakarta-validation-api.version>3.0.2</jakarta-validation-api.version>
    <java.version>17</java.version>
    <javax-validation-api.version>2.0.1.Final</javax-validation-api.version>
    <junit-jupiter.version>5.10.0</junit-jupiter.version>
    <knife4j.version>4.3.0</knife4j.version>
    <lombok.version>1.18.30</lombok.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <milvus.version>2.3.0</milvus.version>
    <minio.version>8.5.7</minio.version>
    <mockito.version>5.8.0</mockito.version>
    <mybatis-plus.version>3.5.4</mybatis-plus.version>
    <mysql.version>8.2.0</mysql.version>
    <nacos.version>2022.0.0.0</nacos.version>
    <neo4j.version>5.13.0</neo4j.version>
    <poi.version>5.4.1</poi.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <redisson.version>3.24.3</redisson.version>
    <rocketmq.version>2.2.3</rocketmq.version>
    <seata.version>1.7.0</seata.version>
    <sentinel.version>2022.0.0.0</sentinel.version>
    <spring-boot.version>2.6.15</spring-boot.version>
    <spring-cloud-alibaba-sentinel-gateway.version>2022.0.0.0</spring-cloud-alibaba-sentinel-gateway.version>
    <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
    <spring-cloud-starter-bootstrap.version>4.0.4</spring-cloud-starter-bootstrap.version>
    <spring-cloud-starter-gateway.version>4.0.4</spring-cloud-starter-gateway.version>
    <spring-cloud.version>2021.0.5</spring-cloud.version>
    <spring-integration.version>5.5.15</spring-integration.version>
    <tcm.version>1.0.0</tcm.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.tcm</groupId>
        <artifactId>tcm-common-core</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.tcm</groupId>
        <artifactId>tcm-common-redis</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.tcm</groupId>
        <artifactId>tcm-common-security</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>javax.validation</groupId>
        <artifactId>validation-api</artifactId>
        <version>2.0.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>6.2.5.Final</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-core</artifactId>
        <version>5.5.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-http</artifactId>
        <version>5.5.15</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.28</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>2.6.15</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.5.4</version>
      </dependency>
      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>8.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
        <version>4.3.0</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>5.8.25</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson</artifactId>
        <version>3.24.3</version>
      </dependency>
      <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>8.5.7</version>
      </dependency>
      <dependency>
        <groupId>co.elastic.clients</groupId>
        <artifactId>elasticsearch-java</artifactId>
        <version>8.12.1</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>8.12.1</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j.driver</groupId>
        <artifactId>neo4j-java-driver</artifactId>
        <version>5.13.0</version>
      </dependency>
      <dependency>
        <groupId>io.milvus</groupId>
        <artifactId>milvus-sdk-java</artifactId>
        <version>2.3.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>2.2.3</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        <version>1.7.0</version>
      </dependency>
      <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>5.8.0</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>5.8.0</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.10.0</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.10.0</version>
      </dependency>
      <dependency>
        <groupId>io.seata</groupId>
        <artifactId>seata-spring-boot-starter</artifactId>
        <version>1.7.0</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bootstrap</artifactId>
        <version>4.0.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-gateway</artifactId>
        <version>4.0.4</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-sentinel-gateway</artifactId>
        <version>2022.0.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.13.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.poi</groupId>
        <artifactId>poi</artifactId>
        <version>5.4.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <scope>provided</scope>
        <optional>true</optional>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-amqp</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-blueprint</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-camel</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-console</artifactId>
        <version>5.16.6</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-http</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jaas</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jdbc-store</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jms-pool</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-kahadb-store</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-karaf</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-leveldb-store</artifactId>
        <version>5.16.6</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-log4j-appender</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-mqtt</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-generator</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-legacy</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-osgi</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-partition</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-pool</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-ra</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-run</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-runtime-config</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-shiro</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-spring</artifactId>
        <version>5.16.6</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-stomp</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web</artifactId>
        <version>5.16.6</version>
      </dependency>
      <dependency>
        <groupId>antlr</groupId>
        <artifactId>antlr</artifactId>
        <version>2.7.7</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>1.9.98</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-amqp-protocol</artifactId>
        <version>2.19.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-commons</artifactId>
        <version>2.19.1</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-core-client</artifactId>
        <version>2.19.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jdbc-store</artifactId>
        <version>2.19.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-client</artifactId>
        <version>2.19.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jms-server</artifactId>
        <version>2.19.1</version>
        <exclusions>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-journal</artifactId>
        <version>2.19.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-quorum-api</artifactId>
        <version>2.19.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-selector</artifactId>
        <version>2.19.1</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-server</artifactId>
        <version>2.19.1</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-json_1.0_spec</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-service-extensions</artifactId>
        <version>2.19.1</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjrt</artifactId>
        <version>1.9.7</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjtools</artifactId>
        <version>1.9.7</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>1.9.7</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>3.21.0</version>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jdbc</artifactId>
        <version>4.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jms</artifactId>
        <version>4.0.6</version>
      </dependency>
      <dependency>
        <groupId>com.atomikos</groupId>
        <artifactId>transactions-jta</artifactId>
        <version>4.0.6</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>4.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-groovy</artifactId>
        <version>4.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-kotlin</artifactId>
        <version>4.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-scala</artifactId>
        <version>4.1.1</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>1.11.22</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>1.11.22</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>2.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>guava</artifactId>
        <version>2.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>jcache</artifactId>
        <version>2.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>simulator</artifactId>
        <version>2.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core</artifactId>
        <version>4.13.0</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.fasterxml</groupId>
        <artifactId>classmate</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.15</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>2.9.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-pool</groupId>
        <artifactId>commons-pool</artifactId>
        <version>1.6</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>2.11.1</version>
      </dependency>
      <dependency>
        <groupId>com.couchbase.client</groupId>
        <artifactId>java-client</artifactId>
        <version>3.2.7</version>
      </dependency>
      <dependency>
        <groupId>com.ibm.db2</groupId>
        <artifactId>jcc</artifactId>
        <version>11.5.7.0</version>
      </dependency>
      <dependency>
        <groupId>io.spring.gradle</groupId>
        <artifactId>dependency-management-plugin</artifactId>
        <version>1.0.15.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derby</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyclient</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbynet</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyoptionaltools</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbytools</artifactId>
        <version>10.14.2.0</version>
      </dependency>
      <dependency>
        <groupId>net.sf.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>2.10.9.2</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>3.9.10</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-clustered</artifactId>
        <version>3.9.10</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-transactions</artifactId>
        <version>3.9.10</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>transport</artifactId>
        <version>7.15.2</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>7.15.2</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client-sniffer</artifactId>
        <version>7.15.2</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-high-level-client</artifactId>
        <version>7.15.2</version>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.distribution.integ-test-zip</groupId>
        <artifactId>elasticsearch</artifactId>
        <version>7.15.2</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.plugin</groupId>
        <artifactId>transport-netty4-client</artifactId>
        <version>7.15.2</version>
      </dependency>
      <dependency>
        <groupId>de.flapdoodle.embed</groupId>
        <artifactId>de.flapdoodle.embed.mongo</artifactId>
        <version>3.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>8.0.5</version>
      </dependency>
      <dependency>
        <groupId>org.freemarker</groupId>
        <artifactId>freemarker</artifactId>
        <version>2.3.32</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish</groupId>
        <artifactId>jakarta.el</artifactId>
        <version>3.0.4</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>codemodel</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>codemodel-annotation-compiler</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-jxc</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-runtime</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-xjc</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>txw2</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>txwc2</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>xsom</artifactId>
        <version>2.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.web</groupId>
        <artifactId>jakarta.servlet.jsp.jstl</artifactId>
        <version>1.2.6</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>2.8.9</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>1.4.200</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast</artifactId>
        <version>4.2.7</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-spring</artifactId>
        <version>4.2.7</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-hibernate52</artifactId>
        <version>2.2.1</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-hibernate53</artifactId>
        <version>2.2.1</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-c3p0</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-core</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-ehcache</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-entitymanager</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-envers</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-hikaricp</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-java8</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-jcache</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-jpamodelgen</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-micrometer</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-proxool</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-spatial</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-testing</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-vibur</artifactId>
        <version>5.6.15.Final</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-annotation-processor</artifactId>
        <version>6.2.5.Final</version>
      </dependency>
      <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
        <version>4.0.3</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>2.5.2</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.htmlunit</groupId>
        <artifactId>htmlunit</artifactId>
        <version>2.54.0</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpasyncclient</artifactId>
        <version>4.1.5</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>fluent-hc</artifactId>
        <version>4.5.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.5.14</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient-cache</artifactId>
        <version>4.5.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient-osgi</artifactId>
        <version>4.5.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient-win</artifactId>
        <version>4.5.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpmime</artifactId>
        <version>4.5.14</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>5.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-cache</artifactId>
        <version>5.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-fluent</artifactId>
        <version>5.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-win</artifactId>
        <version>5.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.16</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore-nio</artifactId>
        <version>4.4.16</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>5.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-h2</artifactId>
        <version>5.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-reactive</artifactId>
        <version>5.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.influxdb</groupId>
        <artifactId>influxdb-java</artifactId>
        <version>2.22</version>
      </dependency>
      <dependency>
        <groupId>com.sun.activation</groupId>
        <artifactId>jakarta.activation</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>1.2.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>1.3.5</version>
      </dependency>
      <dependency>
        <groupId>jakarta.jms</groupId>
        <artifactId>jakarta.jms-api</artifactId>
        <version>2.0.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json</groupId>
        <artifactId>jakarta.json-api</artifactId>
        <version>1.1.6</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json.bind</groupId>
        <artifactId>jakarta.json.bind-api</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.mail</groupId>
        <artifactId>jakarta.mail-api</artifactId>
        <version>1.6.7</version>
      </dependency>
      <dependency>
        <groupId>jakarta.management.j2ee</groupId>
        <artifactId>jakarta.management.j2ee-api</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>jakarta.persistence</groupId>
        <artifactId>jakarta.persistence-api</artifactId>
        <version>2.2.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>4.0.4</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet.jsp.jstl</groupId>
        <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
        <version>1.2.7</version>
      </dependency>
      <dependency>
        <groupId>jakarta.transaction</groupId>
        <artifactId>jakarta.transaction-api</artifactId>
        <version>1.3.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.websocket</groupId>
        <artifactId>jakarta.websocket-api</artifactId>
        <version>1.1.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.ws.rs</groupId>
        <artifactId>jakarta.ws.rs-api</artifactId>
        <version>2.1.6</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.soap</groupId>
        <artifactId>jakarta.xml.soap-api</artifactId>
        <version>1.4.2</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.ws</groupId>
        <artifactId>jakarta.xml.ws-api</artifactId>
        <version>2.3.3</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler</artifactId>
        <version>3.1.9</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler-jdk</artifactId>
        <version>3.1.9</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>3.1.9</version>
      </dependency>
      <dependency>
        <groupId>javax.activation</groupId>
        <artifactId>javax.activation-api</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>javax.annotation</groupId>
        <artifactId>javax.annotation-api</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>javax.cache</groupId>
        <artifactId>cache-api</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>javax.xml.bind</groupId>
        <artifactId>jaxb-api</artifactId>
        <version>2.3.1</version>
      </dependency>
      <dependency>
        <groupId>javax.xml.ws</groupId>
        <artifactId>jaxws-api</artifactId>
        <version>2.3.1</version>
      </dependency>
      <dependency>
        <groupId>javax.jms</groupId>
        <artifactId>javax.jms-api</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>javax.json</groupId>
        <artifactId>javax.json-api</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>javax.json.bind</groupId>
        <artifactId>javax.json.bind-api</artifactId>
        <version>1.0</version>
      </dependency>
      <dependency>
        <groupId>javax.mail</groupId>
        <artifactId>javax.mail-api</artifactId>
        <version>1.6.2</version>
      </dependency>
      <dependency>
        <groupId>javax.money</groupId>
        <artifactId>money-api</artifactId>
        <version>1.1</version>
      </dependency>
      <dependency>
        <groupId>javax.persistence</groupId>
        <artifactId>javax.persistence-api</artifactId>
        <version>2.2</version>
      </dependency>
      <dependency>
        <groupId>javax.transaction</groupId>
        <artifactId>javax.transaction-api</artifactId>
        <version>1.3</version>
      </dependency>
      <dependency>
        <groupId>javax.websocket</groupId>
        <artifactId>javax.websocket-api</artifactId>
        <version>1.1</version>
      </dependency>
      <dependency>
        <groupId>jaxen</groupId>
        <artifactId>jaxen</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>org.firebirdsql.jdbc</groupId>
        <artifactId>jaybird</artifactId>
        <version>4.0.9.java8</version>
      </dependency>
      <dependency>
        <groupId>org.firebirdsql.jdbc</groupId>
        <artifactId>jaybird-jdk18</artifactId>
        <version>4.0.9.java8</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>3.4.3.Final</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom2</artifactId>
        <version>*******</version>
      </dependency>
      <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>3.7.1</version>
      </dependency>
      <dependency>
        <groupId>org.mortbay.jasper</groupId>
        <artifactId>apache-el</artifactId>
        <version>9.0.52</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.orbit</groupId>
        <artifactId>javax.servlet.jsp</artifactId>
        <version>2.2.0.v201112011158</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-reactive-httpclient</artifactId>
        <version>1.1.14</version>
      </dependency>
      <dependency>
        <groupId>com.samskivert</groupId>
        <artifactId>jmustache</artifactId>
        <version>1.15</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-core</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jaxrs</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jsonb</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jsonb-extras</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-jsonschema</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-mapper</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.apache.johnzon</groupId>
        <artifactId>johnzon-websocket</artifactId>
        <version>1.2.20</version>
      </dependency>
      <dependency>
        <groupId>org.jolokia</groupId>
        <artifactId>jolokia-core</artifactId>
        <version>1.7.2</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq</artifactId>
        <version>3.14.16</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen</artifactId>
        <version>3.14.16</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-kotlin</artifactId>
        <version>3.14.16</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-meta</artifactId>
        <version>3.14.16</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path</artifactId>
        <version>2.6.0</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path-assert</artifactId>
        <version>2.6.0</version>
      </dependency>
      <dependency>
        <groupId>net.minidev</groupId>
        <artifactId>json-smart</artifactId>
        <version>2.4.10</version>
      </dependency>
      <dependency>
        <groupId>org.skyscreamer</groupId>
        <artifactId>jsonassert</artifactId>
        <version>1.5.1</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>jstl</artifactId>
        <version>1.2</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.jtds</groupId>
        <artifactId>jtds</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-basic-auth-extension</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-file</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-json</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-mirror</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-mirror-client</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-runtime</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-transforms</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>generator</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>3.0.2</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-log4j-appender</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-metadata</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-raft</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-server-common</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-shell</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-storage</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-storage-api</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.12</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.13</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-test-utils</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-tools</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>3.0.2</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>3.0.2</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>trogdor</artifactId>
        <version>3.0.2</version>
      </dependency>
      <dependency>
        <groupId>io.lettuce</groupId>
        <artifactId>lettuce-core</artifactId>
        <version>6.1.10.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-cdi</artifactId>
        <version>4.5.0</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
        <version>4.5.0</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-access</artifactId>
        <version>1.2.12</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>1.2.12</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>1.2.12</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
        <version>2.7.9</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-stackdriver</artifactId>
        <version>1.8.13</version>
        <exclusions>
          <exclusion>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.jvnet.mimepull</groupId>
        <artifactId>mimepull</artifactId>
        <version>1.9.15</version>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>4.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson</artifactId>
        <version>4.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-core</artifactId>
        <version>4.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-legacy</artifactId>
        <version>4.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-reactivestreams</artifactId>
        <version>4.4.2</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>4.4.2</version>
      </dependency>
      <dependency>
        <groupId>com.microsoft.sqlserver</groupId>
        <artifactId>mssql-jdbc</artifactId>
        <version>9.4.1.jre8</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.33</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.nekohtml</groupId>
        <artifactId>nekohtml</artifactId>
        <version>1.9.22</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.61.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.61.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.61.Final</version>
        <classifier>linux-x86_64-fedora</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.61.Final</version>
        <classifier>linux-aarch_64-fedora</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative</artifactId>
        <version>2.0.61.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.61.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.61.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.61.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.61.Final</version>
        <classifier>windows-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.61.Final</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-boringssl-static</artifactId>
        <version>2.0.61.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-tcnative-classes</artifactId>
        <version>2.0.61.Final</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>logging-interceptor</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>mockwebserver</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okcurl</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-dnsoverhttps</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-sse</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-testing-support</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-tls</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-urlconnection</artifactId>
        <version>3.14.9</version>
      </dependency>
      <dependency>
        <groupId>org.messaginghub</groupId>
        <artifactId>pooled-jms</artifactId>
        <version>1.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>42.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz</artifactId>
        <version>2.3.2</version>
        <exclusions>
          <exclusion>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.zaxxer</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz-jobs</artifactId>
        <version>2.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>amqp-client</artifactId>
        <version>5.13.1</version>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>stream-client</artifactId>
        <version>0.4.0</version>
      </dependency>
      <dependency>
        <groupId>org.reactivestreams</groupId>
        <artifactId>reactive-streams</artifactId>
        <version>1.0.4</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>json-path</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>json-schema-validator</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>kotlin-extensions</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured-common</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>scala-support</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-commons</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-mock-mvc</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-mock-mvc-kotlin-extensions</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>spring-web-test-client</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>xml-path</artifactId>
        <version>4.4.0</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex</groupId>
        <artifactId>rxjava</artifactId>
        <version>1.3.8</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex</groupId>
        <artifactId>rxjava-reactive-streams</artifactId>
        <version>1.2.1</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex.rxjava2</groupId>
        <artifactId>rxjava</artifactId>
        <version>2.2.21</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test-autoconfigure</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure-processor</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-buildpack-platform</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-metadata</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-devtools</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-jarmode-layertools</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader-tools</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-properties-migrator</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-activemq</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-amqp</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-artemis</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-batch</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra-reactive</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase-reactive</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jdbc</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-ldap</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-r2dbc</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-neo4j</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-rest</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-freemarker</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-groovy-templates</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-hateoas</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-integration</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jersey</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jetty</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jooq</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-json</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jta-atomikos</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-log4j2</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-logging</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mustache</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-client</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-quartz</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-reactor-netty</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-rsocket</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-thymeleaf</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-undertow</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web-services</artifactId>
        <version>2.6.15</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.messaging.saaj</groupId>
        <artifactId>saaj-impl</artifactId>
        <version>1.5.3</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>lift</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-api</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-chrome-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-edge-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-firefox-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-ie-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-java</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-leg-rc</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-opera-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-remote-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-safari-driver</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-server</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-support</artifactId>
        <version>3.141.59</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>htmlunit-driver</artifactId>
        <version>2.54.0</version>
      </dependency>
      <dependency>
        <groupId>com.sendgrid</groupId>
        <artifactId>sendgrid-java</artifactId>
        <version>4.7.6</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>4.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>log4j-over-slf4j</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-ext</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jcl</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>1.7.36</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>1.29</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-analysis-extras</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-analytics</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-cell</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-core</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-dataimporthandler</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-dataimporthandler-extras</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-langid</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-ltr</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-solrj</artifactId>
        <version>8.8.2</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-test-framework</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.solr</groupId>
        <artifactId>solr-velocity</artifactId>
        <version>8.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-amqp</artifactId>
        <version>2.4.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit</artifactId>
        <version>2.4.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-stream</artifactId>
        <version>2.4.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-junit</artifactId>
        <version>2.4.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-rabbit-test</artifactId>
        <version>2.4.12</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-core</artifactId>
        <version>4.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-infrastructure</artifactId>
        <version>4.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-integration</artifactId>
        <version>4.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-test</artifactId>
        <version>4.3.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.hateoas</groupId>
        <artifactId>spring-hateoas</artifactId>
        <version>1.4.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
        <version>2.8.11</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka-test</artifactId>
        <version>2.8.11</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-core</artifactId>
        <version>2.3.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-core-tiger</artifactId>
        <version>2.3.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-ldif-batch</artifactId>
        <version>2.3.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-ldif-core</artifactId>
        <version>2.3.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-odm</artifactId>
        <version>2.3.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-test</artifactId>
        <version>2.3.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-asciidoctor</artifactId>
        <version>2.0.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-core</artifactId>
        <version>2.0.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-mockmvc</artifactId>
        <version>2.0.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-restassured</artifactId>
        <version>2.0.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-webtestclient</artifactId>
        <version>2.0.7.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>1.3.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-core</artifactId>
        <version>3.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-security</artifactId>
        <version>3.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-support</artifactId>
        <version>3.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-test</artifactId>
        <version>3.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-xml</artifactId>
        <version>3.1.6</version>
      </dependency>
      <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>3.36.0.3</version>
      </dependency>
      <dependency>
        <groupId>com.sun.mail</groupId>
        <artifactId>jakarta.mail</artifactId>
        <version>1.6.7</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf</artifactId>
        <version>3.0.15.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf-spring5</artifactId>
        <version>3.0.15.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>com.github.mxab.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-data-attribute</artifactId>
        <version>2.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-java8time</artifactId>
        <version>3.0.4.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-springsecurity5</artifactId>
        <version>3.0.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>nz.net.ultraq.thymeleaf</groupId>
        <artifactId>thymeleaf-layout-dialect</artifactId>
        <version>3.0.0</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-annotations-api</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jdbc</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jsp-api</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-el</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-jasper</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-websocket</artifactId>
        <version>9.0.75</version>
      </dependency>
      <dependency>
        <groupId>com.unboundid</groupId>
        <artifactId>unboundid-ldapsdk</artifactId>
        <version>4.0.14</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-core</artifactId>
        <version>2.2.24.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-servlet</artifactId>
        <version>2.2.24.Final</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-websockets-jsr</artifactId>
        <version>2.2.24.Final</version>
      </dependency>
      <dependency>
        <groupId>org.webjars</groupId>
        <artifactId>webjars-locator-core</artifactId>
        <version>0.48</version>
      </dependency>
      <dependency>
        <groupId>wsdl4j</groupId>
        <artifactId>wsdl4j</artifactId>
        <version>1.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj</artifactId>
        <version>2.8.4</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj3</artifactId>
        <version>2.8.4</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-core</artifactId>
        <version>2.8.4</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-legacy</artifactId>
        <version>2.8.4</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-matchers</artifactId>
        <version>2.8.4</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-placeholders</artifactId>
        <version>2.8.4</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core-shaded</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-mapper-processor</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-mapper-runtime</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-query-builder</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-test-infra</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-metrics-micrometer</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-metrics-microprofile</artifactId>
        <version>4.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>native-protocol</artifactId>
        <version>1.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-shaded-guava</artifactId>
        <version>25.1-jre-graal-sub-1</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-annotation</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-caffeine</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-caffeine3</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-core</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-collectd</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-ehcache</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-graphite</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-healthchecks</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-httpclient</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-httpclient5</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-httpasyncclient</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jakarta-servlet</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jakarta-servlets</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jcache</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jdbi</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jdbi3</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jersey2</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jersey3</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jersey31</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jetty9</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jetty10</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jetty11</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jmx</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-json</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-jvm</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-log4j2</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-logback</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-logback13</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-logback14</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-servlet</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-servlets</artifactId>
        <version>4.2.18</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-ant</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-astbuilder</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-bsf</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-cli-commons</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-cli-picocli</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-console</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-datetime</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-dateutil</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-docgenerator</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-groovydoc</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-groovysh</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jaxb</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jmx</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-json</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-jsr223</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-macro</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-nio</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-servlet</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-sql</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-swing</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-templates</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-test</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-test-junit5</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-testng</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-xml</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-yaml</artifactId>
        <version>3.0.17</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-api</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-jdbc</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-jpa</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-remote</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cachestore-rocksdb</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-common</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-embedded</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cdi-remote</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-checkstyle</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cli-client</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-client-hotrod</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-client-rest</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-key-value-store-client</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-clustered-counter</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-clustered-lock</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-commons</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-commons-test</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-component-annotations</artifactId>
        <version>12.1.14.Final</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-component-processor</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-core</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jboss-marshalling</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-extended-statistics</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-commons</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-spi</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-v51</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-hibernate-cache-v53</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache-commons</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-jcache-remote</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-console</artifactId>
        <version>0.14.3.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-marshaller-kryo</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-marshaller-kryo-bundle</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-marshaller-protostuff</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-marshaller-protostuff-bundle</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-multimap</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-objectfilter</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-persistence-soft-index</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query-core</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-query-dsl</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-remote-query-client</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-remote-query-server</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-scripting</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-core</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-hotrod</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-memcached</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-rest</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-router</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-runtime</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-testdriver-core</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-testdriver-junit4</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-server-testdriver-junit5</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring5-common</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring5-embedded</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring5-remote</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring-boot-starter-embedded</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-spring-boot-starter-remote</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tasks</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tasks-api</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-tools</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-anchored-keys</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream</artifactId>
        <version>4.4.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream-types</artifactId>
        <version>4.4.1.Final</version>
      </dependency>
      <dependency>
        <groupId>org.infinispan.protostream</groupId>
        <artifactId>protostream-processor</artifactId>
        <version>4.4.1.Final</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-cloudevents-integration</artifactId>
        <version>12.1.14.Final</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-avro</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-cbor</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-csv</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-ion</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-properties</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-protobuf</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-smile</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-toml</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-yaml</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-eclipse-collections</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-guava</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate4</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate5</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hibernate5-jakarta</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-hppc</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jakarta-jsonp</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jaxrs</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-joda</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-joda-money</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jdk8</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-json-org</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr353</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-pcollections</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-base</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-cbor-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-json-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-smile-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-xml-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jaxrs</groupId>
        <artifactId>jackson-jaxrs-yaml-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-base</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-cbor-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-json-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-smile-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-xml-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
        <artifactId>jackson-jakarta-rs-yaml-provider</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-all</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-annotation-support</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-objects</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-retrofit2</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.jr</groupId>
        <artifactId>jackson-jr-stree</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-afterburner</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-blackbird</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-guice</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jaxb-annotations</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jakarta-xmlbind-annotations</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-jsonSchema</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-kotlin</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-mrbean</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-no-ctor-deser</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-osgi</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-parameter-names</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-paranamer</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.11</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.12</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_2.13</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.module</groupId>
        <artifactId>jackson-module-scala_3</artifactId>
        <version>2.13.5</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-common</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-client</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.core</groupId>
        <artifactId>jersey-server</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.bundles</groupId>
        <artifactId>jaxrs-ri</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-apache-connector</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-helidon-connector</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-grizzly-connector</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jetty-connector</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-jdk-connector</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.connectors</groupId>
        <artifactId>jersey-netty-connector</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-http</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-grizzly2-http</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-grizzly2-servlet</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jetty-servlet</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-jdk-http</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-netty-http</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-servlet</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-servlet-core</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers</groupId>
        <artifactId>jersey-container-simple-http</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.containers.glassfish</groupId>
        <artifactId>jersey-gf-ejb</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-bean-validation</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-entity-filtering</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-metainf-services</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-config</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-bean-validation</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-freemarker</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-jsp</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-mvc-mustache</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-proxy-client</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-servlet-portability</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-spring4</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-spring5</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-declarative-linking</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext</groupId>
        <artifactId>jersey-wadl-doclet</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-weld2-se</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-transaction</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-validation</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-servlet</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi1x-ban-custom-hk2-binding</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.cdi</groupId>
        <artifactId>jersey-cdi-rs-inject</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-guava</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-rxjava</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.rx</groupId>
        <artifactId>jersey-rx-client-rxjava2</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-rest-client</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-jaxb</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jackson</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-jettison</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-processing</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-json-binding</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-kryo</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-moxy</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-multipart</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.media</groupId>
        <artifactId>jersey-media-sse</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-client</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-server</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth1-signature</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.security</groupId>
        <artifactId>oauth2-client</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.inject</groupId>
        <artifactId>jersey-hk2</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.inject</groupId>
        <artifactId>jersey-cdi2-se</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework</groupId>
        <artifactId>jersey-test-framework-core</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-bundle</artifactId>
        <version>2.35</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-external</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-grizzly2</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-inmemory</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jdk-http</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-simple</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework.providers</groupId>
        <artifactId>jersey-test-framework-provider-jetty</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey.test-framework</groupId>
        <artifactId>jersey-test-framework-util</artifactId>
        <version>2.35</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jsp</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>apache-jstl</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-java-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-openjdk8-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-conscrypt-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-alpn-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-annotations</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-ant</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-continuation</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-deploy</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.51.v20230217</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-distribution</artifactId>
        <version>9.4.51.v20230217</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.fcgi</groupId>
        <artifactId>fcgi-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.gcloud</groupId>
        <artifactId>jetty-gcloud-session-manager</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.51.v20230217</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-home</artifactId>
        <version>9.4.51.v20230217</version>
        <type>tar.gz</type>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-common</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-hpack</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-http-client-transport</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.http2</groupId>
        <artifactId>http2-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-http-spi</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-common</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-remote-query</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>infinispan-embedded-query</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-hazelcast</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-io</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaas</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jaspi</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jmx</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-jndi</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.memcached</groupId>
        <artifactId>jetty-memcached-sessions</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-nosql</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-jsp</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-osgi-boot-warurl</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.osgi</groupId>
        <artifactId>jetty-httpservice</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-plus</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-proxy</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-quickstart</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-rewrite</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-security</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-openid</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlet</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-servlets</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-spring</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-unixsocket</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-util-ajax</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-webapp</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-client-impl</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>javax-websocket-server-impl</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-api</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-client</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-common</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-server</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.websocket</groupId>
        <artifactId>websocket-servlet</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-xml</artifactId>
        <version>9.4.51.v20230217</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>5.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-migrationsupport</artifactId>
        <version>5.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>5.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-commons</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-console</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-engine</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-jfr</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-launcher</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-reporting</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-runner</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-api</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-commons</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-suite-engine</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.platform</groupId>
        <artifactId>junit-platform-testkit</artifactId>
        <version>1.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>5.8.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk7</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-js</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-common</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-reflect</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-osgi-bundle</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-junit</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-junit5</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-testng</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-js</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-common</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test-annotations-common</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-main-kts</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-script-runtime</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-script-util</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-common</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-jvm</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-jvm-host</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-scripting-ide-services</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-compiler</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-compiler-embeddable</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-daemon-client</artifactId>
        <version>1.6.21</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-android</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core-jvm</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-core</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-debug</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-guava</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-javafx</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk8</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-jdk9</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-play-services</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactive</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-reactor</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-rx2</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-rx3</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-slf4j</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-swing</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-test</artifactId>
        <version>1.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-layout-template-json</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-1.2-api</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jcl</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-flume-ng</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-taglib</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jmx-gui</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j18-impl</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-to-slf4j</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-appserver</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-web</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-couchdb</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb4</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-mongodb3</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-cassandra</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpa</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-iostreams</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jul</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-jpl</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-liquibase</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-docker</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-kubernetes</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-boot</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-spring-cloud-config-client</artifactId>
        <version>2.17.2</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-core</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-jersey2</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-appoptics</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-atlas</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-azure-monitor</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-cloudwatch2</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-datadog</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-dynatrace</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-elastic</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-ganglia</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-graphite</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-health</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-humio</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-influx</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-jmx</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-kairos</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-new-relic</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-opentsdb</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-prometheus</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-signalfx</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-statsd</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-wavefront</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-test</artifactId>
        <version>1.8.13</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-buffer</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-dns</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-haproxy</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-http</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-http2</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-memcache</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-mqtt</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-redis</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-smtp</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-socks</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-stomp</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-codec-xml</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-common</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-dev-tools</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler-proxy</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-handler-ssl-ocsp</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-rxtx</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-sctp</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-udt</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-example</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-all</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-classes-macos</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-native-macos</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-native-macos</artifactId>
        <version>4.1.92.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-resolver-dns-native-macos</artifactId>
        <version>4.1.92.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.92.Final</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.92.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.92.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-unix-common</artifactId>
        <version>4.1.92.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-classes-epoll</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.92.Final</version>
        <classifier>linux-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-epoll</artifactId>
        <version>4.1.92.Final</version>
        <classifier>linux-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-classes-kqueue</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.92.Final</version>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.92.Final</version>
        <classifier>osx-x86_64</classifier>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-transport-native-kqueue</artifactId>
        <version>4.1.92.Final</version>
        <classifier>osx-aarch_64</classifier>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc11</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ucp</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ucp11</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>rsi</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.security</groupId>
        <artifactId>oraclepki</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.security</groupId>
        <artifactId>osdt_core</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.security</groupId>
        <artifactId>osdt_cert</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.ha</groupId>
        <artifactId>simplefan</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.ha</groupId>
        <artifactId>ons</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.nls</groupId>
        <artifactId>orai18n</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.xml</groupId>
        <artifactId>xdb</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.xml</groupId>
        <artifactId>xmlparserv2</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc11_g</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc8_g</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc8dms_g</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc11dms_g</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.observability</groupId>
        <artifactId>dms</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.observability</groupId>
        <artifactId>ojdbc11dms</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.observability</groupId>
        <artifactId>ojdbc8dms</artifactId>
        <version>21.3.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc11-production</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc8-production</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.observability</groupId>
        <artifactId>ojdbc8-observability</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.observability</groupId>
        <artifactId>ojdbc11-observability</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc8-debug</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc11-debug</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc8-observability-debug</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc.debug</groupId>
        <artifactId>ojdbc11-observability-debug</artifactId>
        <version>21.3.0.0</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_caffeine</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_common</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_dropwizard</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_graphite_bridge</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_guava</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_hibernate</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_hotspot</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_httpserver</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_jetty</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_jetty_jdk8</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_log4j</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_log4j2</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_logback</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_pushgateway</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_servlet</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_servlet_jakarta</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_spring_boot</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_spring_web</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_tracer_otel</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_tracer_otel_agent</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_vertx</artifactId>
        <version>0.12.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-core</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-codegen</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-codegen-utils</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-spatial</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-apt</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-collections</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-guava</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql-spatial</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql-codegen</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-sql-spring</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jpa</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jpa-codegen</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-jdo</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-kotlin-codegen</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-lucene3</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-lucene4</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-lucene5</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-hibernate-search</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-mongodb</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-scala</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-kotlin</artifactId>
        <version>5.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.google.cloud</groupId>
        <artifactId>cloud-spanner-r2dbc</artifactId>
        <version>1.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.r2dbc</groupId>
        <artifactId>oracle-r2dbc</artifactId>
        <version>0.1.0</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-h2</artifactId>
        <version>0.8.5.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb</groupId>
        <artifactId>r2dbc-mariadb</artifactId>
        <version>1.0.3</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-mssql</artifactId>
        <version>0.8.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>dev.miku</groupId>
        <artifactId>r2dbc-mysql</artifactId>
        <version>0.8.2.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-postgresql</artifactId>
        <version>0.8.12.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-pool</artifactId>
        <version>0.8.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-proxy</artifactId>
        <version>0.8.8.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-spi</artifactId>
        <version>0.8.6.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-core</artifactId>
        <version>3.4.29</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-test</artifactId>
        <version>3.4.29</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-tools</artifactId>
        <version>3.4.29</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-extra</artifactId>
        <version>3.4.10</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-adapter</artifactId>
        <version>3.4.10</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty</artifactId>
        <version>1.0.32</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-core</artifactId>
        <version>1.0.32</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-http</artifactId>
        <version>1.0.32</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-http-brave</artifactId>
        <version>1.0.32</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.addons</groupId>
        <artifactId>reactor-pool</artifactId>
        <version>0.2.11</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kafka</groupId>
        <artifactId>reactor-kafka</artifactId>
        <version>1.3.18</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.rabbitmq</groupId>
        <artifactId>reactor-rabbitmq</artifactId>
        <version>1.5.6</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.kotlin</groupId>
        <artifactId>reactor-kotlin-extensions</artifactId>
        <version>1.1.10</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-core</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-load-balancer</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-micrometer</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-test</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-transport-local</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-transport-netty</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-cassandra</artifactId>
        <version>3.3.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-commons</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-couchbase</artifactId>
        <version>4.3.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-elasticsearch</artifactId>
        <version>4.3.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-geode</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-jdbc</artifactId>
        <version>2.3.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-relational</artifactId>
        <version>2.3.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-jpa</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-mongodb</artifactId>
        <version>3.3.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-neo4j</artifactId>
        <version>6.2.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-r2dbc</artifactId>
        <version>1.4.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-redis</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-webmvc</artifactId>
        <version>3.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-core</artifactId>
        <version>3.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-rest-hal-explorer</artifactId>
        <version>3.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-keyvalue</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-envers</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-ldap</artifactId>
        <version>2.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aop</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-aspects</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-beans</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-indexer</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-context-support</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-expression</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-instrument</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jcl</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jdbc</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jms</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-messaging</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-orm</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-oxm</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-r2dbc</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-test</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-tx</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webflux</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-webmvc</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-websocket</artifactId>
        <version>5.3.27</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-amqp</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-event</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-feed</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-file</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ftp</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-gemfire</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-groovy</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ip</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jdbc</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jms</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jmx</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-jpa</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-kafka</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mail</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mongodb</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-mqtt</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-r2dbc</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-redis</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-rmi</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-rsocket</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-scripting</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-security</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-sftp</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-stomp</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-stream</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-syslog</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-test</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-test-support</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-webflux</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-websocket</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-ws</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-xml</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-xmpp</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-zeromq</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-zookeeper</artifactId>
        <version>5.5.18</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-acl</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-aspects</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-cas</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-config</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-core</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-crypto</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-data</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-ldap</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-messaging</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-client</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-core</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-jose</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-resource-server</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-openid</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-remoting</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-rsocket</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-saml2-service-provider</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-taglibs</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-test</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-web</artifactId>
        <version>5.6.10</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-jdbc</artifactId>
        <version>2.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-geode</artifactId>
        <version>2.6.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-hazelcast</artifactId>
        <version>2.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-mongodb</artifactId>
        <version>2.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-core</artifactId>
        <version>2.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-data-redis</artifactId>
        <version>2.6.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-rsa</artifactId>
        <version>1.0.11.RELEASE</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-crypto</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-commons</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-context</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-loadbalancer</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-test-support</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-eureka-client</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-server</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-eureka-server</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-client</artifactId>
        <version>1.10.17</version>
        <exclusions>
          <exclusion>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.github.vlsi.compactmap</groupId>
            <artifactId>compactmap</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-core</artifactId>
        <version>1.10.17</version>
        <exclusions>
          <exclusion>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>blitz4j</groupId>
            <artifactId>com.netflix.blitz4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.amazonaws</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream</artifactId>
        <version>3.2.6</version>
        <type>test-jar</type>
        <classifier>test-binder</classifier>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-test</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-test-support</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-kafka</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-kafka-streams</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-kafka-core</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-stream-kafka</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-rabbit</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-rabbit-core</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-stream-rabbit</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-stream-binder-rabbit-test-support</artifactId>
        <version>3.2.6</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-task</artifactId>
        <version>2.4.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-core</artifactId>
        <version>2.4.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-batch</artifactId>
        <version>2.4.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-task-stream</artifactId>
        <version>2.4.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-resilience4j</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-spring-retry</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-circuitbreaker-resilience4j</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-circuitbreaker-reactor-resilience4j</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-core</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-ratelimiter</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-cache</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-retry</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-circuitbreaker</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-bulkhead</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-all</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-circularbuffer</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-metrics</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-consumer</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-vertx</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-annotations</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-spring</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-spring-boot2</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-spring-cloud2</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-retrofit</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-feign</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-ratpack</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-prometheus</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-timelimiter</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-rxjava2</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-rxjava3</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-reactor</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-micrometer</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-kotlin</artifactId>
        <version>1.7.0</version>
        <scope>compile</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-config</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-client</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-server</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-config-monitor</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.vault</groupId>
        <artifactId>spring-vault-core</artifactId>
        <version>2.3.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.credhub</groupId>
        <artifactId>spring-credhub-core</artifactId>
        <version>2.1.1.RELEASE</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit</artifactId>
        <version>5.13.1.202206130422-r</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit.junit.http</artifactId>
        <version>5.13.1.202206130422-r</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit.http.apache</artifactId>
        <version>5.13.1.202206130422-r</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit.ssh.apache</artifactId>
        <version>5.13.1.202206130422-r</version>
      </dependency>
      <dependency>
        <groupId>org.tmatesoft.svnkit</groupId>
        <artifactId>svnkit</artifactId>
        <version>1.10.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-context</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-core</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-web</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-function-web</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-function-webflux</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-deployer</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-aws</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-azure</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-gcp</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-adapter-openwhisk</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-kotlin</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-rsocket</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-grpc</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-function-grpc-cloudevent-ext</artifactId>
        <version>3.2.8</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-webflux</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-mvc</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-gateway-server</artifactId>
        <version>3.1.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-core</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-binder</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-config</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-consul-discovery</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-bus</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-config</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-consul-all</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>com.ecwid.consul</groupId>
        <artifactId>consul-api</artifactId>
        <version>1.4.5</version>
        <exclusions>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-autoconfigure</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-api</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-instrumentation</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-brave</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-zipkin</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-sleuth</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-sleuth-tests-common</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing.brave</groupId>
        <artifactId>brave-opentracing</artifactId>
        <version>0.37.4</version>
        <exclusions>
          <exclusion>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.github.lognet</groupId>
        <artifactId>grpc-spring-boot-starter</artifactId>
        <version>4.2.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.zipkin2</groupId>
        <artifactId>zipkin</artifactId>
        <version>2.23.2</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-tests</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-jfr</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-log4j2</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-log4j12</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-slf4j</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-context-rxjava2</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-dubbo</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-dubbo-rpc</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-grpc</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-http-tests</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpasyncclient</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-httpclient</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jaxrs2</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jersey-server</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-jms</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-kafka-clients</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-kafka-streams</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-messaging</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mongodb</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql6</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-mysql8</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-netty-codec-http</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-okhttp3</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-p6spy</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-rpc</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-servlet</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-sparkjava</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-rabbit</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-web</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-spring-webmvc</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-instrumentation-vertx-web</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-spring-beans</artifactId>
        <version>5.13.9</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.proto3</groupId>
        <artifactId>zipkin-proto3</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-okhttp3</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-libthrift</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-urlconnection</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-kafka08</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-kafka</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-amqp-client</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-sender-activemq-client</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-spring-beans</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-brave</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>io.zipkin.reporter2</groupId>
        <artifactId>zipkin-reporter-metrics-micrometer</artifactId>
        <version>2.16.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-aws</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-consul</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-databases</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-vault-config-rabbitmq</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-vault-config</artifactId>
        <version>3.1.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-core</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-config</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-zookeeper-discovery</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper-all</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper-config</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-zookeeper-discovery</artifactId>
        <version>3.1.3</version>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-framework</artifactId>
        <version>5.1.0</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-recipes</artifactId>
        <version>5.1.0</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-x-discovery</artifactId>
        <version>5.1.0</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.curator</groupId>
        <artifactId>curator-test</artifactId>
        <version>5.1.0</version>
        <scope>test</scope>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>io.netty</groupId>
            <artifactId>netty</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-discovery</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-commons</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-cloudfoundry-web</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-cloudfoundry</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-amqp</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-kafka</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-stream</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-bus</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-bus-rsocket</artifactId>
        <version>3.1.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-wiremock</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec-java</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec-groovy</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-spec-kotlin</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-verifier</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-converters</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-pact</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-stub-runner</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-stub-runner-boot</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-shade</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-contract-verifier</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-contract-stub-runner</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-contract-stub-runner-jetty</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-maven-plugin</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-contract-gradle-plugin</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>com.github.tomakehurst</groupId>
        <artifactId>wiremock-jre8-standalone</artifactId>
        <version>2.33.0</version>
      </dependency>
      <dependency>
        <groupId>com.toomuchcoding.jsonassert</groupId>
        <artifactId>jsonassert</artifactId>
        <version>0.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.toomuchcoding.jsonassert</groupId>
        <artifactId>jsonassert-shade</artifactId>
        <version>0.6.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security.oauth.boot</groupId>
        <artifactId>spring-security-oauth2-autoconfigure</artifactId>
        <version>2.5.2</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-openfeign-core</artifactId>
        <version>3.1.5</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign.form</groupId>
        <artifactId>feign-form-spring</artifactId>
        <version>3.8.0</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-core</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-gson</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxrs</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-httpclient</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxrs2</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-hc5</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-hystrix</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson-jaxb</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jackson-jr</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-jaxb</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-mock</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-json</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-okhttp</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-googlehttpclient</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-ribbon</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-sax</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-slf4j</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-spring4</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-soap</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-reactive-wrappers</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-micrometer</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-dropwizard-metrics4</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-dropwizard-metrics5</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>io.github.openfeign</groupId>
        <artifactId>feign-annotation-error-decoder</artifactId>
        <version>11.10</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-collections4</artifactId>
        <version>4.4</version>
      </dependency>
      <dependency>
        <groupId>io.kubernetes</groupId>
        <artifactId>client-java</artifactId>
        <version>13.0.2</version>
      </dependency>
      <dependency>
        <groupId>io.kubernetes</groupId>
        <artifactId>client-java-extended</artifactId>
        <version>13.0.2</version>
      </dependency>
      <dependency>
        <groupId>io.kubernetes</groupId>
        <artifactId>client-java-spring-integration</artifactId>
        <version>13.0.2</version>
      </dependency>
      <dependency>
        <groupId>me.snowdrop</groupId>
        <artifactId>istio-client</artifactId>
        <version>1.7.7.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-fabric8-autoconfig</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-fabric8-config</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-client-config</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-client-discovery</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-fabric8-discovery</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-fabric8-istio</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-fabric8-leader</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-commons</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-client-autoconfig</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-fabric8-loadbalancer</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-client-loadbalancer</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-discovery</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-fabric8</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-client</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-fabric8-config</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-client-config</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-fabric8-loadbalancer</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-client-loadbalancer</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-fabric8-all</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-client-all</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-kubernetes-discoveryclient</artifactId>
        <version>2.1.5</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.arquillian.junit</groupId>
        <artifactId>arquillian-junit-standalone</artifactId>
        <version>1.6.0.Final</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.arquillian.cube</groupId>
        <artifactId>arquillian-cube-requirement</artifactId>
        <version>1.15.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.arquillian.cube</groupId>
        <artifactId>arquillian-cube-kubernetes</artifactId>
        <version>1.15.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.arquillian.cube</groupId>
        <artifactId>arquillian-cube-openshift</artifactId>
        <version>1.15.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.specto</groupId>
        <artifactId>hoverfly-java-junit5</artifactId>
        <version>0.13.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.specto</groupId>
        <artifactId>hoverfly-java</artifactId>
        <version>0.13.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.github.tomakehurst</groupId>
        <artifactId>wiremock-jre8</artifactId>
        <version>2.26.3</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-kubernetes-test-support</artifactId>
        <version>2.1.5</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-common</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>model-annotator</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-core</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-rbac</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-admissionregistration</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-apps</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-autoscaling</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-apiextensions</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-batch</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-certificates</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-coordination</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-discovery</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-events</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-extensions</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-networking</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-metrics</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-policy</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-scheduling</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-storageclass</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-jsonschema2pojo</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-flowcontrol</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-model-node</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-clusterautoscaling</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-operator</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-operatorhub</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-machine</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-monitoring</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-console</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-machineconfig</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-tuned</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-whereabouts</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-storageversionmigrator</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-model-miscellaneous</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-model</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>knative-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-model-v1alpha1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-model-v1beta1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-model-triggers</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>tekton-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-model</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>service-catalog-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>servicecatalog-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>volumesnapshot-model</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>volumesnapshot-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>volumesnapshot-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>volumesnapshot-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>volumesnapshot-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>chaosmesh-model</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>chaosmesh-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>chaosmesh-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>chaosmesh-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>chaosmesh-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>camel-k-model-v1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>camel-k-model-v1alpha1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>camel-k-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>camel-k-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>camel-k-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-model-v1alpha2</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-model-v1alpha3</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-model-v1beta1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-model-v1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>certmanager-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>verticalpodautoscaler-model-v1</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>verticalpodautoscaler-client</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>verticalpodautoscaler-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>verticalpodautoscaler-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>verticalpodautoscaler-tests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>openshift-server-mock</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-examples</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8.kubernetes</groupId>
        <artifactId>kubernetes-karaf</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8.kubernetes</groupId>
        <artifactId>kubernetes-karaf-itests</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>crd-generator-api</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>crd-generator-apt</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-test</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-openshift-uberjar</artifactId>
        <version>5.10.2</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.nacos</groupId>
        <artifactId>nacos-client</artifactId>
        <version>2.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-core</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-parameter-flow-control</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-extension</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-apollo</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-zookeeper</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-nacos</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-redis</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-datasource-consul</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-web-servlet</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-spring-cloud-gateway-adapter</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-transport-simple-http</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-annotation-aspectj</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-reactor-adapter</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-cluster-server-default</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-cluster-client-default</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-spring-webflux-adapter</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-api-gateway-adapter-common</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.csp</groupId>
        <artifactId>sentinel-spring-webmvc-adapter</artifactId>
        <version>1.8.6</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-sentinel-datasource</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-circuitbreaker-sentinel</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-stream-rocketmq</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-bus-rocketmq</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-starter-alibaba-sidecar</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-commons</artifactId>
        <version>2021.0.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba.spring</groupId>
        <artifactId>spring-context-support</artifactId>
        <version>1.0.11</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>com.tcm</groupId>
      <artifactId>tcm-common-core</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.tcm</groupId>
      <artifactId>tcm-common-security</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.tcm</groupId>
      <artifactId>tcm-common-redis</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
      <version>3.5.4</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <version>2.6.15</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-logging</artifactId>
      <version>2.6.15</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <version>2.6.15</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-websocket</artifactId>
      <version>2.6.15</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.28</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.mysql</groupId>
      <artifactId>mysql-connector-j</artifactId>
      <version>8.2.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>druid-spring-boot-starter</artifactId>
      <version>1.2.20</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
      <version>2021.0.5.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
      <version>2021.0.5.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
      <version>2021.0.5.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.alibaba.csp</groupId>
      <artifactId>sentinel-datasource-nacos</artifactId>
      <version>1.8.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-openfeign</artifactId>
      <version>3.1.5</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-loadbalancer</artifactId>
      <version>3.1.5</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-logging</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-to-slf4j</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.apache.logging.log4j</groupId>
          <artifactId>log4j-slf4j-impl</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>javax.validation</groupId>
      <artifactId>validation-api</artifactId>
      <version>2.0.1.Final</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>6.2.5.Final</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <repositories>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>ustc</id>
      <name>USTC Maven Mirror</name>
      <url>https://mirrors.ustc.edu.cn/maven/maven2/</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>tsinghua</id>
      <name>Tsinghua Maven Mirror</name>
      <url>https://mirrors.tuna.tsinghua.edu.cn/maven/</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>huaweicloud</id>
      <name>huawei maven</name>
      <url>https://repo.huaweicloud.com/repository/maven/</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>alimaven</id>
      <name>aliyun maven</name>
      <url>https://maven.aliyun.com/repository/public/</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <id>nexus</id>
      <url>http://nexus.hepengju.cn:8081/nexus/content/groups/public/</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-public</id>
      <name>Aliyun Public Repository</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-central</id>
      <name>Aliyun Central Repository</name>
      <url>https://maven.aliyun.com/repository/central</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-spring</id>
      <name>Aliyun Spring Repository</name>
      <url>https://maven.aliyun.com/repository/spring</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-google</id>
      <name>Aliyun Google Repository</name>
      <url>https://maven.aliyun.com/repository/google</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-gradle-plugin</id>
      <name>Aliyun Gradle Repository</name>
      <url>https://maven.aliyun.com/repository/gradle-plugin</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Maven Central</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </repository>
    <repository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>spring-milestone</id>
      <name>Spring Milestone</name>
      <url>https://repo.maven.apache.org/maven2/org/springframework/</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <id>ustc</id>
      <name>USTC Maven Mirror</name>
      <url>https://mirrors.ustc.edu.cn/maven/maven2/</url>
    </pluginRepository>
    <pluginRepository>
      <id>tsinghua</id>
      <name>Tsinghua Maven Mirror</name>
      <url>https://mirrors.tuna.tsinghua.edu.cn/maven/</url>
    </pluginRepository>
    <pluginRepository>
      <id>huaweicloud</id>
      <name>huawei maven</name>
      <url>https://repo.huaweicloud.com/repository/maven/</url>
    </pluginRepository>
    <pluginRepository>
      <id>alimaven</id>
      <name>aliyun maven</name>
      <url>https://maven.aliyun.com/repository/public/</url>
    </pluginRepository>
    <pluginRepository>
      <id>public</id>
      <name>Public Repositories</name>
      <url>http://nexus.hepengju.cn:8081/nexus/content/groups/public/</url>
    </pluginRepository>
    <pluginRepository>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>aliyun-plugin</id>
      <name>Aliyun Plugin Repository</name>
      <url>https://maven.aliyun.com/repository/public</url>
    </pluginRepository>
    <pluginRepository>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>central</id>
      <name>Central Repository</name>
      <url>https://repo.maven.apache.org/maven2</url>
    </pluginRepository>
  </pluginRepositories>
  <build>
    <sourceDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\src\main\java</sourceDirectory>
    <scriptSourceDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\src\main\scripts</scriptSourceDirectory>
    <testSourceDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\src\test\java</testSourceDirectory>
    <outputDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target\classes</outputDirectory>
    <testOutputDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target\test-classes</testOutputDirectory>
    <resources>
      <resource>
        <directory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\src\main\resources</directory>
      </resource>
    </resources>
    <testResources>
      <testResource>
        <directory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\src\test\resources</directory>
      </testResource>
    </testResources>
    <directory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target</directory>
    <finalName>tcm-remote-consultation-1.0.0</finalName>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>3.1.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>3.7.1</version>
        </plugin>
        <plugin>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.7.0</version>
        </plugin>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <version>3.0.1</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <executions>
          <execution>
            <id>default-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <source>17</source>
              <target>17</target>
              <encoding>UTF-8</encoding>
            </configuration>
          </execution>
          <execution>
            <id>default-testCompile</id>
            <phase>test-compile</phase>
            <goals>
              <goal>testCompile</goal>
            </goals>
            <configuration>
              <source>17</source>
              <target>17</target>
              <encoding>UTF-8</encoding>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <source>17</source>
          <target>17</target>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.6.15</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-clean-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>default-clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
        <executions>
          <execution>
            <id>default-testResources</id>
            <phase>process-test-resources</phase>
            <goals>
              <goal>testResources</goal>
            </goals>
          </execution>
          <execution>
            <id>default-resources</id>
            <phase>process-resources</phase>
            <goals>
              <goal>resources</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <id>default-jar</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.2.5</version>
        <executions>
          <execution>
            <id>default-test</id>
            <phase>test</phase>
            <goals>
              <goal>test</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-install-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>default-install</id>
            <phase>install</phase>
            <goals>
              <goal>install</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-deploy-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>default-deploy</id>
            <phase>deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.12.1</version>
        <executions>
          <execution>
            <id>default-site</id>
            <phase>site</phase>
            <goals>
              <goal>site</goal>
            </goals>
            <configuration>
              <outputDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target\site</outputDirectory>
              <reportPlugins>
                <reportPlugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                </reportPlugin>
              </reportPlugins>
            </configuration>
          </execution>
          <execution>
            <id>default-deploy</id>
            <phase>site-deploy</phase>
            <goals>
              <goal>deploy</goal>
            </goals>
            <configuration>
              <outputDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target\site</outputDirectory>
              <reportPlugins>
                <reportPlugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-project-info-reports-plugin</artifactId>
                </reportPlugin>
              </reportPlugins>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <outputDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target\site</outputDirectory>
          <reportPlugins>
            <reportPlugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-project-info-reports-plugin</artifactId>
            </reportPlugin>
          </reportPlugins>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <reporting>
    <outputDirectory>d:\IdeaProjects\tcm-system\tcm-modules\tcm-remote-consultation\target\site</outputDirectory>
  </reporting>
</project>
