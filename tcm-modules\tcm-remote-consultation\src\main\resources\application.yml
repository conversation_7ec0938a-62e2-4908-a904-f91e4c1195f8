server:
  port: ${tcm.service.remote-consultation.port:${TCM_REMOTE_CONSULTATION_PORT:9305}}
  servlet:
    context-path: /consultation

spring:
  application:
    name: tcm-remote-consultation
  # 临时禁用Sentinel以解决启动问题
  cloud:
    sentinel:
      enabled: false
      eager: false
      transport:
        enabled: false

# WebSocket配置
websocket:
  prefix: ${tcm.websocket.prefix:/ws}
  endpoint: ${tcm.websocket.endpoint:/consultation}
  allow-origins: ${tcm.websocket.allow-origins:*}