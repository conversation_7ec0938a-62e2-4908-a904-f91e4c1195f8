package com.tcm.appointment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生排班信息视图对象
 */
@Data
public class DoctorScheduleVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 排班ID
     */
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 科室ID
     */
    private Long departmentId;
    
    /**
     * 科室名称
     */
    private String departmentName;
    
    /**
     * 排班日期
     */
    private Date scheduleDate;
    
    /**
     * 时间段ID
     */
    private Long timeSlotId;
    
    /**
     * 时间段值
     */
    private String timeSlotValue;
    
    /**
     * 时间段标签
     */
    private String timeSlotLabel;
    
    /**
     * 最大预约人数
     */
    private Integer maxAppointments;
    
    /**
     * 剩余可预约数
     */
    private Integer appointmentLimit;
    
    /**
     * 状态（0-停诊 1-正常 2-满号）
     */
    private Integer status;
    
    /**
     * 状态描述
     */
    private String statusDesc;
} 