package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.diagnosis.domain.DiagnosisRecord;
import com.tcm.diagnosis.dto.DiagnosisQuery;
import com.tcm.diagnosis.vo.DiagnosisRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 诊断记录Mapper接口
 */
@Mapper
public interface DiagnosisRecordMapper extends BaseMapper<DiagnosisRecord> {
    
    /**
     * 查询诊断记录列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 诊断记录列表
     */
    IPage<DiagnosisRecordVO> selectDiagnosisRecordList(Page<DiagnosisRecord> page, @Param("query") DiagnosisQuery query);
    
    /**
     * 根据ID查询诊断记录详情
     *
     * @param diagnosisId 诊断记录ID
     * @return 诊断记录详情
     */
    DiagnosisRecordVO selectDiagnosisRecordById(@Param("diagnosisId") Long diagnosisId);
    
    /**
     * 根据患者ID查询诊断记录列表
     *
     * @param patientId 患者ID
     * @return 诊断记录列表
     */
    List<DiagnosisRecordVO> selectDiagnosisRecordByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 根据医生ID查询诊断记录列表
     *
     * @param doctorId 医生ID
     * @return 诊断记录列表
     */
    List<DiagnosisRecordVO> selectDiagnosisRecordByDoctorId(@Param("doctorId") Long doctorId);
} 