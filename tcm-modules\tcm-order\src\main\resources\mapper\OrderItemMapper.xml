<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.order.mapper.OrderItemMapper">

    <!-- 基础列定义 -->
    <sql id="Base_Column_List">
        item_id, order_id, product_id, product_type, product_name, product_spec, product_unit,
        quantity, unit_price, total_price, usage, usage_order, remark, del_flag, create_by,
        create_time, update_by, update_time
    </sql>

    <!-- 批量插入订单明细 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO tcm_order_item (
            order_id, product_id, product_type, product_name, product_spec, product_unit,
            quantity, unit_price, total_price, usage, usage_order, remark, create_by, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.orderId}, #{item.productId}, #{item.productType}, #{item.productName},
            #{item.productSpec}, #{item.productUnit}, #{item.quantity}, #{item.unitPrice},
            #{item.totalPrice}, #{item.usage}, #{item.usageOrder}, #{item.remark},
            #{item.createBy}, NOW()
            )
        </foreach>
    </insert>
    
    <!-- 根据订单ID查询订单明细 -->
    <select id="selectByOrderId" resultType="com.tcm.order.entity.OrderItem">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tcm_order_item
        WHERE del_flag = 0
        AND order_id = #{orderId}
        ORDER BY item_id
    </select>
    
    <!-- 根据订单ID删除订单明细（逻辑删除） -->
    <update id="deleteByOrderId">
        UPDATE tcm_order_item
        SET del_flag = 1,
            update_time = NOW()
        WHERE order_id = #{orderId}
        AND del_flag = 0
    </update>
</mapper>
