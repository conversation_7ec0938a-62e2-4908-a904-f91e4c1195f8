package com.tcm.assistant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 诊断建议VO
 */
@Data
@Schema(description = "诊断建议结果")
public class DiagnosisSuggestionVO {

    /**
     * 建议内容
     */
    @Schema(description = "诊断建议内容")
    private String suggestion;

    /**
     * 相似病例列表
     */
    @Schema(description = "相似病例列表")
    private List<Object> similarCases;

    /**
     * 相关知识
     */
    @Schema(description = "相关知识列表")
    private List<Object> relatedKnowledge;

    /**
     * 治疗方案
     */
    @Schema(description = "推荐治疗方案")
    private String treatmentPlan;

    /**
     * 置信度
     */
    @Schema(description = "建议置信度")
    private Double confidence;
}