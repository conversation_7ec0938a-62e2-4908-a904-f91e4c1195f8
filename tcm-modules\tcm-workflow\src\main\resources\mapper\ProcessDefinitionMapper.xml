<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.workflow.mapper.ProcessDefinitionMapper">
    
    <resultMap type="com.tcm.workflow.entity.ProcessDefinition" id="ProcessDefinitionResult">
        <id property="processDefinitionId" column="process_definition_id"/>
        <result property="processDefinitionKey" column="process_definition_key"/>
        <result property="processDefinitionName" column="process_definition_name"/>
        <result property="category" column="category"/>
        <result property="version" column="version"/>
        <result property="deploymentId" column="deployment_id"/>
        <result property="resourceName" column="resource_name"/>
        <result property="status" column="status"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="description" column="description"/>
        <result property="mainProcess" column="main_process"/>
        <result property="processXml" column="process_xml"/>
        <result property="deployTime" column="deploy_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
</mapper> 