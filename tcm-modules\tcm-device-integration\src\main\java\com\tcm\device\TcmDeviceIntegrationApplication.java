package com.tcm.device;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import com.tcm.common.core.constant.ServiceConstants;

/**
 * 医疗设备集成服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
public class TcmDeviceIntegrationApplication {
    


    public static void main(String[] args) {
        try {
            // 解决日志冲突问题
            System.setProperty("log4j2.disableJmx", "true");
            System.setProperty("org.springframework.boot.logging.LoggingSystem", "org.springframework.boot.logging.logback.LogbackLoggingSystem");
            
            // 解决Java反射和安全限制问题
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.tryReflectionSetAccessible", "false");
            System.setProperty("illegal-access", "permit");
            System.setProperty("jdk.internal.reflect.permitAll", "true");
            System.setProperty("jdk.attach.allowAttachSelf", "true");
            
            // 禁用Nacos中的netty反射访问
            System.setProperty("com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.transport.noUnsafe", "true");
            System.setProperty("com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.channel.noUnsafe", "true");
            System.setProperty("nacos.core.auth.enabled", "false");
            System.setProperty("com.alibaba.nacos.common.remote.client.grpc.secure", "false");
            System.setProperty("com.alibaba.nacos.client.remote.rpc.secure", "false");
            
            // 增强处理Nacos gRPC客户端相关类缺失问题
            System.setProperty("grpc.census.enabled", "false");
            System.setProperty("io.grpc.census.enabled", "false");
            System.setProperty("io.grpc.internal.DnsNameResolverProvider.enable", "false");
            
            // 按照统一配置原则设置关键系统属性
            // 这些设置只包含启动前必需的最小化配置
            // 主要用于解决早期配置需求，占位符无法解析等问题
            
            // 1. 禁用不需要的自动配置
            System.setProperty("spring.cloud.bootstrap.enabled", "false"); // 避免配置冲突
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            
            // 2. 启用Nacos认证并设置认证信息
            // 使用ServiceConstants类中的常量，符合统一配置原则
            System.setProperty("nacos.core.auth.enabled", "true");
            
            // 使用统一配置中的常量
            System.setProperty("spring.cloud.nacos.discovery.server-addr", ServiceConstants.Nacos.SERVER_ADDR);
            System.setProperty("spring.cloud.nacos.config.server-addr", ServiceConstants.Nacos.SERVER_ADDR);
            System.setProperty("spring.cloud.nacos.discovery.username", ServiceConstants.Nacos.USERNAME);
            System.setProperty("spring.cloud.nacos.discovery.password", ServiceConstants.Nacos.PASSWORD);
            System.setProperty("spring.cloud.nacos.config.username", ServiceConstants.Nacos.USERNAME);
            System.setProperty("spring.cloud.nacos.config.password", ServiceConstants.Nacos.PASSWORD);
            
            // 配置网络参数优化Nacos连接
            System.setProperty("nacos.client.connectionTimeout", "3000");
            System.setProperty("nacos.client.naming.pushEmptyProtection", "true");
            
            // 3. 解决RocketMQ配置错误
            // 使用ServiceConstants类中的常量，符合统一配置原则
            System.setProperty("rocketmq.name-server", ServiceConstants.RocketMq.NAME_SERVER_ADDR);
            System.setProperty("tcm.service.rocketMq.nameServerAddress", ServiceConstants.RocketMq.NAME_SERVER_ADDR);
            
            // 4. 配置Redis连接信息
            // 使用ServiceConstants类中的常量，符合统一配置原则
            System.setProperty("spring.redis.host", ServiceConstants.Redis.HOST);
            System.setProperty("spring.redis.port", String.valueOf(ServiceConstants.Redis.PORT));
            System.setProperty("spring.redis.password", ServiceConstants.Redis.PASSWORD);
            
            // 5. 其他配置均从配置文件中读取，不在代码中硬编码
            
            SpringApplication.run(TcmDeviceIntegrationApplication.class, args);
            System.out.println("(♥◠‿◠)ﾉﾞ  医疗设备集成服务启动成功   ლ(´ڡ`ლ)ﾞ");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}