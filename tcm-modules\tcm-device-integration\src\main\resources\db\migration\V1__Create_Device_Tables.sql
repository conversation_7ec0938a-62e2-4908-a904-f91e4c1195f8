-- ----------------------------
-- 1. 设备表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `device_name` varchar(100) NOT NULL COMMENT '设备名称',
  `device_code` varchar(50) NOT NULL COMMENT '设备编码',
  `device_type` varchar(50) NOT NULL COMMENT '设备类型',
  `model` varchar(100) COMMENT '设备型号',
  `manufacturer` varchar(100) COMMENT '厂商',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0离线 1在线 2维护中）',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `port` int(11) COMMENT '端口号',
  `protocol` varchar(50) COMMENT '通信协议',
  `location` varchar(100) COMMENT '设备位置',
  `description` varchar(500) COMMENT '设备描述',
  `register_time` datetime COMMENT '注册时间',
  `last_online_time` datetime COMMENT '最后在线时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `device_code_unique` (`device_code`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='设备表';

-- ----------------------------
-- 2. 设备数据表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `device_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据ID',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID',
  `data_type` varchar(50) NOT NULL COMMENT '数据类型',
  `data_value` varchar(1000) NOT NULL COMMENT '数据值',
  `unit` varchar(20) COMMENT '单位',
  `collect_time` datetime NOT NULL COMMENT '采集时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态（0正常 1异常）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_collect_time` (`collect_time`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='设备数据表';

-- ----------------------------
-- 3. 设备维护记录表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `device_maintenance` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '维护ID',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID',
  `maintenance_type` varchar(50) NOT NULL COMMENT '维护类型',
  `maintenance_desc` varchar(500) NOT NULL COMMENT '维护描述',
  `maintenance_result` varchar(500) COMMENT '维护结果',
  `maintenance_user` varchar(100) NOT NULL COMMENT '维护人员',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态（0未完成 1已完成）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='设备维护记录表';

-- ----------------------------
-- 4. 设备告警表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `device_alarm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '告警ID',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID',
  `alarm_type` varchar(50) NOT NULL COMMENT '告警类型',
  `alarm_level` tinyint(1) NOT NULL COMMENT '告警级别（1低 2中 3高）',
  `alarm_content` varchar(500) NOT NULL COMMENT '告警内容',
  `alarm_time` datetime NOT NULL COMMENT '告警时间',
  `handle_status` tinyint(1) DEFAULT 0 COMMENT '处理状态（0未处理 1已处理）',
  `handle_user` varchar(100) COMMENT '处理人员',
  `handle_time` datetime COMMENT '处理时间',
  `handle_result` varchar(500) COMMENT '处理结果',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) DEFAULT 0 COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_alarm_time` (`alarm_time`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='设备告警表';

-- ----------------------------
-- 5. 设备连接记录表
-- ----------------------------
CREATE TABLE IF NOT EXISTS `device_connection_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID',
  `connection_status` tinyint(1) NOT NULL COMMENT '连接状态（0断开 1连接）',
  `connection_time` datetime NOT NULL COMMENT '连接/断开时间',
  `connection_ip` varchar(50) COMMENT '连接IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_connection_time` (`connection_time`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COMMENT='设备连接记录表'; 