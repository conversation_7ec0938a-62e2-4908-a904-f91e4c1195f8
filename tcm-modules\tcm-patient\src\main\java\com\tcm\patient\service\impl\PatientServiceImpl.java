package com.tcm.patient.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.patient.domain.Patient;
import com.tcm.patient.dto.PatientDTO;
import com.tcm.patient.dto.PatientQuery;
import com.tcm.patient.mapper.PatientMapper;
import com.tcm.patient.service.IPatientService;
import com.tcm.patient.vo.PatientVO;

/**
 * 患者服务实现类
 */
@Service
public class PatientServiceImpl extends ServiceImpl<PatientMapper, Patient> implements IPatientService {

    @Override
    public IPage<PatientVO> listPatients(PatientQuery query) {
        Page<Patient> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectPatientList(page, query);
    }

    @Override
    public PatientVO getPatientById(Long patientId) {
        return baseMapper.selectPatientById(patientId);
    }

    @Override
    public PatientVO getPatientByUserId(Long userId) {
        return baseMapper.selectPatientByUserId(userId);
    }

    @Override
    public PatientVO getPatientByIdCard(String idCard) {
        return baseMapper.selectPatientByIdCard(idCard);
    }

    @Override
    public boolean addPatient(PatientDTO patientDTO) {
        Patient patient = new Patient();
        BeanUtils.copyProperties(patientDTO, patient);

        // 生成患者编号
        if (StringUtils.isEmpty(patient.getPatientCode())) {
            patient.setPatientCode(generatePatientCode());
        }

        // 根据出生日期计算年龄
        if (patient.getBirthday() != null && patient.getAge() == null) {
            patient.setAge(calculateAge(patient.getBirthday()));
        }

        return save(patient);
    }

    @Override
    public boolean updatePatient(PatientDTO patientDTO) {
        Patient patient = getById(patientDTO.getPatientId());
        if (patient == null) {
            return false;
        }

        BeanUtils.copyProperties(patientDTO, patient);

        // 根据出生日期计算年龄
        if (patient.getBirthday() != null) {
            patient.setAge(calculateAge(patient.getBirthday()));
        }

        return updateById(patient);
    }

    @Override
    public boolean deletePatient(Long patientId) {
        return removeById(patientId);
    }

    @Override
    public boolean deletePatients(Long[] patientIds) {
        return removeByIds(Arrays.asList(patientIds));
    }

    @Override
    public boolean updatePatientStatus(Long patientId, Integer status) {
        LambdaUpdateWrapper<Patient> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Patient::getPatientId, patientId).set(Patient::getStatus, status);
        return update(wrapper);
    }

    /**
     * 生成患者编号 规则：PT + 年月日 + 4位随机数
     */
    private String generatePatientCode() {
        String prefix = "PT";
        // 使用 Java 8 的日期时间 API
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String date = LocalDate.now().format(formatter);
        // 使用 Java 自带的随机数生成
        Random random = new Random();
        String randomStr = String.format("%04d", random.nextInt(10000));
        return prefix + date + randomStr;
    }

    /**
     * 根据出生日期计算年龄
     *
     * @param birthday 出生日期
     * @return 年龄
     */
    private Integer calculateAge(Date birthday) {
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthday)) {
            return 0;
        }

        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birthday);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        int age = yearNow - yearBirth;
        if (monthNow < monthBirth || (monthNow == monthBirth && dayOfMonthNow < dayOfMonthBirth)) {
            age--;
        }

        return age;
    }
}