package com.tcm.log.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.log.domain.OperationLog;
import com.tcm.log.service.IOperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作日志控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log/operation")
public class OperationLogController {
    
    @Autowired
    private IOperationLogService operationLogService;
    
    /**
     * 获取操作日志列表
     */
    @GetMapping("/list")
    public Map<String, Object> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "businessType", required = false) String businessType,
            @RequestParam(value = "operationType", required = false) Integer operationType,
            @RequestParam(value = "operatorName", required = false) String operatorName,
            @RequestParam(value = "module", required = false) String module,
            @RequestParam(value = "status", required = false) Integer status) {
        
        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        if (businessType != null && !businessType.isEmpty()) {
            wrapper.eq(OperationLog::getBusinessType, businessType);
        }
        if (operationType != null) {
            wrapper.eq(OperationLog::getOperationType, operationType);
        }
        if (operatorName != null && !operatorName.isEmpty()) {
            wrapper.like(OperationLog::getOperatorName, operatorName);
        }
        if (module != null && !module.isEmpty()) {
            wrapper.like(OperationLog::getModule, module);
        }
        if (status != null) {
            wrapper.eq(OperationLog::getStatus, status);
        }
        wrapper.orderByDesc(OperationLog::getOperateTime);
        
        Page<OperationLog> page = new Page<>(pageNum, pageSize);
        Page<OperationLog> result = operationLogService.page(page, wrapper);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "查询成功");
        response.put("data", result.getRecords());
        response.put("total", result.getTotal());
        response.put("pageNum", result.getCurrent());
        response.put("pageSize", result.getSize());
        
        return response;
    }
    
    /**
     * 获取操作日志详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getInfo(@PathVariable Long id) {
        OperationLog operationLog = operationLogService.getById(id);
        Map<String, Object> response = new HashMap<>();
        
        if (operationLog != null) {
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", operationLog);
        } else {
            response.put("code", 404);
            response.put("msg", "日志不存在");
        }
        
        return response;
    }
    
    /**
     * 获取业务操作日志
     */
    @GetMapping("/business/{businessType}/{businessId}")
    public Map<String, Object> getBusinessLog(
            @PathVariable String businessType,
            @PathVariable String businessId) {
        
        OperationLog operationLog = operationLogService.getByBusinessTypeAndId(businessType, businessId);
        Map<String, Object> response = new HashMap<>();
        
        if (operationLog != null) {
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", operationLog);
        } else {
            response.put("code", 404);
            response.put("msg", "日志不存在");
        }
        
        return response;
    }
    
    /**
     * 新增操作日志
     */
    @PostMapping
    public Map<String, Object> add(@RequestBody OperationLog operationLog) {
        boolean result = operationLogService.saveLog(operationLog);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "保存成功");
        } else {
            response.put("code", 500);
            response.put("msg", "保存失败");
        }
        
        return response;
    }
    
    /**
     * 删除操作日志
     */
    @DeleteMapping("/{ids}")
    public Map<String, Object> remove(@PathVariable Long[] ids) {
        boolean result = operationLogService.deleteLogs(ids);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "删除成功");
        } else {
            response.put("code", 500);
            response.put("msg", "删除失败");
        }
        
        return response;
    }
    
    /**
     * 清空操作日志
     */
    @DeleteMapping("/clear")
    public Map<String, Object> clear() {
        boolean result = operationLogService.clearLogs();
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "清空成功");
        } else {
            response.put("code", 500);
            response.put("msg", "清空失败");
        }
        
        return response;
    }
} 