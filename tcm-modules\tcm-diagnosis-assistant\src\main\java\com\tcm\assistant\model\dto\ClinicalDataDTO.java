package com.tcm.assistant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 临床数据DTO
 */
@Data
@Schema(description = "临床数据请求")
public class ClinicalDataDTO {

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "医生ID")
    private Long doctorId;

    @Schema(description = "诊断会话ID")
    private Long diagnosisSessionId;

    @Schema(description = "主诉")
    private String chiefComplaint;

    @Schema(description = "当前症状列表")
    private List<String> symptoms;

    @Schema(description = "舌象描述")
    private String tongueDescription;

    @Schema(description = "脉象描述")
    private String pulseDescription;

    @Schema(description = "问诊记录")
    private String inquiryRecord;

    @Schema(description = "病史")
    private String medicalHistory;

    @Schema(description = "图像分析结果")
    private Map<String, Object> imageAnalysisResults;

    @Schema(description = "检查结果")
    private List<Map<String, Object>> examinations;
}