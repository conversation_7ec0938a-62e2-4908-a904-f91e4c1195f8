package com.tcm.diagnosis.controller;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.log.annotation.Log;
import com.tcm.common.log.enums.BusinessType;
import com.tcm.common.security.annotation.RequiresPermissions;
import com.tcm.diagnosis.dto.DiagnosisDataDTO;
import com.tcm.diagnosis.service.IDiagnosisSuggestionService;
import com.tcm.diagnosis.vo.DiagnosisSuggestionVO;
import com.tcm.diagnosis.vo.KnowledgeItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 诊断建议控制器
 */
@RestController
@RequestMapping("/diagnosis/suggestion")
public class DiagnosisSuggestionController extends BaseController {

    @Autowired
    private IDiagnosisSuggestionService diagnosisSuggestionService;

    /**
     * 生成诊断建议
     */
    @PostMapping("/generate")
    @RequiresPermissions("diagnosis:suggestion:generate")
    @Log(title = "诊断建议", businessType = BusinessType.INSERT)
    public R<List<Long>> generateSuggestions(@Validated @RequestBody DiagnosisDataDTO diagnosisDataDTO) {
        return R.ok(diagnosisSuggestionService.generateSuggestions(diagnosisDataDTO));
    }

    /**
     * 获取诊断建议
     */
    @GetMapping("/{suggestionId}")
    @RequiresPermissions("diagnosis:suggestion:query")
    public R<DiagnosisSuggestionVO> getSuggestion(@PathVariable Long suggestionId) {
        return R.ok(diagnosisSuggestionService.getSuggestion(suggestionId));
    }

    /**
     * 获取诊断记录的建议列表
     */
    @GetMapping("/list/{diagnosisId}")
    @RequiresPermissions("diagnosis:suggestion:list")
    public R<List<DiagnosisSuggestionVO>> getSuggestionsByDiagnosisId(@PathVariable Long diagnosisId) {
        return R.ok(diagnosisSuggestionService.getSuggestionsByDiagnosisId(diagnosisId));
    }

    /**
     * 获取诊断记录的特定类型建议
     */
    @GetMapping("/list/{diagnosisId}/{suggestionType}")
    @RequiresPermissions("diagnosis:suggestion:list")
    public R<List<DiagnosisSuggestionVO>> getSuggestionsByType(@PathVariable Long diagnosisId,
            @PathVariable String suggestionType) {
        return R.ok(diagnosisSuggestionService.getSuggestionsByType(diagnosisId, suggestionType));
    }

    /**
     * 医生采纳建议
     */
    @PutMapping("/adopt/{suggestionId}/{doctorId}")
    @RequiresPermissions("diagnosis:suggestion:adopt")
    @Log(title = "诊断建议", businessType = BusinessType.UPDATE)
    public R<Boolean> adoptSuggestion(@PathVariable Long suggestionId, @PathVariable Long doctorId) {
        return R.ok(diagnosisSuggestionService.adoptSuggestion(suggestionId, doctorId));
    }

    /**
     * 医生拒绝建议
     */
    @PutMapping("/reject/{suggestionId}/{doctorId}")
    @RequiresPermissions("diagnosis:suggestion:reject")
    @Log(title = "诊断建议", businessType = BusinessType.UPDATE)
    public R<Boolean> rejectSuggestion(@PathVariable Long suggestionId, @PathVariable Long doctorId,
            @RequestParam String reason) {
        return R.ok(diagnosisSuggestionService.rejectSuggestion(suggestionId, doctorId, reason));
    }

    /**
     * 修改建议内容
     */
    @PutMapping("/update/{suggestionId}/{doctorId}")
    @RequiresPermissions("diagnosis:suggestion:edit")
    @Log(title = "诊断建议", businessType = BusinessType.UPDATE)
    public R<Boolean> updateSuggestion(@PathVariable Long suggestionId, @PathVariable Long doctorId,
            @RequestParam String content) {
        return R.ok(diagnosisSuggestionService.updateSuggestion(suggestionId, content, doctorId));
    }

    /**
     * 获取相关知识条目
     */
    @GetMapping("/knowledge/{keyword}/{limit}")
    @RequiresPermissions("diagnosis:suggestion:knowledge")
    public R<List<KnowledgeItemVO>> getRelatedKnowledge(@PathVariable String keyword, @PathVariable Integer limit) {
        return R.ok(diagnosisSuggestionService.getRelatedKnowledge(keyword, limit));
    }

    /**
     * 导出诊断建议报告
     */
    @GetMapping("/export/{diagnosisId}")
    @RequiresPermissions("diagnosis:suggestion:export")
    @Log(title = "诊断建议", businessType = BusinessType.EXPORT)
    public R<String> exportSuggestionReport(@PathVariable Long diagnosisId) {
        return R.ok(diagnosisSuggestionService.exportSuggestionReport(diagnosisId));
    }
}