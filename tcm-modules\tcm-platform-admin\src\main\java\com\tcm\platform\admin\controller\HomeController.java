package com.tcm.platform.admin.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.platform.admin.dto.Result;

/**
 * 首页控制器
 *
 * <AUTHOR>
 */
@RestController
@RefreshScope
public class HomeController extends BaseController {

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${server.port}")
    private Integer serverPort;

    /**
     * 首页信息
     */
    @GetMapping("/")
    public Result<Map<String, Object>> home() {
        Map<String, Object> data = new HashMap<>();
        data.put("applicationName", applicationName);
        data.put("port", serverPort);
        data.put("user", getCurrentUsername());
        data.put("message", "欢迎访问TCM平台管理系统");

        return Result.success(data);
    }

    /**
     * 获取系统配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> config() {
        Map<String, Object> data = new HashMap<>();
        data.put("sessionTimeout", adminConfig.getSessionTimeout());
        data.put("passwordPolicy", adminConfig.getPasswordPolicy());

        return Result.success(data);
    }
}