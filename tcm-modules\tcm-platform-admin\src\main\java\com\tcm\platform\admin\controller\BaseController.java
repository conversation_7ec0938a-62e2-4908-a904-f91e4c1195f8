package com.tcm.platform.admin.controller;

import org.springframework.beans.factory.annotation.Autowired;
import com.tcm.platform.admin.config.AdminConfig;
import javax.servlet.http.HttpServletRequest;

/**
 * 控制器基类
 *
 * <AUTHOR>
 */
public abstract class BaseController {

    @Autowired
    protected AdminConfig adminConfig;

    @Autowired
    protected HttpServletRequest request;

    /**
     * 获取当前用户ID
     */
    protected Long getCurrentUserId() {
        // 实际项目中应从JWT或会话中获取
        String userId = request.getHeader("X-User-Id");
        return userId != null ? Long.valueOf(userId) : null;
    }

    /**
     * 获取当前用户名
     */
    protected String getCurrentUsername() {
        // 实际项目中应从JWT或会话中获取
        return request.getHeader("X-Username");
    }
}