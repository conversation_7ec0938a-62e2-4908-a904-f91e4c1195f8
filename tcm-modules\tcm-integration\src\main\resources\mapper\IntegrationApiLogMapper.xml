<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.integration.mapper.IntegrationApiLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.integration.entity.IntegrationApiLog">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="system_code" property="systemCode" />
        <result column="api_name" property="apiName" />
        <result column="request_url" property="requestUrl" />
        <result column="request_method" property="requestMethod" />
        <result column="request_headers" property="requestHeaders" />
        <result column="request_params" property="requestParams" />
        <result column="response_status" property="responseStatus" />
        <result column="response_result" property="responseResult" />
        <result column="process_time" property="processTime" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="request_ip" property="requestIp" />
        <result column="operate_by" property="operateBy" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 统计接口调用信息 -->
    <select id="getApiStatistics" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_count,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as fail_count,
            AVG(process_time) as avg_process_time,
            MAX(process_time) as max_process_time,
            MIN(process_time) as min_process_time
        FROM
            sys_integration_api_log
        WHERE
            system_code = #{systemCode}
            <if test="startTime != null">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
    </select>

</mapper> 