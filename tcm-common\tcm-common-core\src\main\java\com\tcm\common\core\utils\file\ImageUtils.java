package com.tcm.common.core.utils.file;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 图片处理工具类
 */
public class ImageUtils {
    
    /**
     * 缩放图片
     *
     * @param srcImageFile 源图片文件
     * @param destImageFile 目标图片文件
     * @param width 目标宽度
     * @param height 目标高度
     * @param preserveRatio 是否保持比例
     * @throws IOException IO异常
     */
    public static void resize(File srcImageFile, File destImageFile, int width, int height, boolean preserveRatio) throws IOException {
        BufferedImage srcImage = ImageIO.read(srcImageFile);
        if (srcImage == null) {
            throw new IOException("无法读取源图片");
        }
        
        int targetWidth = width;
        int targetHeight = height;
        
        if (preserveRatio) {
            double ratio = (double) srcImage.getWidth() / srcImage.getHeight();
            if (width / height > ratio) {
                targetWidth = (int) (height * ratio);
            } else {
                targetHeight = (int) (width / ratio);
            }
        }
        
        BufferedImage destImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = destImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(srcImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();
        
        String formatName = FileTypeUtils.getFileExtension(destImageFile.getName());
        if (formatName.isEmpty()) {
            formatName = "jpg";
        }
        
        ImageIO.write(destImage, formatName, destImageFile);
    }
    
    /**
     * 剪裁图片
     *
     * @param srcImageFile 源图片文件
     * @param destImageFile 目标图片文件
     * @param x 起始X坐标
     * @param y 起始Y坐标
     * @param width 宽度
     * @param height 高度
     * @throws IOException IO异常
     */
    public static void crop(File srcImageFile, File destImageFile, int x, int y, int width, int height) throws IOException {
        BufferedImage srcImage = ImageIO.read(srcImageFile);
        if (srcImage == null) {
            throw new IOException("无法读取源图片");
        }
        
        BufferedImage destImage = srcImage.getSubimage(x, y, width, height);
        
        String formatName = FileTypeUtils.getFileExtension(destImageFile.getName());
        if (formatName.isEmpty()) {
            formatName = "jpg";
        }
        
        ImageIO.write(destImage, formatName, destImageFile);
    }
    
    /**
     * 旋转图片
     *
     * @param srcImageFile 源图片文件
     * @param destImageFile 目标图片文件
     * @param degrees 旋转角度，正数表示顺时针旋转
     * @throws IOException IO异常
     */
    public static void rotate(File srcImageFile, File destImageFile, double degrees) throws IOException {
        BufferedImage srcImage = ImageIO.read(srcImageFile);
        if (srcImage == null) {
            throw new IOException("无法读取源图片");
        }
        
        double radians = Math.toRadians(degrees);
        double sin = Math.abs(Math.sin(radians));
        double cos = Math.abs(Math.cos(radians));
        
        int srcWidth = srcImage.getWidth();
        int srcHeight = srcImage.getHeight();
        
        int destWidth = (int) Math.floor(srcWidth * cos + srcHeight * sin);
        int destHeight = (int) Math.floor(srcHeight * cos + srcWidth * sin);
        
        BufferedImage destImage = new BufferedImage(destWidth, destHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = destImage.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        
        g2d.translate((destWidth - srcWidth) / 2, (destHeight - srcHeight) / 2);
        g2d.rotate(radians, srcWidth / 2, srcHeight / 2);
        g2d.drawImage(srcImage, 0, 0, null);
        g2d.dispose();
        
        String formatName = FileTypeUtils.getFileExtension(destImageFile.getName());
        if (formatName.isEmpty()) {
            formatName = "jpg";
        }
        
        ImageIO.write(destImage, formatName, destImageFile);
    }
    
    /**
     * 压缩图片
     *
     * @param srcImageFile 源图片文件
     * @param destImageFile 目标图片文件
     * @param quality 质量（0.0-1.0）
     * @throws IOException IO异常
     */
    public static void compress(File srcImageFile, File destImageFile, float quality) throws IOException {
        BufferedImage srcImage = ImageIO.read(srcImageFile);
        if (srcImage == null) {
            throw new IOException("无法读取源图片");
        }
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        
        String formatName = FileTypeUtils.getFileExtension(destImageFile.getName());
        if (formatName.isEmpty()) {
            formatName = "jpg";
        }
        
        ImageIO.write(srcImage, formatName, outputStream);
        
        InputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        BufferedImage resultImage = ImageIO.read(inputStream);
        
        ImageIO.write(resultImage, formatName, destImageFile);
    }
    
    /**
     * 保存图片
     *
     * @param imageData 图片数据
     * @param fileName 文件名
     * @return 保存路径
     * @throws IOException IO异常
     */
    public static String saveImage(byte[] imageData, String fileName) throws IOException {
        String uploadDir = "uploaded/images/";
        File dir = new File(uploadDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        
        String filePath = uploadDir + fileName;
        File file = new File(filePath);
        Files.write(file.toPath(), imageData);
        return filePath;
    }
    
    /**
     * 删除图片
     *
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean deleteImage(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
    
    /**
     * 获取图片分辨率
     *
     * @param imageData 图片数据
     * @return 宽高Map，包含width和height键
     * @throws IOException IO异常
     */
    public static Map<String, Integer> getImageResolution(byte[] imageData) throws IOException {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData);
        BufferedImage image = ImageIO.read(inputStream);
        Map<String, Integer> resolution = new HashMap<>();
        
        if (image != null) {
            resolution.put("width", image.getWidth());
            resolution.put("height", image.getHeight());
        } else {
            resolution.put("width", 0);
            resolution.put("height", 0);
        }
        
        return resolution;
    }
    
    /**
     * 创建缩略图
     *
     * @param imageData 图片数据
     * @param width 目标宽度
     * @param height 目标高度
     * @return 缩略图字节数组
     * @throws IOException IO异常
     */
    public static byte[] createThumbnail(byte[] imageData, int width, int height) throws IOException {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData);
        BufferedImage srcImage = ImageIO.read(inputStream);
        
        if (srcImage == null) {
            throw new IOException("无法读取图片数据");
        }
        
        // 计算等比例缩放的尺寸
        int targetWidth = width;
        int targetHeight = height;
        double ratio = (double) srcImage.getWidth() / srcImage.getHeight();
        
        if (width / height > ratio) {
            targetWidth = (int) (height * ratio);
        } else {
            targetHeight = (int) (width / ratio);
        }
        
        BufferedImage thumbnail = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = thumbnail.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.drawImage(srcImage, 0, 0, targetWidth, targetHeight, null);
        g2d.dispose();
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(thumbnail, "jpg", outputStream);
        
        return outputStream.toByteArray();
    }
}
