package com.tcm.schedule.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 排班信息实体类
 */
@Data
@TableName("tcm_schedule")
public class Schedule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 排班ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生名称
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 班次类型（1上午 2下午 3晚上）
     */
    private Integer shiftType;

    /**
     * 排班日期
     */
    private LocalDateTime scheduleDate;

    /**
     * 最大可预约数
     */
    private Integer maxAppointment;

    /**
     * 已预约数
     */
    private Integer appointmentCount;

    /**
     * 状态（0正常 1停诊）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Integer delFlag;
}
