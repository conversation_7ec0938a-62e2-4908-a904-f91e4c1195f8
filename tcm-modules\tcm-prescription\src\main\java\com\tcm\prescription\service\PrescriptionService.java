package com.tcm.prescription.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.prescription.domain.Prescription;
import com.tcm.prescription.dto.PrescriptionDTO;
import com.tcm.prescription.vo.PrescriptionVO;

/**
 * 处方服务接口
 */
public interface PrescriptionService extends IService<Prescription> {
    
    /**
     * 分页查询处方列表
     *
     * @param page 页码
     * @param size 每页记录数
     * @param prescription 查询参数
     * @return 处方分页列表
     */
    IPage<PrescriptionVO> selectPrescriptionPage(int page, int size, Prescription prescription);
    
    /**
     * 根据ID查询处方详情
     *
     * @param prescriptionId 处方ID
     * @return 处方详情
     */
    PrescriptionVO getPrescriptionById(Long prescriptionId);
    
    /**
     * 创建处方
     *
     * @param prescriptionDTO 处方信息
     * @return 处方ID
     */
    Long createPrescription(PrescriptionDTO prescriptionDTO);
    
    /**
     * 更新处方
     *
     * @param prescriptionDTO 处方信息
     * @return 是否成功
     */
    boolean updatePrescription(PrescriptionDTO prescriptionDTO);
    
    /**
     * 删除处方
     *
     * @param prescriptionId 处方ID
     * @return 是否成功
     */
    boolean deletePrescription(Long prescriptionId);
    
    /**
     * 审核处方
     *
     * @param prescriptionId 处方ID
     * @param status 状态（1：通过，3：不通过）
     * @param remark 审核备注
     * @return 是否成功
     */
    boolean auditPrescription(Long prescriptionId, Integer status, String remark);
    
    /**
     * 根据患者ID查询处方列表
     *
     * @param patientId 患者ID
     * @return 处方列表
     */
    List<PrescriptionVO> getPrescriptionsByPatientId(Long patientId);
    
    /**
     * 根据诊断ID查询处方列表
     *
     * @param diagnosisId 诊断ID
     * @return 处方列表
     */
    List<PrescriptionVO> getPrescriptionsByDiagnosisId(Long diagnosisId);
} 