<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.inventory.mapper.InventoryItemMapper">
    
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.inventory.entity.InventoryItem">
        <id column="id" property="id" />
        <result column="item_code" property="itemCode" />
        <result column="item_name" property="itemName" />
        <result column="item_type" property="itemType" />
        <result column="specification" property="specification" />
        <result column="unit" property="unit" />
        <result column="manufacturer" property="manufacturer" />
        <result column="batch_number" property="batchNumber" />
        <result column="production_date" property="productionDate" />
        <result column="expiry_date" property="expiryDate" />
        <result column="quantity" property="quantity" />
        <result column="cost_price" property="costPrice" />
        <result column="retail_price" property="retailPrice" />
        <result column="status" property="status" />
        <result column="storage_location" property="storageLocation" />
        <result column="remark" property="remark" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, item_code, item_name, item_type, specification, unit, manufacturer, batch_number, 
        production_date, expiry_date, quantity, cost_price, retail_price, status, 
        storage_location, remark, create_by, create_time, update_by, update_time, del_flag
    </sql>
    
</mapper> 