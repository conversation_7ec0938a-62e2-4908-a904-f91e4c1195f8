package com.tcm.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.analytics.entity.AnalyticsDashboard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 分析仪表盘Mapper接口
 */
@Mapper
public interface AnalyticsDashboardMapper extends BaseMapper<AnalyticsDashboard> {

    /**
     * 查询系统预设仪表盘
     * 
     * @return 仪表盘列表
     */
    @Select("SELECT * FROM analytics_dashboard WHERE type = 0 AND del_flag = 0 ORDER BY order_num")
    List<AnalyticsDashboard> selectSystemDashboards();
    
    /**
     * 查询用户自定义仪表盘
     * 
     * @param creator 创建者
     * @return 仪表盘列表
     */
    @Select("SELECT * FROM analytics_dashboard WHERE type = 1 AND creator = #{creator} AND del_flag = 0 ORDER BY order_num")
    List<AnalyticsDashboard> selectUserDashboards(@Param("creator") String creator);
    
    /**
     * 更新仪表盘查询配置
     * 
     * @param id 仪表盘ID
     * @param queryConfig 查询配置
     * @return 更新行数
     */
    @Update("UPDATE analytics_dashboard SET query_config = #{queryConfig} WHERE id = #{id}")
    int updateDashboardQueryConfig(@Param("id") Long id, @Param("queryConfig") String queryConfig);
    
    /**
     * 根据权限范围查询仪表盘
     * 
     * @param scope 权限范围
     * @return 仪表盘列表
     */
    @Select("SELECT * FROM analytics_dashboard WHERE scope = #{scope} AND del_flag = 0 ORDER BY order_num")
    List<AnalyticsDashboard> selectByScope(@Param("scope") Integer scope);
} 