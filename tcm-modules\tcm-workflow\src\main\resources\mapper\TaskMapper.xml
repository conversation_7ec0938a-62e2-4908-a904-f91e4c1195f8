<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.workflow.mapper.TaskMapper">
    
    <resultMap type="com.tcm.workflow.entity.Task" id="TaskResult">
        <id property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="taskDefinitionKey" column="task_definition_key"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="processDefinitionId" column="process_definition_id"/>
        <result property="processDefinitionKey" column="process_definition_key"/>
        <result property="processDefinitionName" column="process_definition_name"/>
        <result property="executionId" column="execution_id"/>
        <result property="assignee" column="assignee"/>
        <result property="assigneeName" column="assignee_name"/>
        <result property="formKey" column="form_key"/>
        <result property="formData" column="form_data"/>
        <result property="businessKey" column="business_key"/>
        <result property="parentTaskId" column="parent_task_id"/>
        <result property="createTime" column="create_time"/>
        <result property="startTime" column="start_time"/>
        <result property="completeTime" column="complete_time"/>
        <result property="dueDate" column="due_date"/>
        <result property="priority" column="priority"/>
        <result property="taskType" column="task_type"/>
        <result property="status" column="status"/>
        <result property="result" column="result"/>
        <result property="comment" column="comment"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
</mapper> 