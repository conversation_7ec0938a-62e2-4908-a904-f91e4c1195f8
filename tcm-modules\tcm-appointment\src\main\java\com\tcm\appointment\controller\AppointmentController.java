package com.tcm.appointment.controller;

import com.tcm.appointment.dto.AppointmentDTO;
import com.tcm.appointment.dto.AppointmentQuery;
import com.tcm.appointment.service.IAppointmentService;
import com.tcm.appointment.vo.AppointmentStatisticsVO;
import com.tcm.appointment.vo.AppointmentVO;
import com.tcm.appointment.vo.TimeSlotVO;
import com.tcm.common.core.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 预约挂号控制器
 */
@RestController
@RequestMapping("/appointment")
public class AppointmentController {
    
    @Autowired
    private IAppointmentService appointmentService;
    
    /**
     * 获取预约列表
     */
    @GetMapping("/list")
    public R<List<AppointmentVO>> list(AppointmentQuery query) {
        List<AppointmentVO> list = appointmentService.listAppointments(query);
        return R.ok(list);
    }
    
    /**
     * 获取预约详情
     */
    @GetMapping("/{appointmentId}")
    public R<AppointmentVO> getInfo(@PathVariable Long appointmentId) {
        AppointmentVO appointment = appointmentService.getAppointmentById(appointmentId);
        return R.ok(appointment);
    }
    
    /**
     * 创建预约
     */
    @PostMapping
    public R<Long> create(@RequestBody AppointmentDTO appointment) {
        Long appointmentId = appointmentService.createAppointment(appointment);
        return R.ok(appointmentId);
    }
    
    /**
     * 取消预约
     */
    @PutMapping("/cancel/{appointmentId}")
    public R<Boolean> cancel(@PathVariable Long appointmentId, @RequestParam String reason) {
        boolean result = appointmentService.cancelAppointment(appointmentId, reason);
        return R.ok(result);
    }
    
    /**
     * 更新预约状态
     */
    @PutMapping("/status/{appointmentId}")
    public R<Boolean> updateStatus(@PathVariable Long appointmentId, @RequestParam Integer status) {
        boolean result = appointmentService.updateAppointmentStatus(appointmentId, status);
        return R.ok(result);
    }
    
    /**
     * 获取可用时间段
     */
    @GetMapping("/time-slots")
    public R<List<TimeSlotVO>> getTimeSlots(@RequestParam Long doctorId, @RequestParam Date date) {
        List<TimeSlotVO> timeSlots = appointmentService.getAvailableTimeSlots(doctorId, date);
        return R.ok(timeSlots);
    }
    
    /**
     * 患者到诊
     */
    @PutMapping("/arrive/{appointmentId}")
    public R<Boolean> patientArrive(@PathVariable Long appointmentId) {
        boolean result = appointmentService.patientArrive(appointmentId);
        return R.ok(result);
    }
    
    /**
     * 获取预约统计
     */
    @GetMapping("/statistics")
    public R<AppointmentStatisticsVO> getStatistics(@RequestParam Date startDate, @RequestParam Date endDate) {
        AppointmentStatisticsVO statistics = appointmentService.getAppointmentStatistics(startDate, endDate);
        return R.ok(statistics);
    }
} 