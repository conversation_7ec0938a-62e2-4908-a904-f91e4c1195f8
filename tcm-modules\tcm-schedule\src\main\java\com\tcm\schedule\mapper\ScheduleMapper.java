package com.tcm.schedule.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.schedule.entity.Schedule;
import com.tcm.schedule.vo.ScheduleVO;

/**
 * 排班Mapper接口
 */
@Mapper
public interface ScheduleMapper extends BaseMapper<Schedule> {

    /**
     * 查询排班分页列表
     *
     * @param page     分页参数
     * @param schedule 查询参数
     * @return 分页结果
     */
    IPage<ScheduleVO> selectSchedulePage(Page<Schedule> page, @Param("sch") Schedule schedule);

    /**
     * 根据ID查询排班
     *
     * @param id 排班ID
     * @return 排班信息
     */
    ScheduleVO selectScheduleById(@Param("id") Long id);

    /**
     * 查询医生排班列表
     *
     * @param doctorId  医生ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 排班列表
     */
    List<ScheduleVO> selectScheduleByDoctorId(@Param("doctorId") Long doctorId,
            @Param("beginDate") LocalDateTime beginDate, @Param("endDate") LocalDateTime endDate);

    /**
     * 查询科室排班列表
     *
     * @param deptId    科室ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 排班列表
     */
    List<ScheduleVO> selectScheduleByDeptId(@Param("deptId") Long deptId, @Param("beginDate") LocalDateTime beginDate,
            @Param("endDate") LocalDateTime endDate);

    /**
     * 更新排班预约数
     *
     * @param id 排班ID
     * @return 结果
     */
    int updateAppointmentCount(@Param("id") Long id);
}
