package com.tcm.consultation.service.impl;

import org.springframework.stereotype.Service;
import com.tcm.consultation.service.ConsultationParticipantService;
import com.tcm.consultation.mapper.ConsultationParticipantMapper;
import com.tcm.consultation.domain.ConsultationParticipant;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;
import java.util.Date;

/**
 * 会诊参与者服务实现类
 */
@Service
@Slf4j
public class ConsultationParticipantServiceImpl implements ConsultationParticipantService {

    @Autowired
    private ConsultationParticipantMapper participantMapper;

    @Override
    public boolean updateOnlineStatus(Long consultationId, Long userId, boolean isOnline) {
        try {
            // 查询参与者记录
            ConsultationParticipant participant = participantMapper.selectByConsultationAndUser(consultationId, userId);
            if (participant == null) {
                log.error("参与者记录不存在: 会诊ID={}, 用户ID={}", consultationId, userId);
                return false;
            }

            // 更新在线状态
            participant.setOnline(isOnline ? 1 : 0);
            participant.setUpdateTime(new Date());

            // 更新参与者记录
            int result = participantMapper.updateById(participant);

            log.info("更新参与者在线状态: 会诊ID={}, 用户ID={}, 在线状态={}, 结果={}", consultationId, userId, isOnline, result > 0);
            return result > 0;
        } catch (Exception e) {
            log.error("更新参与者在线状态失败", e);
            return false;
        }
    }

    @Override
    public String getDoctorName(Long userId) {
        try {
            // 查询参与者信息
            String doctorName = participantMapper.selectDoctorNameByUserId(userId);
            if (doctorName == null || doctorName.isEmpty()) {
                log.warn("未找到医生姓名: 用户ID={}", userId);
                return "未知医生";
            }

            log.info("获取医生姓名: 用户ID={}, 姓名={}", userId, doctorName);
            return doctorName;
        } catch (Exception e) {
            log.error("获取医生姓名失败", e);
            return "未知医生";
        }
    }
}