package com.tcm.workflow.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程实例实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wf_process_instance")
public class ProcessInstance extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 流程实例ID */
    @TableId(value = "process_instance_id", type = IdType.ASSIGN_ID)
    private String processInstanceId;

    /** 流程定义ID */
    private String processDefinitionId;

    /** 流程定义KEY */
    private String processDefinitionKey;

    /** 流程定义名称 */
    private String processDefinitionName;

    /** 流程业务KEY */
    private String businessKey;

    /** 流程部署ID */
    private String deploymentId;

    /** 流程发起人 */
    private String startUserId;

    /** 流程发起人名称 */
    private String startUserName;

    /** 流程开始时间 */
    private Date startTime;

    /** 流程结束时间 */
    private Date endTime;

    /** 流程状态：1进行中，2已完成，3已终止 */
    private Integer status;

    /** 租户ID */
    private String tenantId;

    /** 关联业务表单数据 */
    private String formData;

    /** 当前任务ID */
    private String currentTaskId;

    /** 当前任务名称 */
    private String currentTaskName;

    /** 当前任务处理人 */
    private String currentAssignee;
}