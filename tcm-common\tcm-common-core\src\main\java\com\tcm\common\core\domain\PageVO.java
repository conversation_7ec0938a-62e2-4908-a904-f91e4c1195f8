package com.tcm.common.core.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页数据封装类
 *
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageVO<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    private long total;

    /**
     * 当前页码
     */
    private long current;

    /**
     * 每页记录数
     */
    private long size;

    /**
     * 分页数据
     */
    private List<T> records;

    /**
     * 总页数
     */
    private long pages;

    /**
     * 构造函数
     *
     * @param records 分页数据
     * @param total   总记录数
     * @param current 当前页码
     * @param size    每页记录数
     */
    public PageVO(List<T> records, long total, long current, long size) {
        this.records = records;
        this.total = total;
        this.current = current;
        this.size = size;
        this.pages = (total + size - 1) / size;
    }
} 