package com.tcm.common.core.service;

import com.tcm.common.core.config.JdkProperties;
import com.tcm.common.core.util.JdkPathValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * JDK配置服务
 */
@Service
public class JdkConfigService {

    private static final Logger logger = LoggerFactory.getLogger(JdkConfigService.class);

    @Autowired
    private JdkProperties jdkProperties;

    /**
     * 在应用启动时设置JDK路径
     */
    @PostConstruct
    public void init() {
        // 检查当前JDK是否已经配置
        String currentJavaHome = System.getProperty("java.home");
        if (currentJavaHome != null && !currentJavaHome.isEmpty()) {
            logger.info("当前JDK路径已配置: {}", currentJavaHome);
            // 如果已经配置了JDK路径，则不需要再设置
            return;
        }

        // 根据操作系统类型选择合适的JDK路径
        String jdkPath = getJdkPathByOsType();
        if (jdkPath != null && !jdkPath.isEmpty()) {
            // 验证JDK路径是否有效
            if (JdkPathValidator.isValidJdkPath(jdkPath)) {
                // 设置JDK路径
                System.setProperty("java.home", jdkPath);
                logger.info("已设置JDK路径: {}", jdkPath);
            } else {
                logger.warn("配置的JDK路径无效: {}", jdkPath);
            }
        } else {
            logger.warn("未找到有效的JDK路径配置");
        }
    }

    /**
     * 根据操作系统类型获取JDK路径
     *
     * @return JDK路径
     */
    private String getJdkPathByOsType() {
        // 首先检查是否有通用配置
        String jdkPath = jdkProperties.getHome();
        if (jdkPath != null && !jdkPath.isEmpty()) {
            logger.info("使用通用JDK路径配置: {}", jdkPath);
            return jdkPath;
        }

        // 根据操作系统类型选择合适的JDK路径
        String osType = JdkPathValidator.getOsType();
        logger.info("当前操作系统类型: {}", osType);

        switch (osType) {
        case "windows":
            jdkPath = jdkProperties.getWindows().getHome();
            logger.info("使用Windows JDK路径配置: {}", jdkPath);
            break;
        case "linux":
            jdkPath = jdkProperties.getLinux().getHome();
            logger.info("使用Linux JDK路径配置: {}", jdkPath);
            break;
        case "mac":
            jdkPath = jdkProperties.getMac().getHome();
            logger.info("使用Mac JDK路径配置: {}", jdkPath);
            break;
        default:
            logger.warn("未知操作系统类型: {}, 无法配置JDK路径", osType);
            break;
        }

        return jdkPath;
    }
}