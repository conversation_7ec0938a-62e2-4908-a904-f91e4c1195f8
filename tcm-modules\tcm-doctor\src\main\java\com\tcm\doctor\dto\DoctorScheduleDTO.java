package com.tcm.doctor.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 医生排班数据传输对象
 */
@Data
public class DoctorScheduleDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 排班ID
     */
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;
    
    /**
     * 排班日期
     */
    @NotNull(message = "排班日期不能为空")
    private Date scheduleDate;
    
    /**
     * 时段类型（1-上午 2-下午 3-晚上）
     */
    @NotNull(message = "时段类型不能为空")
    private Integer periodType;
    
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @Size(max = 10, message = "开始时间长度不能超过10个字符")
    private String startTime;
    
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @Size(max = 10, message = "结束时间长度不能超过10个字符")
    private String endTime;
    
    /**
     * 限诊人数
     */
    @NotNull(message = "限诊人数不能为空")
    private Integer limitCount;
    
    /**
     * 出诊状态（0-正常 1-停诊）
     */
    private Integer status;
    
    /**
     * 挂号费
     */
    @NotNull(message = "挂号费不能为空")
    private Double registrationFee;
} 