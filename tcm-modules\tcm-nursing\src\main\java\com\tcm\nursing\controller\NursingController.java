package com.tcm.nursing.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;

import java.util.HashMap;
import java.util.Map;

/**
 * 护理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/nursing")
public class NursingController {

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Nursing service is running");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建护理记录
     */
    @PostMapping("/record")
    public ResponseEntity<Map<String, Object>> createNursingRecord(@RequestBody Map<String, Object> recordRequest) {
        Map<String, Object> response = new HashMap<>();
        
        // 这里可以实现护理记录创建逻辑，如存储到数据库等
        response.put("success", true);
        response.put("message", "Nursing record created");
        response.put("recordId", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取护理计划
     */
    @GetMapping("/plan")
    public ResponseEntity<Map<String, Object>> getNursingPlan() {
        Map<String, Object> response = new HashMap<>();
        Map<String, Object> plan = new HashMap<>();
        
        plan.put("id", 1);
        plan.put("name", "标准护理计划");
        plan.put("description", "包含基础护理项目的标准护理计划");
        plan.put("createdTime", System.currentTimeMillis());
        
        response.put("success", true);
        response.put("data", plan);
        
        return ResponseEntity.ok(response);
    }
} 