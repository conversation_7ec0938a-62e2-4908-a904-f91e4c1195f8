package com.tcm.analytics.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.analytics.entity.AnalyticsDashboard;
import com.tcm.analytics.service.AnalyticsDashboardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据分析仪表盘Controller
 */
@Slf4j
@RestController
@RequestMapping("/analytics/dashboard")
public class AnalyticsDashboardController {

    @Autowired
    private AnalyticsDashboardService dashboardService;

    /**
     * 创建仪表盘
     */
    @PostMapping
    public Map<String, Object> createDashboard(@RequestBody AnalyticsDashboard dashboard) {
        Map<String, Object> result = new HashMap<>();
        try {
            Long dashboardId = dashboardService.createDashboard(dashboard);
            result.put("code", 200);
            result.put("msg", "创建仪表盘成功");
            result.put("data", dashboardId);
        } catch (Exception e) {
            log.error("创建仪表盘失败", e);
            result.put("code", 500);
            result.put("msg", "创建仪表盘失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新仪表盘
     */
    @PutMapping("/{id}")
    public Map<String, Object> updateDashboard(@PathVariable Long id, @RequestBody AnalyticsDashboard dashboard) {
        Map<String, Object> result = new HashMap<>();
        try {
            dashboard.setId(id);
            boolean success = dashboardService.updateDashboard(dashboard);
            result.put("code", success ? 200 : 500);
            result.put("msg", success ? "更新仪表盘成功" : "更新仪表盘失败");
        } catch (Exception e) {
            log.error("更新仪表盘失败", e);
            result.put("code", 500);
            result.put("msg", "更新仪表盘失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取仪表盘详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getDashboardDetail(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            AnalyticsDashboard dashboard = dashboardService.getDashboardDetail(id);
            result.put("code", dashboard != null ? 200 : 404);
            result.put("msg", dashboard != null ? "获取仪表盘成功" : "仪表盘不存在");
            result.put("data", dashboard);
        } catch (Exception e) {
            log.error("获取仪表盘详情失败", e);
            result.put("code", 500);
            result.put("msg", "获取仪表盘详情失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 分页查询仪表盘列表
     */
    @GetMapping("/page")
    public Map<String, Object> listDashboardsByPage(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String creator) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Page<AnalyticsDashboard> page = new Page<>(pageNum, pageSize);
            Map<String, Object> params = new HashMap<>();
            if (name != null && !name.trim().isEmpty()) {
                params.put("name", name);
            }
            if (creator != null && !creator.trim().isEmpty()) {
                params.put("creator", creator);
            }
            
            Page<AnalyticsDashboard> dashboardPage = dashboardService.listDashboardsByPage(page, params);
            
            result.put("code", 200);
            result.put("msg", "查询成功");
            result.put("data", dashboardPage);
        } catch (Exception e) {
            log.error("分页查询仪表盘失败", e);
            result.put("code", 500);
            result.put("msg", "分页查询仪表盘失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 根据创建者获取仪表盘列表
     */
    @GetMapping("/creator/{creator}")
    public Map<String, Object> listDashboardsByCreator(@PathVariable String creator) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<AnalyticsDashboard> dashboards = dashboardService.listDashboardsByCreator(creator);
            result.put("code", 200);
            result.put("msg", "查询成功");
            result.put("data", dashboards);
        } catch (Exception e) {
            log.error("查询用户仪表盘失败", e);
            result.put("code", 500);
            result.put("msg", "查询用户仪表盘失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取仪表盘数据
     */
    @PostMapping("/{id}/data")
    public Map<String, Object> getDashboardData(@PathVariable Long id, @RequestBody(required = false) Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (params == null) {
                params = new HashMap<>();
            }
            Map<String, Object> data = dashboardService.getDashboardData(id, params);
            result.put("code", 200);
            result.put("msg", "获取数据成功");
            result.put("data", data);
        } catch (Exception e) {
            log.error("获取仪表盘数据失败", e);
            result.put("code", 500);
            result.put("msg", "获取仪表盘数据失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 添加组件到仪表盘
     */
    @PostMapping("/{id}/component")
    public Map<String, Object> addComponent(@PathVariable Long id, @RequestBody Map<String, Object> componentConfig) {
        Map<String, Object> result = new HashMap<>();
        try {
            String componentId = dashboardService.addComponent(id, componentConfig);
            result.put("code", 200);
            result.put("msg", "添加组件成功");
            result.put("data", componentId);
        } catch (Exception e) {
            log.error("添加仪表盘组件失败", e);
            result.put("code", 500);
            result.put("msg", "添加仪表盘组件失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新仪表盘组件
     */
    @PutMapping("/{id}/component/{componentId}")
    public Map<String, Object> updateComponent(
            @PathVariable Long id,
            @PathVariable String componentId,
            @RequestBody Map<String, Object> componentConfig) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = dashboardService.updateComponent(id, componentId, componentConfig);
            result.put("code", success ? 200 : 404);
            result.put("msg", success ? "更新组件成功" : "组件不存在");
        } catch (Exception e) {
            log.error("更新仪表盘组件失败", e);
            result.put("code", 500);
            result.put("msg", "更新仪表盘组件失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除仪表盘组件
     */
    @DeleteMapping("/{id}/component/{componentId}")
    public Map<String, Object> removeComponent(@PathVariable Long id, @PathVariable String componentId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = dashboardService.removeComponent(id, componentId);
            result.put("code", success ? 200 : 404);
            result.put("msg", success ? "删除组件成功" : "组件不存在");
        } catch (Exception e) {
            log.error("删除仪表盘组件失败", e);
            result.put("code", 500);
            result.put("msg", "删除仪表盘组件失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 导出仪表盘配置
     */
    @GetMapping("/{id}/export")
    public Map<String, Object> exportDashboardConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            String config = dashboardService.exportDashboardConfig(id);
            result.put("code", config != null ? 200 : 404);
            result.put("msg", config != null ? "导出配置成功" : "仪表盘不存在");
            result.put("data", config);
        } catch (Exception e) {
            log.error("导出仪表盘配置失败", e);
            result.put("code", 500);
            result.put("msg", "导出仪表盘配置失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 导入仪表盘配置
     */
    @PostMapping("/import")
    public Map<String, Object> importDashboardConfig(
            @RequestParam String dashboardName,
            @RequestBody String config) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Long dashboardId = dashboardService.importDashboardConfig(config, dashboardName);
            result.put("code", 200);
            result.put("msg", "导入配置成功");
            result.put("data", dashboardId);
        } catch (Exception e) {
            log.error("导入仪表盘配置失败", e);
            result.put("code", 500);
            result.put("msg", "导入仪表盘配置失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除仪表盘
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteDashboard(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = dashboardService.deleteDashboard(id);
            result.put("code", success ? 200 : 404);
            result.put("msg", success ? "删除仪表盘成功" : "仪表盘不存在");
        } catch (Exception e) {
            log.error("删除仪表盘失败", e);
            result.put("code", 500);
            result.put("msg", "删除仪表盘失败: " + e.getMessage());
        }
        return result;
    }
} 