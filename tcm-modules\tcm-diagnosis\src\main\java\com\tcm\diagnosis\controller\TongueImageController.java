package com.tcm.diagnosis.controller;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.log.annotation.Log;
import com.tcm.common.log.enums.BusinessType;
import com.tcm.common.security.annotation.RequiresPermissions;
import com.tcm.diagnosis.dto.TongueImageCaptureDTO;
import com.tcm.diagnosis.service.ITongueImageService;
import com.tcm.diagnosis.vo.TongueImageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 舌象图像控制器
 */
@RestController
@RequestMapping("/tongue/image")
public class TongueImageController extends BaseController {

    @Autowired
    private ITongueImageService tongueImageService;

    /**
     * 上传舌象图像
     */
    @PostMapping("/upload")
    @RequiresPermissions("diagnosis:tongue:upload")
    @Log(title = "舌象图像", businessType = BusinessType.INSERT)
    public R<Long> uploadTongueImage(@Validated @RequestBody TongueImageCaptureDTO tongueImageDTO) {
        return R.ok(tongueImageService.uploadTongueImage(tongueImageDTO));
    }

    /**
     * 获取舌象图像详情
     */
    @GetMapping("/{imageId}")
    @RequiresPermissions("diagnosis:tongue:query")
    public R<TongueImageVO> getTongueImage(@PathVariable Long imageId) {
        return R.ok(tongueImageService.getTongueImage(imageId));
    }

    /**
     * 获取诊断记录关联的舌象图像列表
     */
    @GetMapping("/list/diagnosis/{diagnosisId}")
    @RequiresPermissions("diagnosis:tongue:list")
    public R<List<TongueImageVO>> getTongueImageListByDiagnosisId(@PathVariable Long diagnosisId) {
        return R.ok(tongueImageService.getTongueImageListByDiagnosisId(diagnosisId));
    }

    /**
     * 获取患者的舌象图像列表
     */
    @GetMapping("/list/patient/{patientId}")
    @RequiresPermissions("diagnosis:tongue:list")
    public R<List<TongueImageVO>> getTongueImageListByPatientId(@PathVariable Long patientId) {
        return R.ok(tongueImageService.getTongueImageListByPatientId(patientId));
    }

    /**
     * 删除舌象图像
     */
    @DeleteMapping("/{imageId}")
    @RequiresPermissions("diagnosis:tongue:remove")
    @Log(title = "舌象图像", businessType = BusinessType.DELETE)
    public R<Boolean> deleteTongueImage(@PathVariable Long imageId) {
        return R.ok(tongueImageService.deleteTongueImage(imageId));
    }

    /**
     * 获取最近的舌象图像列表
     */
    @GetMapping("/recent/{patientId}/{limit}")
    @RequiresPermissions("diagnosis:tongue:list")
    public R<List<TongueImageVO>> getRecentTongueImages(@PathVariable Long patientId, @PathVariable Integer limit) {
        return R.ok(tongueImageService.getRecentTongueImages(patientId, limit));
    }
}