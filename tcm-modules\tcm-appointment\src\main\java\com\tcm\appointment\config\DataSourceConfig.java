package com.tcm.appointment.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.SQLException;

/**
 * 数据源配置
 * 手动配置数据源，避免使用Druid自动配置
 * 使用硬编码的数据库连接信息，避免占位符解析问题
 */
@Configuration
public class DataSourceConfig {

    @Bean
    @Primary
    public DataSource dataSource() throws SQLException {
        DruidDataSource datasource = new DruidDataSource();
        
        // 使用硬编码的数据库连接信息，避免占位符解析问题
        datasource.setUrl("***********************************************************************************************************************************************************");
        datasource.setUsername("root");
        datasource.setPassword("Kaixin207@1984");
        datasource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // 配置初始化大小、最小、最大
        datasource.setInitialSize(5);
        datasource.setMinIdle(10);
        datasource.setMaxActive(20);
        
        // 配置获取连接等待超时的时间
        datasource.setMaxWait(60000);
        
        // 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        datasource.setTimeBetweenEvictionRunsMillis(60000);
        
        // 配置一个连接在池中最小、最大生存的时间，单位是毫秒
        datasource.setMinEvictableIdleTimeMillis(300000);
        datasource.setMaxEvictableIdleTimeMillis(900000);
        
        // 用来检测连接是否有效的sql
        datasource.setValidationQuery("SELECT 1 FROM DUAL");
        
        // 建议配置为true，不影响性能，并且保证安全性
        datasource.setTestWhileIdle(true);
        
        // 申请连接时执行validationQuery检测连接是否有效
        datasource.setTestOnBorrow(false);
        datasource.setTestOnReturn(false);
        
        return datasource;
    }
}
