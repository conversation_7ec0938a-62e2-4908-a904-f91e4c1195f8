package com.tcm.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.admin.dto.MenuDTO;
import com.tcm.admin.entity.Menu;
import com.tcm.admin.vo.MenuVO;
import com.tcm.admin.vo.TreeSelectVO;

import java.util.List;
import java.util.Set;

/**
 * 菜单服务接口
 */
public interface MenuService extends IService<Menu> {

    /**
     * 查询系统菜单列表
     *
     * @param menuDTO 菜单查询条件
     * @return 菜单列表
     */
    List<MenuVO> selectMenuList(MenuDTO menuDTO);

    /**
     * 根据用户ID查询菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<MenuVO> selectMenuListByUserId(Long userId);

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端所需要的菜单树
     *
     * @param menus 菜单列表
     * @return 菜单树
     */
    List<MenuVO> buildMenuTree(List<MenuVO> menus);

    /**
     * 构建前端所需要的树形选择框数据
     *
     * @param menus 菜单列表
     * @return 下拉树选项列表
     */
    List<TreeSelectVO> buildMenuTreeSelect(List<MenuVO> menus);

    /**
     * 根据菜单ID查询菜单
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    MenuVO getMenuById(Long id);

    /**
     * 创建菜单
     *
     * @param menuDTO 菜单信息
     * @return 创建结果
     */
    boolean createMenu(MenuDTO menuDTO);

    /**
     * 更新菜单
     *
     * @param menuDTO 菜单信息
     * @return 更新结果
     */
    boolean updateMenu(MenuDTO menuDTO);

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 删除结果
     */
    boolean deleteMenu(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menuDTO 菜单信息
     * @return 结果
     */
    boolean checkMenuNameUnique(MenuDTO menuDTO);
}