package com.tcm.assistant.client;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.TimeoutException;
import java.time.Duration;

import com.tcm.assistant.exception.AIServiceException;

/**
 * DeepSeek API客户端
 */
@Component
@Slf4j
public class DeepSeekApiClient {

    @Value("${tcm.ai.deepseek.api-key:}")
    private String apiKey;

    @Value("${tcm.ai.deepseek.base-url:https://api.deepseek.com}")
    private String baseUrl;

    @Value("${tcm.ai.deepseek.timeout:30000}")
    private long timeout;

    private final WebClient webClient;

    public DeepSeekApiClient(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder
                .baseUrl(baseUrl)
                .defaultHeader("Authorization", "Bearer " + apiKey)
                .defaultHeader("Content-Type", "application/json")
                .build();
    }

    /**
     * 分析医疗图像
     *
     * @param imageType 图像类型
     * @param imageData 图像数据（Base64）
     * @return 分析结果
     */
    public Map<String, Object> analyzeImage(String imageType, String imageData) {
        log.info("发送图像分析请求，类型: {}", imageType);

        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("type", imageType);
        requestBody.put("data", imageData);

        try {
            return webClient.post()
                    .uri("/v1/image-analysis")
                    .bodyValue(requestBody)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .timeout(Duration.ofMillis(timeout))
                    .block();
        } catch (Exception e) {
            log.error("图像分析请求失败", e);
            if (e instanceof TimeoutException) {
                throw new AIServiceException("图像分析请求超时", e);
            }
            throw new AIServiceException("图像分析请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取诊断建议
     *
     * @param clinicalData 临床数据
     * @return 诊断建议
     */
    public Map<String, Object> getDiagnosisSuggestion(Map<String, Object> clinicalData) {
        log.info("发送诊断建议请求");

        try {
            return webClient.post()
                    .uri("/v1/diagnosis-suggestion")
                    .bodyValue(clinicalData)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .timeout(Duration.ofMillis(timeout))
                    .block();
        } catch (Exception e) {
            log.error("诊断建议请求失败", e);
            if (e instanceof TimeoutException) {
                throw new AIServiceException("诊断建议请求超时", e);
            }
            throw new AIServiceException("诊断建议请求失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取相似病例
     *
     * @param query 查询参数
     * @return 相似病例
     */
    public Map<String, Object> getSimilarCases(Map<String, Object> query) {
        log.info("发送相似病例查询请求");

        try {
            return webClient.post()
                    .uri("/v1/similar-cases")
                    .bodyValue(query)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .timeout(Duration.ofMillis(timeout))
                    .block();
        } catch (Exception e) {
            log.error("相似病例查询请求失败", e);
            if (e instanceof TimeoutException) {
                throw new AIServiceException("相似病例查询请求超时", e);
            }
            throw new AIServiceException("相似病例查询请求失败: " + e.getMessage(), e);
        }
    }
}