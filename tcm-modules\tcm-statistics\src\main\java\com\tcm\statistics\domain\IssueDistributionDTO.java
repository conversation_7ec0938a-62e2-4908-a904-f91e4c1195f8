package com.tcm.statistics.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 问题分布数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IssueDistributionDTO {
    
    /**
     * 问题类型
     */
    private String issueType;
    
    /**
     * 问题描述
     */
    private String description;
    
    /**
     * 问题数量
     */
    private Integer count;
    
    /**
     * 问题占比
     */
    private Double percentage;
} 