package com.tcm.doctor.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.AbstractDataSource;

import javax.sql.DataSource;
import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;

/**
 * 数据库连接失败优雅处理配置
 * 提供一个能够处理数据库连接失败的机制，确保即使数据库不可用，应用程序仍能启动
 */
@Configuration
public class DataSourceFailoverConfig {
    private static final Logger logger = LoggerFactory.getLogger(DataSourceFailoverConfig.class);
    
    /**
     * 这个配置类只会在应用程序启动时记录基本信息
     */
    public DataSourceFailoverConfig() {
        logger.info("初始化数据源弹性配置，提供数据库连接失败处理机制");
    }

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 创建一个数据源包装器，能够处理原始数据源连接失败的情况
     */
    @Bean
    @Primary
    public DataSource resilientDataSource(DataSourceProperties properties) {
        return new ResilientDataSource(properties);
    }

    /**
     * 弹性数据源实现，能够处理连接失败的情况
     * 优先尝试使用真实数据源，但在连接失败时不会导致应用程序崩溃
     */
    public class ResilientDataSource extends AbstractDataSource {
        private DataSourceProperties properties;
        private DataSource realDataSource;
        private boolean dataSourceInitialized = false;
        private boolean connectionFailed = false;

        public ResilientDataSource(DataSourceProperties properties) {
            this.properties = properties;
            initializeRealDataSource();
        }

        private synchronized void initializeRealDataSource() {
            if (dataSourceInitialized) {
                return;
            }

            try {
                // 尝试从应用上下文获取真实数据源
                realDataSource = applicationContext.getBean("dataSource", DataSource.class);
                logger.info("成功获取真实数据源");
            } catch (NoSuchBeanDefinitionException e) {
                try {
                    // 如果找不到，尝试创建一个新的
                    realDataSource = properties.initializeDataSourceBuilder().build();
                    logger.info("创建了新的数据源: " + properties.getUrl());
                } catch (Exception ex) {
                    // 如果创建失败，记录错误但不抛出异常
                    logger.error("无法创建数据源", ex);
                    connectionFailed = true;
                }
            }

            dataSourceInitialized = true;
        }

        @Override
        public Connection getConnection() throws SQLException {
            if (connectionFailed) {
                throw new SQLException("数据库连接已被标记为失败状态，操作被禁用");
            }

            try {
                if (realDataSource != null) {
                    // 获取真实数据源的连接
                    Connection conn = realDataSource.getConnection();
                    if (conn != null) {
                        logger.info("数据库连接成功获取");
                        return conn;
                    }
                }
            } catch (SQLException e) {
                connectionFailed = true;
                logger.error("无法获取数据库连接", e);
            }

            throw new SQLException("数据库连接失败，操作被禁用");
        }

        @Override
        public Connection getConnection(String username, String password) throws SQLException {
            if (connectionFailed) {
                throw new SQLException("数据库连接已被标记为失败状态，操作被禁用");
            }

            try {
                if (realDataSource != null) {
                    return realDataSource.getConnection(username, password);
                }
            } catch (SQLException e) {
                connectionFailed = true;
                logger.error("无法获取数据库连接", e);
            }

            throw new SQLException("数据库连接失败，操作被禁用");
        }

        @Override
        public PrintWriter getLogWriter() {
            if (realDataSource != null) {
                try {
                    return realDataSource.getLogWriter();
                } catch (SQLException e) {
                    logger.warn("获取日志写入器失败", e);
                }
            }
            return null;
        }

        @Override
        public void setLogWriter(PrintWriter out) throws SQLException {
            if (realDataSource != null) {
                realDataSource.setLogWriter(out);
            }
        }

        @Override
        public void setLoginTimeout(int seconds) throws SQLException {
            if (realDataSource != null) {
                realDataSource.setLoginTimeout(seconds);
            }
        }

        @Override
        public int getLoginTimeout() throws SQLException {
            if (realDataSource != null) {
                return realDataSource.getLoginTimeout();
            }
            return 0;
        }

        @Override
        public java.util.logging.Logger getParentLogger() {
            if (realDataSource != null) {
                try {
                    return realDataSource.getParentLogger();
                } catch (SQLFeatureNotSupportedException e) {
                    logger.warn("不支持获取父日志记录器", e);
                }
            }
            return java.util.logging.Logger.getGlobal();
        }

        /**
         * 检查数据库连接状态
         * @return 如果连接成功则返回true，否则返回false
         */
        public boolean isConnectionFailed() {
            return connectionFailed;
        }
    }
}
