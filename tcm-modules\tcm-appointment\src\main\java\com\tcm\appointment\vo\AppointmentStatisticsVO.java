package com.tcm.appointment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 预约统计视图对象
 */
@Data
public class AppointmentStatisticsVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 总预约数
     */
    private Integer totalCount;
    
    /**
     * 已完成预约数
     */
    private Integer completedCount;
    
    /**
     * 已取消预约数
     */
    private Integer canceledCount;
    
    /**
     * 爽约数
     */
    private Integer noShowCount;
    
    /**
     * 预约率
     */
    private Double appointmentRate;
    
    /**
     * 到诊率
     */
    private Double arrivalRate;
    
    /**
     * 按日期统计数据
     */
    private List<Map<String, Object>> dailyStatistics;
    
    /**
     * 按科室统计数据
     */
    private List<Map<String, Object>> departmentStatistics;
    
    /**
     * 按医生统计数据
     */
    private List<Map<String, Object>> doctorStatistics;
    
    /**
     * 按病种统计数据
     */
    private List<Map<String, Object>> diseaseStatistics;
} 