package com.tcm.analytics.util;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.Arrays;

import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;

/**
 * 诊断工具类，用于验证配置和环境
 */
public class DiagnosticHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(DiagnosticHelper.class);

    /**
     * 诊断系统环境
     * 
     * @param context Spring应用上下文
     */
    public static void diagnoseEnvironment(ConfigurableApplicationContext context) {
        LOGGER.info("========== 系统诊断开始 ==========");

        // 检查Spring上下文是否正常
        if (context == null || !context.isActive()) {
            LOGGER.error("Spring上下文未正确初始化或未激活");
            return;
        }

        // 输出JVM内存状态
        diagnoseMemory();

        // 输出系统属性
        diagnoseSystemProperties();

        // 输出Spring Bean信息
        diagnoseSpringBeans(context);

        // 诊断Elasticsearch连接
        diagnoseElasticsearch(context);

        LOGGER.info("========== 系统诊断结束 ==========");
    }

    /**
     * 诊断Elasticsearch连接
     * 
     * @param context Spring应用上下文
     */
    private static void diagnoseElasticsearch(ConfigurableApplicationContext context) {
        try {
            LOGGER.info("========== Elasticsearch诊断开始 ==========");

            // 获取RestHighLevelClient bean
            LOGGER.info("正在获取RestHighLevelClient...");
            RestHighLevelClient client = context.getBean(RestHighLevelClient.class);

            if (client == null) {
                LOGGER.error("无法获取RestHighLevelClient Bean");
                return;
            }

            LOGGER.info("成功获取RestHighLevelClient");

            // 尝试执行简单的Elasticsearch API调用
            try {
                LOGGER.info("正在测试Elasticsearch连接...");
                boolean pingSuccess = client.ping(RequestOptions.DEFAULT);
                LOGGER.info("Elasticsearch连接测试结果: {}", pingSuccess ? "成功" : "失败");

                // 尝试获取索引信息
                GetIndexRequest request = new GetIndexRequest("*");
                boolean indicesExist = client.indices().exists(request, RequestOptions.DEFAULT);
                LOGGER.info("索引检查结果: {}", indicesExist ? "存在索引" : "无索引");
            } catch (Exception e) {
                LOGGER.error("Elasticsearch API调用失败", e);
            }

            // 检查ElasticsearchOperations
            try {
                LOGGER.info("正在获取ElasticsearchOperations...");
                ElasticsearchOperations operations = context.getBean(ElasticsearchOperations.class);

                if (operations == null) {
                    LOGGER.error("无法获取ElasticsearchOperations Bean");
                    return;
                }

                LOGGER.info("成功获取ElasticsearchOperations");
            } catch (Exception e) {
                LOGGER.error("获取ElasticsearchOperations失败", e);
            }

            LOGGER.info("========== Elasticsearch诊断结束 ==========");
        } catch (Exception e) {
            LOGGER.error("Elasticsearch诊断过程中发生异常", e);
        }
    }

    /**
     * 诊断JVM内存状态
     */
    private static void diagnoseMemory() {
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
        MemoryUsage nonHeapMemoryUsage = memoryMXBean.getNonHeapMemoryUsage();

        LOGGER.info("JVM内存状态:");
        LOGGER.info("  堆内存: 已用={} MB, 最大={} MB, 已提交={} MB", heapMemoryUsage.getUsed() / (1024 * 1024),
                heapMemoryUsage.getMax() / (1024 * 1024), heapMemoryUsage.getCommitted() / (1024 * 1024));
        LOGGER.info("  非堆内存: 已用={} MB, 最大={} MB, 已提交={} MB", nonHeapMemoryUsage.getUsed() / (1024 * 1024),
                nonHeapMemoryUsage.getMax() / (1024 * 1024), nonHeapMemoryUsage.getCommitted() / (1024 * 1024));
    }

    /**
     * 诊断系统属性
     */
    private static void diagnoseSystemProperties() {
        LOGGER.info("系统属性:");
        LOGGER.info("  Java版本: {}", System.getProperty("java.version"));
        LOGGER.info("  Java供应商: {}", System.getProperty("java.vendor"));
        LOGGER.info("  Java主目录: {}", System.getProperty("java.home"));
        LOGGER.info("  操作系统: {} {}", System.getProperty("os.name"), System.getProperty("os.version"));
        LOGGER.info("  用户目录: {}", System.getProperty("user.dir"));
        LOGGER.info("  文件编码: {}", System.getProperty("file.encoding"));
    }

    /**
     * 诊断Spring Bean信息
     * 
     * @param context Spring应用上下文
     */
    private static void diagnoseSpringBeans(ConfigurableApplicationContext context) {
        if (context == null) {
            LOGGER.warn("Spring上下文为null，无法诊断Bean信息");
            return;
        }

        String[] beanNames = context.getBeanDefinitionNames();
        Arrays.sort(beanNames);

        LOGGER.info("Spring Bean总数: {}", beanNames.length);
        LOGGER.info("Bean列表 (前10个):");

        for (int i = 0; i < Math.min(10, beanNames.length); i++) {
            LOGGER.info("  {}", beanNames[i]);
        }
    }
}
