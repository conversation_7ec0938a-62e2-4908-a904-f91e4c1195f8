package com.tcm.diagnosis.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 脉诊数据采集数据传输对象
 */
@Data
public class PulseDataCaptureDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 脉象位置（寸/关/尺）
     */
    @NotBlank(message = "脉象位置不能为空")
    private String position;

    /**
     * 信号数据（Base64编码）
     */
    @NotBlank(message = "信号数据不能为空")
    private String signalData;

    /**
     * 采样率（Hz）
     */
    @NotNull(message = "采样率不能为空")
    private Integer sampleRate;

    /**
     * 采样时长（秒）
     */
    private Integer sampleDuration;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 备注
     */
    private String remark;
}