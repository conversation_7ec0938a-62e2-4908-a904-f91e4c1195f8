-- 创建库存项目表
CREATE TABLE IF NOT EXISTS `tcm_inventory_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `item_code` varchar(50) NOT NULL COMMENT '药品编码',
  `item_name` varchar(100) NOT NULL COMMENT '药品名称',
  `item_type` tinyint(1) NOT NULL COMMENT '药品类型（1:中药材, 2:中成药, 3:西药, 4:器械）',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `manufacturer` varchar(200) DEFAULT NULL COMMENT '生产厂家',
  `batch_number` varchar(50) DEFAULT NULL COMMENT '批号',
  `production_date` datetime DEFAULT NULL COMMENT '生产日期',
  `expiry_date` datetime DEFAULT NULL COMMENT '有效期至',
  `quantity` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
  `retail_price` decimal(10,2) DEFAULT NULL COMMENT '零售价',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '库存状态（0:正常, 1:低库存, 2:缺货, 3:过期）',
  `storage_location` varchar(100) DEFAULT NULL COMMENT '存储位置',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_item_code` (`item_code`) COMMENT '药品编码唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='库存项目表';

-- 插入示例数据
INSERT INTO `tcm_inventory_item` (`item_code`, `item_name`, `item_type`, `specification`, `unit`, `manufacturer`, `batch_number`, `production_date`, `expiry_date`, `quantity`, `cost_price`, `retail_price`, `status`, `storage_location`, `create_time`)
VALUES
('ZY001', '人参', 1, '50g/袋', '袋', '吉林参业有限公司', 'B20230501', '2023-05-01', '2025-05-01', 100, 120.00, 150.00, 0, 'A区-01-01', NOW()),
('ZY002', '黄芪', 1, '100g/袋', '袋', '甘肃中药材有限公司', 'B20230502', '2023-05-02', '2025-05-02', 150, 45.00, 60.00, 0, 'A区-01-02', NOW()),
('ZY003', '当归', 1, '50g/袋', '袋', '甘肃中药材有限公司', 'B20230503', '2023-05-03', '2025-05-03', 120, 35.00, 48.00, 0, 'A区-01-03', NOW()),
('ZY004', '白术', 1, '50g/袋', '袋', '安徽中药材有限公司', 'B20230504', '2023-05-04', '2025-05-04', 80, 25.00, 35.00, 0, 'A区-01-04', NOW()),
('ZY005', '茯苓', 1, '100g/袋', '袋', '云南中药材有限公司', 'B20230505', '2023-05-05', '2025-05-05', 90, 30.00, 42.00, 0, 'A区-01-05', NOW()),
('ZCY001', '六味地黄丸', 2, '200丸/瓶', '瓶', '北京同仁堂', 'B20230601', '2023-06-01', '2025-06-01', 200, 15.00, 28.00, 0, 'B区-01-01', NOW()),
('ZCY002', '感冒清热颗粒', 2, '10袋/盒', '盒', '云南白药集团', 'B20230602', '2023-06-02', '2025-06-02', 300, 12.00, 24.00, 0, 'B区-01-02', NOW()),
('ZCY003', '藿香正气水', 2, '10ml*10支/盒', '盒', '广州白云山制药', 'B20230603', '2023-06-03', '2025-06-03', 250, 10.00, 18.00, 0, 'B区-01-03', NOW()),
('XY001', '阿莫西林胶囊', 3, '0.25g*24粒/盒', '盒', '哈药集团', 'B20230701', '2023-07-01', '2025-07-01', 400, 8.00, 15.00, 0, 'C区-01-01', NOW()),
('XY002', '布洛芬片', 3, '0.2g*24片/盒', '盒', '上海医药集团', 'B20230702', '2023-07-02', '2025-07-02', 350, 6.00, 12.00, 0, 'C区-01-02', NOW());

-- 创建库存操作记录表
CREATE TABLE IF NOT EXISTS `tcm_inventory_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `item_id` bigint(20) NOT NULL COMMENT '库存项目ID',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型（1:入库, 2:出库, 3:调整）',
  `quantity` int(11) NOT NULL COMMENT '操作数量',
  `before_quantity` int(11) NOT NULL COMMENT '操作前数量',
  `after_quantity` int(11) NOT NULL COMMENT '操作后数量',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `operator` varchar(64) DEFAULT NULL COMMENT '操作人',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_item_id` (`item_id`) COMMENT '库存项目ID索引',
  KEY `idx_operation_time` (`operation_time`) COMMENT '操作时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='库存操作记录表'; 