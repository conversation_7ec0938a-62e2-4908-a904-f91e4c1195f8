package com.tcm.consultation;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

/**
 * 远程会诊服务启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.tcm.consultation.mapper")
public class TcmRemoteConsultationApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmRemoteConsultationApplication.class);

    // 通过静态代码块预先设置系统属性，确保在任何类加载之前执行
    static {
        // 1. 处理日志冲突问题
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 禁用日志多绑定警告
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "warn");
        System.setProperty("org.slf4j.logger.org.slf4j", "error");
        System.setProperty("org.slf4j.loggerFactory.multipleBindingCheck", "ignore");

        // 强制使用Logback
        System.setProperty("logging.config", "classpath:logback-spring.xml");
        System.setProperty("java.util.logging.manager", "org.apache.logging.log4j.jul.LogManager");

        // 2. 设置统一配置文件
        System.setProperty("spring.profiles.include", "common");

        // 3. 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-remote-consultation");
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 彻底解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

            // 3. 禁用Redisson自动配置
            System.setProperty("spring.autoconfigure.exclude",
                    "org.redisson.spring.starter.RedissonAutoConfiguration,"
                            + "org.springframework.boot.autoconfigure.logging.Log4J2AutoConfiguration,"
                            + "com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration,"
                            + "com.alibaba.cloud.sentinel.SentinelWebFluxAutoConfiguration,"
                            + "org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration");

            // 4. 临时禁用Sentinel
            System.setProperty("spring.cloud.sentinel.enabled", "false");
            System.setProperty("spring.cloud.sentinel.eager", "false");
            System.setProperty("spring.cloud.sentinel.transport.enabled", "false");

            // 5. 临时禁用RocketMQ
            System.setProperty("rocketmq.enabled", "false");

            // 6. 设置应用名称
            System.setProperty("spring.application.name", "tcm-remote-consultation");

            // 7. 设置配置文件包含
            System.setProperty("spring.config.import", "optional:classpath:/config/tcm-common.yml");

            // 8. Nacos配置 - 禁用配置导入检查
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

            // 9. 禁用gRPC相关功能，解决Java 9+环境中的类加载问题
            System.setProperty("nacos.client.naming.grpc.enabled", "false");
            System.setProperty("nacos.client.config.grpc.enabled", "false");
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");

            // 10. 服务注册配置 - 确保应用能在Nacos不可用时正常启动
            System.setProperty("spring.cloud.nacos.discovery.register-enabled", "false");
            System.setProperty("spring.cloud.nacos.discovery.fail-fast", "false");
            System.setProperty("spring.cloud.service-registry.auto-registration.enabled", "false");

            // 启动应用并获取上下文
            LOGGER.info("正在启动远程会诊服务...");
            ConfigurableApplicationContext context = SpringApplication.run(TcmRemoteConsultationApplication.class,
                    args);

            // 获取环境对象，用于解析配置
            Environment env = context.getEnvironment();
            String port = env.getProperty("server.port", "9450");
            String appName = env.getProperty("spring.application.name", "tcm-remote-consultation");

            // 成功运行提示
            LOGGER.info("=====================================================");
            LOGGER.info("          远程会诊服务启动成功!                      ");
            LOGGER.info("=====================================================");
            LOGGER.info("应用名称: {}", appName);
            LOGGER.info("服务端口: {}", port);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("当前环境: {}", activeProfile);

            System.out.println("  _____   _____ __  __          _    _ _______ _    _ ");
            System.out.println(" |_   _| / ____|  \\/  |   /\\   | |  | |__   __| |  | |");
            System.out.println("   | |  | |    | \\  / |  /  \\  | |  | |  | |  | |__| |");
            System.out.println("   | |  | |    | |\\/| | / /\\ \\ | |  | |  | |  |  __  |");
            System.out.println("  _| |_ | |____| |  | |/ ____ \\| |__| |  | |  | |  | |");
            System.out.println(" |_____| \\_____|_|  |_/_/    \\_\\\\____/   |_|  |_|  |_|");
            System.out.println("                                                      ");
            System.out.println("  远程会诊服务启动成功: http://localhost:" + port);
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}