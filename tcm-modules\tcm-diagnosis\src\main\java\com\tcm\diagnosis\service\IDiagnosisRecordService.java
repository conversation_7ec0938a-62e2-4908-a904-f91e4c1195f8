package com.tcm.diagnosis.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.diagnosis.domain.DiagnosisRecord;
import com.tcm.diagnosis.dto.DiagnosisQuery;
import com.tcm.diagnosis.dto.DiagnosisRecordDTO;
import com.tcm.diagnosis.vo.DiagnosisRecordVO;

import java.util.List;

/**
 * 诊断记录服务接口
 */
public interface IDiagnosisRecordService extends IService<DiagnosisRecord> {
    
    /**
     * 分页查询诊断记录列表
     *
     * @param query 查询条件
     * @return 诊断记录列表
     */
    IPage<DiagnosisRecordVO> listDiagnosisRecords(DiagnosisQuery query);
    
    /**
     * 根据ID查询诊断记录详情
     *
     * @param diagnosisId 诊断记录ID
     * @return 诊断记录详情
     */
    DiagnosisRecordVO getDiagnosisRecordById(Long diagnosisId);
    
    /**
     * 根据患者ID查询诊断记录列表
     *
     * @param patientId 患者ID
     * @return 诊断记录列表
     */
    List<DiagnosisRecordVO> getDiagnosisRecordsByPatientId(Long patientId);
    
    /**
     * 根据医生ID查询诊断记录列表
     *
     * @param doctorId 医生ID
     * @return 诊断记录列表
     */
    List<DiagnosisRecordVO> getDiagnosisRecordsByDoctorId(Long doctorId);
    
    /**
     * 新增诊断记录
     *
     * @param diagnosisRecordDTO 诊断记录信息
     * @return 结果
     */
    boolean addDiagnosisRecord(DiagnosisRecordDTO diagnosisRecordDTO);
    
    /**
     * 修改诊断记录
     *
     * @param diagnosisRecordDTO 诊断记录信息
     * @return 结果
     */
    boolean updateDiagnosisRecord(DiagnosisRecordDTO diagnosisRecordDTO);
    
    /**
     * 删除诊断记录
     *
     * @param diagnosisId 诊断记录ID
     * @return 结果
     */
    boolean deleteDiagnosisRecord(Long diagnosisId);
    
    /**
     * 批量删除诊断记录
     *
     * @param diagnosisIds 诊断记录ID数组
     * @return 结果
     */
    boolean deleteDiagnosisRecords(Long[] diagnosisIds);
    
    /**
     * 修改诊断记录状态
     *
     * @param diagnosisId 诊断记录ID
     * @param status      状态
     * @return 结果
     */
    boolean updateDiagnosisRecordStatus(Long diagnosisId, Integer status);
} 