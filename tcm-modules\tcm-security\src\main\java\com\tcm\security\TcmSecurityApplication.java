package com.tcm.security;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import lombok.extern.slf4j.Slf4j;

/**
 * 安全服务启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@Slf4j
public class TcmSecurityApplication {

    // 静态初始化块，确保在类加载时正确设置日志系统
    static {
        // 禁用已知冲突的日志实现
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.disable", "true");
        System.setProperty("log4j2.disableJmx", "true");
        // 强制使用logback
        System.setProperty("org.springframework.boot.logging.LoggingSystem",
                "org.springframework.boot.logging.logback.LogbackLoggingSystem");
        // 禁止Spring Boot尝试查找Log4j
        System.setProperty("spring.main.log-startup-info", "false");
        // 禁用Nacos配置检查
        System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");
    }

    public static void main(String[] args) {
        try {
            // 先进行一次包清理，确保冲突类不会被加载
            ClassLoader.getSystemClassLoader().setClassAssertionStatus("org.apache.logging.log4j.LoggingException",
                    false);

            ConfigurableApplicationContext ctx = SpringApplication.run(TcmSecurityApplication.class, args);
            Environment env = ctx.getEnvironment();
            String port = env.getProperty("server.port");
            String active = env.getProperty("spring.profiles.active");
            String serviceName = env.getProperty("spring.application.name");

            log.info("\n----------------------------------------------------------\n" + "【" + serviceName
                    + "】安全认证服务已成功启动！\n" + "运行环境: " + active + "\n" + "运行端口: " + port + "\n" + "服务健康状态: 正常运行\n"
                    + "JDK路径: " + System.getProperty("java.home") + "\n"
                    + "----------------------------------------------------------");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}