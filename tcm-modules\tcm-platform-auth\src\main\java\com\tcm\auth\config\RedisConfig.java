package com.tcm.auth.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类 使用统一配置中的Redis配置
 */
@Configuration
public class RedisConfig {

    @Autowired
    private Environment environment;

    /**
     * 创建Redis连接工厂
     */
    @Bean
    @Primary
    public LettuceConnectionFactory lettuceConnectionFactory() {
        // 从统一配置中获取Redis配置
        String host = environment.getProperty("tcm.service.redis.host", "localhost");
        int port = environment.getProperty("tcm.service.redis.port", Integer.class, 6379);
        String password = environment.getProperty("tcm.service.redis.password", "");
        int database = environment.getProperty("tcm.service.redis.database", Integer.class, 0);

        // 创建Redis单机配置
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(host);
        config.setPort(port);
        if (password != null && !password.isEmpty()) {
            config.setPassword(password);
        }
        config.setDatabase(database);

        // 创建Lettuce连接工厂
        LettuceConnectionFactory factory = new LettuceConnectionFactory(config);
        factory.afterPropertiesSet();
        return factory;
    }

    /**
     * 创建RedisTemplate
     */
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(lettuceConnectionFactory());
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 创建Redisson客户端
     */
    @Bean
    @Primary
    public RedissonClient redisson() {
        // 从统一配置中获取Redis配置
        String host = environment.getProperty("tcm.service.redis.host", "localhost");
        int port = environment.getProperty("tcm.service.redis.port", Integer.class, 6379);
        String password = environment.getProperty("tcm.service.redis.password", "");
        int database = environment.getProperty("tcm.service.redis.database", Integer.class, 0);

        // 创建Redisson配置
        Config config = new Config();
        String address = "redis://" + host + ":" + port;

        config.useSingleServer().setAddress(address);

        if (password != null && !password.isEmpty()) {
            config.useSingleServer().setPassword(password);
        }

        config.useSingleServer().setDatabase(database).setConnectionMinimumIdleSize(5).setConnectionPoolSize(20)
                .setConnectTimeout(3000).setIdleConnectionTimeout(10000);

        // 创建Redisson客户端
        return Redisson.create(config);
    }
}