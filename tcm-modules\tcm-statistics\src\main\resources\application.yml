# 服务器配置
server:
  port: ${tcm.service.statistics.port:9205}
  servlet:
    context-path: /statistics
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 800
      min-spare: 30
  # 使用统一错误处理配置
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: never
    include-exception: false 

# Spring配置
spring:
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  application:
    name: tcm-statistics
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
  # 禁用Liquibase
  liquibase:
    enabled: false
  # 禁用Thymeleaf
  thymeleaf:
    enabled: false
  # 数据源配置 - 使用统一配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/${tcm.service.mysql.database}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
  # Redis配置 - 使用统一配置
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    password: ${tcm.service.redis.password}
    database: ${tcm.service.redis.database}
    timeout: 10000
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
  # 禁用JMX
  jmx:
    enabled: false
  # 禁用自动配置
  autoconfigure:
    exclude:
      - org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration
      - org.springframework.boot.autoconfigure.webflux.WebFluxAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.endpoint.jmx.JmxEndpointAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.web.servlet.WebMvcMetricsAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.export.simple.SimpleMetricsExportAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.info.InfoContributorAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.web.mappings.MappingsEndpointAutoConfiguration
  # 服务注册与发现配置
  cloud:
    # 禁用Sentinel相关配置
    sentinel:
      enabled: false
      eager: false
      transport:
        enabled: false
    nacos:
      discovery:
        enabled: false
        fail-fast: false
      config:
        enabled: false
        import-check:
          enabled: false
  # Jackson配置 - 使用统一配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  
# 日志配置
logging:
  level:
    "[com.tcm.statistics]": debug
    "[org.springframework.web]": info
    "[org.springframework.security]": info
    "[com.alibaba.nacos]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc]": ERROR
    "[com.alibaba.nacos.shaded.io.perfmark]": ERROR
    "[com.alibaba.nacos.common.remote.client]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc.netty]": ERROR
    "[com.alibaba.cloud.nacos]": ERROR
    "[com.alibaba.nacos.client.naming]": ERROR
    "[org.springframework.cloud]": debug
    "[org.springframework.boot.autoconfigure]": warn
    org.springframework.boot: warn
    org.springframework: warn
  config: classpath:logback-spring.xml

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.statistics.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    banner: false
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 禁用Spring Boot Actuator
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: ""
    jmx:
      exposure:
        include: ""
  endpoint:
    health:
      enabled: false
    info:
      enabled: false
    shutdown:
      enabled: false
    metrics:
      enabled: false
    prometheus:
    enabled: false

# 设置完全禁用Seata - 使用统一配置
seata:
  enabled: false
  enable-auto-data-source-proxy: false
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}-group
  
# RocketMQ配置 - 禁用
rocketmq:
  enabled: false