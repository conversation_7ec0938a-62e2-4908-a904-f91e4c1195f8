package com.tcm.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.integration.dto.SystemIntegrationConfigDTO;
import com.tcm.integration.entity.SystemIntegrationConfig;

/**
 * 系统集成配置服务接口
 *
 * <AUTHOR>
 */
public interface SystemIntegrationConfigService extends IService<SystemIntegrationConfig> {

    /**
     * 分页查询系统集成配置
     *
     * @param page       页码
     * @param pageSize   每页大小
     * @param systemName 系统名称
     * @param systemCode 系统编码
     * @param systemType 系统类型
     * @return 分页结果
     */
    IPage<SystemIntegrationConfig> page(int page, int pageSize, String systemName, String systemCode,
            String systemType);

    /**
     * 根据ID获取系统配置详情
     *
     * @param id ID
     * @return 系统配置详情
     */
    SystemIntegrationConfig getById(Long id);

    /**
     * 根据系统编码获取系统配置
     *
     * @param systemCode 系统编码
     * @return 系统配置
     */
    SystemIntegrationConfig getBySystemCode(String systemCode);

    /**
     * 新增系统配置
     *
     * @param dto 系统配置信息
     * @return 是否成功
     */
    boolean add(SystemIntegrationConfigDTO dto);

    /**
     * 更新系统配置
     *
     * @param dto 系统配置信息
     * @return 是否成功
     */
    boolean update(SystemIntegrationConfigDTO dto);

    /**
     * 删除系统配置
     *
     * @param id ID
     * @return 是否成功
     */
    boolean deleteById(Long id);

    /**
     * 启用系统
     *
     * @param id ID
     * @return 是否成功
     */
    boolean enable(Long id);

    /**
     * 禁用系统
     *
     * @param id ID
     * @return 是否成功
     */
    boolean disable(Long id);

    /**
     * 测试系统连接
     *
     * @param id ID
     * @return 是否成功
     */
    boolean testConnection(Long id);
}