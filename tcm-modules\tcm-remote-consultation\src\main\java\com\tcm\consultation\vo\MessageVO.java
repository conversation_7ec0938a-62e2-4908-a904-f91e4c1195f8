package com.tcm.consultation.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 会诊消息视图对象
 */
@Data
public class MessageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private Long messageId;

    /**
     * 会诊记录ID
     */
    private Long consultationId;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者姓名
     */
    private String senderName;

    /**
     * 发送者头像
     */
    private String senderAvatar;

    /**
     * 发送者类型（1：主诊医生，2：会诊医生，3：专家，4：系统）
     */
    private Integer senderType;

    /**
     * 发送者类型名称
     */
    private String senderTypeName;

    /**
     * 消息类型（1：文本，2：图片，3：文件，4：系统通知）
     */
    private Integer messageType;

    /**
     * 消息类型名称
     */
    private String messageTypeName;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 媒体资源URL
     */
    private String mediaUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小（KB）
     */
    private Long fileSize;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 是否已读
     */
    private Boolean isRead;

    /**
     * 获取发送者类型名称
     */
    public String getSenderTypeName() {
        if (senderType == null) {
            return "";
        }
        switch (senderType) {
            case 1:
                return "主诊医生";
            case 2:
                return "会诊医生";
            case 3:
                return "专家";
            case 4:
                return "系统";
            default:
                return "未知";
        }
    }

    /**
     * 获取消息类型名称
     */
    public String getMessageTypeName() {
        if (messageType == null) {
            return "";
        }
        switch (messageType) {
            case 1:
                return "文本";
            case 2:
                return "图片";
            case 3:
                return "文件";
            case 4:
                return "系统通知";
            default:
                return "未知";
        }
    }
}