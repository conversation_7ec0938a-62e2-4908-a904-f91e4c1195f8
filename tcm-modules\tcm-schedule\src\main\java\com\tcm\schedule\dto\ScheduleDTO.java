package com.tcm.schedule.dto;

import java.time.LocalDateTime;

import com.tcm.common.core.domain.PageQuery;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 排班查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ScheduleDTO extends PageQuery {

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生名称
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 班次类型（1上午 2下午 3晚上）
     */
    private Integer shiftType;

    /**
     * 排班开始日期
     */
    private LocalDateTime beginDate;

    /**
     * 排班结束日期
     */
    private LocalDateTime endDate;

    /**
     * 状态（0正常 1停诊）
     */
    private Integer status;
}
