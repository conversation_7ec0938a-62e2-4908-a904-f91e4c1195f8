package com.tcm.diagnosis.service;

import com.tcm.diagnosis.domain.TongueImage;
import com.tcm.diagnosis.dto.TongueImageCaptureDTO;
import com.tcm.diagnosis.vo.TongueImageVO;

import java.util.List;

/**
 * 舌象图像服务接口
 */
public interface ITongueImageService {

    /**
     * 上传并保存舌象图像
     *
     * @param tongueImageDTO 舌象图像数据
     * @return 图像ID
     */
    Long uploadTongueImage(TongueImageCaptureDTO tongueImageDTO);

    /**
     * 获取舌象图像详情
     *
     * @param imageId 图像ID
     * @return 舌象图像视图对象
     */
    TongueImageVO getTongueImage(Long imageId);

    /**
     * 获取诊断记录关联的舌象图像列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 舌象图像视图对象列表
     */
    List<TongueImageVO> getTongueImageListByDiagnosisId(Long diagnosisId);

    /**
     * 获取患者的舌象图像列表
     *
     * @param patientId 患者ID
     * @return 舌象图像视图对象列表
     */
    List<TongueImageVO> getTongueImageListByPatientId(Long patientId);

    /**
     * 删除舌象图像
     *
     * @param imageId 图像ID
     * @return 是否成功
     */
    boolean deleteTongueImage(Long imageId);

    /**
     * 获取最近的舌象图像列表
     *
     * @param patientId 患者ID
     * @param limit     限制数量
     * @return 舌象图像视图对象列表
     */
    List<TongueImageVO> getRecentTongueImages(Long patientId, Integer limit);
}