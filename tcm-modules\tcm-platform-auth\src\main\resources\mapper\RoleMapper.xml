<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.auth.mapper.RoleMapper">

    <!-- 角色结果映射 -->
    <resultMap id="RoleResultMap" type="com.tcm.auth.domain.Role">
        <id property="id" column="id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleCode" column="role_code"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="dataScope" column="data_scope"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 根据用户ID查询角色列表 -->
    <select id="selectRolesByUserId" parameterType="Long" resultMap="RoleResultMap">
        select r.id, r.role_name, r.role_code, r.sort, r.status, r.data_scope, r.remark,
        r.create_by, r.create_time, r.update_by, r.update_time, r.del_flag
        from sys_role r
        inner join sys_user_role ur on r.id = ur.role_id
        where ur.user_id = #{userId} and r.del_flag = 0 and r.status = 1
    </select>
</mapper>