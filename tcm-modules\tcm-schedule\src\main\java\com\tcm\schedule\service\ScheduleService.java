package com.tcm.schedule.service;

import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.common.core.domain.PageVO;
import com.tcm.schedule.dto.ScheduleDTO;
import com.tcm.schedule.entity.Schedule;
import com.tcm.schedule.vo.ScheduleVO;

/**
 * 排班服务接口
 */
public interface ScheduleService extends IService<Schedule> {

    /**
     * 查询排班分页列表
     *
     * @param dto 查询条件
     * @return 分页列表
     */
    PageVO<ScheduleVO> selectSchedulePage(ScheduleDTO dto);

    /**
     * 查询排班详细信息
     *
     * @param id 排班ID
     * @return 排班信息
     */
    ScheduleVO getScheduleById(Long id);

    /**
     * 新增排班
     *
     * @param schedule 排班信息
     * @return 结果
     */
    boolean addSchedule(Schedule schedule);

    /**
     * 修改排班
     *
     * @param schedule 排班信息
     * @return 结果
     */
    boolean updateSchedule(Schedule schedule);

    /**
     * 删除排班
     *
     * @param ids 排班ID数组
     * @return 结果
     */
    boolean deleteScheduleByIds(Long[] ids);

    /**
     * 查询医生排班列表
     *
     * @param doctorId  医生ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 排班列表
     */
    List<ScheduleVO> getScheduleByDoctorId(Long doctorId, LocalDateTime beginDate, LocalDateTime endDate);

    /**
     * 查询科室排班列表
     *
     * @param deptId    科室ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 排班列表
     */
    List<ScheduleVO> getScheduleByDeptId(Long deptId, LocalDateTime beginDate, LocalDateTime endDate);

    /**
     * 更新排班预约数
     *
     * @param id 排班ID
     * @return 结果
     */
    boolean updateAppointmentCount(Long id);
}
