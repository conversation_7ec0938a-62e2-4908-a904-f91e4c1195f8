package com.tcm.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.admin.dto.RoleDTO;
import com.tcm.admin.entity.Role;
import com.tcm.admin.vo.RoleVO;
import com.tcm.common.core.domain.PageVO;

import java.util.List;

/**
 * 角色服务接口
 */
public interface RoleService extends IService<Role> {

    /**
     * 分页查询角色列表
     *
     * @param roleDTO  查询条件
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @return 角色分页数据
     */
    PageVO<RoleVO> listByPage(RoleDTO roleDTO, Integer pageNum, Integer pageSize);

    /**
     * 根据ID查询角色
     *
     * @param id 角色ID
     * @return 角色信息
     */
    RoleVO getById(Long id);

    /**
     * 创建角色
     *
     * @param roleDTO 角色信息
     * @return 创建结果
     */
    boolean create(RoleDTO roleDTO);

    /**
     * 更新角色
     *
     * @param roleDTO 角色信息
     * @return 更新结果
     */
    boolean update(RoleDTO roleDTO);

    /**
     * 删除角色
     *
     * @param ids 角色ID数组
     * @return 删除结果
     */
    boolean delete(Long[] ids);

    /**
     * 修改角色状态
     *
     * @param id     角色ID
     * @param status 角色状态
     * @return 修改结果
     */
    boolean changeStatus(Long id, Integer status);

    /**
     * 分配角色菜单权限
     *
     * @param roleId  角色ID
     * @param menuIds 菜单ID数组
     * @return 分配结果
     */
    boolean assignMenus(Long roleId, Long[] menuIds);

    /**
     * 分配角色数据权限
     *
     * @param roleDTO 角色信息
     * @return 分配结果
     */
    boolean assignDataScope(RoleDTO roleDTO);

    /**
     * 查询用户拥有的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<RoleVO> getUserRoles(Long userId);

    /**
     * 查询所有可用角色列表
     *
     * @return 角色列表
     */
    List<RoleVO> listAllEnabledRoles();
}