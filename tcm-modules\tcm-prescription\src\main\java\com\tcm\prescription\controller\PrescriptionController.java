package com.tcm.prescription.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.Result;
import com.tcm.prescription.domain.Prescription;
import com.tcm.prescription.dto.PrescriptionDTO;
import com.tcm.prescription.service.PrescriptionService;
import com.tcm.prescription.vo.PrescriptionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 处方管理控制器
 */
@RestController
@RequestMapping("/prescription")
public class PrescriptionController {

    @Autowired
    private PrescriptionService prescriptionService;

    /**
     * 分页查询处方列表
     */
    @GetMapping("/list")
    public Result<IPage<PrescriptionVO>> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            Prescription prescription) {
        IPage<PrescriptionVO> page = prescriptionService.selectPrescriptionPage(pageNum, pageSize, prescription);
        return Result.success(page);
    }

    /**
     * 获取处方详情
     */
    @GetMapping("/{prescriptionId}")
    public Result<PrescriptionVO> getInfo(@PathVariable Long prescriptionId) {
        PrescriptionVO vo = prescriptionService.getPrescriptionById(prescriptionId);
        return Result.success(vo);
    }

    /**
     * 创建处方
     */
    @PostMapping
    public Result<Long> add(@Validated @RequestBody PrescriptionDTO prescriptionDTO) {
        Long prescriptionId = prescriptionService.createPrescription(prescriptionDTO);
        return Result.success(prescriptionId);
    }

    /**
     * 更新处方
     */
    @PutMapping
    public Result<Boolean> update(@Validated @RequestBody PrescriptionDTO prescriptionDTO) {
        boolean success = prescriptionService.updatePrescription(prescriptionDTO);
        return Result.success(success);
    }

    /**
     * 删除处方
     */
    @DeleteMapping("/{prescriptionId}")
    public Result<Boolean> remove(@PathVariable Long prescriptionId) {
        boolean success = prescriptionService.deletePrescription(prescriptionId);
        return Result.success(success);
    }

    /**
     * 审核处方
     */
    @PutMapping("/audit")
    public Result<Boolean> audit(@RequestBody Map<String, Object> params) {
        Long prescriptionId = Long.valueOf(params.get("prescriptionId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());
        String remark = params.get("remark").toString();
        
        boolean success = prescriptionService.auditPrescription(prescriptionId, status, remark);
        return Result.success(success);
    }

    /**
     * 根据患者ID获取处方列表
     */
    @GetMapping("/patient/{patientId}")
    public Result<List<PrescriptionVO>> getByPatientId(@PathVariable Long patientId) {
        List<PrescriptionVO> list = prescriptionService.getPrescriptionsByPatientId(patientId);
        return Result.success(list);
    }

    /**
     * 根据诊断ID获取处方列表
     */
    @GetMapping("/diagnosis/{diagnosisId}")
    public Result<List<PrescriptionVO>> getByDiagnosisId(@PathVariable Long diagnosisId) {
        List<PrescriptionVO> list = prescriptionService.getPrescriptionsByDiagnosisId(diagnosisId);
        return Result.success(list);
    }
}