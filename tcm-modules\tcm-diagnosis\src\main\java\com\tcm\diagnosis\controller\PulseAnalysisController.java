package com.tcm.diagnosis.controller;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.log.annotation.Log;
import com.tcm.common.log.enums.BusinessType;
import com.tcm.common.security.annotation.RequiresPermissions;
import com.tcm.diagnosis.service.IPulseAnalysisService;
import com.tcm.diagnosis.vo.PulseAnalysisVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 脉象分析控制器
 */
@RestController
@RequestMapping("/pulse/analysis")
public class PulseAnalysisController extends BaseController {

    @Autowired
    private IPulseAnalysisService pulseAnalysisService;

    /**
     * 分析脉诊数据
     */
    @PostMapping("/analyze/{dataId}")
    @RequiresPermissions("diagnosis:pulse:analyze")
    @Log(title = "脉象分析", businessType = BusinessType.INSERT)
    public R<Long> analyzePulseData(@PathVariable Long dataId) {
        return R.ok(pulseAnalysisService.analyzePulseData(dataId));
    }

    /**
     * 批量分析脉诊数据
     */
    @PostMapping("/analyze/batch")
    @RequiresPermissions("diagnosis:pulse:analyze")
    @Log(title = "脉象分析", businessType = BusinessType.INSERT)
    public R<Boolean> analyzePulseDataBatch(@RequestBody List<Long> dataIds) {
        return R.ok(pulseAnalysisService.analyzePulseDataBatch(dataIds));
    }

    /**
     * 获取脉象分析结果
     */
    @GetMapping("/{analysisId}")
    @RequiresPermissions("diagnosis:pulse:query")
    public R<PulseAnalysisVO> getPulseAnalysis(@PathVariable Long analysisId) {
        return R.ok(pulseAnalysisService.getPulseAnalysis(analysisId));
    }

    /**
     * 根据脉诊数据ID获取脉象分析结果
     */
    @GetMapping("/data/{dataId}")
    @RequiresPermissions("diagnosis:pulse:query")
    public R<PulseAnalysisVO> getPulseAnalysisByDataId(@PathVariable Long dataId) {
        return R.ok(pulseAnalysisService.getPulseAnalysisByDataId(dataId));
    }

    /**
     * 获取诊断记录关联的脉象分析结果列表
     */
    @GetMapping("/list/diagnosis/{diagnosisId}")
    @RequiresPermissions("diagnosis:pulse:list")
    public R<List<PulseAnalysisVO>> getPulseAnalysisListByDiagnosisId(@PathVariable Long diagnosisId) {
        return R.ok(pulseAnalysisService.getPulseAnalysisListByDiagnosisId(diagnosisId));
    }

    /**
     * 获取患者的脉象分析结果列表
     */
    @GetMapping("/list/patient/{patientId}")
    @RequiresPermissions("diagnosis:pulse:list")
    public R<List<PulseAnalysisVO>> getPulseAnalysisListByPatientId(@PathVariable Long patientId) {
        return R.ok(pulseAnalysisService.getPulseAnalysisListByPatientId(patientId));
    }

    /**
     * 删除脉象分析结果
     */
    @DeleteMapping("/{analysisId}")
    @RequiresPermissions("diagnosis:pulse:remove")
    @Log(title = "脉象分析", businessType = BusinessType.DELETE)
    public R<Boolean> deletePulseAnalysis(@PathVariable Long analysisId) {
        return R.ok(pulseAnalysisService.deletePulseAnalysis(analysisId));
    }

    /**
     * 导出脉象分析结果报告
     */
    @GetMapping("/export/{analysisId}")
    @RequiresPermissions("diagnosis:pulse:export")
    @Log(title = "脉象分析", businessType = BusinessType.EXPORT)
    public R<String> exportPulseAnalysisReport(@PathVariable Long analysisId) {
        return R.ok(pulseAnalysisService.exportPulseAnalysisReport(analysisId));
    }
}