package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 脉象分析结果视图对象
 */
@Data
public class PulseAnalysisVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分析ID
     */
    private Long id;
    
    /**
     * 分析ID（与id字段兼容）
     */
    private Long analysisId;
    
    /**
     * 脉诊数据ID（兼容方法名）
     */
    private Long pulseId;

    /**
     * 脉诊数据ID
     */
    private Long dataId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 脉象位置（寸/关/尺）
     */
    private String pulsePosition;

    /**
     * 脉象类型（浮/沉/迟/数/虚/实等）
     */
    private String pulseType;

    /**
     * 脉象类型描述
     */
    private String pulseTypeDesc;

    /**
     * 脉率（次/分钟）
     */
    private Integer pulseRate;

    /**
     * 脉力（强/中/弱）
     */
    private String pulseStrength;

    /**
     * 脉律（整齐/不整齐）
     */
    private String pulseRhythm;
    
    /**
     * 脉象宽度
     */
    private String pulseWidth;

    /**
     * 脉象特征
     */
    private String pulseCharacteristics;

    /**
     * 分析结果详情
     */
    private Map<String, Object> analysisDetails;
    
    /**
     * 分析结果详情（JSON格式）
     */
    private String analysisResult;

    /**
     * 临床意义
     */
    private String clinicalImplications;

    /**
     * 分析时间
     */
    private Date analysisTime;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 模型版本
     */
    private String modelVersion;

    /**
     * 分析置信度（0-100）
     */
    private Integer confidence;
}