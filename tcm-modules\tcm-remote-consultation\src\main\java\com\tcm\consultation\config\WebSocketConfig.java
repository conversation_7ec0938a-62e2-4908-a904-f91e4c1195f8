package com.tcm.consultation.config;

import com.tcm.consultation.websocket.ConsultationWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    @Value("${websocket.prefix:/ws}")
    private String prefix;

    @Value("${websocket.endpoint:/consultation}")
    private String endpoint;

    @Value("${websocket.allow-origins:*}")
    private String allowOrigins;

    @Autowired
    private ConsultationWebSocketHandler consultationWebSocketHandler;

    /**
     * 注册WebSocket处理器
     */
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // 添加WebSocket处理器，并允许跨域访问
        registry.addHandler(consultationWebSocketHandler, prefix + endpoint).setAllowedOrigins(allowOrigins.split(","));
    }

    /**
     * 配置ServerEndpointExporter，它会自动注册使用了@ServerEndpoint注解的Bean
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}