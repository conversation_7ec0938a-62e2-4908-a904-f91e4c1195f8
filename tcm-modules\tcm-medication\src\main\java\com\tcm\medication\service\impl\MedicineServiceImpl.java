package com.tcm.medication.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.medication.dto.MedicineDTO;
import com.tcm.medication.entity.Medicine;
import com.tcm.medication.mapper.MedicineMapper;
import com.tcm.medication.service.MedicineService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * 药品服务实现类
 *
 * <AUTHOR>
 */
@Service
public class MedicineServiceImpl extends ServiceImpl<MedicineMapper, Medicine> implements MedicineService {

    @Override
    public IPage<Medicine> page(int page, int pageSize, String medicineName, String medicineCode, Integer medicineType,
            Long categoryId) {
        Page<Medicine> pageable = new Page<>(page, pageSize);
        return baseMapper.selectMedicinePage(pageable, medicineName, medicineCode, medicineType, categoryId);
    }

    @Override
    public MedicineDTO getDetail(Long id) {
        Medicine medicine = getById(id);
        if (medicine == null) {
            return null;
        }
        MedicineDTO dto = new MedicineDTO();
        BeanUtils.copyProperties(medicine, dto);
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(MedicineDTO dto) {
        // 检查药品编码是否已存在
        Medicine existMedicine = baseMapper.selectByCode(dto.getMedicineCode());
        if (existMedicine != null) {
            throw new RuntimeException("药品编码已存在");
        }

        // 检查药品名称是否已存在
        existMedicine = baseMapper.selectByName(dto.getMedicineName());
        if (existMedicine != null) {
            throw new RuntimeException("药品名称已存在");
        }

        Medicine medicine = new Medicine();
        BeanUtils.copyProperties(dto, medicine);
        medicine.setCreateTime(LocalDateTime.now());
        medicine.setUpdateTime(LocalDateTime.now());

        return save(medicine);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MedicineDTO dto) {
        Medicine medicine = getById(dto.getId());
        if (medicine == null) {
            throw new RuntimeException("药品不存在");
        }

        // 检查药品编码是否已被其他药品使用
        if (!StringUtils.hasText(dto.getMedicineCode()) || !dto.getMedicineCode().equals(medicine.getMedicineCode())) {
            Medicine existMedicine = baseMapper.selectByCode(dto.getMedicineCode());
            if (existMedicine != null && !existMedicine.getId().equals(dto.getId())) {
                throw new RuntimeException("药品编码已存在");
            }
        }

        // 检查药品名称是否已被其他药品使用
        if (!StringUtils.hasText(dto.getMedicineName()) || !dto.getMedicineName().equals(medicine.getMedicineName())) {
            Medicine existMedicine = baseMapper.selectByName(dto.getMedicineName());
            if (existMedicine != null && !existMedicine.getId().equals(dto.getId())) {
                throw new RuntimeException("药品名称已存在");
            }
        }

        BeanUtils.copyProperties(dto, medicine);
        medicine.setUpdateTime(LocalDateTime.now());

        return updateById(medicine);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStock(Long id, int stockDelta) {
        Medicine medicine = getById(id);
        if (medicine == null) {
            throw new RuntimeException("药品不存在");
        }

        int newStock = medicine.getStock() + stockDelta;
        if (newStock < 0) {
            throw new RuntimeException("库存不足");
        }

        return baseMapper.updateStock(id, newStock) > 0;
    }

    @Override
    public boolean checkExist(Long id) {
        return getById(id) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enable(Long id) {
        Medicine medicine = getById(id);
        if (medicine == null) {
            throw new RuntimeException("药品不存在");
        }

        if (medicine.getStatus() == 1) {
            return true;
        }

        medicine.setStatus(1);
        medicine.setUpdateTime(LocalDateTime.now());
        return updateById(medicine);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disable(Long id) {
        Medicine medicine = getById(id);
        if (medicine == null) {
            throw new RuntimeException("药品不存在");
        }

        if (medicine.getStatus() == 0) {
            return true;
        }

        medicine.setStatus(0);
        medicine.setUpdateTime(LocalDateTime.now());
        return updateById(medicine);
    }
}