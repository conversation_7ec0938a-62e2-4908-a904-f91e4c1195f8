package com.tcm.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 数据同步记录实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_data_sync_record")
public class DataSyncRecord {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的系统配置ID
     */
    private Long configId;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 同步类型（患者信息、药品信息、检查结果等）
     */
    private String syncType;

    /**
     * 同步方向（IN-导入数据，OUT-导出数据）
     */
    private String syncDirection;

    /**
     * 同步总数据量
     */
    private Integer totalCount;

    /**
     * 成功数据量
     */
    private Integer successCount;

    /**
     * 失败数据量
     */
    private Integer failCount;

    /**
     * 同步开始时间
     */
    private LocalDateTime startTime;

    /**
     * 同步结束时间
     */
    private LocalDateTime endTime;

    /**
     * 耗时(毫秒)
     */
    private Long duration;

    /**
     * 同步状态（0-同步中，1-成功，2-部分成功，3-失败）
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 操作人
     */
    private String operateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 备注
     */
    private String remark;
}