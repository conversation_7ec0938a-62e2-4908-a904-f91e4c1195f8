package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.diagnosis.domain.PulseAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 脉象分析结果Mapper接口
 */
@Mapper
public interface PulseAnalysisMapper extends BaseMapper<PulseAnalysis> {

    /**
     * 根据脉诊数据ID查询脉象分析结果
     *
     * @param dataId 脉诊数据ID
     * @return 脉象分析结果
     */
    PulseAnalysis selectPulseAnalysisByDataId(@Param("dataId") Long dataId);

    /**
     * 根据诊断ID查询脉象分析结果列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 脉象分析结果列表
     */
    List<PulseAnalysis> selectPulseAnalysisByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据患者ID查询脉象分析结果列表
     *
     * @param patientId 患者ID
     * @return 脉象分析结果列表
     */
    List<PulseAnalysis> selectPulseAnalysisByPatientId(@Param("patientId") Long patientId);
}