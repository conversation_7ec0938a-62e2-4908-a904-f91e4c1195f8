package com.tcm.device.exception;

/**
 * 设备数据异常
 * 
 * <AUTHOR>
 */
public class DeviceDataException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public DeviceDataException() {
        super();
    }

    public DeviceDataException(String message) {
        super(message);
    }

    public DeviceDataException(String message, Throwable cause) {
        super(message, cause);
    }

    public DeviceDataException(Throwable cause) {
        super(cause);
    }
}
