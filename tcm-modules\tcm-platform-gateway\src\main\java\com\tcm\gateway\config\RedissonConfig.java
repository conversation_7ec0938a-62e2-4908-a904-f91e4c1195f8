package com.tcm.gateway.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Redisson配置类
 *
 * <AUTHOR>
 */
@Configuration
public class RedissonConfig {

    @Autowired
    private CommonConfig commonConfig;

    @Bean
    @Primary
    public RedissonClient redisson() {
        Config config = new Config();
        String redisUrl = String.format("redis://%s:%d", commonConfig.getRedis().getHost(),
                commonConfig.getRedis().getPort());

        if (commonConfig.getRedis().getPassword() != null && !commonConfig.getRedis().getPassword().isEmpty()) {
            config.useSingleServer().setAddress(redisUrl).setPassword(commonConfig.getRedis().getPassword())
                    .setDatabase(commonConfig.getRedis().getDatabase());
        } else {
            config.useSingleServer().setAddress(redisUrl).setDatabase(commonConfig.getRedis().getDatabase());
        }

        return Redisson.create(config);
    }
}
