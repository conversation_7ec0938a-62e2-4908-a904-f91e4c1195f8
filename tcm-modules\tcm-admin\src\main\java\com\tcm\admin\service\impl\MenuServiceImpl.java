package com.tcm.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.admin.dto.MenuDTO;
import com.tcm.admin.entity.Menu;
import com.tcm.admin.mapper.MenuMapper;
import com.tcm.admin.service.MenuService;
import com.tcm.admin.vo.MenuVO;
import com.tcm.admin.vo.TreeSelectVO;
import com.tcm.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements MenuService {

    /**
     * 查询系统菜单列表
     *
     * @param menuDTO 菜单查询条件
     * @return 菜单列表
     */
    @Override
    public List<MenuVO> selectMenuList(MenuDTO menuDTO) {
        Menu menu = new Menu();
        if (menuDTO != null) {
            BeanUtils.copyProperties(menuDTO, menu);
        }

        List<Menu> menus = baseMapper.selectMenuList(menu);
        return menus.stream()
                .map(this::convertToVO)
                .sorted(Comparator.comparing(MenuVO::getOrderNum))
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID查询菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    public List<MenuVO> selectMenuListByUserId(Long userId) {
        List<Menu> menus = baseMapper.selectMenuListByUserId(userId);
        return menus.stream()
                .map(this::convertToVO)
                .sorted(Comparator.comparing(MenuVO::getOrderNum))
                .collect(Collectors.toList());
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(Long userId) {
        List<String> perms = baseMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();

        for (String perm : perms) {
            if (StringUtils.hasText(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }

        return permsSet;
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId) {
        return baseMapper.selectMenuListByRoleId(roleId);
    }

    /**
     * 构建前端所需要的菜单树
     *
     * @param menus 菜单列表
     * @return 菜单树
     */
    @Override
    public List<MenuVO> buildMenuTree(List<MenuVO> menus) {
        List<MenuVO> returnList = new ArrayList<>();
        List<Long> tempList = menus.stream().map(MenuVO::getId).collect(Collectors.toList());

        for (MenuVO menu : menus) {
            // 如果是顶级节点，遍历该父节点的所有子节点
            if (!tempList.contains(menu.getParentId())) {
                recursionBuild(menus, menu);
                returnList.add(menu);
            }
        }

        if (returnList.isEmpty()) {
            returnList = menus;
        }

        return returnList;
    }

    /**
     * 递归构建菜单树
     */
    private void recursionBuild(List<MenuVO> menus, MenuVO parent) {
        // 得到子节点列表
        List<MenuVO> children = getChildList(menus, parent);
        parent.setChildren(children);

        // 递归获取子节点的子节点
        for (MenuVO child : children) {
            if (hasChild(menus, child)) {
                recursionBuild(menus, child);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<MenuVO> getChildList(List<MenuVO> menus, MenuVO parent) {
        return menus.stream()
                .filter(menu -> parent.getId().equals(menu.getParentId()))
                .sorted(Comparator.comparing(MenuVO::getOrderNum))
                .collect(Collectors.toList());
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<MenuVO> menus, MenuVO parent) {
        return menus.stream().anyMatch(menu -> parent.getId().equals(menu.getParentId()));
    }

    /**
     * 构建前端所需要的树形选择框数据
     *
     * @param menus 菜单列表
     * @return 下拉树选项列表
     */
    @Override
    public List<TreeSelectVO> buildMenuTreeSelect(List<MenuVO> menus) {
        List<MenuVO> menuTrees = buildMenuTree(menus);
        return menuTrees.stream().map(TreeSelectVO::new).collect(Collectors.toList());
    }

    /**
     * 根据菜单ID查询菜单
     *
     * @param id 菜单ID
     * @return 菜单信息
     */
    @Override
    public MenuVO getMenuById(Long id) {
        Menu menu = getById(id);
        if (menu == null) {
            throw new BusinessException("菜单不存在");
        }

        MenuVO menuVO = convertToVO(menu);

        // 获取父菜单名称
        if (menuVO.getParentId() > 0) {
            Menu parentMenu = getById(menuVO.getParentId());
            if (parentMenu != null) {
                menuVO.setParentName(parentMenu.getMenuName());
            }
        }

        return menuVO;
    }

    /**
     * 创建菜单
     *
     * @param menuDTO 菜单信息
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMenu(MenuDTO menuDTO) {
        Menu menu = new Menu();
        BeanUtils.copyProperties(menuDTO, menu);

        // 设置默认值
        if (menu.getParentId() == null) {
            menu.setParentId(0L);
        }
        if (menu.getIsFrame() == null) {
            menu.setIsFrame(0);
        }
        if (menu.getIsCache() == null) {
            menu.setIsCache(0);
        }
        if (menu.getVisible() == null) {
            menu.setVisible(0);
        }
        if (menu.getStatus() == null) {
            menu.setStatus(0);
        }
        menu.setCreateTime(LocalDateTime.now());

        return save(menu);
    }

    /**
     * 更新菜单
     *
     * @param menuDTO 菜单信息
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(MenuDTO menuDTO) {
        Menu oldMenu = getById(menuDTO.getId());
        if (oldMenu == null) {
            throw new BusinessException("菜单不存在");
        }

        // 如果当前菜单的父菜单是自己，则提示错误
        if (menuDTO.getId().equals(menuDTO.getParentId())) {
            throw new BusinessException("修改菜单'" + menuDTO.getMenuName() + "'失败，上级菜单不能选择自己");
        }

        Menu menu = new Menu();
        BeanUtils.copyProperties(menuDTO, menu);
        menu.setUpdateTime(LocalDateTime.now());

        return updateById(menu);
    }

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(Long menuId) {
        // 判断是否存在子菜单
        if (baseMapper.hasChildByMenuId(menuId) > 0) {
            throw new BusinessException("存在子菜单，不允许删除");
        }

        // 判断菜单是否已分配角色
        if (baseMapper.checkMenuExistRole(menuId) > 0) {
            throw new BusinessException("菜单已分配角色，不允许删除");
        }

        return removeById(menuId);
    }

    /**
     * 校验菜单名称是否唯一
     *
     * @param menuDTO 菜单信息
     * @return 结果
     */
    @Override
    public boolean checkMenuNameUnique(MenuDTO menuDTO) {
        Long menuId = menuDTO.getId() == null ? -1L : menuDTO.getId();

        LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Menu::getMenuName, menuDTO.getMenuName())
                .eq(Menu::getParentId, menuDTO.getParentId());

        Menu menu = getOne(queryWrapper);
        return menu == null || menu.getId().equals(menuId);
    }

    /**
     * 将实体转换为VO
     */
    private MenuVO convertToVO(Menu menu) {
        if (menu == null) {
            return null;
        }

        MenuVO menuVO = new MenuVO();
        BeanUtils.copyProperties(menu, menuVO);

        // 设置菜单类型描述
        if (StringUtils.hasText(menu.getMenuType())) {
            switch (menu.getMenuType()) {
                case "M":
                    menuVO.setMenuTypeDesc("目录");
                    break;
                case "C":
                    menuVO.setMenuTypeDesc("菜单");
                    break;
                case "F":
                    menuVO.setMenuTypeDesc("按钮");
                    break;
                default:
                    menuVO.setMenuTypeDesc("未知");
            }
        }

        // 设置状态描述
        if (menu.getStatus() != null) {
            menuVO.setStatusDesc(menu.getStatus() == 0 ? "正常" : "停用");
        }

        return menuVO;
    }
}