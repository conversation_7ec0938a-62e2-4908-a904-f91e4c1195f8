package com.tcm.platform.admin.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.platform.admin.entity.SysMenu;
import com.tcm.platform.admin.mapper.SysMenuMapper;
import com.tcm.platform.admin.mapper.SysRoleMenuMapper;
import com.tcm.platform.admin.service.SysMenuService;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统菜单服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysRoleMenuMapper roleMenuMapper;

    /**
     * 查询菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu) {
        return menuMapper.selectMenuList(menu);
    }

    /**
     * 构建前端路由所需要的菜单树
     */
    @Override
    public List<SysMenu> buildMenuTree(Long userId) {
        List<SysMenu> menus = menuMapper.selectMenuTreeByUserId(userId);
        return buildTree(menus, 0L);
    }

    /**
     * 根据用户ID查询菜单权限
     */
    @Override
    public List<String> selectMenuPermsByUserId(Long userId) {
        return menuMapper.selectMenuPermsByUserId(userId);
    }

    /**
     * 根据角色ID查询菜单ID列表
     */
    @Override
    public List<Long> selectMenuIdsByRoleId(Long roleId) {
        return menuMapper.selectMenuIdsByRoleId(roleId);
    }

    /**
     * 根据菜单ID查询信息
     */
    @Override
    public SysMenu selectMenuById(Long menuId) {
        return menuMapper.selectById(menuId);
    }

    /**
     * 新增菜单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertMenu(SysMenu menu) {
        // 设置创建时间等字段
        LocalDateTime now = LocalDateTime.now();
        menu.setCreateTime(now);
        menu.setUpdateTime(now);
        menu.setDelFlag(0);
        menu.setStatus(1); // 默认启用

        return menuMapper.insert(menu) > 0;
    }

    /**
     * 修改菜单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(SysMenu menu) {
        menu.setUpdateTime(LocalDateTime.now());
        return menuMapper.updateById(menu) > 0;
    }

    /**
     * 删除菜单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenuById(Long menuId) {
        // 检查是否有子菜单
        if (hasChildrenMenu(menuId)) {
            throw new RuntimeException("存在子菜单，不允许删除");
        }

        // 删除菜单
        int rows = menuMapper.deleteById(menuId);
        if (rows <= 0) {
            return false;
        }

        // 删除角色菜单关联
        roleMenuMapper.deleteByMenuId(menuId);

        return true;
    }

    /**
     * 检查菜单是否有子菜单
     */
    @Override
    public boolean hasChildrenMenu(Long menuId) {
        return menuMapper.hasChildByMenuId(menuId) > 0;
    }

    /**
     * 构建菜单树
     *
     * @param menus    菜单列表
     * @param parentId 父菜单ID
     * @return 菜单树
     */
    private List<SysMenu> buildTree(List<SysMenu> menus, Long parentId) {
        return menus.stream()
                .filter(menu -> menu.getParentId().equals(parentId))
                .sorted(Comparator.comparing(SysMenu::getSort))
                .peek(menu -> menu.setChildren(buildTree(menus, menu.getId())))
                .collect(Collectors.toList());
    }
}