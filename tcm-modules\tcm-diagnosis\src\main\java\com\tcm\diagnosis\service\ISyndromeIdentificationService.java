package com.tcm.diagnosis.service;

import com.tcm.diagnosis.domain.SyndromeIdentification;
import com.tcm.diagnosis.dto.SyndromeIdentificationDTO;
import com.tcm.diagnosis.vo.SyndromeIdentificationVO;
import com.tcm.diagnosis.vo.SimilarCaseVO;

import java.util.List;

/**
 * 辨证服务接口
 */
public interface ISyndromeIdentificationService {

    /**
     * 进行辨证
     *
     * @param syndromeDTO 辨证数据
     * @return 辨证结果ID
     */
    Long identifySyndrome(SyndromeIdentificationDTO syndromeDTO);

    /**
     * 获取辨证结果
     *
     * @param syndromeId 辨证结果ID
     * @return 辨证结果视图对象
     */
    SyndromeIdentificationVO getSyndromeIdentification(Long syndromeId);

    /**
     * 根据诊断ID获取辨证结果
     *
     * @param diagnosisId 诊断记录ID
     * @return 辨证结果视图对象
     */
    SyndromeIdentificationVO getSyndromeByDiagnosisId(Long diagnosisId);

    /**
     * 获取患者的辨证结果列表
     *
     * @param patientId 患者ID
     * @return 辨证结果视图对象列表
     */
    List<SyndromeIdentificationVO> getSyndromeListByPatientId(Long patientId);

    /**
     * 医生确认辨证结果
     *
     * @param syndromeId 辨证结果ID
     * @param doctorId   医生ID
     * @return 是否成功
     */
    boolean confirmSyndrome(Long syndromeId, Long doctorId);

    /**
     * 修改辨证结果
     *
     * @param syndromeDTO 辨证数据
     * @return 是否成功
     */
    boolean updateSyndrome(SyndromeIdentificationDTO syndromeDTO);

    /**
     * 删除辨证结果
     *
     * @param syndromeId 辨证结果ID
     * @return 是否成功
     */
    boolean deleteSyndrome(Long syndromeId);

    /**
     * 获取相似病例
     *
     * @param syndromeId 辨证结果ID
     * @param limit      限制数量
     * @return 相似病例视图对象列表
     */
    List<SimilarCaseVO> getSimilarCases(Long syndromeId, Integer limit);

    /**
     * 导出辨证报告
     *
     * @param syndromeId 辨证结果ID
     * @return 报告文件路径
     */
    String exportSyndromeReport(Long syndromeId);
}