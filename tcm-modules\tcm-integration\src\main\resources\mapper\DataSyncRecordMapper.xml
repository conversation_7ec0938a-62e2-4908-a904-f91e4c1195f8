<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.integration.mapper.DataSyncRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.integration.entity.DataSyncRecord">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="system_code" property="systemCode" />
        <result column="sync_type" property="syncType" />
        <result column="sync_direction" property="syncDirection" />
        <result column="total_count" property="totalCount" />
        <result column="success_count" property="successCount" />
        <result column="fail_count" property="failCount" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="duration" property="duration" />
        <result column="status" property="status" />
        <result column="fail_reason" property="failReason" />
        <result column="operate_by" property="operateBy" />
        <result column="create_time" property="createTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 查询最近一次同步记录 -->
    <select id="selectLastSyncRecord" resultMap="BaseResultMap">
        SELECT *
        FROM sys_data_sync_record
        WHERE system_code = #{systemCode}
          AND sync_type = #{syncType}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 统计同步成功率 -->
    <select id="calculateSuccessRate" resultType="java.lang.Double">
        SELECT
            IFNULL(SUM(success_count) / SUM(total_count) * 100, 0) as success_rate
        FROM
            sys_data_sync_record
        WHERE
            system_code = #{systemCode}
            AND sync_type = #{syncType}
            AND total_count > 0
    </select>
</mapper> 