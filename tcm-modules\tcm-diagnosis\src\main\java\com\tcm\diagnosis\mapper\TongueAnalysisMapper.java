package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.diagnosis.domain.TongueAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 舌象分析结果Mapper接口
 */
@Mapper
public interface TongueAnalysisMapper extends BaseMapper<TongueAnalysis> {

    /**
     * 根据图像ID查询舌象分析结果
     *
     * @param imageId 图像ID
     * @return 舌象分析结果
     */
    TongueAnalysis selectTongueAnalysisByImageId(@Param("imageId") Long imageId);

    /**
     * 根据诊断ID查询舌象分析结果列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 舌象分析结果列表
     */
    List<TongueAnalysis> selectTongueAnalysisByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据患者ID查询舌象分析结果列表
     *
     * @param patientId 患者ID
     * @return 舌象分析结果列表
     */
    List<TongueAnalysis> selectTongueAnalysisByPatientId(@Param("patientId") Long patientId);
}