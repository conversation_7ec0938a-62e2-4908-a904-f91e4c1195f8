package com.tcm.file.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件信息实体类
 */
@Data
@TableName("file_info")
public class FileInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件ID
     */
    @TableId(value = "file_id", type = IdType.AUTO)
    private Long fileId;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 原始文件名
     */
    private String originalName;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件存储路径
     */
    private String filePath;

    /**
     * 文件访问URL
     */
    private String fileUrl;

    /**
     * 存储类型（0:本地存储 1:MinIO 2:阿里云OSS）
     */
    private Integer storageType;

    /**
     * 文件类型（0:普通文件 1:图片 2:视频 3:音频 4:文档）
     */
    private Integer fileType;

    /**
     * 文件MIME类型
     */
    private String mimeType;

    /**
     * 文件MD5值
     */
    private String md5;

    /**
     * 文件状态（0:临时 1:永久）
     */
    private Integer status;

    /**
     * 业务模块
     */
    private String module;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0:未删除 1:已删除）
     */
    private Integer delFlag;
} 