package com.tcm.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.device.domain.DeviceData;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 设备数据服务接口
 * 
 * <AUTHOR>
 */
public interface IDeviceDataService extends IService<DeviceData> {

    /**
     * 保存设备数据
     * 
     * @param deviceId  设备ID
     * @param dataType  数据类型
     * @param dataValue 数据值
     * @param unit      单位
     * @return 结果
     */
    boolean saveDeviceData(Long deviceId, String dataType, String dataValue, String unit);

    /**
     * 批量保存设备数据
     * 
     * @param deviceId 设备ID
     * @param dataMap  数据集合 (key: 数据类型, value: 数据值)
     * @param unitMap  单位集合 (key: 数据类型, value: 单位)
     * @return 结果
     */
    boolean batchSaveDeviceData(Long deviceId, Map<String, String> dataMap, Map<String, String> unitMap);

    /**
     * 分页查询设备数据
     * 
     * @param page      分页参数
     * @param deviceId  设备ID
     * @param dataType  数据类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 设备数据分页列表
     */
    IPage<DeviceData> selectDeviceDataPage(IPage<DeviceData> page, Long deviceId, String dataType, Date startTime,
            Date endTime);

    /**
     * 获取指定设备最新数据
     * 
     * @param deviceId 设备ID
     * @return 设备最新数据
     */
    List<DeviceData> getLatestDeviceData(Long deviceId);

    /**
     * 获取指定设备、指定类型的最新数据
     * 
     * @param deviceId 设备ID
     * @param dataType 数据类型
     * @return 设备最新数据
     */
    DeviceData getLatestDeviceData(Long deviceId, String dataType);

    /**
     * 获取指定设备、指定时间范围的数据
     * 
     * @param deviceId  设备ID
     * @param dataType  数据类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 设备数据列表
     */
    List<DeviceData> getDeviceDataByTimeRange(Long deviceId, String dataType, Date startTime, Date endTime);

    /**
     * 删除指定时间之前的数据
     * 
     * @param time 时间
     * @return 结果
     */
    int deleteDeviceDataBefore(Date time);
}