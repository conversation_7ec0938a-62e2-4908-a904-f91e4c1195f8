package com.tcm.payment.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 支付模块全局异常处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class PaymentExceptionHandler {

    /**
     * 处理支付业务异常
     */
    @ExceptionHandler(PaymentBusinessException.class)
    public ResponseEntity<Map<String, Object>> handlePaymentBusinessException(PaymentBusinessException e) {
        log.error("支付业务异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        result.put("message", "支付业务处理失败: " + e.getMessage());
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理支付数据异常
     */
    @ExceptionHandler(PaymentDataException.class)
    public ResponseEntity<Map<String, Object>> handlePaymentDataException(PaymentDataException e) {
        log.error("支付数据异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        result.put("message", "支付数据错误: " + e.getMessage());
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        result.put("message", "系统内部错误");
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
}
