package com.tcm.knowledge.controller;

import com.tcm.knowledge.service.IKnowledgeService;
import com.tcm.knowledge.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 知识库控制器
 */
@RestController
@RequestMapping("/knowledge")
@Api(tags = "中医知识库接口")
public class KnowledgeController {

    @Autowired
    private IKnowledgeService knowledgeService;

    /**
     * 知识库全文检索
     */
    @GetMapping("/search")
    @ApiOperation("知识库全文检索")
    public Result<PageVO<KnowledgeItemVO>> search(
            @ApiParam("检索关键词") @RequestParam String keyword,
            @ApiParam("知识类别") @RequestParam(required = false) String category,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页条数") @RequestParam(defaultValue = "10") Integer pageSize) {
        PageVO<KnowledgeItemVO> result = knowledgeService.search(keyword, category, pageNum, pageSize);
        return Result.success(result);
    }

    /**
     * 获取经典著作详情
     */
    @GetMapping("/classics/{id}")
    @ApiOperation("获取经典著作详情")
    public Result<ClassicDetailVO> getClassicDetail(@PathVariable Long id) {
        ClassicDetailVO result = knowledgeService.getClassicDetail(id);
        return Result.success(result);
    }

    /**
     * 获取医案列表
     */
    @GetMapping("/medical-cases")
    @ApiOperation("获取医案列表")
    public Result<PageVO<MedicalCaseVO>> getMedicalCases(
            @ApiParam("过滤条件：症状/疾病/证型") @RequestParam(required = false) String filter,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页条数") @RequestParam(defaultValue = "10") Integer pageSize) {
        PageVO<MedicalCaseVO> result = knowledgeService.getMedicalCases(filter, pageNum, pageSize);
        return Result.success(result);
    }

    /**
     * 获取处方模板
     */
    @GetMapping("/prescription-templates")
    @ApiOperation("获取处方模板")
    public Result<List<PrescriptionTemplateVO>> getPrescriptionTemplates(
            @ApiParam("证型") @RequestParam String syndrome) {
        List<PrescriptionTemplateVO> result = knowledgeService.getPrescriptionTemplates(syndrome);
        return Result.success(result);
    }
    
    /**
     * 获取知识图谱数据
     */
    @GetMapping("/knowledge-graph")
    @ApiOperation("获取知识图谱数据")
    public Result<KnowledgeGraphVO> getKnowledgeGraph(
            @ApiParam("中心概念") @RequestParam String concept,
            @ApiParam("展开深度") @RequestParam(defaultValue = "2") Integer depth) {
        KnowledgeGraphVO result = knowledgeService.getKnowledgeGraph(concept, depth);
        return Result.success(result);
    }
    
    /**
     * 获取知识分类列表
     */
    @GetMapping("/categories")
    @ApiOperation("获取知识分类列表")
    public Result<List<KnowledgeCategoryVO>> getCategories() {
        List<KnowledgeCategoryVO> result = knowledgeService.getCategories();
        return Result.success(result);
    }
}