package com.tcm.doctor.dto;

import com.tcm.common.core.domain.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 问诊查询条件类
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ConsultationQuery extends PageQuery {
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 科室
     */
    private String department;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 问诊类型
     */
    private Integer type;
    
    /**
     * 开始日期
     */
    private Date beginDate;
    
    /**
     * 结束日期
     */
    private Date endDate;
    
    /**
     * 问诊状态
     */
    private Integer status;
    
    /**
     * 支付状态
     */
    private Integer payStatus;
    
    /**
     * 主诉关键词
     */
    private String keyword;
} 