server:
  port: ${tcm.service.order.port:${TCM_ORDER_PORT:9101}}
  servlet:
    context-path: /api

spring:
  application:
    name: tcm-order
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/META-INF/tcm-common.yml
  # 数据源和Redis配置从application-common.yml中继承
  datasource:
    # 直接使用HikariCP数据源，避免Druid依赖问题
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${tcm.service.mysql.url}
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 900000
      auto-commit: true
      connection-test-query: SELECT 1
  cloud:
    # 确保服务注册正常
    discovery:
      enabled: true
    nacos:
      discovery:
        enabled: true
        register-enabled: true
        heart-beat-interval: 5000
        heart-beat-timeout: 15000

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.order.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.tcm.order: debug
    org.springframework: warn
    com.alibaba.nacos: warn
