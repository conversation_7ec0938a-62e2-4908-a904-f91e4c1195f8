package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 舌象分析结果视图对象
 */
@Data
public class TongueAnalysisVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分析ID
     */
    private Long id;

    /**
     * 图像ID
     */
    private Long imageId;

    /**
     * 缩略图（Base64编码）
     */
    private String thumbnail;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 舌色
     */
    private String tongueColor;

    /**
     * 舌色描述
     */
    private String tongueColorDesc;

    /**
     * 舌形
     */
    private String tongueShape;

    /**
     * 舌形描述
     */
    private String tongueShapeDesc;

    /**
     * 舌苔颜色
     */
    private String coatingColor;

    /**
     * 舌苔颜色描述
     */
    private String coatingColorDesc;

    /**
     * 舌苔厚度
     */
    private String coatingThickness;

    /**
     * 舌苔厚度描述
     */
    private String coatingThicknessDesc;

    /**
     * 舌态特征
     */
    private String tongueCharacteristics;

    /**
     * 临床意义
     */
    private String clinicalImplications;

    /**
     * 分析时间
     */
    private Date analysisTime;

    /**
     * 分析置信度（0-100）
     */
    private Integer confidence;
}