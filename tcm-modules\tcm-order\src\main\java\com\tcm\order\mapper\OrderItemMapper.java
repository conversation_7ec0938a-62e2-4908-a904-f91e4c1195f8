package com.tcm.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.order.entity.OrderItem;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单明细Mapper接口
 *
 * <AUTHOR> Team
 */
@Repository
public interface OrderItemMapper extends BaseMapper<OrderItem> {

    /**
     * 批量插入订单明细
     *
     * @param orderItems 订单明细列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<OrderItem> orderItems);
    
    /**
     * 根据订单ID查询订单明细
     *
     * @param orderId 订单ID
     * @return 订单明细列表
     */
    List<OrderItem> selectByOrderId(@Param("orderId") Long orderId);
    
    /**
     * 根据订单ID删除订单明细（逻辑删除）
     *
     * @param orderId 订单ID
     * @return 影响行数
     */
    int deleteByOrderId(@Param("orderId") Long orderId);
}
