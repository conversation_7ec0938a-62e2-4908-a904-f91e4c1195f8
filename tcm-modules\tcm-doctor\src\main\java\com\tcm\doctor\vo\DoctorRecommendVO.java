package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生推荐视图对象
 */
@Data
public class DoctorRecommendVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生编号
     */
    private String doctorCode;
    
    /**
     * 职称
     */
    private Integer title;
    
    /**
     * 职称名称
     */
    private String titleName;
    
    /**
     * 专长
     */
    private String specialty;
    
    /**
     * 简介
     */
    private String introduction;
    
    /**
     * 所属医院
     */
    private String hospital;
    
    /**
     * 所属科室
     */
    private String department;
    
    /**
     * 工作年限
     */
    private Integer workYears;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 平均评分
     */
    private Double averageScore;
    
    /**
     * 评价数量
     */
    private Integer evaluationCount;
    
    /**
     * 擅长疾病
     */
    private String goodAtDiseases;
    
    /**
     * 推荐理由
     */
    private String recommendReason;
    
    /**
     * 近期出诊信息
     */
    private DoctorScheduleVO recentSchedule;
}