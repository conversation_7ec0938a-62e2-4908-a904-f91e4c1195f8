package com.tcm.assistant.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 图像分析结果VO
 */
@Data
@Schema(description = "图像分析结果")
public class ImageAnalysisVO {

    @Schema(description = "分析结果描述")
    private String analysisResult;

    @Schema(description = "图像类型")
    private String imageType;

    @Schema(description = "特征列表")
    private Map<String, Object> features;

    @Schema(description = "舌象颜色（仅舌象分析时有效）")
    private String tongueColor;

    @Schema(description = "舌苔颜色（仅舌象分析时有效）")
    private String coatingColor;

    @Schema(description = "舌象形状（仅舌象分析时有效）")
    private String tongueShape;

    @Schema(description = "舌苔厚度（仅舌象分析时有效）")
    private String coatingThickness;

    @Schema(description = "置信度")
    private Double confidence;

    @Schema(description = "处理时间（毫秒）")
    private Long processingTime;
}