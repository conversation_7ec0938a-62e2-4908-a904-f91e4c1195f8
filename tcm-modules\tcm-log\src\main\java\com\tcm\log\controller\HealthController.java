package com.tcm.log.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 健康检查控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/")
public class HealthController {
    
    @Value("${spring.application.name}")
    private String applicationName;
    
    @Value("${server.port}")
    private String serverPort;
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", applicationName);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 服务状态
     */
    @GetMapping("/status")
    public Map<String, Object> status() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "active");
        result.put("service", applicationName);
        result.put("port", serverPort);
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
} 