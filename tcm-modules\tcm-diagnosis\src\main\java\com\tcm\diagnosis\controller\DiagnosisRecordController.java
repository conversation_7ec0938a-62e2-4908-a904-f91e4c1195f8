package com.tcm.diagnosis.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.R;
import com.tcm.diagnosis.dto.DiagnosisQuery;
import com.tcm.diagnosis.dto.DiagnosisRecordDTO;
import com.tcm.diagnosis.service.IDiagnosisRecordService;
import com.tcm.diagnosis.vo.DiagnosisRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 诊断记录控制器
 */
@RestController
@RequestMapping("/diagnosis")
public class DiagnosisRecordController {
    
    @Autowired
    private IDiagnosisRecordService diagnosisRecordService;
    
    /**
     * 获取诊断记录列表
     */
    @GetMapping("/list")
    public R<IPage<DiagnosisRecordVO>> list(DiagnosisQuery query) {
        IPage<DiagnosisRecordVO> page = diagnosisRecordService.listDiagnosisRecords(query);
        return R.ok(page);
    }
    
    /**
     * 获取诊断记录详情
     */
    @GetMapping("/{diagnosisId}")
    public R<DiagnosisRecordVO> getInfo(@PathVariable Long diagnosisId) {
        DiagnosisRecordVO diagnosisRecord = diagnosisRecordService.getDiagnosisRecordById(diagnosisId);
        return R.ok(diagnosisRecord);
    }
    
    /**
     * 根据患者ID获取诊断记录列表
     */
    @GetMapping("/patient/{patientId}")
    public R<List<DiagnosisRecordVO>> getDiagnosisByPatient(@PathVariable Long patientId) {
        List<DiagnosisRecordVO> diagnosisRecords = diagnosisRecordService.getDiagnosisRecordsByPatientId(patientId);
        return R.ok(diagnosisRecords);
    }
    
    /**
     * 根据医生ID获取诊断记录列表
     */
    @GetMapping("/doctor/{doctorId}")
    public R<List<DiagnosisRecordVO>> getDiagnosisByDoctor(@PathVariable Long doctorId) {
        List<DiagnosisRecordVO> diagnosisRecords = diagnosisRecordService.getDiagnosisRecordsByDoctorId(doctorId);
        return R.ok(diagnosisRecords);
    }
    
    /**
     * 新增诊断记录
     */
    @PostMapping
    public R<Boolean> add(@RequestBody @Valid DiagnosisRecordDTO diagnosisRecordDTO) {
        boolean result = diagnosisRecordService.addDiagnosisRecord(diagnosisRecordDTO);
        return R.ok(result);
    }
    
    /**
     * 修改诊断记录
     */
    @PutMapping
    public R<Boolean> update(@RequestBody @Valid DiagnosisRecordDTO diagnosisRecordDTO) {
        boolean result = diagnosisRecordService.updateDiagnosisRecord(diagnosisRecordDTO);
        return R.ok(result);
    }
    
    /**
     * 删除诊断记录
     */
    @DeleteMapping("/{diagnosisId}")
    public R<Boolean> remove(@PathVariable Long diagnosisId) {
        boolean result = diagnosisRecordService.deleteDiagnosisRecord(diagnosisId);
        return R.ok(result);
    }
    
    /**
     * 批量删除诊断记录
     */
    @DeleteMapping("/batch/{diagnosisIds}")
    public R<Boolean> removeBatch(@PathVariable Long[] diagnosisIds) {
        boolean result = diagnosisRecordService.deleteDiagnosisRecords(diagnosisIds);
        return R.ok(result);
    }
    
    /**
     * 修改诊断记录状态
     */
    @PutMapping("/status/{diagnosisId}/{status}")
    public R<Boolean> updateStatus(@PathVariable Long diagnosisId, @PathVariable Integer status) {
        boolean result = diagnosisRecordService.updateDiagnosisRecordStatus(diagnosisId, status);
        return R.ok(result);
    }
} 