package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生评价视图对象
 */
@Data
public class DoctorEvaluationVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 评价ID
     */
    private Long evaluationId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生职称
     */
    private String titleName;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 诊断日期
     */
    private Date diagnosisDate;
    
    /**
     * 评分（1-5分）
     */
    private Integer score;
    
    /**
     * 评价内容
     */
    private String content;
    
    /**
     * 是否匿名（0-否 1-是）
     */
    private Integer anonymous;
    
    /**
     * 医生回复
     */
    private String reply;
    
    /**
     * 回复时间
     */
    private Date replyTime;
    
    /**
     * 状态（0-显示 1-隐藏）
     */
    private Integer status;
    
    /**
     * 评价时间
     */
    private Date createTime;
} 