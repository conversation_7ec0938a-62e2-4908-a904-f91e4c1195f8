package com.tcm.analytics.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcm.analytics.entity.AnalyticsDashboard;
import com.tcm.analytics.mapper.AnalyticsDashboardMapper;
import com.tcm.analytics.service.AnalyticsDashboardService;

import lombok.extern.slf4j.Slf4j;

/**
 * 分析仪表盘服务实现类
 */
@Service
@Slf4j
public class AnalyticsDashboardServiceImpl extends ServiceImpl<AnalyticsDashboardMapper, AnalyticsDashboard>
        implements AnalyticsDashboardService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createDashboard(AnalyticsDashboard dashboard) {
        log.info("创建仪表盘: {}", dashboard.getName());
        dashboard.setCreateTime(new Date());
        dashboard.setUpdateTime(new Date());
        save(dashboard);
        return dashboard.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDashboard(AnalyticsDashboard dashboard) {
        log.info("更新仪表盘: {}", dashboard.getId());
        dashboard.setUpdateTime(new Date());
        return updateById(dashboard);
    }

    @Override
    public AnalyticsDashboard getDashboardDetail(Long dashboardId) {
        return getById(dashboardId);
    }

    @Override
    public Page<AnalyticsDashboard> listDashboardsByPage(Page<AnalyticsDashboard> page, Map<String, Object> params) {
        LambdaQueryWrapper<AnalyticsDashboard> queryWrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (params.containsKey("name")) {
            queryWrapper.like(AnalyticsDashboard::getName, params.get("name"));
        }
        
        if (params.containsKey("creator")) {
            queryWrapper.eq(AnalyticsDashboard::getCreator, params.get("creator"));
        }
        
        // 排序
        queryWrapper.orderByDesc(AnalyticsDashboard::getUpdateTime);
        
        return page(page, queryWrapper);
    }

    @Override
    public List<AnalyticsDashboard> listDashboardsByCreator(String creator) {
        LambdaQueryWrapper<AnalyticsDashboard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalyticsDashboard::getCreator, creator);
        queryWrapper.orderByDesc(AnalyticsDashboard::getUpdateTime);
        return list(queryWrapper);
    }

    @Override
    public Map<String, Object> getDashboardData(Long dashboardId, Map<String, Object> params) {
        log.info("获取仪表盘数据, dashboardId: {}, params: {}", dashboardId, params);
        AnalyticsDashboard dashboard = getById(dashboardId);
        if (dashboard == null) {
            return Collections.emptyMap();
        }
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("dashboardInfo", dashboard);
        
        try {
            // 从数据源获取数据
            String dataSource = dashboard.getDataSource();
            if ("mysql".equals(dataSource)) {
                    processRdbmsData(result, dashboard, params);
            } else {
                // 其他数据源暂不支持
                result.put("data", Collections.emptyList());
                result.put("total", 0);
                    log.warn("不支持的数据源类型: {}", dataSource);
            }
        } catch (Exception e) {
            log.error("处理仪表盘数据时发生错误", e);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 处理关系型数据库数据
     */
    private void processRdbmsData(Map<String, Object> result, AnalyticsDashboard dashboard,
            Map<String, Object> params) {
        // 构建SQL查询
        String sql = dashboard.getQueryConfig();
        
        // 使用JdbcTemplate执行查询
        List<Map<String, Object>> queryResult = jdbcTemplate.queryForList(sql);

        // 执行数据分析
        analyzeData(result, queryResult);
    }

    /**
     * 分析数据
     */
    private void analyzeData(Map<String, Object> result, List<Map<String, Object>> data) {
        // 数据统计
        Map<String, Object> stats = new HashMap<>();
        stats.put("count", data.size());

        // 添加到结果
        result.put("data", data);
        result.put("stats", stats);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addComponent(Long dashboardId, Map<String, Object> componentConfig) {
        log.info("添加组件到仪表盘: {}", dashboardId);
        AnalyticsDashboard dashboard = getById(dashboardId);
        if (dashboard == null) {
            return null;
        }
        
        // 生成组件ID
        String componentId = UUID.randomUUID().toString();
        componentConfig.put("id", componentId);
        
        // 获取现有组件
        Map<String, Object> componentsConfig = dashboard.getComponentsConfig();
        if (componentsConfig == null) {
            componentsConfig = new HashMap<>();
        }
        
        // 添加新组件
        componentsConfig.put(componentId, componentConfig);
        dashboard.setComponentsConfig(componentsConfig);

        // 同时更新components字段
        try {
            dashboard.setComponents(objectMapper.writeValueAsString(componentsConfig));
        } catch (Exception e) {
            log.error("序列化组件配置失败", e);
        }

        dashboard.setUpdateTime(new Date());

        // 更新仪表盘
        updateById(dashboard);
        
        return componentId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateComponent(Long dashboardId, String componentId, Map<String, Object> componentConfig) {
        log.info("更新仪表盘组件: {}, {}", dashboardId, componentId);
        AnalyticsDashboard dashboard = getById(dashboardId);
        if (dashboard == null) {
            return false;
        }
        
        // 获取现有组件
        Map<String, Object> componentsConfig = dashboard.getComponentsConfig();
        if (componentsConfig == null || !componentsConfig.containsKey(componentId)) {
            return false;
        }
        
        // 更新组件
        componentConfig.put("id", componentId);
        componentsConfig.put(componentId, componentConfig);
        dashboard.setComponentsConfig(componentsConfig);

        // 同时更新components字段
        try {
            dashboard.setComponents(objectMapper.writeValueAsString(componentsConfig));
        } catch (Exception e) {
            log.error("序列化组件配置失败", e);
        }

        dashboard.setUpdateTime(new Date());

        // 更新仪表盘
        return updateById(dashboard);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeComponent(Long dashboardId, String componentId) {
        log.info("删除仪表盘组件: {}, {}", dashboardId, componentId);
        AnalyticsDashboard dashboard = getById(dashboardId);
        if (dashboard == null) {
            return false;
        }
        
        // 获取现有组件
        Map<String, Object> componentsConfig = dashboard.getComponentsConfig();
        if (componentsConfig == null || !componentsConfig.containsKey(componentId)) {
            return false;
        }
        
        // 删除组件
        componentsConfig.remove(componentId);
        dashboard.setComponentsConfig(componentsConfig);

        // 同时更新components字段
        try {
            dashboard.setComponents(objectMapper.writeValueAsString(componentsConfig));
        } catch (Exception e) {
            log.error("序列化组件配置失败", e);
        }

        dashboard.setUpdateTime(new Date());

        // 更新仪表盘
        return updateById(dashboard);
    }

    @Override
    public String exportDashboardConfig(Long dashboardId) {
        AnalyticsDashboard dashboard = getById(dashboardId);
        if (dashboard == null) {
            return null;
        }
        
        return mapToJsonString(objectMapper.convertValue(dashboard, Map.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long importDashboardConfig(String config, String dashboardName) {
        Map<String, Object> dashboardMap = parseJsonToMap(config);
        AnalyticsDashboard dashboard = objectMapper.convertValue(dashboardMap, AnalyticsDashboard.class);
        dashboard.setId(null);
        dashboard.setName(dashboardName);
        dashboard.setCreateTime(new Date());
        dashboard.setUpdateTime(new Date());
        save(dashboard);
        return dashboard.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDashboard(Long dashboardId) {
        log.info("删除仪表盘: {}", dashboardId);
        AnalyticsDashboard dashboard = getById(dashboardId);
        if (dashboard == null) {
            return false;
        }

        // 删除仪表盘
        return removeById(dashboardId);
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> parseJsonToMap(String json) {
        try {
            return objectMapper.readValue(json, Map.class);
        } catch (Exception e) {
            log.error("解析JSON失败", e);
            return new HashMap<>();
        }
    }

    private String mapToJsonString(Map<String, Object> map) {
        try {
            return objectMapper.writeValueAsString(map);
        } catch (Exception e) {
            log.error("转换为JSON失败", e);
            return "{}";
        }
    }

    @Override
    public List<AnalyticsDashboard> getDashboardsByType(Integer type) {
        LambdaQueryWrapper<AnalyticsDashboard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AnalyticsDashboard::getType, type);
        queryWrapper.orderByDesc(AnalyticsDashboard::getUpdateTime);
        return list(queryWrapper);
    }

    @Override
    public List<AnalyticsDashboard> searchDashboards(String keyword) {
        LambdaQueryWrapper<AnalyticsDashboard> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(AnalyticsDashboard::getName, keyword);
        queryWrapper.orderByDesc(AnalyticsDashboard::getUpdateTime);
        return list(queryWrapper);
    }

    @Override
    public Map<String, Object> getDashboardStats() {
        Map<String, Object> stats = new HashMap<>();

        // 统计仪表盘总数
        long totalCount = count();
        stats.put("totalCount", totalCount);

        // 按类型统计
        LambdaQueryWrapper<AnalyticsDashboard> queryWrapper = new LambdaQueryWrapper<>();
        List<AnalyticsDashboard> allDashboards = list(queryWrapper);

        Map<String, Long> typeStats = allDashboards.stream().collect(
                Collectors.groupingBy(dashboard -> String.valueOf(dashboard.getType()), Collectors.counting()));
        stats.put("typeStats", typeStats);

        // 按数据源统计
        Map<String, Long> dataSourceStats = allDashboards.stream()
                .collect(Collectors.groupingBy(AnalyticsDashboard::getDataSource, Collectors.counting()));
        stats.put("dataSourceStats", dataSourceStats);

        return stats;
    }
} 