package com.tcm.patient;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 患者服务启动程序
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication
@MapperScan("com.tcm.patient.mapper")
public class TcmPatientApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmPatientApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 1. 处理日志冲突问题
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 2. 设置统一配置文件
        System.setProperty("spring.profiles.include", "common");

        // 3. 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-patient");

        // 4. 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_PATIENT_PORT") == null) {
            System.setProperty("server.port", "9310");
        }
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false");
            System.setProperty("spring.main.allow-bean-definition-overriding", "true");

            // 3. Nacos配置 - 禁用配置导入检查
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

            // 4. 禁用gRPC相关功能，解决Java 9+环境中的类加载问题
            System.setProperty("nacos.client.naming.grpc.enabled", "false");
            System.setProperty("nacos.client.config.grpc.enabled", "false");
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");

            LOGGER.info("Starting patient service module...");

            // 优先设置固定端口，确保Spring Boot使用这个端口启动
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.patient.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_PATIENT_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "9310";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            ConfigurableApplicationContext context = SpringApplication.run(TcmPatientApplication.class, args);

            // 获取实际使用的端口（包括随机分配的端口）
            String serverPort;

            // 如果PortListener已经捕获到了实际端口，则使用它
            if (actualServerPort > 0) {
                serverPort = String.valueOf(actualServerPort);
            } else {
                // 尝试从环境中获取端口
                serverPort = context.getEnvironment().getProperty("server.port");

                // 如果环境中没有端口配置，则尝试从系统属性和环境变量获取
                if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                    serverPort = System.getProperty("tcm.service.patient.port");

                    // 如果系统属性中没有端口配置，则尝试从环境变量获取
                    if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                        serverPort = System.getenv("TCM_PATIENT_PORT");

                        // 如果环境变量中没有端口配置，则使用默认端口
                        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                            serverPort = "9310";
                        }
                    }
                }
            }

            // 确保端口被设置到系统属性中（使之可被其他组件获取）
            System.setProperty("server.port", serverPort);

            LOGGER.info("=====================================================");
            LOGGER.info("          Patient Service Module Started!           ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            System.out.println("  _____   _____ __  __   ____        _   _            _   ");
            System.out.println(" |_   _| / ____|  \\/  | |  _ \\      | | (_)          | |  ");
            System.out.println("   | |  | |    | \\  / | | |_) | __ _| |_ _  ___ _ __ | |_ ");
            System.out.println("   | |  | |    | |\\/| | |  _ < / _` | __| |/ _ \\ '_ \\| __|");
            System.out.println("  _| |_ | |____| |  | | | |_) | (_| | |_| |  __/ | | | |_ ");
            System.out.println(" |_____| \\_____|_|  |_| |____/ \\__,_|\\__|_|\\___|_| |_|\\__|");
            System.out.println("                                                          ");

            System.out.println("  Started successfully: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort);
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("Startup failed: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web server initialized with port: {}", actualServerPort);
        }
    }
}