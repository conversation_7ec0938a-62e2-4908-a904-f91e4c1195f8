package com.tcm.knowledge.service;

import com.tcm.knowledge.vo.*;
import java.util.List;

/**
 * 知识库服务接口
 */
public interface IKnowledgeService {
    
    /**
     * 知识库搜索
     */
    PageVO<KnowledgeItemVO> search(String keyword, String category, Integer pageNum, Integer pageSize);
    
    /**
     * 获取经典著作详情
     */
    ClassicDetailVO getClassicDetail(Long id);
    
    /**
     * 获取医案列表
     */
    PageVO<MedicalCaseVO> getMedicalCases(String filter, Integer pageNum, Integer pageSize);
    
    /**
     * 获取处方模板
     */
    List<PrescriptionTemplateVO> getPrescriptionTemplates(String syndrome);
    
    /**
     * 获取知识图谱数据
     */
    KnowledgeGraphVO getKnowledgeGraph(String concept, Integer depth);
    
    /**
     * 获取知识分类列表
     */
    List<KnowledgeCategoryVO> getCategories();
} 