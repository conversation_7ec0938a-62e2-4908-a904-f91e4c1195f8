package com.tcm.consultation.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 远程会诊数据传输对象
 */
@Data
public class ConsultationDTO {

    /**
     * 会诊记录ID（更新时需要）
     */
    private Long consultationId;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    /**
     * 主诊医生ID
     */
    @NotNull(message = "主诊医生ID不能为空")
    private Long primaryDoctorId;

    /**
     * 会诊主题
     */
    @NotBlank(message = "会诊主题不能为空")
    private String title;

    /**
     * 会诊类型（1：病例分析会诊，2：实时远程会诊，3：MDT多学科会诊）
     */
    @NotNull(message = "会诊类型不能为空")
    private Integer type;

    /**
     * 会诊预约时间
     */
    @NotNull(message = "会诊预约时间不能为空")
    private Date appointmentTime;

    /**
     * 会诊内容摘要
     */
    private String summary;

    /**
     * 诊断ID
     */
    private Long diagnosisId;

    /**
     * 参与医生ID列表
     */
    @NotEmpty(message = "参与医生不能为空")
    private List<ParticipantDTO> participants;
}