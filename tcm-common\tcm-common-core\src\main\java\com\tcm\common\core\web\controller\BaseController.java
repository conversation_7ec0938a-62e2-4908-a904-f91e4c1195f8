package com.tcm.common.core.web.controller;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.springframework.web.bind.annotation.ModelAttribute;

import com.tcm.common.core.domain.R;

/**
 * Web层通用数据处理基类
 */
public class BaseController {

    protected HttpServletRequest request;
    protected HttpServletResponse response;
    protected HttpSession session;

    /**
     * 在每个控制器方法执行前自动注入请求、响应和会话对象
     */
    @ModelAttribute
    public void setReqAndRes(HttpServletRequest request, HttpServletResponse response) {
        this.request = request;
        this.response = response;
        this.session = request.getSession(true);
    }

    /**
     * 获取请求参数并转换为指定的类型
     * 
     * @param name         参数名
     * @param defaultValue 默认值
     * @param <T>          类型参数
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    protected <T> T getParameter(String name, T defaultValue) {
        String parameter = request.getParameter(name);
        return (T) (parameter != null ? parameter : defaultValue);
    }

    /**
     * 获取整型参数
     * 
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 整型参数值
     */
    protected Integer getIntParameter(String name, Integer defaultValue) {
        String parameter = request.getParameter(name);
        return parameter != null ? Integer.parseInt(parameter) : defaultValue;
    }

    /**
     * 获取长整型参数
     * 
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 长整型参数值
     */
    protected Long getLongParameter(String name, Long defaultValue) {
        String parameter = request.getParameter(name);
        return parameter != null ? Long.parseLong(parameter) : defaultValue;
    }

    /**
     * 获取布尔型参数
     * 
     * @param name         参数名
     * @param defaultValue 默认值
     * @return 布尔型参数值
     */
    protected Boolean getBooleanParameter(String name, Boolean defaultValue) {
        String parameter = request.getParameter(name);
        return parameter != null ? Boolean.parseBoolean(parameter) : defaultValue;
    }

    /**
     * 响应返回结果
     * 
     * @param rows 影响行数
     * @return 操作结果
     */
    protected R<Void> toAjax(int rows) {
        return rows > 0 ? R.ok() : R.fail();
    }

    /**
     * 响应返回结果
     * 
     * @param result 结果
     * @return 操作结果
     */
    protected R<Void> toAjax(boolean result) {
        return result ? R.ok() : R.fail();
    }
}
