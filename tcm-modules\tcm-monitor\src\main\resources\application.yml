server:
  port: ${tcm.service.monitor.port:${TCM_MONITOR_PORT:9310}}

spring:
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  application:
    name: tcm-monitor
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    password: ${tcm.service.redis.password}
    database: ${tcm.service.redis.database}
  liquibase:
    enabled: false
  thymeleaf:
    enabled: false
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.webflux.WebFluxAutoConfiguration,org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        enabled: true
        metadata:
          "[preserved.heart.beat.interval]": 10000
          "[preserved.heart.beat.timeout]": 30000
          "[preserved.ip.delete.timeout]": 30000
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: application-common.yml
            refresh: true
        timeout: 10000
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/${tcm.service.mysql.database}_monitor?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    druid:
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: tcm
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 20MB
  devtools:
    restart:
      enabled: true
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.tcm.monitor.domain
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.tcm.monitor.controller

# 禁用Spring Boot Actuator
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      enabled: false
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true

# 日志配置
logging:
  level:
    "[com.tcm]": debug
    "[org.springframework.web]": info
    "[org.springframework.security]": info
    "[com.alibaba.nacos]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc]": ERROR
    "[com.alibaba.nacos.shaded.io.perfmark]": ERROR
    "[com.alibaba.nacos.common.remote.client]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc.netty]": ERROR
    "[com.alibaba.cloud.nacos]": ERROR
    "[com.alibaba.nacos.client.naming]": ERROR
    "[org.springframework.cloud]": debug
    "[org.springframework.boot.autoconfigure]": warn
    org.springframework.boot: warn
    org.springframework: warn

# RocketMQ配置
tcm:
  service:
    rocketMq:
      nameServerAddress: ${tcm.service.rocketMq.host}:${tcm.service.rocketMq.port}
      producerGroup: ${spring.application.name}-producer-group
      enable-msg-trace: false
      customized-trace-topic: false
      retry-times-when-send-failed: 0
      retry-times-when-send-async-failed: 0
      check-connection: false
    rocket-mq:
      host: ${tcm.service.rocketMq.host}
      port: ${tcm.service.rocketMq.port}

# 防止XSS攻击
xss:
  enabled: true
  excludes: /system/notice
  urlPatterns: /system/*,/monitor/* 