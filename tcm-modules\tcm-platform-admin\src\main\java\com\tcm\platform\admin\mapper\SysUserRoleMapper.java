package com.tcm.platform.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.platform.admin.entity.SysUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户角色关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 批量新增用户角色关联
     * 
     * @param userRoles 用户角色列表
     * @return 结果
     */
    int batchInsert(@Param("list") List<SysUserRole> userRoles);

    /**
     * 通过用户ID删除用户角色关联
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deleteByUserId(Long userId);

    /**
     * 通过角色ID删除用户角色关联
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteByRoleId(Long roleId);
}