package com.tcm.inventory.controller;

import com.tcm.inventory.entity.InventoryItem;
import com.tcm.inventory.service.IInventoryItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 库存管理控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/inventory")
public class InventoryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryController.class);
    
    @Autowired
    private IInventoryItemService inventoryItemService;
    
    /**
     * 健康检查接口
     * 
     * @return 服务状态信息
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        LOGGER.info("健康检查接口被调用");
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("service", "tcm-inventory");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
    
    /**
     * 获取库存状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        LOGGER.info("获取库存状态接口被调用");
        Map<String, Object> result = new HashMap<>();
        result.put("status", "active");
        result.put("totalItems", 100);
        result.put("availableItems", 85);
        result.put("reservedItems", 15);
        return result;
    }
    
    /**
     * 获取所有库存项目
     */
    @GetMapping("/items")
    public List<InventoryItem> getAllItems() {
        LOGGER.info("获取所有库存项目");
        return inventoryItemService.list();
    }
    
    /**
     * 根据ID获取库存项目
     */
    @GetMapping("/items/{id}")
    public InventoryItem getItemById(@PathVariable Long id) {
        LOGGER.info("根据ID获取库存项目: {}", id);
        return inventoryItemService.getById(id);
    }
    
    /**
     * 根据药品编码获取库存项目
     */
    @GetMapping("/items/code/{itemCode}")
    public InventoryItem getItemByCode(@PathVariable String itemCode) {
        LOGGER.info("根据药品编码获取库存项目: {}", itemCode);
        return inventoryItemService.getByItemCode(itemCode);
    }
    
    /**
     * 添加库存项目
     */
    @PostMapping("/items")
    public boolean addItem(@RequestBody InventoryItem item) {
        LOGGER.info("添加库存项目: {}", item);
        return inventoryItemService.save(item);
    }
    
    /**
     * 更新库存项目
     */
    @PutMapping("/items")
    public boolean updateItem(@RequestBody InventoryItem item) {
        LOGGER.info("更新库存项目: {}", item);
        return inventoryItemService.updateById(item);
    }
    
    /**
     * 删除库存项目
     */
    @DeleteMapping("/items/{id}")
    public boolean deleteItem(@PathVariable Long id) {
        LOGGER.info("删除库存项目: {}", id);
        return inventoryItemService.removeById(id);
    }
    
    /**
     * 更新库存数量
     */
    @PutMapping("/items/{id}/quantity/{quantity}")
    public boolean updateQuantity(@PathVariable Long id, @PathVariable int quantity) {
        LOGGER.info("更新库存数量: id={}, quantity={}", id, quantity);
        return inventoryItemService.updateQuantity(id, quantity);
    }
} 