package com.tcm.diagnosis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 舌象图像实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_tongue_image")
public class TongueImage extends BaseEntity {

    /**
     * 图像ID
     */
    @TableId
    private Long imageId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 图像数据（存储路径）
     */
    private String imagePath;

    /**
     * 缩略图路径
     */
    private String thumbnailPath;

    /**
     * 图像采集时间
     */
    private Date captureTime;

    /**
     * 图像大小（KB）
     */
    private Long imageSize;

    /**
     * 图像格式
     */
    private String imageFormat;

    /**
     * 图像分辨率
     */
    private String resolution;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 状态（0：未分析，1：已分析）
     */
    private Integer status;
}