package com.tcm.assistant;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import com.tcm.common.core.constant.ServiceConstants;

/**
 * AI辅助诊疗服务启动类
 * 采用统一的日志和配置策略，确保应用稳定启动
 */
@SpringBootApplication(exclude = {
    // 数据源相关配置，先排除以确保应用可以正常启动
    org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
    org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration.class,
    com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure.class,

    // 其他可能导致启动问题的自动配置
    org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration.class,
    org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration.class,
    org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration.class
})
public class TcmDiagnosisAssistantApplication {
    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("logging.level.root", "INFO"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅
            
            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖
            
            // 3. Nacos配置 - 使用统一配置常量
            // 禁用Nacos配置导入检查，以避免启动错误
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");
            // 启用Nacos认证
            System.setProperty("nacos.core.auth.enabled", "true");
            // 使用ServiceConstants中的常量设置Nacos连接信息
            System.setProperty("spring.cloud.nacos.discovery.server-addr", ServiceConstants.Nacos.SERVER_ADDR);
            System.setProperty("spring.cloud.nacos.config.server-addr", ServiceConstants.Nacos.SERVER_ADDR);
            System.setProperty("spring.cloud.nacos.discovery.username", ServiceConstants.Nacos.USERNAME);
            System.setProperty("spring.cloud.nacos.discovery.password", ServiceConstants.Nacos.PASSWORD);
            System.setProperty("spring.cloud.nacos.config.username", ServiceConstants.Nacos.USERNAME);
            System.setProperty("spring.cloud.nacos.config.password", ServiceConstants.Nacos.PASSWORD);
            // Nacos网络连接超时设置
            System.setProperty("nacos.client.connectionTimeout", "3000");
            System.setProperty("nacos.client.naming.pushEmptyProtection", "true");
            
            // 4. RocketMQ配置 - 统一设置
            System.setProperty("rocketmq.name-server", ServiceConstants.RocketMq.NAME_SERVER_ADDR);
            System.setProperty("tcm.service.rocketMq.nameServerAddress", ServiceConstants.RocketMq.NAME_SERVER_ADDR);
            
            // 5. Redis配置 - 统一设置
            System.setProperty("spring.redis.host", ServiceConstants.Redis.HOST);
            System.setProperty("spring.redis.port", String.valueOf(ServiceConstants.Redis.PORT));
            System.setProperty("spring.redis.password", ServiceConstants.Redis.PASSWORD);
            
            // 6. MySQL数据库配置 - 统一设置
            String jdbcUrl = "jdbc:mysql://" + ServiceConstants.Mysql.HOST + ":" + ServiceConstants.Mysql.PORT + "/" + 
                    ServiceConstants.Mysql.DATABASE + "?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true";
            System.setProperty("spring.datasource.url", jdbcUrl);
            System.setProperty("spring.datasource.username", ServiceConstants.Mysql.USERNAME);
            System.setProperty("spring.datasource.password", ServiceConstants.Mysql.PASSWORD);
            System.setProperty("spring.datasource.driver-class-name", "com.mysql.cj.jdbc.Driver");
            System.setProperty("spring.datasource.type", "com.alibaba.druid.pool.DruidDataSource");
            
            // 7. 显示运行模式
            // 端口配置从配置文件中读取，避免硬编码
            System.out.println("AI辅助诊疗服务正在启动...");
            
            SpringApplication.run(TcmDiagnosisAssistantApplication.class, args);
            System.out.println("(♥◠‿◠)ﾉﾞ  AI辅助诊疗服务启动成功   ლ(´ڡ`ლ)ﾞ");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}