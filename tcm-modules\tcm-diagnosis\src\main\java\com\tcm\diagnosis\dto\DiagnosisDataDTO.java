package com.tcm.diagnosis.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 诊断数据传输对象
 */
@Data
public class DiagnosisDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 诊断记录ID
     */
    @NotNull(message = "诊断记录ID不能为空")
    private Long diagnosisId;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String presentIllness;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 个人史
     */
    private String personalHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 望诊结果
     */
    private String inspection;

    /**
     * 闻诊结果
     */
    private String auscultation;

    /**
     * 问诊结果
     */
    private String interrogation;

    /**
     * 切诊结果
     */
    private String palpation;

    /**
     * 舌象
     */
    private String tongueCondition;

    /**
     * 脉象
     */
    private String pulseCondition;

    /**
     * 西医诊断
     */
    private String westernDiagnosis;

    /**
     * 中医诊断
     */
    private String tcmDiagnosis;

    /**
     * 中医证型
     */
    private String tcmSyndrome;

    /**
     * 症状列表
     */
    private List<String> symptoms;

    /**
     * 辨证结果ID
     */
    private Long syndromeIdentificationId;

    /**
     * 舌诊图像ID列表
     */
    private List<Long> tongueImageIds;

    /**
     * 脉诊数据ID列表
     */
    private List<Long> pulseDataIds;

    /**
     * 辅助检查数据
     */
    private Map<String, Object> examinations;

    /**
     * 建议类型（诊断建议/治疗建议/用药建议/注意事项等）
     */
    private String suggestionType;
}