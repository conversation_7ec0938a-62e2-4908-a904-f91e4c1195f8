package com.tcm.common.core.utils.file;

import com.tcm.common.core.utils.uuid.IdUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;

/**
 * 文件上传工具类
 */
public class FileUploadUtils {
    
    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024;
    
    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;
    
    /**
     * 默认上传路径
     */
    private static final String DEFAULT_BASE_DIR = "/uploads";
    
    /**
     * 默认的文件路径分隔符
     */
    public static final String PATH_SEPARATOR = "/";

    /**
     * 递归创建目录
     *
     * @param dirPath 目录地址
     * @return 目录地址
     */
    public static String createDirectory(String dirPath) {
        File directory = new File(dirPath);
        if (!directory.exists()) {
            directory.mkdirs();
        }
        return dirPath;
    }
    
    /**
     * 以默认配置进行文件上传
     *
     * @param file 上传的文件
     * @return 文件名称
     * @throws IOException IO异常
     */
    public static String upload(MultipartFile file) throws IOException {
        return upload(DEFAULT_BASE_DIR, file, FileTypeUtils.getFileExtension(file.getOriginalFilename()));
    }
    
    /**
     * 文件上传
     *
     * @param baseDir 相对路径
     * @param file 上传的文件
     * @param extension 文件扩展名
     * @return 返回上传成功的文件名
     * @throws IOException IO异常
     */
    public static String upload(String baseDir, MultipartFile file, String extension) throws IOException {
        String fileName = IdUtils.fastUUID() + "." + extension;
        
        File desc = new File(baseDir + PATH_SEPARATOR + fileName);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        file.transferTo(desc);
        return fileName;
    }
    
    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     * @param maxSize 最大大小
     * @return 返回上传成功的文件名
     */
    public static void assertAllowed(MultipartFile file, long maxSize) {
        long size = file.getSize();
        if (size > maxSize) {
            throw new RuntimeException("文件大小超出限制");
        }
    }
    
    /**
     * 获取文件名的后缀
     *
     * @param fileName 文件名
     * @return 后缀名
     */
    public static String getExtension(String fileName) {
        return FileTypeUtils.getFileExtension(fileName);
    }
    
    /**
     * 编码文件名
     *
     * @param fileName 文件名
     * @return 编码后的文件名
     */
    public static String encodingFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        fileName = fileName.replace("\\", "/");
        fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
        
        return IdUtils.fastUUID() + "_" + fileName;
    }
}
