package com.tcm.medication.dto;

import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 药品数据传输对象
 *
 * <AUTHOR>
 */
@Data
public class MedicineDTO {

    /**
     * 药品ID
     */
    private Long id;

    /**
     * 药品编码
     */
    @NotBlank(message = "药品编码不能为空")
    @Size(max = 50, message = "药品编码长度不能超过50个字符")
    private String medicineCode;

    /**
     * 药品名称
     */
    @NotBlank(message = "药品名称不能为空")
    @Size(max = 100, message = "药品名称长度不能超过100个字符")
    private String medicineName;

    /**
     * 拼音码
     */
    @Size(max = 50, message = "拼音码长度不能超过50个字符")
    private String pinyinCode;

    /**
     * 药品类型（1：中药材，2：中成药，3：西药）
     */
    @NotNull(message = "药品类型不能为空")
    private Integer medicineType;

    /**
     * 药品分类ID
     */
    @NotNull(message = "药品分类不能为空")
    private Long categoryId;

    /**
     * 分类名称（非数据库字段）
     */
    private String categoryName;

    /**
     * 规格
     */
    @Size(max = 100, message = "规格长度不能超过100个字符")
    private String specification;

    /**
     * 剂型
     */
    @Size(max = 50, message = "剂型长度不能超过50个字符")
    private String dosageForm;

    /**
     * 单位
     */
    @NotBlank(message = "单位不能为空")
    @Size(max = 20, message = "单位长度不能超过20个字符")
    private String unit;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0.01", message = "单价必须大于0")
    private BigDecimal price;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 预警库存
     */
    private Integer warnStock;

    /**
     * 生产厂家
     */
    @Size(max = 200, message = "生产厂家长度不能超过200个字符")
    private String manufacturer;

    /**
     * 功效
     */
    @Size(max = 500, message = "功效长度不能超过500个字符")
    private String efficacy;

    /**
     * 性味归经
     */
    @Size(max = 200, message = "性味归经长度不能超过200个字符")
    private String property;

    /**
     * 用法用量
     */
    @Size(max = 500, message = "用法用量长度不能超过500个字符")
    private String usage;

    /**
     * 禁忌
     */
    @Size(max = 500, message = "禁忌长度不能超过500个字符")
    private String contraindication;

    /**
     * 状态（0：停用，1：启用）
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}