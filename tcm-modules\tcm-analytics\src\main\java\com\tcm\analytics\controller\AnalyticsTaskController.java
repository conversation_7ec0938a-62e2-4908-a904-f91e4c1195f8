package com.tcm.analytics.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.analytics.entity.AnalyticsTask;
import com.tcm.analytics.service.AnalyticsTaskService;

/**
 * 分析任务控制器
 */
@RestController
@RequestMapping("/analytics/task")
public class AnalyticsTaskController {
    private static final Logger log = LoggerFactory.getLogger(AnalyticsTaskController.class);

    @Autowired
    private AnalyticsTaskService analyticsTaskService;

    /**
     * 创建分析任务
     */
    @PostMapping
    public Map<String, Object> createTask(@RequestBody AnalyticsTask task) {
        Map<String, Object> result = new HashMap<>();
        try {
            AnalyticsTask createdTask = analyticsTaskService.createTask(task);
            result.put("code", 0);
            result.put("message", "创建任务成功");
            result.put("data", createdTask.getId());
        } catch (Exception e) {
            log.error("创建任务失败: {}", e.getMessage(), e);
            result.put("code", 1);
            result.put("message", "创建任务失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行分析任务
     */
    @PostMapping("/{taskId}/execute")
    public Map<String, Object> executeTask(@PathVariable Long taskId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取任务详情
            AnalyticsTask task = analyticsTaskService.getTaskDetail(taskId);
            if (task != null) {
                // 创建一个新任务来执行
                AnalyticsTask newTask = new AnalyticsTask();
                newTask.setTaskName(task.getTaskName() + "-重新执行");
                newTask.setTaskType(task.getTaskType());
                newTask.setTaskParams(task.getTaskParams());
                newTask.setCreator(task.getCreator());

                // 创建新任务并自动执行
                AnalyticsTask createdTask = analyticsTaskService.createTask(newTask);

                result.put("code", 0);
                result.put("message", "执行任务成功");
                result.put("data", createdTask.getId());
            } else {
                result.put("code", 1);
                result.put("message", "任务不存在");
            }
        } catch (Exception e) {
            log.error("执行任务失败: {}", e.getMessage(), e);
            result.put("code", 1);
            result.put("message", "执行任务失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 取消分析任务
     */
    @PostMapping("/{taskId}/cancel")
    public Map<String, Object> cancelTask(@PathVariable Long taskId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = analyticsTaskService.cancelTask(taskId);
            if (success) {
                result.put("code", 0);
                result.put("message", "取消任务成功");
            } else {
                result.put("code", 1);
                result.put("message", "取消任务失败");
            }
        } catch (Exception e) {
            log.error("取消任务失败: {}", e.getMessage(), e);
            result.put("code", 1);
            result.put("message", "取消任务失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取任务详情
     */
    @GetMapping("/{taskId}")
    public Map<String, Object> getTaskDetail(@PathVariable Long taskId) {
        Map<String, Object> result = new HashMap<>();
        try {
            String status = analyticsTaskService.getTaskStatus(taskId);
            if (status != null) {
                result.put("code", 0);
                result.put("message", "获取任务状态成功");
                result.put("data", status);
            } else {
                result.put("code", 1);
                result.put("message", "任务不存在");
            }
        } catch (Exception e) {
            log.error("获取任务详情失败: {}", e.getMessage(), e);
            result.put("code", 1);
            result.put("message", "获取任务详情失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 分页查询任务列表
     */
    @GetMapping("/page")
    public Map<String, Object> listTasksByPage(@RequestParam(defaultValue = "1") Integer current,
            @RequestParam(defaultValue = "10") Integer size, @RequestParam(required = false) Long userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<AnalyticsTask> tasks = null;
            if (userId != null) {
                tasks = analyticsTaskService.getUserTasks(userId);
            } else {
                tasks = new ArrayList<>();
            }

            // 手动分页
            int total = tasks.size();
            int fromIndex = (current - 1) * size;
            int toIndex = Math.min(fromIndex + size, total);

            List<AnalyticsTask> pageData = fromIndex < toIndex ? tasks.subList(fromIndex, toIndex) : new ArrayList<>();

            Page<AnalyticsTask> taskPage = new Page<>(current, size, total);
            taskPage.setRecords(pageData);

            result.put("code", 0);
            result.put("message", "获取任务列表成功");
            result.put("data", taskPage);
        } catch (Exception e) {
            log.error("获取任务列表失败: {}", e.getMessage(), e);
            result.put("code", 1);
            result.put("message", "获取任务列表失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 根据类型获取任务列表
     */
    @GetMapping("/type/{taskType}")
    public Map<String, Object> listTasksByType(@PathVariable Integer taskType) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("message", "此功能在新版本中不可用");
        return result;
    }

    /**
     * 获取任务执行结果
     */
    @GetMapping("/{taskId}/result")
    public Map<String, Object> getTaskResult(@PathVariable Long taskId) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> taskResult = analyticsTaskService.getTaskResult(taskId);
            if (taskResult != null) {
                result.put("code", 0);
                result.put("message", "获取任务结果成功");
                result.put("data", taskResult);
            } else {
                result.put("code", 1);
                result.put("message", "获取任务结果失败");
            }
        } catch (Exception e) {
            log.error("获取任务结果失败: {}", e.getMessage(), e);
            result.put("code", 1);
            result.put("message", "获取任务结果失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 删除任务
     */
    @DeleteMapping("/{taskId}")
    public Map<String, Object> deleteTask(@PathVariable Long taskId) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 1);
        result.put("message", "此功能在新版本中不可用");
        return result;
    }
}