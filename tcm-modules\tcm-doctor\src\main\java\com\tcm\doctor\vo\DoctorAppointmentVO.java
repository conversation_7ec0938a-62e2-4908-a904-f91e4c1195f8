package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生预约视图对象
 */
@Data
public class DoctorAppointmentVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预约ID
     */
    private Long appointmentId;
    
    /**
     * 排班ID
     */
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生职称
     */
    private String titleName;
    
    /**
     * 医生科室
     */
    private String department;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 预约日期
     */
    private Date appointmentDate;
    
    /**
     * 预约时段
     */
    private Integer periodType;
    
    /**
     * 时段名称
     */
    private String periodName;
    
    /**
     * 预约号序
     */
    private Integer sequenceNumber;
    
    /**
     * 主诉
     */
    private String chiefComplaint;
    
    /**
     * 病情描述
     */
    private String illnessDesc;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 预约状态（0-待就诊 1-已就诊 2-已取消 3-爽约）
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 取消时间
     */
    private Date cancelTime;
    
    /**
     * 就诊时间
     */
    private Date visitTime;
    
    /**
     * 就诊记录ID（关联诊断记录）
     */
    private Long diagnosisId;
    
    /**
     * 预约时间
     */
    private Date createTime;
} 