package com.tcm.user.vo;

import java.io.Serializable;

/**
 * 统一返回结果
 */
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    public R() {
    }

    public R(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public R(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功返回结果
     */
    public static <T> R<T> ok() {
        return new R<>(200, "操作成功");
    }

    /**
     * 成功返回结果
     *
     * @param data 返回数据
     */
    public static <T> R<T> ok(T data) {
        return new R<>(200, "操作成功", data);
    }

    /**
     * 成功返回结果
     *
     * @param msg  返回消息
     * @param data 返回数据
     */
    public static <T> R<T> ok(String msg, T data) {
        return new R<>(200, msg, data);
    }

    /**
     * 失败返回结果
     */
    public static <T> R<T> error() {
        return new R<>(500, "操作失败");
    }

    /**
     * 失败返回结果
     *
     * @param msg 返回消息
     */
    public static <T> R<T> error(String msg) {
        return new R<>(500, msg);
    }

    /**
     * 失败返回结果
     *
     * @param code 状态码
     * @param msg  返回消息
     */
    public static <T> R<T> error(int code, String msg) {
        return new R<>(code, msg);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}