package com.tcm.user.vo;

import java.io.Serializable;

/**
 * 统一返回结果
 *
 * @param <T> 数据类型
 */
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成功
     */
    public static final int SUCCESS = 200;

    /**
     * 失败
     */
    public static final int FAIL = 500;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    /**
     * 私有构造函数
     */
    private R() {
    }

    /**
     * 构造函数
     *
     * @param code 状态码
     * @param msg  消息
     * @param data 数据
     */
    private R(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功返回结果
     *
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> R<T> ok() {
        return new R<>(SUCCESS, "操作成功", null);
    }

    /**
     * 成功返回结果
     *
     * @param data 数据
     * @param <T>  数据类型
     * @return 结果
     */
    public static <T> R<T> ok(T data) {
        return new R<>(SUCCESS, "操作成功", data);
    }

    /**
     * 成功返回结果
     *
     * @param msg  消息
     * @param data 数据
     * @param <T>  数据类型
     * @return 结果
     */
    public static <T> R<T> ok(String msg, T data) {
        return new R<>(SUCCESS, msg, data);
    }

    /**
     * 失败返回结果
     *
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> R<T> fail() {
        return new R<>(FAIL, "操作失败", null);
    }

    /**
     * 失败返回结果
     *
     * @param msg 消息
     * @param <T> 数据类型
     * @return 结果
     */
    public static <T> R<T> fail(String msg) {
        return new R<>(FAIL, msg, null);
    }

    /**
     * 失败返回结果
     *
     * @param code 状态码
     * @param msg  消息
     * @param <T>  数据类型
     * @return 结果
     */
    public static <T> R<T> fail(int code, String msg) {
        return new R<>(code, msg, null);
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public T getData() {
        return data;
    }
}