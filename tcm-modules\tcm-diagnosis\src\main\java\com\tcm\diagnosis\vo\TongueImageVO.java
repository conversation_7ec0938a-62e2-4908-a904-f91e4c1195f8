package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 舌象图像视图对象
 */
@Data
public class TongueImageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图像ID
     */
    private Long id;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 图像URL
     */
    private String imageUrl;

    /**
     * 缩略图（Base64编码）
     */
    private String thumbnail;

    /**
     * 图像采集时间
     */
    private Date captureTime;

    /**
     * 图像大小（KB）
     */
    private Long imageSize;

    /**
     * 图像格式
     */
    private String imageFormat;

    /**
     * 图像分辨率
     */
    private String resolution;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 状态（0：未分析，1：已分析）
     */
    private Integer status;
}