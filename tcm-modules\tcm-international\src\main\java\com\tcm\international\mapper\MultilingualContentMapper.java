package com.tcm.international.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.international.entity.MultilingualContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 多语言内容数据访问接口
 */
@Mapper
public interface MultilingualContentMapper extends BaseMapper<MultilingualContent> {

    /**
     * 根据内容类型和语言代码获取翻译映射
     *
     * @param contentType 内容类型
     * @param locale      语言代码
     * @return 内容键值对映射
     */
    @Select("SELECT content_key, content FROM tcm_multilingual_content " +
            "WHERE content_type = #{contentType} AND locale = #{locale} AND status = 1")
    List<Map<String, String>> getContentMapByTypeAndLocale(
            @Param("contentType") String contentType,
            @Param("locale") String locale);

    /**
     * 获取指定键的所有翻译
     *
     * @param contentKey  内容键
     * @param contentType 内容类型
     * @return 所有语言的翻译列表
     */
    @Select("SELECT * FROM tcm_multilingual_content " +
            "WHERE content_key = #{contentKey} AND content_type = #{contentType} AND status = 1")
    List<MultilingualContent> getTranslationsByKey(
            @Param("contentKey") String contentKey,
            @Param("contentType") String contentType);
}
