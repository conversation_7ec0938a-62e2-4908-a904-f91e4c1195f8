package com.tcm.evaluation.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;

/**
 * 评估记录实体
 */
@Data
@TableName(value = "eval_record", autoResultMap = true)
public class EvaluationRecord {
    
    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单ID
     */
    private Long formId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 评估类型
     */
    private String evaluationType;
    
    /**
     * 评估状态
     */
    private String status;
    
    /**
     * 评估结果，JSON格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object evaluationResult;
    
    /**
     * 用户回答，JSON格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object userAnswers;
    
    /**
     * 评分
     */
    private Integer score;
    
    /**
     * 评估总结
     */
    private String summary;
    
    /**
     * 评估建议
     */
    private String recommendations;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 删除标志（0代表未删除，1代表已删除）
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 版本号
     */
    @Version
    private Integer version;
} 