package com.tcm.file.controller;

import com.tcm.common.core.domain.R;
import com.tcm.file.domain.FileInfo;
import com.tcm.file.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件控制器
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Tag(name = "文件服务接口", description = "提供文件上传、下载、管理等功能")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传单个文件并返回文件信息")
    public R<FileInfo> upload(
            @Parameter(description = "文件对象") @RequestParam("file") MultipartFile file,
            @Parameter(description = "业务模块") @RequestParam(value = "module", required = false, defaultValue = "common") String module,
            @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) String businessId) {
        try {
            FileInfo fileInfo = fileService.upload(file, module, businessId);
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return R.fail("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量上传文件", description = "上传多个文件并返回文件信息列表")
    public R<List<FileInfo>> uploadBatch(
            @Parameter(description = "文件对象列表") @RequestParam("files") MultipartFile[] files,
            @Parameter(description = "业务模块") @RequestParam(value = "module", required = false, defaultValue = "common") String module,
            @Parameter(description = "业务ID") @RequestParam(value = "businessId", required = false) String businessId) {
        try {
            List<FileInfo> fileInfos = new ArrayList<>();
            for (MultipartFile file : files) {
                FileInfo fileInfo = fileService.upload(file, module, businessId);
                fileInfos.add(fileInfo);
            }
            return R.ok(fileInfos);
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return R.fail("批量文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 下载文件
     */
    @GetMapping("/download/{fileId}")
    @Operation(summary = "下载文件", description = "根据文件ID下载文件")
    public void download(
            @Parameter(description = "文件ID") @PathVariable Long fileId,
            HttpServletResponse response) {
        try {
            FileInfo fileInfo = fileService.getFileInfo(fileId);
            if (fileInfo == null) {
                response.setStatus(HttpStatus.NOT_FOUND.value());
                response.getWriter().write("文件不存在");
                return;
            }

            // 设置响应头
            response.setContentType(fileInfo.getMimeType());
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            String fileName = URLEncoder.encode(fileInfo.getOriginalName(), StandardCharsets.UTF_8.name());
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileName + "\"");

            // 写入文件内容
            try (InputStream inputStream = fileService.download(fileId)) {
                IOUtils.copy(inputStream, response.getOutputStream());
                response.flushBuffer();
            }
        } catch (Exception e) {
            log.error("文件下载失败", e);
            try {
                response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
                response.getWriter().write("文件下载失败: " + e.getMessage());
            } catch (Exception ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/info/{fileId}")
    @Operation(summary = "获取文件信息", description = "根据文件ID获取文件详细信息")
    public R<FileInfo> getFileInfo(
            @Parameter(description = "文件ID") @PathVariable Long fileId) {
        try {
            FileInfo fileInfo = fileService.getFileInfo(fileId);
            if (fileInfo == null) {
                return R.fail("文件不存在");
            }
            return R.ok(fileInfo);
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return R.fail("获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务ID获取文件列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取文件列表", description = "根据业务ID获取相关文件列表")
    public R<List<FileInfo>> listFiles(
            @Parameter(description = "业务ID") @RequestParam String businessId,
            @Parameter(description = "业务模块") @RequestParam(required = false, defaultValue = "common") String module) {
        try {
            List<FileInfo> fileInfos = fileService.listByBusinessId(businessId, module);
            return R.ok(fileInfos);
        } catch (Exception e) {
            log.error("获取文件列表失败", e);
            return R.fail("获取文件列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{fileId}")
    @Operation(summary = "删除文件", description = "根据文件ID删除文件")
    public R<Boolean> deleteFile(
            @Parameter(description = "文件ID") @PathVariable Long fileId) {
        try {
            boolean result = fileService.deleteFile(fileId);
            if (result) {
                return R.ok(true, "文件删除成功");
            } else {
                return R.fail("文件删除失败");
            }
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return R.fail("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除文件", description = "根据文件ID列表批量删除文件")
    public R<Boolean> deleteFiles(
            @Parameter(description = "文件ID列表") @RequestBody List<Long> fileIds) {
        try {
            boolean result = fileService.deleteFiles(fileIds);
            if (result) {
                return R.ok(true, "文件批量删除成功");
            } else {
                return R.fail("文件批量删除失败");
            }
        } catch (Exception e) {
            log.error("文件批量删除失败", e);
            return R.fail("文件批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 根据业务ID删除文件
     */
    @DeleteMapping("/business")
    @Operation(summary = "根据业务ID删除文件", description = "删除指定业务ID关联的所有文件")
    public R<Boolean> deleteByBusinessId(
            @Parameter(description = "业务ID") @RequestParam String businessId,
            @Parameter(description = "业务模块") @RequestParam(required = false, defaultValue = "common") String module) {
        try {
            boolean result = fileService.deleteByBusinessId(businessId, module);
            if (result) {
                return R.ok(true, "业务相关文件删除成功");
            } else {
                return R.fail("业务相关文件删除失败");
            }
        } catch (Exception e) {
            log.error("业务相关文件删除失败", e);
            return R.fail("业务相关文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 更新文件状态（临时->永久）
     */
    @PutMapping("/status")
    @Operation(summary = "更新文件状态", description = "将临时文件状态更新为永久")
    public R<Boolean> updateStatus(
            @Parameter(description = "文件ID列表") @RequestBody List<Long> fileIds) {
        try {
            boolean result = fileService.updateStatus(fileIds);
            if (result) {
                return R.ok(true, "文件状态更新成功");
            } else {
                return R.fail("文件状态更新失败");
            }
        } catch (Exception e) {
            log.error("文件状态更新失败", e);
            return R.fail("文件状态更新失败: " + e.getMessage());
        }
    }
} 