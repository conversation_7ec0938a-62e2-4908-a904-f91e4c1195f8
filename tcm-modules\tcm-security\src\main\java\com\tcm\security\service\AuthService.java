package com.tcm.security.service;

import com.tcm.security.model.dto.LoginDTO;
import com.tcm.security.model.vo.TokenVO;
import com.tcm.security.model.vo.UserInfoVO;

/**
 * 认证服务接口
 * 
 * <AUTHOR>
 */
public interface AuthService {

    /**
     * 用户登录
     * 
     * @param loginDTO 登录信息
     * @return 令牌信息
     */
    TokenVO login(LoginDTO loginDTO);

    /**
     * 用户登出
     * 
     * @return 是否成功
     */
    Boolean logout();

    /**
     * 刷新令牌
     * 
     * @param refreshToken 刷新令牌
     * @return 新的令牌信息
     */
    TokenVO refreshToken(String refreshToken);

    /**
     * 获取当前登录用户信息
     * 
     * @return 用户信息
     */
    UserInfoVO getUserInfo();

    /**
     * 生成验证码
     * 
     * @return 验证码信息
     */
    Object generateCaptcha();
}