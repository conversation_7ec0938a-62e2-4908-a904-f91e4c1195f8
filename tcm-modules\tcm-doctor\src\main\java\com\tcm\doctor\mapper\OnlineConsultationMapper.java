package com.tcm.doctor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.doctor.domain.OnlineConsultation;
import com.tcm.doctor.dto.ConsultationQuery;
import com.tcm.doctor.vo.OnlineConsultationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 在线问诊Mapper接口
 */
@Mapper
public interface OnlineConsultationMapper extends BaseMapper<OnlineConsultation> {
    
    /**
     * 查询问诊列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 问诊列表
     */
    IPage<OnlineConsultationVO> selectConsultationList(Page<OnlineConsultation> page, @Param("query") ConsultationQuery query);
    
    /**
     * 根据ID查询问诊详情
     *
     * @param consultationId 问诊ID
     * @return 问诊详情
     */
    OnlineConsultationVO selectConsultationById(@Param("consultationId") Long consultationId);
    
    /**
     * 根据患者ID查询问诊列表
     *
     * @param patientId 患者ID
     * @return 问诊列表
     */
    List<OnlineConsultationVO> selectConsultationByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 根据医生ID查询问诊列表
     *
     * @param doctorId 医生ID
     * @param status   状态（可选）
     * @return 问诊列表
     */
    List<OnlineConsultationVO> selectConsultationByDoctorId(@Param("doctorId") Long doctorId, @Param("status") Integer status);
    
    /**
     * 更新问诊状态
     *
     * @param consultationId 问诊ID
     * @param status         状态
     * @param updateBy       更新人
     * @return 结果
     */
    int updateConsultationStatus(@Param("consultationId") Long consultationId,
                                @Param("status") Integer status,
                                @Param("updateBy") String updateBy);
    
    /**
     * 接诊
     *
     * @param consultationId 问诊ID
     * @param doctorId       医生ID
     * @param updateBy       更新人
     * @return 结果
     */
    int acceptConsultation(@Param("consultationId") Long consultationId,
                          @Param("doctorId") Long doctorId,
                          @Param("updateBy") String updateBy);
    
    /**
     * 完成问诊
     *
     * @param consultationId 问诊ID
     * @param doctorAdvice   医生建议
     * @param diagnosis      诊断结果
     * @param prescriptionId 处方ID
     * @param updateBy       更新人
     * @return 结果
     */
    int finishConsultation(@Param("consultationId") Long consultationId,
                          @Param("doctorAdvice") String doctorAdvice,
                          @Param("diagnosis") String diagnosis,
                          @Param("prescriptionId") Long prescriptionId,
                          @Param("updateBy") String updateBy);
    
    /**
     * 评价问诊
     *
     * @param consultationId 问诊ID
     * @param score          评分
     * @param evaluation     评价内容
     * @param updateBy       更新人
     * @return 结果
     */
    int evaluateConsultation(@Param("consultationId") Long consultationId,
                            @Param("score") Integer score,
                            @Param("evaluation") String evaluation,
                            @Param("updateBy") String updateBy);
    
    /**
     * 更新支付状态
     *
     * @param consultationId 问诊ID
     * @param payStatus      支付状态
     * @param updateBy       更新人
     * @return 结果
     */
    int updatePayStatus(@Param("consultationId") Long consultationId,
                       @Param("payStatus") Integer payStatus,
                       @Param("updateBy") String updateBy);
}