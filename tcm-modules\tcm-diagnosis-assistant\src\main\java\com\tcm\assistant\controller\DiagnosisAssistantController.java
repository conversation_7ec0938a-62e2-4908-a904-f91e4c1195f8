package com.tcm.assistant.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import com.tcm.assistant.service.DiagnosisAssistantService;
import com.tcm.assistant.model.dto.DiagnosisDataDTO;
import com.tcm.assistant.model.dto.ClinicalDataDTO;
import com.tcm.assistant.model.dto.MedicalImageDTO;
import com.tcm.assistant.model.vo.DiagnosisSuggestionVO;
import com.tcm.assistant.model.vo.ImageAnalysisVO;
import com.tcm.assistant.model.common.Result;

import java.util.List;

/**
 * AI辅助诊疗控制器
 */
@RestController
@RequestMapping("/api/v1/ai-assistant")
@Slf4j
@Tag(name = "AI辅助诊疗接口", description = "提供AI辅助诊断、分析和预测功能")
public class DiagnosisAssistantController {

    @Autowired
    private DiagnosisAssistantService assistantService;

    /**
     * 图像分析
     */
    @PostMapping("/image-analysis")
    @Operation(summary = "医疗图像分析", description = "分析医疗图像并提取关键特征")
    public Result<ImageAnalysisVO> analyzeImage(@RequestBody MedicalImageDTO dto) {
        log.info("接收图像分析请求，类型: {}", dto.getImageType());
        ImageAnalysisVO result = assistantService.analyzeImage(dto);
        return Result.success(result);
    }

    /**
     * 获取诊断建议
     */
    @PostMapping("/diagnosis-suggestion")
    @Operation(summary = "获取AI诊断建议", description = "基于患者症状和临床数据生成诊断建议")
    public Result<DiagnosisSuggestionVO> getDiagnosisSuggestion(@RequestBody DiagnosisDataDTO dto) {
        log.info("获取诊断建议请求：{}", dto);
        DiagnosisSuggestionVO result = assistantService.getDiagnosisSuggestion(dto);
        return Result.success(result);
    }

    /**
     * 获取治疗建议
     */
    @PostMapping("/treatment-suggestion")
    @Operation(summary = "获取治疗建议", description = "基于诊断结果提供中医治疗方案建议")
    public Result<?> getTreatmentSuggestion(@RequestBody DiagnosisDataDTO dto) {
        log.info("获取治疗建议请求：{}", dto);
        return Result.success(assistantService.getTreatmentSuggestion(dto));
    }

    /**
     * 获取相似病例
     */
    @PostMapping("/similar-cases")
    @Operation(summary = "查询相似病例", description = "查找与当前症状和证型相似的历史病例")
    public Result<?> getSimilarCases(@RequestBody DiagnosisDataDTO dto) {
        log.info("获取相似病例请求：{}", dto);
        return Result.success(assistantService.getSimilarCases(dto));
    }

    /**
     * 根据临床数据获取分析建议
     */
    @PostMapping("/clinical-analysis")
    @Operation(summary = "临床数据综合分析", description = "综合分析患者临床数据，提供全面诊断建议")
    public Result<List<DiagnosisSuggestionVO>> getClinicalAnalysis(@RequestBody ClinicalDataDTO dto) {
        log.info("临床数据分析请求");
        // 转换为诊断数据DTO并调用现有方法
        DiagnosisDataDTO diagnosisDTO = new DiagnosisDataDTO();
        diagnosisDTO.setPatientId(dto.getPatientId());
        diagnosisDTO.setDoctorId(dto.getDoctorId());
        diagnosisDTO.setSymptoms(dto.getSymptoms());
        diagnosisDTO.setTongueDescription(dto.getTongueDescription());
        diagnosisDTO.setPulseDescription(dto.getPulseDescription());
        diagnosisDTO.setInquiryInfo(dto.getInquiryRecord());

        DiagnosisSuggestionVO suggestion = assistantService.getDiagnosisSuggestion(diagnosisDTO);
        return Result.success(List.of(suggestion));
    }
}