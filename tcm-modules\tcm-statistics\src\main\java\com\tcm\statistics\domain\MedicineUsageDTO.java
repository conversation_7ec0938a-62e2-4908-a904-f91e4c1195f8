package com.tcm.statistics.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 药物使用统计数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MedicineUsageDTO {
    
    /**
     * 药物ID
     */
    private Long medicineId;
    
    /**
     * 药物名称
     */
    private String medicineName;
    
    /**
     * 使用次数
     */
    private Long count;
    
    /**
     * 使用百分比
     */
    private Double percentage;
} 