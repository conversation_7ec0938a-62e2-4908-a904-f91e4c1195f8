package com.tcm.workflow.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;

/**
 * Swagger配置
 * 
 * <AUTHOR>
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI().info(new Info().title("工作流服务API").description("基于Flowable的工作流引擎服务接口").version("v1.0.0")
                .license(new License().name("TCM License").url("https://tcm.com")));
    }
}