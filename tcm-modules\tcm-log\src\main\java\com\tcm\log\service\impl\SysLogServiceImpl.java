package com.tcm.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.log.domain.SysLog;
import com.tcm.log.mapper.SysLogMapper;
import com.tcm.log.service.ISysLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 系统日志服务实现
 * 
 * <AUTHOR>
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLog> implements ISysLogService {

    /**
     * 保存系统日志
     * 
     * @param sysLog 系统日志信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLog(SysLog sysLog) {
        return save(sysLog);
    }

    /**
     * 清空系统日志
     * 
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearLogs() {
        LambdaQueryWrapper<SysLog> wrapper = new LambdaQueryWrapper<>();
        return remove(wrapper);
    }

    /**
     * 删除系统日志
     * 
     * @param ids 需要删除的日志ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLogs(Long[] ids) {
        return removeByIds(Arrays.asList(ids));
    }
} 