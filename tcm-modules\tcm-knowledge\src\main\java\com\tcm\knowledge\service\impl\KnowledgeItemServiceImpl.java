package com.tcm.knowledge.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.knowledge.domain.KnowledgeItem;
import com.tcm.knowledge.dto.KnowledgeItemDTO;
import com.tcm.knowledge.mapper.KnowledgeItemMapper;
import com.tcm.knowledge.service.IKnowledgeItemService;
import com.tcm.knowledge.vo.KnowledgeItemVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class KnowledgeItemServiceImpl extends ServiceImpl<KnowledgeItemMapper, KnowledgeItem>
        implements IKnowledgeItemService {

    @Autowired
    private KnowledgeItemMapper knowledgeItemMapper;

    @Override
    public Long addKnowledgeItem(KnowledgeItemDTO dto) {
        KnowledgeItem item = new KnowledgeItem();
        BeanUtils.copyProperties(dto, item);
        item.setCreateTime(new Date());
        item.setUpdateTime(new Date());
        knowledgeItemMapper.insert(item);
        return item.getId();
    }

    @Override
    public boolean updateKnowledgeItem(Long id, KnowledgeItemDTO dto) {
        KnowledgeItem item = knowledgeItemMapper.selectById(id);
        if (item == null)
            return false;
        BeanUtils.copyProperties(dto, item);
        item.setUpdateTime(new Date());
        return knowledgeItemMapper.updateById(item) > 0;
    }

    @Override
    public boolean deleteKnowledgeItem(Long id) {
        return knowledgeItemMapper.deleteById(id) > 0;
    }

    @Override
    public KnowledgeItemVO getKnowledgeItem(Long id) {
        KnowledgeItem item = knowledgeItemMapper.selectById(id);
        if (item == null)
            return null;
        KnowledgeItemVO vo = new KnowledgeItemVO();
        BeanUtils.copyProperties(item, vo);
        return vo;
    }

    @Override
    public List<KnowledgeItemVO> listKnowledgeItems(String type, String keyword) {
        LambdaQueryWrapper<KnowledgeItem> wrapper = new LambdaQueryWrapper<>();
        if (type != null && !type.isEmpty()) {
            wrapper.eq(KnowledgeItem::getType, type);
        }
        if (keyword != null && !keyword.isEmpty()) {
            wrapper.and(w -> w.like(KnowledgeItem::getTitle, keyword).or().like(KnowledgeItem::getContent, keyword));
        }
        List<KnowledgeItem> list = knowledgeItemMapper.selectList(wrapper);
        return list.stream().map(item -> {
            KnowledgeItemVO vo = new KnowledgeItemVO();
            BeanUtils.copyProperties(item, vo);
            return vo;
        }).collect(Collectors.toList());
    }
}