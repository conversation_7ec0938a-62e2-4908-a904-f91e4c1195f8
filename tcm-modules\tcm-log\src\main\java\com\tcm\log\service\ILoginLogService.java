package com.tcm.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.log.domain.LoginLog;

/**
 * 登录日志服务接口
 * 
 * <AUTHOR>
 */
public interface ILoginLogService extends IService<LoginLog> {
    
    /**
     * 记录登录信息
     * 
     * @param loginLog 登录日志信息
     * @return 结果
     */
    boolean saveLoginLog(LoginLog loginLog);
    
    /**
     * 记录登出信息
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    boolean saveLogoutLog(String sessionId);
    
    /**
     * 清空登录日志
     * 
     * @return 结果
     */
    boolean clearLogs();
    
    /**
     * 删除登录日志
     * 
     * @param ids 需要删除的日志ID
     * @return 结果
     */
    boolean deleteLogs(Long[] ids);
    
    /**
     * 查询指定用户的登录日志
     * 
     * @param username 用户名
     * @return 登录日志列表
     */
    LoginLog getLatestLoginLog(String username);
} 