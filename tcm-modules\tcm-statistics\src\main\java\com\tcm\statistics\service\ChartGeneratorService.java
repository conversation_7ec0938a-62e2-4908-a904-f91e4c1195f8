package com.tcm.statistics.service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.tcm.statistics.domain.ChartDataDTO;
import com.tcm.statistics.domain.ChartSeriesDTO;
import com.tcm.statistics.domain.IssueDistributionDTO;
import com.tcm.statistics.domain.MedicineUsageDTO;
import com.tcm.statistics.domain.SyndromeDistributionDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 图表生成服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChartGeneratorService {
    
    /**
     * 生成饼图数据
     * 
     * @param title 图表标题
     * @param data 数据源
     * @param nameField 名称字段
     * @param valueField 值字段
     * @return 图表数据
     */
    public <T> ChartDataDTO generatePieChart(String title, List<T> data, String nameField, String valueField) {
        log.info("生成饼图数据: title={}, nameField={}, valueField={}", title, nameField, valueField);
        
        ChartDataDTO chartData = new ChartDataDTO();
        chartData.setTitle(title);
        chartData.setType("pie");
        
        List<String> categories = new ArrayList<>();
        List<Number> values = new ArrayList<>();
        
        try {
            if (data != null && !data.isEmpty()) {
                for (T item : data) {
                    String name = getFieldValue(item, nameField, String.class);
                    Number value = getFieldValue(item, valueField, Number.class);
                    
                    if (name != null && value != null) {
                        categories.add(name);
                        values.add(value);
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成饼图数据失败", e);
        }
        
        chartData.setCategories(categories);
        
        ChartSeriesDTO series = new ChartSeriesDTO();
        series.setName(title);
        series.setData(values);
        
        chartData.setSeries(Collections.singletonList(series));
        
        return chartData;
    }
    
    /**
     * 生成条形图数据
     * 
     * @param title 图表标题
     * @param data 数据源
     * @param nameField 名称字段
     * @param valueField 值字段
     * @return 图表数据
     */
    public <T> ChartDataDTO generateBarChart(String title, List<T> data, String nameField, String valueField) {
        log.info("生成条形图数据: title={}, nameField={}, valueField={}", title, nameField, valueField);
        
        ChartDataDTO chartData = new ChartDataDTO();
        chartData.setTitle(title);
        chartData.setType("bar");
        
        List<String> categories = new ArrayList<>();
        List<Number> values = new ArrayList<>();
        
        try {
            if (data != null && !data.isEmpty()) {
                for (T item : data) {
                    String name = getFieldValue(item, nameField, String.class);
                    Number value = getFieldValue(item, valueField, Number.class);
                    
                    if (name != null && value != null) {
                        categories.add(name);
                        values.add(value);
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成条形图数据失败", e);
        }
        
        chartData.setCategories(categories);
        
        ChartSeriesDTO series = new ChartSeriesDTO();
        series.setName(title);
        series.setData(values);
        
        chartData.setSeries(Collections.singletonList(series));
        
        return chartData;
    }
    
    /**
     * 生成折线图数据
     * 
     * @param title 图表标题
     * @param data 数据源
     * @param nameField 名称字段
     * @param valueField 值字段
     * @return 图表数据
     */
    public <T> ChartDataDTO generateLineChart(String title, List<T> data, String nameField, String valueField) {
        log.info("生成折线图数据: title={}, nameField={}, valueField={}", title, nameField, valueField);
        
        ChartDataDTO chartData = new ChartDataDTO();
        chartData.setTitle(title);
        chartData.setType("line");
        
        List<String> categories = new ArrayList<>();
        List<Number> values = new ArrayList<>();
        
        try {
            if (data != null && !data.isEmpty()) {
                for (T item : data) {
                    String name = getFieldValue(item, nameField, String.class);
                    Number value = getFieldValue(item, valueField, Number.class);
                    
                    if (name != null && value != null) {
                        categories.add(name);
                        values.add(value);
                    }
                }
            }
        } catch (Exception e) {
            log.error("生成折线图数据失败", e);
        }
        
        chartData.setCategories(categories);
        
        ChartSeriesDTO series = new ChartSeriesDTO();
        series.setName(title);
        series.setData(values);
        
        chartData.setSeries(Collections.singletonList(series));
        
        return chartData;
    }
    
    /**
     * 生成多系列图表数据
     * 
     * @param title 图表标题
     * @param type 图表类型
     * @param categories 类别
     * @param seriesData 系列数据
     * @return 图表数据
     */
    public ChartDataDTO generateMultiSeriesChart(String title, String type, 
                                              List<String> categories, 
                                              Map<String, List<Number>> seriesData) {
        log.info("生成多系列图表数据: title={}, type={}", title, type);
        
        ChartDataDTO chartData = new ChartDataDTO();
        chartData.setTitle(title);
        chartData.setType(type);
        chartData.setCategories(categories);
        
        List<ChartSeriesDTO> seriesList = new ArrayList<>();
        
        for (Map.Entry<String, List<Number>> entry : seriesData.entrySet()) {
            ChartSeriesDTO series = new ChartSeriesDTO();
            series.setName(entry.getKey());
            series.setData(entry.getValue());
            seriesList.add(series);
        }
        
        chartData.setSeries(seriesList);
        
        return chartData;
    }
    
    /**
     * 从对象中获取字段值
     * 
     * @param <T> 对象类型
     * @param <R> 返回值类型
     * @param obj 对象
     * @param fieldName 字段名
     * @param returnType 返回值类型
     * @return 字段值
     */
    @SuppressWarnings("unchecked")
    private <T, R> R getFieldValue(T obj, String fieldName, Class<R> returnType) {
        if (obj == null || fieldName == null) {
            return null;
        }
        
        // 处理特殊对象类型
        if (obj instanceof MedicineUsageDTO) {
            MedicineUsageDTO medicine = (MedicineUsageDTO) obj;
            if ("medicineName".equals(fieldName)) {
                return (R) medicine.getMedicineName();
            } else if ("count".equals(fieldName)) {
                return (R) medicine.getCount();
            } else if ("percentage".equals(fieldName)) {
                return (R) medicine.getPercentage();
            }
        } else if (obj instanceof SyndromeDistributionDTO) {
            SyndromeDistributionDTO syndrome = (SyndromeDistributionDTO) obj;
            if ("syndrome".equals(fieldName)) {
                return (R) syndrome.getSyndrome();
            } else if ("count".equals(fieldName)) {
                return (R) syndrome.getCount();
            } else if ("percentage".equals(fieldName)) {
                return (R) syndrome.getPercentage();
            }
        } else if (obj instanceof IssueDistributionDTO) {
            IssueDistributionDTO issue = (IssueDistributionDTO) obj;
            if ("issueType".equals(fieldName)) {
                return (R) issue.getIssueType();
            } else if ("description".equals(fieldName)) {
                return (R) issue.getDescription();
            } else if ("count".equals(fieldName)) {
                return (R) issue.getCount();
            } else if ("percentage".equals(fieldName)) {
                return (R) issue.getPercentage();
            }
        }
        
        // 通用处理方式 - 通过反射获取字段值
        try {
            java.lang.reflect.Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            
            if (value != null && returnType.isAssignableFrom(value.getClass())) {
                return (R) value;
            }
        } catch (Exception e) {
            log.warn("获取字段值失败: fieldName={}, error={}", fieldName, e.getMessage());
        }
        
        return null;
    }
} 