package com.tcm.consultation.controller;

import com.tcm.common.core.domain.AjaxResult;
import com.tcm.consultation.service.ConsultationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 远程会诊控制器
 */
@RestController
@RequestMapping("/api/v1")
public class ConsultationController {

    @Autowired
    private ConsultationRecordService consultationRecordService;

    /**
     * 开始会诊
     */
    @PostMapping("/consultations/{consultationId}/start")
    public AjaxResult startConsultation(@PathVariable Long consultationId) {
        boolean result = consultationRecordService.startConsultation(consultationId);
        return result ? AjaxResult.success("会诊已开始") : AjaxResult.error("开始会诊失败");
    }

    /**
     * 结束会诊
     */
    @PostMapping("/consultations/{consultationId}/end")
    public AjaxResult endConsultation(@PathVariable Long consultationId) {
        boolean result = consultationRecordService.endConsultation(consultationId);
        return result ? AjaxResult.success("会诊已结束") : AjaxResult.error("结束会诊失败");
    }

    /**
     * 获取会诊状态
     */
    @GetMapping("/consultations/{consultationId}/status")
    public AjaxResult getConsultationStatus(@PathVariable Long consultationId) {
        // TODO: 实现获取会诊状态的逻辑
        return AjaxResult.success("会诊状态查询成功", "进行中");
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public AjaxResult health() {
        return AjaxResult.success("远程会诊服务运行正常");
    }
}