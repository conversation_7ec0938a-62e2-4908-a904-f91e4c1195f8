<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.platform.admin.mapper.SysUserRoleMapper">

    <!-- 批量新增用户角色关联 -->
    <insert id="batchInsert">
        insert into sys_user_role(user_id, role_id, create_by, create_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 通过用户ID删除用户角色关联 -->
    <delete id="deleteByUserId">
        delete from sys_user_role where user_id = #{userId}
    </delete>

    <!-- 通过角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId">
        delete from sys_user_role where role_id = #{roleId}
    </delete>
</mapper> 