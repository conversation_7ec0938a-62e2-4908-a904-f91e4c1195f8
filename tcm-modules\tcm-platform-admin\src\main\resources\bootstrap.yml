spring:
  application:
    name: tcm-platform-admin
  profiles:
    active: dev
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        file-extension: yml
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        shared-configs:
          - data-id: application-common.yml
            group: DEFAULT_GROUP
            refresh: true
    sentinel:
      transport:
        dashboard: ${tcm.service.sentinel.host}:${tcm.service.sentinel.port}
      eager: true
      datasource:
        ds-flow:
          nacos:
            server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
            username: ${tcm.service.nacos.username}
            password: ${tcm.service.nacos.password}
            data-id: sentinel-rules-flow
            group-id: DEFAULT_GROUP
            data-type: json
            rule-type: flow 