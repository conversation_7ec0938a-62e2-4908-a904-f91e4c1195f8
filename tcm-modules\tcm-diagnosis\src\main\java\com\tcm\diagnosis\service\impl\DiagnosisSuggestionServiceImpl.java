package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.ServiceException;
import com.tcm.common.core.utils.DateUtils;
import com.tcm.diagnosis.domain.DiagnosisSuggestion;
import com.tcm.diagnosis.domain.SyndromeIdentification;
import com.tcm.diagnosis.dto.DiagnosisDataDTO;
import com.tcm.diagnosis.mapper.DiagnosisSuggestionMapper;
import com.tcm.diagnosis.mapper.SyndromeIdentificationMapper;
import com.tcm.diagnosis.service.IDiagnosisSuggestionService;
import com.tcm.diagnosis.vo.DiagnosisSuggestionVO;
import com.tcm.diagnosis.vo.KnowledgeItemVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 诊断建议服务实现类
 */
@Slf4j
@Service
public class DiagnosisSuggestionServiceImpl extends ServiceImpl<DiagnosisSuggestionMapper, DiagnosisSuggestion>
        implements IDiagnosisSuggestionService {

    @Autowired
    private DiagnosisSuggestionMapper diagnosisSuggestionMapper;

    @Autowired
    private SyndromeIdentificationMapper syndromeIdentificationMapper;

    /**
     * 生成诊断建议
     *
     * @param diagnosisDataDTO 诊断数据
     * @return 建议ID列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> generateSuggestions(DiagnosisDataDTO diagnosisDataDTO) {
        if (diagnosisDataDTO == null || diagnosisDataDTO.getDiagnosisId() == null) {
            throw new ServiceException("诊断数据不完整");
        }

        Long diagnosisId = diagnosisDataDTO.getDiagnosisId();
        Long patientId = diagnosisDataDTO.getPatientId();
        Long doctorId = diagnosisDataDTO.getDoctorId();

        // 获取辨证结果
        SyndromeIdentification syndromeIdentification = getSyndromeIdentification(diagnosisId);

        // 删除已有的建议
        deleteExistingSuggestions(diagnosisId);

        // 生成不同类型的建议
        List<DiagnosisSuggestion> suggestions = new ArrayList<>();

        // 1. 生成治疗原则建议
        suggestions.add(generateTreatmentPrincipleSuggestion(syndromeIdentification, diagnosisId, patientId, doctorId));

        // 2. 生成中药处方建议
        suggestions.add(generateHerbalPrescriptionSuggestion(syndromeIdentification, diagnosisId, patientId, doctorId));

        // 3. 生成针灸建议
        suggestions.add(generateAcupunctureSuggestion(syndromeIdentification, diagnosisId, patientId, doctorId));

        // 4. 生成生活调养建议
        suggestions.add(generateLifestyleSuggestion(syndromeIdentification, diagnosisId, patientId, doctorId));

        // 批量保存建议
        saveBatch(suggestions);

        // 返回建议ID列表
        return suggestions.stream()
                .map(DiagnosisSuggestion::getSuggestionId)
                .collect(Collectors.toList());
    }

    /**
     * 获取诊断建议
     *
     * @param suggestionId 建议ID
     * @return 诊断建议视图对象
     */
    @Override
    public DiagnosisSuggestionVO getSuggestion(Long suggestionId) {
        DiagnosisSuggestion suggestion = getById(suggestionId);
        if (suggestion == null) {
            throw new ServiceException("诊断建议不存在");
        }

        return convertToVO(suggestion);
    }

    /**
     * 获取诊断记录的建议列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 诊断建议视图对象列表
     */
    @Override
    public List<DiagnosisSuggestionVO> getSuggestionsByDiagnosisId(Long diagnosisId) {
        LambdaQueryWrapper<DiagnosisSuggestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiagnosisSuggestion::getDiagnosisId, diagnosisId)
                .orderByAsc(DiagnosisSuggestion::getSuggestionType);
        List<DiagnosisSuggestion> suggestions = list(queryWrapper);

        return convertToVOList(suggestions);
    }

    /**
     * 获取诊断记录的特定类型建议
     *
     * @param diagnosisId    诊断记录ID
     * @param suggestionType 建议类型
     * @return 诊断建议视图对象列表
     */
    @Override
    public List<DiagnosisSuggestionVO> getSuggestionsByType(Long diagnosisId, String suggestionType) {
        LambdaQueryWrapper<DiagnosisSuggestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiagnosisSuggestion::getDiagnosisId, diagnosisId)
                .eq(DiagnosisSuggestion::getSuggestionType, suggestionType);
        List<DiagnosisSuggestion> suggestions = list(queryWrapper);

        return convertToVOList(suggestions);
    }

    /**
     * 医生采纳建议
     *
     * @param suggestionId 建议ID
     * @param doctorId     医生ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean adoptSuggestion(Long suggestionId, Long doctorId) {
        DiagnosisSuggestion suggestion = getById(suggestionId);
        if (suggestion == null) {
            throw new ServiceException("诊断建议不存在");
        }

        suggestion.setStatus(1); // 已采纳
        suggestion.setAdoptTime(DateUtils.getNowDate());
        suggestion.setAdoptBy(String.valueOf(doctorId));
        suggestion.setUpdateBy(String.valueOf(doctorId));
        suggestion.setUpdateTime(DateUtils.getNowDate());

        return updateById(suggestion);
    }

    /**
     * 医生拒绝建议
     *
     * @param suggestionId 建议ID
     * @param doctorId     医生ID
     * @param reason       拒绝原因
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rejectSuggestion(Long suggestionId, Long doctorId, String reason) {
        DiagnosisSuggestion suggestion = getById(suggestionId);
        if (suggestion == null) {
            throw new ServiceException("诊断建议不存在");
        }

        suggestion.setStatus(2); // 已拒绝
        suggestion.setRejectReason(reason);
        suggestion.setRejectTime(DateUtils.getNowDate());
        suggestion.setRejectBy(String.valueOf(doctorId));
        suggestion.setUpdateBy(String.valueOf(doctorId));
        suggestion.setUpdateTime(DateUtils.getNowDate());

        return updateById(suggestion);
    }

    /**
     * 修改建议内容
     *
     * @param suggestionId 建议ID
     * @param content      新的建议内容
     * @param doctorId     医生ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSuggestion(Long suggestionId, String content, Long doctorId) {
        DiagnosisSuggestion suggestion = getById(suggestionId);
        if (suggestion == null) {
            throw new ServiceException("诊断建议不存在");
        }

        suggestion.setSuggestionContent(content);
        suggestion.setIsModified(1); // 已修改
        suggestion.setUpdateBy(String.valueOf(doctorId));
        suggestion.setUpdateTime(DateUtils.getNowDate());

        return updateById(suggestion);
    }

    /**
     * 获取相关知识条目
     *
     * @param keyword 关键词
     * @param limit   限制数量
     * @return 知识条目视图对象列表
     */
    @Override
    public List<KnowledgeItemVO> getRelatedKnowledge(String keyword, Integer limit) {
        // TODO: 实现知识库检索逻辑
        // 实际项目中应集成知识图谱或搜索引擎，根据关键词检索相关知识条目
        // 这里简化为模拟返回结果

        List<KnowledgeItemVO> knowledgeItems = new ArrayList<>();
        for (int i = 0; i < limit; i++) {
            KnowledgeItemVO item = new KnowledgeItemVO();
            item.setItemId((long) (10000 + i));
            item.setTitle(keyword + "相关知识" + (i + 1));
            item.setCategory("中医诊断");
            item.setSummary("这是关于" + keyword + "的相关知识介绍，包含基本概念、临床应用等内容...");
            item.setSource("《中医诊断学》");
            item.setRelevance(90 - i * 5);

            knowledgeItems.add(item);
        }

        return knowledgeItems;
    }

    /**
     * 导出诊断建议报告
     *
     * @param diagnosisId 诊断记录ID
     * @return 报告文件路径
     */
    @Override
    public String exportSuggestionReport(Long diagnosisId) {
        // 检查是否有建议
        LambdaQueryWrapper<DiagnosisSuggestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiagnosisSuggestion::getDiagnosisId, diagnosisId);
        List<DiagnosisSuggestion> suggestions = list(queryWrapper);

        if (suggestions.isEmpty()) {
            throw new ServiceException("没有可导出的诊断建议");
        }

        // TODO: 实现报告生成逻辑
        // 1. 生成PDF报告文件，包含各类建议、治疗方案等信息

        // 2. 返回报告文件的下载路径
        return "reports/suggestion/" + DateUtils.datePath() + "/" + diagnosisId + ".pdf";
    }

    /**
     * 获取辨证结果
     */
    private SyndromeIdentification getSyndromeIdentification(Long diagnosisId) {
        LambdaQueryWrapper<SyndromeIdentification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SyndromeIdentification::getDiagnosisId, diagnosisId);
        SyndromeIdentification syndromeIdentification = syndromeIdentificationMapper.selectOne(queryWrapper);

        if (syndromeIdentification == null) {
            throw new ServiceException("未找到辨证结果，无法生成诊断建议");
        }

        return syndromeIdentification;
    }

    /**
     * 删除已有的建议
     */
    private void deleteExistingSuggestions(Long diagnosisId) {
        LambdaQueryWrapper<DiagnosisSuggestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DiagnosisSuggestion::getDiagnosisId, diagnosisId);
        remove(queryWrapper);
    }

    /**
     * 生成治疗原则建议
     */
    private DiagnosisSuggestion generateTreatmentPrincipleSuggestion(
            SyndromeIdentification syndromeIdentification, Long diagnosisId, Long patientId, Long doctorId) {
        DiagnosisSuggestion suggestion = new DiagnosisSuggestion();
        suggestion.setDiagnosisId(diagnosisId);
        suggestion.setPatientId(patientId);
        suggestion.setDoctorId(doctorId);
        suggestion.setSuggestionType("treatment_principle");
        suggestion.setSuggestionTitle("治疗原则建议");

        // 基于辨证结果生成治疗原则建议
        String syndromeType = syndromeIdentification.getSyndromeType();
        String treatmentPrinciple = syndromeIdentification.getTreatmentPrinciple();

        StringBuilder content = new StringBuilder();
        content.append("基于辨证结果：").append(syndromeType).append("\n\n");
        content.append("治疗原则：").append(treatmentPrinciple).append("\n\n");
        content.append("治疗方向：\n");

        // 根据证型补充具体治疗方向
        if (syndromeType.contains("实热")) {
            content.append("1. 清热解毒，可选用黄连、黄芩、栀子等清热药物。\n");
            content.append("2. 泻火解表，可考虑石膏、知母等药物清泄火热。\n");
            content.append("3. 可配合物理降温措施，如温水擦浴等。\n");
        } else if (syndromeType.contains("虚热")) {
            content.append("1. 滋阴降火，可选用生地、玄参、麦冬等养阴药物。\n");
            content.append("2. 清热养阴，可考虑青蒿、地骨皮等清虚热药物。\n");
            content.append("3. 注意休息，避免劳累，避免辛辣刺激食物。\n");
        } else if (syndromeType.contains("实寒")) {
            content.append("1. 温经散寒，可选用干姜、肉桂、附子等温热药物。\n");
            content.append("2. 行气活血，可考虑川芎、红花等活血化瘀药物。\n");
            content.append("3. 可配合热敷、艾灸等外治法。\n");
        } else if (syndromeType.contains("虚寒")) {
            content.append("1. 温阳补虚，可选用人参、黄芪、肉桂等补阳药物。\n");
            content.append("2. 回阳救逆，可考虑附子、干姜等温阳药物。\n");
            content.append("3. 保暖，适当增加营养，进食温热食物。\n");
        } else if (syndromeType.contains("气血两虚")) {
            content.append("1. 补气养血，可选用黄芪、党参、当归、熟地等补益药物。\n");
            content.append("2. 调和气血，可考虑甘草、陈皮等调和药物。\n");
            content.append("3. 加强营养，适当休息，避免过度劳累。\n");
        } else {
            content.append("1. 根据具体症状，辨证施治。\n");
            content.append("2. 建议结合患者体质特点，个体化治疗。\n");
        }

        suggestion.setSuggestionContent(content.toString());
        suggestion.setStatus(0); // 待处理
        suggestion.setIsModified(0); // 未修改
        suggestion.setGenerationTime(DateUtils.getNowDate());
        suggestion.setCreateBy(String.valueOf(doctorId));

        return suggestion;
    }

    /**
     * 生成中药处方建议
     */
    private DiagnosisSuggestion generateHerbalPrescriptionSuggestion(
            SyndromeIdentification syndromeIdentification, Long diagnosisId, Long patientId, Long doctorId) {
        DiagnosisSuggestion suggestion = new DiagnosisSuggestion();
        suggestion.setDiagnosisId(diagnosisId);
        suggestion.setPatientId(patientId);
        suggestion.setDoctorId(doctorId);
        suggestion.setSuggestionType("herbal_prescription");
        suggestion.setSuggestionTitle("中药处方建议");

        // 基于辨证结果生成中药处方建议
        String syndromeType = syndromeIdentification.getSyndromeType();
        String diseaseType = syndromeIdentification.getDiseaseType();

        StringBuilder content = new StringBuilder();
        content.append("证型：").append(syndromeType).append("\n");
        content.append("病名：").append(diseaseType).append("\n\n");
        content.append("推荐方剂：\n");

        // 根据证型和病名推荐方剂
        if (diseaseType.contains("感冒") && syndromeType.contains("实热")) {
            content.append("【银翘散】\n");
            content.append("组成：金银花、连翘、桔梗、荆芥、淡豆豉、薄荷、牛蒡子、甘草。\n");
            content.append("功效：辛凉解表，清热解毒。\n");
            content.append("用法：水煎服，日1剂，分2次服用。\n");
        } else if (diseaseType.contains("感冒") && syndromeType.contains("实寒")) {
            content.append("【桂枝汤】\n");
            content.append("组成：桂枝、白芍、生姜、大枣、甘草。\n");
            content.append("功效：发汗解表，调和营卫。\n");
            content.append("用法：水煎服，日1剂，分2次服用。\n");
        } else if (diseaseType.contains("胃痛") && syndromeType.contains("实热")) {
            content.append("【黄连汤】\n");
            content.append("组成：黄连、干姜、半夏、甘草。\n");
            content.append("功效：清热燥湿，和胃止痛。\n");
            content.append("用法：水煎服，日1剂，分2次服用。\n");
        } else if (diseaseType.contains("胃痛") && syndromeType.contains("虚寒")) {
            content.append("【理中汤】\n");
            content.append("组成：人参、白术、干姜、甘草。\n");
            content.append("功效：温中补虚，和胃止痛。\n");
            content.append("用法：水煎服，日1剂，分2次服用。\n");
        } else if (diseaseType.contains("咳嗽") && syndromeType.contains("实热")) {
            content.append("【麻杏石甘汤】\n");
            content.append("组成：麻黄、杏仁、石膏、甘草。\n");
            content.append("功效：宣肺清热，止咳平喘。\n");
            content.append("用法：水煎服，日1剂，分2次服用。\n");
        } else if (diseaseType.contains("咳嗽") && syndromeType.contains("虚寒")) {
            content.append("【小青龙汤】\n");
            content.append("组成：麻黄、桂枝、干姜、细辛、半夏、五味子、白芍、甘草。\n");
            content.append("功效：温肺化饮，止咳平喘。\n");
            content.append("用法：水煎服，日1剂，分2次服用。\n");
        } else {
            content.append("【辨证组方】\n");
            content.append("建议根据患者具体表现，辨证组方用药。\n");
        }

        content.append("\n注意事项：\n");
        content.append("1. 药量可根据患者体质酌情增减。\n");
        content.append("2. 若症状变化明显，宜随症加减。\n");
        content.append("3. 特殊人群（孕妇、儿童、老人）用药需谨慎。\n");

        suggestion.setSuggestionContent(content.toString());
        suggestion.setStatus(0); // 待处理
        suggestion.setIsModified(0); // 未修改
        suggestion.setGenerationTime(DateUtils.getNowDate());
        suggestion.setCreateBy(String.valueOf(doctorId));

        return suggestion;
    }

    /**
     * 生成针灸建议
     */
    private DiagnosisSuggestion generateAcupunctureSuggestion(
            SyndromeIdentification syndromeIdentification, Long diagnosisId, Long patientId, Long doctorId) {
        DiagnosisSuggestion suggestion = new DiagnosisSuggestion();
        suggestion.setDiagnosisId(diagnosisId);
        suggestion.setPatientId(patientId);
        suggestion.setDoctorId(doctorId);
        suggestion.setSuggestionType("acupuncture");
        suggestion.setSuggestionTitle("针灸治疗建议");

        // 基于辨证结果生成针灸建议
        String syndromeType = syndromeIdentification.getSyndromeType();
        String diseaseType = syndromeIdentification.getDiseaseType();

        StringBuilder content = new StringBuilder();
        content.append("证型：").append(syndromeType).append("\n");
        content.append("病名：").append(diseaseType).append("\n\n");
        content.append("推荐穴位：\n");

        // 基础穴位
        content.append("基础穴位：合谷、太冲、足三里。\n\n");

        // 根据病名和证型推荐穴位
        if (diseaseType.contains("头痛") || diseaseType.contains("眩晕")) {
            content.append("头部穴位：百会、印堂、太阳、风池。\n");
        } else if (diseaseType.contains("胃痛") || diseaseType.contains("腹痛")) {
            content.append("腹部穴位：中脘、梁门、天枢、内关。\n");
        } else if (diseaseType.contains("咳嗽")) {
            content.append("胸部穴位：肺俞、膻中、天突、列缺。\n");
        } else if (diseaseType.contains("失眠")) {
            content.append("安神穴位：神门、三阴交、安眠、四神聪。\n");
        }

        // 根据证型补充特殊穴位
        content.append("\n证型特殊穴位：\n");
        if (syndromeType.contains("实热")) {
            content.append("清热穴位：大椎、曲池、十宣（泻法）。\n");
        } else if (syndromeType.contains("虚热")) {
            content.append("滋阴穴位：太溪、复溜、三阴交（平补平泻）。\n");
        } else if (syndromeType.contains("实寒")) {
            content.append("温经穴位：关元、气海、命门（艾灸为佳）。\n");
        } else if (syndromeType.contains("虚寒")) {
            content.append("温阳穴位：关元、命门、肾俞（温补法或艾灸）。\n");
        } else if (syndromeType.contains("气血两虚")) {
            content.append("补益穴位：足三里、气海、脾俞、胃俞（补法）。\n");
        }

        content.append("\n操作方法：\n");
        content.append("1. 毫针刺法：常规消毒，平刺或直刺，得气后留针20-30分钟。\n");
        content.append("2. 艾灸：可选温和灸或雷火灸，每穴15-20分钟。\n");
        content.append("3. 推荐疗程：每周2-3次，10次为一疗程。\n");

        content.append("\n注意事项：\n");
        content.append("1. 操作时注意针刺深度，避免伤及重要脏器和血管。\n");
        content.append("2. 针灸中如出现晕针反应，应立即处理。\n");
        content.append("3. 孕妇、过度疲劳者、醉酒者慎用针灸。\n");

        suggestion.setSuggestionContent(content.toString());
        suggestion.setStatus(0); // 待处理
        suggestion.setIsModified(0); // 未修改
        suggestion.setGenerationTime(DateUtils.getNowDate());
        suggestion.setCreateBy(String.valueOf(doctorId));

        return suggestion;
    }

    /**
     * 生成生活调养建议
     */
    private DiagnosisSuggestion generateLifestyleSuggestion(
            SyndromeIdentification syndromeIdentification, Long diagnosisId, Long patientId, Long doctorId) {
        DiagnosisSuggestion suggestion = new DiagnosisSuggestion();
        suggestion.setDiagnosisId(diagnosisId);
        suggestion.setPatientId(patientId);
        suggestion.setDoctorId(doctorId);
        suggestion.setSuggestionType("lifestyle");
        suggestion.setSuggestionTitle("生活调养建议");

        // 基于辨证结果生成生活调养建议
        String syndromeType = syndromeIdentification.getSyndromeType();

        StringBuilder content = new StringBuilder();
        content.append("证型：").append(syndromeType).append("\n\n");
        content.append("生活调养建议：\n\n");

        // 饮食调养
        content.append("【饮食调养】\n");
        if (syndromeType.contains("实热")) {
            content.append("1. 宜清淡饮食，多食绿叶蔬菜、水果、薏米、绿豆等。\n");
            content.append("2. 忌辛辣刺激、油腻、煎炸、烧烤食物。\n");
            content.append("3. 可饮用菊花茶、绿茶、金银花茶等清热饮品。\n");
        } else if (syndromeType.contains("虚热")) {
            content.append("1. 宜清淡而富有营养的食物，如鸭肉、鱼肉、豆腐、百合等。\n");
            content.append("2. 忌辛辣刺激、煎炸食物，少食羊肉、韭菜等温热之品。\n");
            content.append("3. 可适量食用莲子、百合、银耳、梨等滋阴食品。\n");
        } else if (syndromeType.contains("实寒")) {
            content.append("1. 宜温热食物，如羊肉、牛肉、生姜、大葱等。\n");
            content.append("2. 忌生冷、寒凉食物，少食西瓜、梨、柿子等寒性水果。\n");
            content.append("3. 可饮用生姜红糖水、桂圆红枣茶等温热饮品。\n");
        } else if (syndromeType.contains("虚寒")) {
            content.append("1. 宜温补食物，如羊肉、鸡肉、核桃、桂圆等。\n");
            content.append("2. 忌生冷、寒凉食物，少食冷饮、冰品。\n");
            content.append("3. 可适量食用人参、黄芪炖鸡等滋补食品。\n");
        } else if (syndromeType.contains("气血两虚")) {
            content.append("1. 宜富含蛋白质和铁质的食物，如瘦肉、动物肝脏、鸡蛋、豆类等。\n");
            content.append("2. 适量食用红枣、桂圆、阿胶等补血食品。\n");
            content.append("3. 少食生冷、油腻食物，饮食宜定时定量。\n");
        }

        // 起居调摄
        content.append("\n【起居调摄】\n");
        if (syndromeType.contains("实热") || syndromeType.contains("虚热")) {
            content.append("1. 注意保持居室通风，夏季适当使用空调，但避免直吹。\n");
            content.append("2. 保持充足睡眠，避免熬夜。\n");
            content.append("3. 穿着宜宽松透气，避免过度暴晒。\n");
        } else if (syndromeType.contains("实寒") || syndromeType.contains("虚寒")) {
            content.append("1. 注意保暖，特别是腹部、背部和足部。\n");
            content.append("2. 居室保持温暖干燥，避免湿冷环境。\n");
            content.append("3. 睡眠充足，早睡早起，避免受寒。\n");
        } else if (syndromeType.contains("气血两虚")) {
            content.append("1. 作息规律，保证充足休息，避免过度劳累。\n");
            content.append("2. 居室环境宜安静、舒适，避免噪音干扰。\n");
            content.append("3. 保持心情舒畅，避免情绪波动过大。\n");
        }

        // 运动锻炼
        content.append("\n【运动锻炼】\n");
        if (syndromeType.contains("实热")) {
            content.append("1. 宜进行舒缓运动，如散步、太极拳、气功等。\n");
            content.append("2. 避免剧烈运动，防止体内热邪进一步郁结。\n");
            content.append("3. 运动时间宜选在清晨或傍晚凉爽时段。\n");
        } else if (syndromeType.contains("虚热")) {
            content.append("1. 宜进行轻柔运动，如慢走、八段锦、导引等。\n");
            content.append("2. 运动量宜适中，避免大汗淋漓，防止耗伤阴液。\n");
            content.append("3. 运动后注意休息，避免过度疲劳。\n");
        } else if (syndromeType.contains("实寒") || syndromeType.contains("虚寒")) {
            content.append("1. 宜进行温阳活血运动，如快走、慢跑、健身操等。\n");
            content.append("2. 运动前充分热身，运动后及时保暖，避免受凉。\n");
            content.append("3. 可选择室内有氧运动，如跳绳、室内单车等。\n");
        } else if (syndromeType.contains("气血两虚")) {
            content.append("1. 宜进行缓和、持久的运动，如散步、慢跑、游泳等。\n");
            content.append("2. 运动量由小到大，逐渐增加，避免突然剧烈运动。\n");
            content.append("3. 注意运动与休息结合，防止过度疲劳。\n");
        }

        // 情志调节
        content.append("\n【情志调节】\n");
        content.append("1. 保持心情舒畅，避免情绪过激。\n");
        content.append("2. 可尝试音乐疗法、读书、绘画等兴趣活动调节情绪。\n");
        content.append("3. 适当进行冥想、瑜伽等放松训练。\n");
        content.append("4. 建立良好的社交圈，增加正面社会支持。\n");

        suggestion.setSuggestionContent(content.toString());
        suggestion.setStatus(0); // 待处理
        suggestion.setIsModified(0); // 未修改
        suggestion.setGenerationTime(DateUtils.getNowDate());
        suggestion.setCreateBy(String.valueOf(doctorId));

        return suggestion;
    }

    /**
     * 将实体转换为视图对象
     */
    private DiagnosisSuggestionVO convertToVO(DiagnosisSuggestion suggestion) {
        if (suggestion == null) {
            return null;
        }

        DiagnosisSuggestionVO vo = new DiagnosisSuggestionVO();
        vo.setSuggestionId(suggestion.getSuggestionId());
        vo.setDiagnosisId(suggestion.getDiagnosisId());
        vo.setPatientId(suggestion.getPatientId());
        vo.setDoctorId(suggestion.getDoctorId());
        vo.setSuggestionType(suggestion.getSuggestionType());
        vo.setSuggestionTitle(suggestion.getSuggestionTitle());
        
        // 设置建议内容（兼容新旧两种命名）
        if (suggestion.getSuggestionContent() != null) {
            vo.setSuggestionContent(suggestion.getSuggestionContent());
            vo.setSuggestion(suggestion.getSuggestionContent()); // 旧的字段名
        } else if (suggestion.getSuggestion() != null) {
            vo.setSuggestionContent(suggestion.getSuggestion());
            vo.setSuggestion(suggestion.getSuggestion());
        }
        
        vo.setBasis(suggestion.getBasis());
        vo.setStatus(suggestion.getStatus());
        vo.setIsModified(suggestion.getIsModified());
        vo.setGenerationTime(suggestion.getGenerationTime());
        vo.setAdoptTime(suggestion.getAdoptTime());
        vo.setAdoptBy(suggestion.getAdoptBy());
        vo.setRejectTime(suggestion.getRejectTime());
        vo.setRejectBy(suggestion.getRejectBy());
        vo.setRejectReason(suggestion.getRejectReason());
        vo.setCreateTime(suggestion.getCreateTime());
        vo.setConfidence(suggestion.getConfidence());
        vo.setDoctorAdopted(suggestion.getDoctorAdopted());
        
        // 关联知识
        vo.setRelatedKnowledgeJson(suggestion.getRelatedKnowledge());
        
        // 参考来源
        vo.setReferencesText(suggestion.getReferences());

        return vo;
    }

    /**
     * 将实体列表转换为视图对象列表
     */
    private List<DiagnosisSuggestionVO> convertToVOList(List<DiagnosisSuggestion> suggestionList) {
        if (suggestionList == null) {
            return List.of();
        }

        return suggestionList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
}