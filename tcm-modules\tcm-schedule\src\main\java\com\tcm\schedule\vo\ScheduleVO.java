package com.tcm.schedule.vo;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * 排班视图对象
 */
@Data
public class ScheduleVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 排班ID
     */
    private Long id;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生名称
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long deptId;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 班次类型（1上午 2下午 3晚上）
     */
    private Integer shiftType;

    /**
     * 班次类型描述
     */
    private String shiftTypeDesc;

    /**
     * 排班日期
     */
    private LocalDateTime scheduleDate;

    /**
     * 最大可预约数
     */
    private Integer maxAppointment;

    /**
     * 已预约数
     */
    private Integer appointmentCount;

    /**
     * 剩余可预约数
     */
    private Integer remainingAppointment;

    /**
     * 状态（0正常 1停诊）
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
