package com.tcm.notification.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/notification")
public class NotificationController {

    @Autowired
    private JavaMailSender mailSender;

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Notification service is running");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送邮件通知
     */
    @PostMapping("/email")
    public ResponseEntity<Map<String, Object>> sendEmail(@RequestBody Map<String, String> emailRequest) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom("<EMAIL>");
            message.setTo(emailRequest.get("to"));
            message.setSubject(emailRequest.get("subject"));
            message.setText(emailRequest.get("content"));
            
            mailSender.send(message);
            
            response.put("success", true);
            response.put("message", "Email sent successfully");
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to send email: " + e.getMessage());
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 发送系统通知
     */
    @PostMapping("/system")
    public ResponseEntity<Map<String, Object>> sendSystemNotification(@RequestBody Map<String, Object> notificationRequest) {
        Map<String, Object> response = new HashMap<>();
        
        // 这里可以实现系统内部通知逻辑，如存储到数据库等
        response.put("success", true);
        response.put("message", "System notification sent");
        response.put("notificationId", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
} 