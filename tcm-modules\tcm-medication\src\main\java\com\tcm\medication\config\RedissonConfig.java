package com.tcm.medication.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;

/**
 * Redisson配置类，使用tcm-common.yml中的统一Redis连接配置
 */
@Configuration
@Lazy
public class RedissonConfig {

    @Value("${tcm.service.redis.host}")
    private String host;

    @Value("${tcm.service.redis.port}")
    private int port;

    @Value("${tcm.service.redis.password}")
    private String password;

    @Value("${tcm.service.redis.database:0}")
    private int database;

    @Bean
    @Primary
    @Lazy
    @DependsOn({ "springCloudNacosConfigSingletonClassName" })
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 创建单节点配置
        config.useSingleServer().setAddress("redis://" + host + ":" + port).setPassword(password).setDatabase(database)
                .setConnectionMinimumIdleSize(1).setConnectionPoolSize(2);

        return Redisson.create(config);
    }
}
