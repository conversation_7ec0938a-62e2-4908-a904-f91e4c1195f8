package com.tcm.inspection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.inspection.domain.InspectionRecord;
import com.tcm.inspection.mapper.InspectionRecordMapper;
import com.tcm.inspection.service.InspectionRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 检查记录服务实现类
 */
@Service
public class InspectionRecordServiceImpl extends ServiceImpl<InspectionRecordMapper, InspectionRecord> implements InspectionRecordService {

    @Autowired
    private InspectionRecordMapper inspectionRecordMapper;

    /**
     * 查询检查记录列表
     *
     * @param inspectionRecord 检查记录信息
     * @return 检查记录集合
     */
    @Override
    public List<InspectionRecord> selectInspectionRecordList(InspectionRecord inspectionRecord) {
        return inspectionRecordMapper.selectInspectionRecordList(inspectionRecord);
    }

    /**
     * 分页查询检查记录列表
     *
     * @param page             分页信息
     * @param inspectionRecord 检查记录信息
     * @return 检查记录分页信息
     */
    @Override
    public IPage<InspectionRecord> selectInspectionRecordPage(Page<InspectionRecord> page, InspectionRecord inspectionRecord) {
        return inspectionRecordMapper.selectInspectionRecordPage(page, inspectionRecord);
    }

    /**
     * 根据ID查询检查记录
     *
     * @param recordId 检查记录ID
     * @return 检查记录信息
     */
    @Override
    public InspectionRecord selectInspectionRecordById(Long recordId) {
        return inspectionRecordMapper.selectInspectionRecordById(recordId);
    }

    /**
     * 根据检查单号查询检查记录
     *
     * @param inspectionNo 检查单号
     * @return 检查记录信息
     */
    @Override
    public InspectionRecord selectInspectionRecordByInspectionNo(String inspectionNo) {
        return inspectionRecordMapper.selectInspectionRecordByInspectionNo(inspectionNo);
    }

    /**
     * 根据患者ID查询检查记录列表
     *
     * @param patientId 患者ID
     * @return 检查记录集合
     */
    @Override
    public List<InspectionRecord> selectInspectionRecordByPatientId(Long patientId) {
        return inspectionRecordMapper.selectInspectionRecordByPatientId(patientId);
    }

    /**
     * 新增检查记录
     *
     * @param inspectionRecord 检查记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertInspectionRecord(InspectionRecord inspectionRecord) {
        // 生成检查单号
        if (inspectionRecord.getInspectionNo() == null || inspectionRecord.getInspectionNo().isEmpty()) {
            inspectionRecord.setInspectionNo(generateInspectionNo());
        }
        
        // 设置初始状态为待执行
        if (inspectionRecord.getStatus() == null) {
            inspectionRecord.setStatus(0);
        }
        
        return save(inspectionRecord);
    }

    /**
     * 修改检查记录
     *
     * @param inspectionRecord 检查记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateInspectionRecord(InspectionRecord inspectionRecord) {
        return updateById(inspectionRecord);
    }

    /**
     * 删除检查记录
     *
     * @param recordId 检查记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteInspectionRecordById(Long recordId) {
        // 逻辑删除
        InspectionRecord record = new InspectionRecord();
        record.setRecordId(recordId);
        record.setDelFlag(1);
        return updateById(record);
    }

    /**
     * 批量删除检查记录
     *
     * @param recordIds 需要删除的检查记录ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteInspectionRecordByIds(Long[] recordIds) {
        // 批量逻辑删除
        InspectionRecord record = new InspectionRecord();
        record.setDelFlag(1);
        return update(record,
                new LambdaQueryWrapper<InspectionRecord>()
                        .in(InspectionRecord::getRecordId, Arrays.asList(recordIds)));
    }

    /**
     * 更新检查结果
     *
     * @param inspectionRecord 检查记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateInspectionResult(InspectionRecord inspectionRecord) {
        // 更新记录状态为已出结果
        inspectionRecord.setStatus(2);
        // 设置执行时间（如果未设置）
        if (inspectionRecord.getExecuteTime() == null) {
            inspectionRecord.setExecuteTime(new Date());
        }
        return updateById(inspectionRecord);
    }

    /**
     * 取消检查记录
     *
     * @param recordId 检查记录ID
     * @param reason   取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public boolean cancelInspectionRecord(Long recordId, String reason) {
        InspectionRecord record = getById(recordId);
        if (record != null) {
            record.setStatus(3); // 设置为已取消
            record.setRemark(reason); // 设置取消原因
            return updateById(record);
        }
        return false;
    }

    /**
     * 生成检查单号
     * 
     * @return 检查单号
     */
    @Override
    public String generateInspectionNo() {
        // 生成格式：JC + 年月日 + 6位随机数
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String dateStr = dateFormat.format(new Date());
        
        int randomNum = ThreadLocalRandom.current().nextInt(100000, 999999);
        
        return "JC" + dateStr + randomNum;
    }
} 