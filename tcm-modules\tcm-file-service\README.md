# TCM文件服务模块

## 简介

TCM文件服务模块（tcm-file-service）是中医智能诊疗系统的文件管理服务，提供文件上传、下载、存储和管理功能。支持本地存储和MinIO对象存储，实现文件的集中管理和高效访问。

## 功能特点

- 文件上传：支持单文件和批量文件上传
- 文件下载：通过文件ID下载文件
- 文件管理：提供文件信息查询、删除等管理功能
- 业务关联：支持将文件与业务ID关联，便于业务模块调用
- 多存储支持：支持本地文件系统和MinIO对象存储
- 文件去重：基于MD5实现文件去重，节省存储空间
- 安全控制：文件类型检测，防止恶意文件上传

## 技术栈

- Spring Boot 2.7.x
- Spring Cloud Alibaba
- MyBatis Plus
- MinIO Client
- Apache Tika

## 配置说明

### 基本配置

```yaml
tcm:
  file:
    storage:
      # 本地存储基础路径
      base-path: /tcm-files
      # 临时文件存储路径
      temp-path: /tcm-files/temp
      # 最大文件大小
      max-size: 50MB
      # MinIO配置
      minio:
        # 是否启用MinIO
        enabled: false
        # MinIO服务端点
        endpoint: http://minio:9000
        # 访问密钥
        access-key: minioadmin
        # 密钥
        secret-key: minioadmin
        # 存储桶名称
        bucket: tcm-files
```

## API接口

### 文件上传

- 单文件上传: `POST /file/upload`
- 批量文件上传: `POST /file/upload/batch`

### 文件下载

- 下载文件: `GET /file/download/{fileId}`

### 文件管理

- 获取文件信息: `GET /file/info/{fileId}`
- 获取文件列表: `GET /file/list?businessId={businessId}&module={module}`
- 删除文件: `DELETE /file/{fileId}`
- 批量删除文件: `DELETE /file/batch`
- 根据业务ID删除文件: `DELETE /file/business?businessId={businessId}&module={module}`
- 更新文件状态: `PUT /file/status`

## 数据库表结构

文件信息表(file_info):

| 字段名 | 类型 | 说明 |
| --- | --- | --- |
| file_id | bigint | 文件ID |
| file_name | varchar(255) | 文件名称 |
| original_name | varchar(255) | 原始文件名 |
| extension | varchar(50) | 文件扩展名 |
| file_size | bigint | 文件大小(字节) |
| file_path | varchar(500) | 文件存储路径 |
| file_url | varchar(500) | 文件访问URL |
| storage_type | tinyint | 存储类型(0:本地存储 1:MinIO 2:阿里云OSS) |
| file_type | tinyint | 文件类型(0:普通文件 1:图片 2:视频 3:音频 4:文档) |
| mime_type | varchar(128) | 文件MIME类型 |
| md5 | varchar(32) | 文件MD5值 |
| status | tinyint | 文件状态(0:临时 1:永久) |
| module | varchar(100) | 业务模块 |
| business_id | varchar(64) | 业务ID |
| create_by | bigint | 创建者ID |
| create_time | datetime | 创建时间 |
| update_by | bigint | 更新者ID |
| update_time | datetime | 更新时间 |
| remark | varchar(500) | 备注 |
| del_flag | tinyint | 删除标志(0:未删除 1:已删除) |

## 启动说明

1. 确保MySQL数据库已创建相应表结构
2. 配置application.yml中的数据库连接信息
3. 如需使用MinIO，配置MinIO相关参数并设置enabled为true
4. 运行start.bat脚本或使用java -jar命令启动服务

## 其他说明

- 文件上传默认为临时状态，需调用updateStatus接口将其转为永久状态
- 临时文件可通过定时任务清理（待实现）
- 文件实际存储路径为：base-path/module/fileName 