package com.tcm.appointment.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.io.Serializable;

/**
 * 时间段视图对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TimeSlotVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 时间段值（例如：08:00-08:30）
     */
    private String value;
    
    /**
     * 时间段标签（例如：上午 08:00-08:30）
     */
    private String label;
    
    /**
     * 时间段状态（0-可用 1-已约满）
     */
    private Integer status;
    
    /**
     * 时间段类型（1-上午 2-下午 3-晚上）
     */
    private Integer type;
    
    /**
     * 时间段类型名称
     */
    private String typeName;
    
    /**
     * 排序号
     */
    private Integer orderNum;
} 