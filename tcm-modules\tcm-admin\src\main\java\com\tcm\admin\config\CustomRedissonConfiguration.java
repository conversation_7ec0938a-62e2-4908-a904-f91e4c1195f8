package com.tcm.admin.config;

import org.redisson.spring.starter.RedissonAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.context.annotation.Configuration;

/**
 * 自定义Redisson配置类，用于解决Redisson与Spring Boot版本不兼容的问题
 * 
 * <AUTHOR>
 */
@Configuration
@AutoConfigureBefore({ RedisAutoConfiguration.class, RedissonAutoConfiguration.class })
public class CustomRedissonConfiguration {
    // 空配置类，目的是改变加载顺序，避免版本不兼容导致的FactoryBean问题
}