server:
  port: ${tcm.service.knowledge.port:${TCM_KNOWLEDGE_PORT:9300}}
  servlet:
    context-path: /knowledge

spring:
  application:
    name: tcm-knowledge
  # 引入公共配置
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
    
  # 允许Bean覆盖，解决RedisTemplate等Bean定义冲突问题
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
    
  # 数据库配置 - 使用application-common.yml中的配置
  datasource:
    # 只配置额外的连接池参数
    druid:
      # 高可用性配置
      keep-alive: true
      # 缓解连接失败问题
      test-on-borrow: true
  
  # 数据库初始化配置 - 替代已废弃的datasource.continue-on-error
  sql:
    init:
      continue-on-error: true
      
  # 暂时禁用Liquibase自动配置
  liquibase:
    enabled: false
      
  # Redis配置
  redis:
    # 从统一配置中获取
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    database: ${tcm.service.redis.database:0}
    password: ${tcm.service.redis.password:}
    timeout: ${tcm.service.redis.timeout:10000}
    lettuce:
      pool:
        max-active: ${tcm.service.redis.pool.max-active:8}
        max-wait: ${tcm.service.redis.pool.max-wait:-1}
        max-idle: ${tcm.service.redis.pool.max-idle:8}
        min-idle: ${tcm.service.redis.pool.min-idle:0}
      
  # Neo4j配置
  neo4j:
    uri: bolt://${tcm.service.neo4j.host}:${tcm.service.neo4j.boltPort}
    authentication:
      username: ${tcm.service.neo4j.username}
      password: ${tcm.service.neo4j.password}
    
  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: tcm-common.yml
            refresh: true

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.knowledge.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    banner: false
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh-CN
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 知识库服务API
    enable-host-text: ${server.servlet.context-path}
    enable-footer-custom: true
    footer-custom-content: 中医诊疗系统 - 知识库服务
  production: false

# 日志配置
logging:
  level:
    com.tcm: debug
    org.springframework: warn 

# Feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

# RocketMQ配置 - 使用统一配置中的参数
rocketmq:
  name-server: ${tcm.service.rocketMq.name-server}
  producer:
    group: ${spring.application.name}-producer-group

# Sentinel配置 - 简化配置避免冲突
spring.cloud.sentinel:
  transport:
    # 仅配置基本端口
    port: 8719
  eager: false

