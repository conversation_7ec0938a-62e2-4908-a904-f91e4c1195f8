package com.tcm.medication.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 药品实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("tcm_medicine")
public class Medicine {

    /**
     * 药品ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 药品编码
     */
    private String medicineCode;

    /**
     * 药品名称
     */
    private String medicineName;

    /**
     * 拼音码
     */
    private String pinyinCode;

    /**
     * 药品类型（1：中药材，2：中成药，3：西药）
     */
    private Integer medicineType;

    /**
     * 药品分类ID
     */
    private Long categoryId;

    /**
     * 规格
     */
    private String specification;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 库存
     */
    private Integer stock;

    /**
     * 预警库存
     */
    private Integer warnStock;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 功效
     */
    private String efficacy;

    /**
     * 性味归经
     */
    private String property;

    /**
     * 用法用量
     */
    private String usage;

    /**
     * 禁忌
     */
    private String contraindication;

    /**
     * 状态（0：停用，1：启用）
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}