<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.message.mapper.MessageMapper">

    <resultMap id="MessageResult" type="com.tcm.message.entity.Message">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="senderId" column="sender_id"/>
        <result property="senderName" column="sender_name"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="receiverName" column="receiver_name"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <select id="countUnreadMessages" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM tcm_message
        WHERE receiver_id = #{receiverId}
          AND status = 0
          AND del_flag = 0
    </select>

    <select id="selectMessagesByReceiverId" resultMap="MessageResult">
        SELECT *
        FROM tcm_message
        WHERE receiver_id = #{receiverId}
          AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <update id="markAsRead">
        UPDATE tcm_message
        SET status     = 1,
            update_by  = 'system',
            update_time = NOW()
        WHERE id = #{id}
          AND status = 0
    </update>

    <update id="markAllAsRead">
        UPDATE tcm_message
        SET status     = 1,
            update_by  = 'system',
            update_time = NOW()
        WHERE receiver_id = #{receiverId}
          AND status = 0
    </update>
</mapper> 