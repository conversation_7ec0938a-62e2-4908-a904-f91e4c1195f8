package com.tcm.prescription.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.prescription.domain.PrescriptionMedicine;
import com.tcm.prescription.dto.PrescriptionMedicineDTO;

/**
 * 处方药品服务接口
 */
public interface PrescriptionMedicineService extends IService<PrescriptionMedicine> {
    
    /**
     * 批量保存处方药品
     *
     * @param prescriptionId 处方ID
     * @param medicines 药品列表
     * @return 是否成功
     */
    boolean saveBatch(Long prescriptionId, List<PrescriptionMedicineDTO> medicines);
    
    /**
     * 根据处方ID查询药品列表
     *
     * @param prescriptionId 处方ID
     * @return 药品列表
     */
    List<PrescriptionMedicine> getByPrescriptionId(Long prescriptionId);
    
    /**
     * 删除处方药品
     *
     * @param prescriptionId 处方ID
     * @return 是否成功
     */
    boolean deleteByPrescriptionId(Long prescriptionId);
    
    /**
     * 更新处方药品
     *
     * @param prescriptionId 处方ID
     * @param medicines 药品列表
     * @return 是否成功
     */
    boolean updateBatch(Long prescriptionId, List<PrescriptionMedicineDTO> medicines);
} 