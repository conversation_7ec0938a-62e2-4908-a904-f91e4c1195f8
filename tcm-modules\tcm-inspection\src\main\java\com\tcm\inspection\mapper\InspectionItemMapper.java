package com.tcm.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.inspection.domain.InspectionItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查项目Mapper接口
 */
public interface InspectionItemMapper extends BaseMapper<InspectionItem> {

    /**
     * 查询检查项目列表
     *
     * @param inspectionItem 检查项目信息
     * @return 检查项目集合
     */
    List<InspectionItem> selectInspectionItemList(InspectionItem inspectionItem);

    /**
     * 分页查询检查项目列表
     *
     * @param page           分页信息
     * @param inspectionItem 检查项目信息
     * @return 检查项目集合
     */
    IPage<InspectionItem> selectInspectionItemPage(Page<InspectionItem> page, @Param("item") InspectionItem inspectionItem);

    /**
     * 根据ID查询检查项目
     *
     * @param itemId 检查项目ID
     * @return 检查项目信息
     */
    InspectionItem selectInspectionItemById(Long itemId);

    /**
     * 根据分类ID查询检查项目列表
     *
     * @param categoryId 分类ID
     * @return 检查项目集合
     */
    List<InspectionItem> selectInspectionItemByCategoryId(Long categoryId);

    /**
     * 根据科室ID查询检查项目列表
     *
     * @param deptId 科室ID
     * @return 检查项目集合
     */
    List<InspectionItem> selectInspectionItemByDeptId(Long deptId);

    /**
     * 检查项目名称是否存在
     *
     * @param itemName 检查项目名称
     * @return 结果
     */
    int checkItemNameUnique(String itemName);

    /**
     * 检查项目编码是否存在
     *
     * @param itemCode 检查项目编码
     * @return 结果
     */
    int checkItemCodeUnique(String itemCode);
} 