package com.tcm.appointment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 患者信息视图对象
 */
@Data
public class PatientVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 性别（0-女 1-男）
     */
    private Integer gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 出生日期
     */
    private Date birthday;
    
    /**
     * 身份证号
     */
    private String idCard;
    
    /**
     * 手机号
     */
    private String phone;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 紧急联系人
     */
    private String emergencyContact;
    
    /**
     * 紧急联系人电话
     */
    private String emergencyPhone;
    
    /**
     * 医保卡号
     */
    private String medicalInsuranceId;
    
    /**
     * 过敏史
     */
    private String allergicHistory;
    
    /**
     * 病史
     */
    private String medicalHistory;
    
    /**
     * 家族病史
     */
    private String familyHistory;
    
    /**
     * 状态（0-禁用 1-正常）
     */
    private Integer status;
} 