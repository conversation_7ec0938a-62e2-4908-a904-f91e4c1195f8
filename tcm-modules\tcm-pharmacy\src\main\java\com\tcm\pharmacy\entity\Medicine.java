package com.tcm.pharmacy.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

/**
 * 中药药品实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("tcm_medicine")
public class Medicine {
    
    /**
     * 药品ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 药品编码
     */
    @TableField("medicine_code")
    private String medicineCode;

    /**
     * 药品名称
     */
    @TableField("medicine_name")
    private String medicineName;

    /**
     * 药品拼音码
     */
    @TableField("pinyin_code")
    private String pinyinCode;

    /**
     * 药品分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 规格
     */
    @TableField("specification")
    private String specification;

    /**
     * 单位
     */
    @TableField("unit")
    private String unit;

    /**
     * 采购价
     */
    @TableField("purchase_price")
    private BigDecimal purchasePrice;

    /**
     * 零售价
     */
    @TableField("retail_price")
    private BigDecimal retailPrice;

    /**
     * 库存数量
     */
    @TableField("stock_quantity")
    private Integer stockQuantity;

    /**
     * 预警库存
     */
    @TableField("alert_stock")
    private Integer alertStock;

    /**
     * 药品产地
     */
    @TableField("origin")
    private String origin;

    /**
     * 生产厂商
     */
    @TableField("manufacturer")
    private String manufacturer;

    /**
     * 功效描述
     */
    @TableField("description")
    private String description;

    /**
     * 用法用量
     */
    @TableField("usage_guide")
    private String usageGuide;

    /**
     * 状态（0正常 1停用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建者
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新者
     */
    @TableField("update_by")
    private String updateBy;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    @TableField("del_flag")
    private Integer delFlag;
} 