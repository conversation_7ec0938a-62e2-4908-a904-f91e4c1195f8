package com.tcm.knowledge.vo;

import lombok.Data;
import java.util.List;

/**
 * 知识分类VO
 */
@Data
public class KnowledgeCategoryVO {
    /**
     * 分类ID
     */
    private Long id;
    
    /**
     * 分类名称
     */
    private String name;
    
    /**
     * 分类编码
     */
    private String code;
    
    /**
     * 分类描述
     */
    private String description;
    
    /**
     * 排序
     */
    private Integer sort;
    
    /**
     * 父分类ID
     */
    private Long parentId;
    
    /**
     * 子分类列表
     */
    private List<KnowledgeCategoryVO> children;
}
