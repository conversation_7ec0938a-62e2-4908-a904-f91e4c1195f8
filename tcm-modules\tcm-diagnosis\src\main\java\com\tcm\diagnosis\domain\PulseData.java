package com.tcm.diagnosis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 脉诊数据实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_pulse_data")
public class PulseData extends BaseEntity {

    /**
     * 数据ID
     */
    @TableId
    private Long dataId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 脉象位置（寸/关/尺）
     */
    private String pulsePosition;
    
    /**
     * 脉象深度（浅/中/深）
     */
    private String pulseDepth;

    /**
     * 采集时间
     */
    private Date captureTime;

    /**
     * 原始数据文件路径
     */
    private String rawDataPath;
    
    /**
     * 脉象波形数据
     */
    private String pulseWaveData;

    /**
     * 特征数据（JSON格式）
     */
    private String featureData;

    /**
     * 采样率（Hz）
     */
    private Integer sampleRate;

    /**
     * 采样时长（秒）
     */
    private Integer sampleDuration;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 状态（0：未分析，1：已分析）
     */
    private Integer status;
}