package com.tcm.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.integration.entity.DataSyncRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据同步记录Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface DataSyncRecordMapper extends BaseMapper<DataSyncRecord> {

    /**
     * 查询最近一次同步记录
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @return 同步记录
     */
    DataSyncRecord selectLastSyncRecord(@Param("systemCode") String systemCode, @Param("syncType") String syncType);

    /**
     * 统计同步成功率
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @return 成功率
     */
    Double calculateSuccessRate(@Param("systemCode") String systemCode, @Param("syncType") String syncType);
}