package com.tcm.message.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.message.entity.Message;
import com.tcm.message.mapper.MessageMapper;
import com.tcm.message.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 消息服务实现类
 */
@Service
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message> implements MessageService {

    @Autowired
    private MessageMapper messageMapper;

    @Override
    @Transactional
    public boolean sendMessage(Message message) {
        // 设置默认值
        if (message.getStatus() == null) {
            message.setStatus(0); // 未读状态
        }
        if (message.getDelFlag() == null) {
            message.setDelFlag(0); // 未删除
        }
        if (message.getCreateTime() == null) {
            message.setCreateTime(LocalDateTime.now());
        }

        return save(message);
    }

    @Override
    @Transactional
    public boolean batchSendMessage(Message message, List<Long> receiverIds) {
        if (receiverIds == null || receiverIds.isEmpty()) {
            return false;
        }

        List<Message> messageList = new ArrayList<>();
        for (Long receiverId : receiverIds) {
            Message newMessage = new Message();
            // 复制消息基本信息
            newMessage.setTitle(message.getTitle());
            newMessage.setContent(message.getContent());
            newMessage.setType(message.getType());
            newMessage.setSenderId(message.getSenderId());
            newMessage.setSenderName(message.getSenderName());

            // 设置接收者信息
            newMessage.setReceiverId(receiverId);
            newMessage.setReceiverName(message.getReceiverName()); // 实际应用中可能需要查询接收者名称

            // 设置默认值
            newMessage.setStatus(0); // 未读状态
            newMessage.setDelFlag(0); // 未删除
            newMessage.setCreateTime(LocalDateTime.now());
            newMessage.setCreateBy(message.getCreateBy());
            newMessage.setRemark(message.getRemark());

            messageList.add(newMessage);
        }

        return saveBatch(messageList);
    }

    @Override
    public Page<Message> getMessageList(Long receiverId, Page<Message> page) {
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getReceiverId, receiverId)
                .eq(Message::getDelFlag, 0)
                .orderByDesc(Message::getCreateTime);

        return page(page, queryWrapper);
    }

    @Override
    public Integer getUnreadMessageCount(Long receiverId) {
        return messageMapper.countUnreadMessages(receiverId);
    }

    @Override
    @Transactional
    public boolean markAsRead(Long id) {
        return messageMapper.markAsRead(id) > 0;
    }

    @Override
    @Transactional
    public boolean markAllAsRead(Long receiverId) {
        return messageMapper.markAllAsRead(receiverId) > 0;
    }

    @Override
    @Transactional
    public boolean deleteMessage(Long id) {
        // 逻辑删除
        Message message = new Message();
        message.setId(id);
        message.setDelFlag(1);
        message.setUpdateTime(LocalDateTime.now());
        message.setUpdateBy("system");

        return updateById(message);
    }
}