package com.tcm.device.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.common.core.domain.R;
import com.tcm.device.domain.DeviceData;
import com.tcm.device.service.IDeviceDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 设备数据控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/device/data")
public class DeviceDataController {

    @Autowired
    private IDeviceDataService deviceDataService;

    /**
     * 获取设备数据列表
     */
    @GetMapping("/list")
    public R<List<DeviceData>> list(
            @RequestParam(required = true) Long deviceId,
            @RequestParam(required = false) String dataType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        IPage<DeviceData> page = new Page<>(pageNum, pageSize);
        IPage<DeviceData> pageResult = deviceDataService.selectDeviceDataPage(page, deviceId, dataType, startTime,
                endTime);
        return R.ok(pageResult.getRecords());
    }

    /**
     * 上传设备数据
     */
    @PostMapping("/upload")
    public R<Boolean> uploadData(
            @RequestParam(required = true) Long deviceId,
            @RequestParam(required = true) String dataType,
            @RequestParam(required = true) String dataValue,
            @RequestParam(required = false) String unit) {
        return R.ok(deviceDataService.saveDeviceData(deviceId, dataType, dataValue, unit));
    }

    /**
     * 批量上传设备数据
     */
    @PostMapping("/upload/batch")
    public R<Boolean> uploadBatchData(
            @RequestParam(required = true) Long deviceId,
            @RequestBody Map<String, Object> requestData) {

        // 从请求体中提取数据和单位
        @SuppressWarnings("unchecked")
        Map<String, String> dataMap = (Map<String, String>) requestData.get("dataMap");

        @SuppressWarnings("unchecked")
        Map<String, String> unitMap = (Map<String, String>) requestData.get("unitMap");

        if (dataMap == null) {
            return R.fail("设备数据不能为空");
        }

        if (unitMap == null) {
            unitMap = new HashMap<>();
        }

        return R.ok(deviceDataService.batchSaveDeviceData(deviceId, dataMap, unitMap));
    }

    /**
     * 获取最新设备数据
     */
    @GetMapping("/latest")
    public R<List<DeviceData>> getLatestData(@RequestParam(required = true) Long deviceId) {
        return R.ok(deviceDataService.getLatestDeviceData(deviceId));
    }

    /**
     * 获取指定类型的最新设备数据
     */
    @GetMapping("/latest/{dataType}")
    public R<DeviceData> getLatestDataByType(
            @RequestParam(required = true) Long deviceId,
            @PathVariable(required = true) String dataType) {
        return R.ok(deviceDataService.getLatestDeviceData(deviceId, dataType));
    }

    /**
     * 按时间范围获取设备数据
     */
    @GetMapping("/timerange")
    public R<List<DeviceData>> getDataByTimeRange(
            @RequestParam(required = true) Long deviceId,
            @RequestParam(required = false) String dataType,
            @RequestParam(required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam(required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        return R.ok(deviceDataService.getDeviceDataByTimeRange(deviceId, dataType, startTime, endTime));
    }

    /**
     * 删除历史数据
     */
    @DeleteMapping("/history")
    public R<Integer> deleteHistoryData(
            @RequestParam(required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date beforeTime) {
        return R.ok(deviceDataService.deleteDeviceDataBefore(beforeTime));
    }
}