package com.tcm.user.service.impl;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.user.entity.SysLogOperation;
import com.tcm.user.mapper.SysLogOperationMapper;
import com.tcm.user.service.SysLogOperationService;

import lombok.extern.slf4j.Slf4j;

/**
 * 系统操作日志服务实现类
 */
@Slf4j
@Service
public class SysLogOperationServiceImpl extends ServiceImpl<SysLogOperationMapper, SysLogOperation>
        implements SysLogOperationService {

    /**
     * 保存操作日志
     *
     * @param log 日志信息
     */
    @Async
    @Override
    public void saveLog(SysLogOperation logOperation) {
        try {
            // 异步保存日志
            save(logOperation);
        } catch (Exception e) {
            log.error("保存操作日志异常", e);
        }
    }
}