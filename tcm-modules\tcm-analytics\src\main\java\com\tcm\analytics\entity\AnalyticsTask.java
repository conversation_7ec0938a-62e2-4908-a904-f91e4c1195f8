package com.tcm.analytics.entity;

import java.time.LocalDateTime;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 分析任务实体类
 */
@Data
@TableName("analytics_task")
public class AnalyticsTask {

    /**
     * 任务ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型（0:实时分析, 1:批量分析, 2:预测分析）
     */
    private Integer taskType;

    /**
     * 任务状态（0:等待中, 1:运行中, 2:已完成, 3:失败）
     */
    private Integer taskStatus;

    /**
     * 任务参数（JSON格式）
     */
    private String taskParams;

    /**
     * 任务结果（JSON格式）
     */
    private String taskResult;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标记（0:未删除, 1:已删除）
     */
    private Integer delFlag;

    /**
     * 用户ID
     */
    private Long userId;

    // 兼容旧代码的方法

    /**
     * 获取任务类型
     */
    public String getType() {
        if (taskType == null)
            return null;
        switch (taskType) {
        case 0:
            return "DATA_PREVIEW";
        case 1:
            return "DATA_ANALYSIS";
        case 2:
            return "DATA_VISUALIZATION";
        default:
            return "UNKNOWN";
        }
    }

    /**
     * 获取任务状态
     */
    public String getStatus() {
        if (taskStatus == null)
            return null;
        switch (taskStatus) {
        case 0:
            return "WAITING";
        case 1:
            return "RUNNING";
        case 2:
            return "COMPLETED";
        case 3:
            return "FAILED";
        case 4:
            return "CANCELLED";
        default:
            return "UNKNOWN";
        }
    }

    /**
     * 获取任务结果
     */
    public String getResult() {
        return taskResult;
    }

    /**
     * 设置错误信息
     */
    public void setErrorMessage(String message) {
        this.remark = message;
    }

    /**
     * 获取任务参数
     */
    public Map<String, Object> getParameters() {
        // 简化实现，实际应该解析JSON
        return Map.of("chartType", "bar");
    }
}