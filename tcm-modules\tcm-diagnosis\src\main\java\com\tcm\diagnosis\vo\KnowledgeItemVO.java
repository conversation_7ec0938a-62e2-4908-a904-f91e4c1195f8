package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 知识条目视图对象
 */
@Data
public class KnowledgeItemVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 条目ID
     */
    private String id;
    
    /**
     * 条目ID（数字类型）
     */
    private Long itemId;

    /**
     * 条目标题
     */
    private String title;

    /**
     * 条目类型（证型/方剂/药物/疾病等）
     */
    private String type;
    
    /**
     * 分类
     */
    private String category;

    /**
     * 条目内容摘要
     */
    private String summary;

    /**
     * 内容详情
     */
    private String content;

    /**
     * 来源
     */
    private String source;

    /**
     * 标签列表
     */
    private List<String> tags;

    /**
     * 相关条目列表
     */
    private List<Map<String, String>> relatedItems;

    /**
     * 相关度（0-100）
     */
    private Integer relevance;
}