# TCM系统配置管理规范

## 一、通用配置使用规则

### 1. 配置文件结构
- **通用配置文件**: `tcm-common.yml` - 存放所有模块共享的配置项
- **模块配置文件**: 各模块的 `application.yml` - 存放模块特有配置
- **启动配置文件**: 各模块的 `bootstrap.yml` - 用于引入通用配置

### 2. 通用配置引入方式
所有模块必须在 `bootstrap.yml` 中引入通用配置：
```yaml
spring:
  cloud:
    nacos:
      config:
        shared-configs:
          - data-id: tcm-common.yml
            group: DEFAULT_GROUP
            refresh: true
```

### 3. 配置引用规则
- 使用 `${tcm.service.xxx}` 的方式引用通用配置中的值
- 示例：`${tcm.service.mysql.host}`、`${tcm.service.redis.port}`

### 4. 配置优先级
1. 环境变量 (最高优先级)
2. 模块特定配置 `application.yml`
3. 通用配置 `tcm-common.yml`
4. 默认值 (最低优先级)

### 5. 禁止事项
- **禁止**直接在模块配置中使用硬编码的服务地址（如localhost）
- **禁止**重复定义通用配置中已有的基础配置项
- **禁止**修改通用配置中的关键参数

### 6. 必要依赖
每个模块必须添加以下依赖以支持配置中心：
```xml
<dependency>
    <groupId>com.alibaba.cloud</groupId>
    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
</dependency>
```

## 二、通用配置项说明

### 1. 数据库配置
使用方式：
```yaml
spring:
  datasource:
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/数据库名?参数
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
```

### 2. Redis配置
使用方式：
```yaml
spring:
  data:
    redis:
      host: ${tcm.service.redis.host}
      port: ${tcm.service.redis.port}
      password: ${tcm.service.redis.password}
      database: ${tcm.service.redis.database}
```

### 3. Nacos配置
使用方式：
```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${TCM_NACOS_HOST:***********}:${TCM_NACOS_PORT:8848}
```

## 三、最佳实践

1. 开发环境可使用默认配置，生产环境通过环境变量覆盖
2. 模块特有配置放在各自的application.yml中
3. 公共的第三方服务配置统一使用tcm.service前缀
4. 对于敏感配置，使用环境变量方式注入
5. 配置变更需通过配置中心，不允许直接修改部署环境的配置文件 