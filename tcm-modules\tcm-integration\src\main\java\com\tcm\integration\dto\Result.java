package com.tcm.integration.dto;

import lombok.Data;

/**
 * 通用返回结果
 *
 * <AUTHOR>
 */
@Data
public class Result<T> {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 状态码
     */
    private int code;

    /**
     * 消息内容
     */
    private String message;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 构造成功结果
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    /**
     * 构造成功结果
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setSuccess(true);
        result.setCode(200);
        result.setMessage("操作成功");
        result.setData(data);
        return result;
    }

    /**
     * 构造失败结果
     */
    public static <T> Result<T> error(String message) {
        return error(500, message);
    }

    /**
     * 构造失败结果
     */
    public static <T> Result<T> error(int code, String message) {
        Result<T> result = new Result<>();
        result.setSuccess(false);
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}