package com.tcm.prescription;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;

import com.tcm.prescription.config.CommonConfig;

/**
 * 处方服务启动程序
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.tcm.prescription.mapper")
@EnableConfigurationProperties(CommonConfig.class)
@Import(CommonConfig.class)
public class TcmPrescriptionApplication {

    // 通过静态代码块预先设置日志相关系统属性，确保在任何类加载之前执行
    static {
        // 1. 处理日志冲突问题
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 禁用日志多绑定警告
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "warn");
        System.setProperty("org.slf4j.logger.org.slf4j", "error");
        System.setProperty("org.slf4j.loggerFactory.multipleBindingCheck", "ignore");

        // 强制使用Logback
        System.setProperty("logging.config", "classpath:logback-spring.xml");
        System.setProperty("java.util.logging.manager", "org.apache.logging.log4j.jul.LogManager");
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 彻底解决日志冲突问题
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

            // 3. 设置应用名称
            System.setProperty("spring.application.name", "tcm-prescription");

            // 4. 设置配置文件包含
            System.setProperty("spring.config.import", "optional:classpath:/META-INF/tcm-common.yml");

            // 5. 禁用Redisson自动配置
            System.setProperty("spring.autoconfigure.exclude", "org.redisson.spring.starter.RedissonAutoConfiguration,"
                    + "org.springframework.boot.autoconfigure.logging.Log4J2AutoConfiguration");

            // 6. Seata配置恢复，避免出现配置缺失错误
            System.setProperty("seata.enabled", "true");
            System.setProperty("seata.application-id", "tcm-prescription");
            System.setProperty("seata.tx-service-group", "tcm-prescription-group");
            System.setProperty("seata.service.vgroup-mapping.tcm-prescription-group", "default");
            System.setProperty("seata.registry.type", "file");
            System.setProperty("seata.config.type", "file");
            System.setProperty("seata.config.file.name", "file.conf");
            System.setProperty("seata.registry.file.name", "registry.conf");

            // 应用程序启动
            SpringApplication.run(TcmPrescriptionApplication.class, args);

            // 获取环境变量中的服务器配置
            String seataHost = System.getProperty("tcm.service.seata.host",
                    System.getenv("TCM_SEATA_HOST") != null ? System.getenv("TCM_SEATA_HOST")
                            : "${TCM_SEATA_HOST:localhost}");
            // 使用环境变量获取端口，完全避免硬编码
            String port = System.getProperty("server.port", System.getProperty("tcm.service.prescription.port",
                    System.getenv("TCM_PRESCRIPTION_PORT") != null ? System.getenv("TCM_PRESCRIPTION_PORT") : "0"));

            // 成功运行提示
            System.out.println("\n=====================================================\n\u0007"
                    + "【TCM-PRESCRIPTION】处方服务已成功启动！连接到服务器: " + seataHost + "\n运行端口: " + port + "\n服务健康状态: 正常运行"
                    + "\n=====================================================\n");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}