package com.tcm.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.order.entity.Order;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单Mapper接口
 *
 * <AUTHOR> Team
 */
@Repository
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 分页查询订单列表
     *
     * @param page      分页参数
     * @param orderType 订单类型
     * @param status    订单状态
     * @param keyword   关键字（订单号/患者姓名/医生姓名）
     * @return 订单分页列表
     */
    IPage<Order> selectOrderPage(Page<Order> page, @Param("orderType") Integer orderType,
                                 @Param("status") Integer status, @Param("keyword") String keyword);

    /**
     * 查询订单统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    List<Map<String, Object>> selectOrderStatistics(@Param("startTime") LocalDateTime startTime,
                                                   @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查询患者订单列表
     *
     * @param patientId 患者ID
     * @param status    订单状态
     * @return 订单列表
     */
    List<Order> selectPatientOrders(@Param("patientId") Long patientId, @Param("status") Integer status);
    
    /**
     * 查询医生订单列表
     *
     * @param doctorId 医生ID
     * @param status   订单状态
     * @return 订单列表
     */
    List<Order> selectDoctorOrders(@Param("doctorId") Long doctorId, @Param("status") Integer status);
    
    /**
     * 更新订单支付状态
     *
     * @param orderId      订单ID
     * @param paymentStatus 支付状态
     * @param transactionNo 交易号
     * @param paymentTime  支付时间
     * @return 影响行数
     */
    int updateOrderPaymentStatus(@Param("orderId") Long orderId, @Param("paymentStatus") Integer paymentStatus,
                                 @Param("transactionNo") String transactionNo, @Param("paymentTime") LocalDateTime paymentTime);
}
