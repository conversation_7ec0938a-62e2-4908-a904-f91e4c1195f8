server:
  port: ${tcm.service.monitor.port:9310}
  servlet:
    context-path: /monitor

spring:
  application:
    name: tcm-monitor
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************
    username: root
    password: password
    druid:
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: tcm
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  messages:
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 20MB

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.tcm.monitor.domain
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 禁用Spring Boot Actuator
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: ''
  endpoint:
    health:
      enabled: false

# 日志配置
logging:
  level:
    "[com.tcm]": info
    "[org.springframework.web]": warn
    "[org.springframework.security]": warn
    "[com.alibaba.nacos]": ERROR
    "[org.springframework.cloud]": warn
    "[org.springframework.boot.autoconfigure]": warn
    "[org.springframework.boot]": warn
    "[org.springframework]": warn
