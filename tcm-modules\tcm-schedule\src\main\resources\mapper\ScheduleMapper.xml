<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tcm.schedule.mapper.ScheduleMapper">

    <resultMap id="ScheduleMap" type="com.tcm.schedule.entity.Schedule">
        <id property="id" column="id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="shiftType" column="shift_type"/>
        <result property="scheduleDate" column="schedule_date"/>
        <result property="maxAppointment" column="max_appointment"/>
        <result property="appointmentCount" column="appointment_count"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <resultMap id="ScheduleVOMap" type="com.tcm.schedule.vo.ScheduleVO">
        <id property="id" column="id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="shiftType" column="shift_type"/>
        <result property="shiftTypeDesc" column="shift_type_desc"/>
        <result property="scheduleDate" column="schedule_date"/>
        <result property="maxAppointment" column="max_appointment"/>
        <result property="appointmentCount" column="appointment_count"/>
        <result property="remainingAppointment" column="remaining_appointment"/>
        <result property="status" column="status"/>
        <result property="statusDesc" column="status_desc"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectSchedule">
        SELECT
            s.id, s.doctor_id, s.doctor_name, s.dept_id, s.dept_name,
            s.shift_type,
            CASE s.shift_type
                WHEN 1 THEN '上午'
                WHEN 2 THEN '下午'
                WHEN 3 THEN '晚上'
                ELSE '未知'
            END AS shift_type_desc,
            s.schedule_date, s.max_appointment, s.appointment_count,
            (s.max_appointment - s.appointment_count) AS remaining_appointment,
            s.status,
            CASE s.status
                WHEN 0 THEN '正常'
                WHEN 1 THEN '停诊'
                ELSE '未知'
            END AS status_desc,
            s.remark, s.create_time
        FROM tcm_schedule s
    </sql>

    <select id="selectSchedulePage" resultMap="ScheduleVOMap">
        <include refid="selectSchedule"/>
        <where>
            s.del_flag = 0
            <if test="sch.doctorId != null">
                AND s.doctor_id = #{sch.doctorId}
            </if>
            <if test="sch.doctorName != null and sch.doctorName != ''">
                AND s.doctor_name LIKE CONCAT('%', #{sch.doctorName}, '%')
            </if>
            <if test="sch.deptId != null">
                AND s.dept_id = #{sch.deptId}
            </if>
            <if test="sch.shiftType != null">
                AND s.shift_type = #{sch.shiftType}
            </if>
            <if test="sch.status != null">
                AND s.status = #{sch.status}
            </if>
        </where>
        ORDER BY s.schedule_date ASC
    </select>

    <select id="selectScheduleById" resultMap="ScheduleVOMap">
        <include refid="selectSchedule"/>
        WHERE s.id = #{id} AND s.del_flag = 0
    </select>

    <select id="selectScheduleByDoctorId" resultMap="ScheduleVOMap">
        <include refid="selectSchedule"/>
        <where>
            s.del_flag = 0
            AND s.doctor_id = #{doctorId}
            <if test="beginDate != null">
                AND s.schedule_date &gt;= #{beginDate}
            </if>
            <if test="endDate != null">
                AND s.schedule_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY s.schedule_date ASC
    </select>
    
    <select id="selectScheduleByDeptId" resultMap="ScheduleVOMap">
        <include refid="selectSchedule"/>
        <where>
            s.del_flag = 0
            AND s.dept_id = #{deptId}
            <if test="beginDate != null">
                AND s.schedule_date &gt;= #{beginDate}
            </if>
            <if test="endDate != null">
                AND s.schedule_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY s.schedule_date ASC
    </select>

    <update id="updateAppointmentCount">
        UPDATE tcm_schedule
        SET appointment_count = appointment_count + 1
        WHERE id = #{id} AND (max_appointment - appointment_count) > 0
    </update>
</mapper> 
