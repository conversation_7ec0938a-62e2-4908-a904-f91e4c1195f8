package com.tcm.doctor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.doctor.domain.DoctorSchedule;
import com.tcm.doctor.dto.DoctorScheduleDTO;
import com.tcm.doctor.vo.DoctorScheduleVO;

import java.util.Date;
import java.util.List;

/**
 * 医生排班服务接口
 */
public interface IDoctorScheduleService extends IService<DoctorSchedule> {
    
    /**
     * 分页查询医生排班列表
     *
     * @param doctorId  医生ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @param pageNum   页码
     * @param pageSize  页大小
     * @return 医生排班列表
     */
    IPage<DoctorScheduleVO> listDoctorSchedules(Long doctorId, Date beginDate, Date endDate, int pageNum, int pageSize);
    
    /**
     * 根据ID查询医生排班详情
     *
     * @param scheduleId 排班ID
     * @return 医生排班详情
     */
    DoctorScheduleVO getDoctorScheduleById(Long scheduleId);
    
    /**
     * 根据医生ID和日期查询排班信息
     *
     * @param doctorId     医生ID
     * @param scheduleDate 排班日期
     * @return 医生排班信息
     */
    List<DoctorScheduleVO> getDoctorSchedulesByDoctorIdAndDate(Long doctorId, Date scheduleDate);
    
    /**
     * 根据科室和日期查询排班信息
     *
     * @param department   科室
     * @param scheduleDate 排班日期
     * @return 医生排班信息
     */
    List<DoctorScheduleVO> getDoctorSchedulesByDepartmentAndDate(String department, Date scheduleDate);
    
    /**
     * 新增医生排班
     *
     * @param scheduleDTO 排班信息
     * @return 结果
     */
    boolean addDoctorSchedule(DoctorScheduleDTO scheduleDTO);
    
    /**
     * 修改医生排班
     *
     * @param scheduleDTO 排班信息
     * @return 结果
     */
    boolean updateDoctorSchedule(DoctorScheduleDTO scheduleDTO);
    
    /**
     * 删除医生排班
     *
     * @param scheduleId 排班ID
     * @return 结果
     */
    boolean deleteDoctorSchedule(Long scheduleId);
    
    /**
     * 批量新增医生排班
     *
     * @param scheduleDTOs 排班信息列表
     * @return 结果
     */
    boolean batchAddDoctorSchedules(List<DoctorScheduleDTO> scheduleDTOs);
    
    /**
     * 更新医生排班状态
     *
     * @param scheduleId 排班ID
     * @param status     状态（0-正常 1-停诊）
     * @return 结果
     */
    boolean updateDoctorScheduleStatus(Long scheduleId, Integer status);
    
    /**
     * 更新预约人数
     *
     * @param scheduleId 排班ID
     * @param count      增加数量
     * @return 结果
     */
    boolean updateAppointmentCount(Long scheduleId, int count);
} 