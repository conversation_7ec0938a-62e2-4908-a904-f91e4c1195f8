package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcm.common.core.config.TcmServiceConfig;
import com.tcm.common.core.domain.R;
import com.tcm.common.core.exception.ServiceException;
import com.tcm.common.core.utils.DateUtils;
import com.tcm.common.core.utils.SecurityUtils;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.common.core.utils.file.FileUploadUtils;
import com.tcm.common.security.utils.SecurityUtil;
import com.tcm.diagnosis.domain.PulseData;
import com.tcm.diagnosis.dto.PulseDataCaptureDTO;
import com.tcm.diagnosis.mapper.PulseDataMapper;
import com.tcm.diagnosis.service.IPulseDataService;
import com.tcm.diagnosis.vo.PulseDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 脉诊数据服务实现类
 */
@Slf4j
@Service
public class PulseDataServiceImpl extends ServiceImpl<PulseDataMapper, PulseData> implements IPulseDataService {

    @Autowired
    private TcmServiceConfig tcmServiceConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${tcm.upload.path:/tcm/upload}")
    private String uploadPath;

    /**
     * 上传并保存脉诊数据
     *
     * @param pulseDataDTO 脉诊数据
     * @return 数据ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long uploadPulseData(PulseDataCaptureDTO pulseDataDTO) {
        try {
            PulseData pulseData = new PulseData();

            // 设置基础信息
            pulseData.setPatientId(pulseDataDTO.getPatientId());
            pulseData.setDoctorId(pulseDataDTO.getDoctorId());
            pulseData.setDiagnosisId(pulseDataDTO.getDiagnosisId());
            pulseData.setPulsePosition(pulseDataDTO.getPosition());
            pulseData.setCaptureTime(new Date());
            pulseData.setSampleRate(pulseDataDTO.getSampleRate());
            pulseData
                    .setSampleDuration(pulseDataDTO.getSampleDuration() != null ? pulseDataDTO.getSampleDuration() : 0);
            pulseData.setDeviceInfo(pulseDataDTO.getDeviceInfo());
            pulseData.setStatus(0); // 初始状态设为未分析

            // 保存原始信号数据
            String dataFilePath = saveSignalData(pulseDataDTO);
            pulseData.setRawDataPath(dataFilePath);

            // 提取特征并保存
            Map<String, Object> features = extractPulseFeatures(pulseDataDTO.getSignalData());
            pulseData.setFeatureData(objectMapper.writeValueAsString(features));

            // 设置创建者
            pulseData.setCreateBy(SecurityUtils.getUsername());

            // 保存到数据库
            this.save(pulseData);

            return pulseData.getDataId();
        } catch (Exception e) {
            log.error("保存脉诊数据失败", e);
            throw new ServiceException("保存脉诊数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取脉诊数据详情
     *
     * @param dataId 数据ID
     * @return 脉诊数据视图对象
     */
    @Override
    public PulseDataVO getPulseData(Long dataId) {
        PulseData pulseData = this.getById(dataId);
        if (pulseData == null) {
            throw new ServiceException("脉诊数据不存在");
        }

        return convertToPulseDataVO(pulseData);
    }

    /**
     * 获取诊断记录关联的脉诊数据列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 脉诊数据视图对象列表
     */
    @Override
    public List<PulseDataVO> getPulseDataListByDiagnosisId(Long diagnosisId) {
        List<PulseData> pulseDataList = baseMapper.selectPulseDataByDiagnosisId(diagnosisId);
        return pulseDataList.stream()
                .map(this::convertToPulseDataVO)
                .collect(Collectors.toList());
    }

    /**
     * 获取患者的脉诊数据列表
     *
     * @param patientId 患者ID
     * @return 脉诊数据视图对象列表
     */
    @Override
    public List<PulseDataVO> getPulseDataListByPatientId(Long patientId) {
        List<PulseData> pulseDataList = baseMapper.selectPulseDataByPatientId(patientId);
        return pulseDataList.stream()
                .map(this::convertToPulseDataVO)
                .collect(Collectors.toList());
    }

    /**
     * 删除脉诊数据
     *
     * @param dataId 数据ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePulseData(Long dataId) {
        PulseData pulseData = this.getById(dataId);
        if (pulseData == null) {
            throw new ServiceException("脉诊数据不存在");
        }

        // 删除文件
        if (StringUtils.isNotEmpty(pulseData.getRawDataPath())) {
            try {
                Path path = Paths.get(pulseData.getRawDataPath());
                Files.deleteIfExists(path);
            } catch (Exception e) {
                log.error("删除脉诊数据文件失败", e);
            }
        }

        // 删除数据库记录
        return this.removeById(dataId);
    }

    /**
     * 获取最近的脉诊数据列表
     *
     * @param patientId 患者ID
     * @param limit     限制数量
     * @return 脉诊数据视图对象列表
     */
    @Override
    public List<PulseDataVO> getRecentPulseData(Long patientId, Integer limit) {
        LambdaQueryWrapper<PulseData> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PulseData::getPatientId, patientId)
                .orderByDesc(PulseData::getCaptureTime)
                .last("LIMIT " + limit);

        List<PulseData> pulseDataList = this.list(queryWrapper);
        return pulseDataList.stream()
                .map(this::convertToPulseDataVO)
                .collect(Collectors.toList());
    }

    /**
     * 保存信号数据到文件
     *
     * @param pulseDataDTO 脉诊数据DTO
     * @return 文件路径
     */
    private String saveSignalData(PulseDataCaptureDTO pulseDataDTO) throws Exception {
        // 创建保存目录
        String dataPath = uploadPath + File.separator + "pulsedata";
        Path dirPath = Paths.get(dataPath);
        if (!Files.exists(dirPath)) {
            Files.createDirectories(dirPath);
        }

        // 生成文件名
        String fileName = "pulse_" + pulseDataDTO.getPatientId() + "_" +
                DateUtils.dateTimeNow() + "_" + pulseDataDTO.getPosition() + ".dat";

        // 解码Base64并保存文件
        String filePath = dataPath + File.separator + fileName;
        byte[] decodedData = Base64.getDecoder().decode(pulseDataDTO.getSignalData());
        Files.write(Paths.get(filePath), decodedData);

        return filePath;
    }

    /**
     * 提取脉诊特征
     *
     * @param signalDataBase64 Base64编码的信号数据
     * @return 特征数据Map
     */
    private Map<String, Object> extractPulseFeatures(String signalDataBase64) {
        // 实际项目中应该调用专业的信号处理算法提取特征
        // 这里简单模拟一些基本特征
        Map<String, Object> features = new HashMap<>();

        try {
            // 解码Base64数据
            byte[] decodedData = Base64.getDecoder().decode(signalDataBase64);

            // 模拟特征提取（实际项目中应替换为真实算法）
            Random random = new Random();
            features.put("frequency", 60 + random.nextInt(40)); // 脉率
            features.put("amplitude", 0.5 + random.nextDouble()); // 振幅
            features.put("timeInterval", 0.8 + random.nextDouble(0.4)); // 时间间隔
            features.put("waveType", Arrays.asList("弦脉", "滑脉", "浮脉").get(random.nextInt(3))); // 波形类型

            // 生成波形数据（用于前端显示）
            List<Double> waveform = new ArrayList<>();
            for (int i = 0; i < 100; i++) {
                waveform.add(Math.sin(i * 0.1) + random.nextDouble() * 0.3);
            }
            features.put("waveform", waveform);

        } catch (Exception e) {
            log.error("提取脉诊特征失败", e);
            features.put("error", "特征提取失败");
        }

        return features;
    }

    /**
     * 将PulseData实体转换为PulseDataVO视图对象
     *
     * @param pulseData 脉诊数据实体
     * @return 脉诊数据视图对象
     */
    private PulseDataVO convertToPulseDataVO(PulseData pulseData) {
        PulseDataVO pulseDataVO = new PulseDataVO();

        pulseDataVO.setId(pulseData.getDataId());
        pulseDataVO.setPatientId(pulseData.getPatientId());
        pulseDataVO.setDoctorId(pulseData.getDoctorId());
        pulseDataVO.setDiagnosisId(pulseData.getDiagnosisId());
        pulseDataVO.setPulsePosition(pulseData.getPulsePosition());
        pulseDataVO.setCaptureTime(pulseData.getCaptureTime());
        pulseDataVO.setSampleRate(pulseData.getSampleRate());
        pulseDataVO.setSampleDuration(pulseData.getSampleDuration());
        pulseDataVO.setDeviceInfo(pulseData.getDeviceInfo());
        pulseDataVO.setStatus(pulseData.getStatus());

        // 解析特征数据
        if (StringUtils.isNotEmpty(pulseData.getFeatureData())) {
            try {
                Map<String, Object> features = objectMapper.readValue(pulseData.getFeatureData(), Map.class);
                pulseDataVO.setFeatures(features);

                // 提取波形数据
                if (features.containsKey("waveform")) {
                    pulseDataVO.setWaveform((List<Double>) features.get("waveform"));
                }
            } catch (JsonProcessingException e) {
                log.error("解析特征数据失败", e);
            }
        }

        return pulseDataVO;
    }
}