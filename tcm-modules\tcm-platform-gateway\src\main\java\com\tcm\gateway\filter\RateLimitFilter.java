package com.tcm.gateway.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;

/**
 * 请求限流过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RateLimitFilter implements GlobalFilter, Ordered {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 限流时间窗口，单位秒
     */
    private final int timeWindow = 1;

    /**
     * 限流请求数
     */
    private final int maxRequests = 50;

    /**
     * 限流键前缀
     */
    private static final String RATE_LIMIT_KEY = "gateway:ratelimit:";

    /**
     * Lua脚本，保证原子性
     */
    private static final String RATE_LIMIT_SCRIPT = "local key = KEYS[1]\n" +
            "local count = tonumber(ARGV[1])\n" +
            "local time = tonumber(ARGV[2])\n" +
            "local current = tonumber(redis.call('get', key) or '0')\n" +
            "if current + 1 > count then\n" +
            "    return 0\n" +
            "else\n" +
            "    redis.call('incrby', key, 1)\n" +
            "    redis.call('expire', key, time)\n" +
            "    return current + 1\n" +
            "end";

    private final RedisScript<Long> redisScript = RedisScript.of(RATE_LIMIT_SCRIPT, Long.class);

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        // 构建限流键
        String ipAddress = getIpAddress(request);
        String uri = request.getURI().getPath();
        String rateKey = RATE_LIMIT_KEY + ipAddress + ":" + uri;

        // 执行限流
        try {
            Long current = redisTemplate.execute(redisScript, Arrays.asList(rateKey),
                    maxRequests, timeWindow);

            if (current == null || current == 0) {
                log.warn("请求限流: {} - {}", ipAddress, uri);
                return tooManyRequestsResponse(response);
            }

            log.debug("请求计数: {} - {} - {}", ipAddress, uri, current);
        } catch (Exception e) {
            log.error("限流异常: ", e);
            // 发生异常不限流，继续执行
        }

        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        // 设置过滤器优先级，确保在认证过滤器之前执行
        return -200;
    }

    /**
     * 生成过多请求的响应
     */
    private Mono<Void> tooManyRequestsResponse(ServerHttpResponse response) {
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);

        String body = "{\"code\":429,\"message\":\"请求过于频繁，请稍后重试\"}";
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 获取请求IP地址
     */
    private String getIpAddress(ServerHttpRequest request) {
        String ip = request.getHeaders().getFirst("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddress().getAddress().getHostAddress();
        }

        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }
}