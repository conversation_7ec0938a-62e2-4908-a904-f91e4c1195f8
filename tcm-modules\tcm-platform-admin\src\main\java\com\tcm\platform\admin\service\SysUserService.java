package com.tcm.platform.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.platform.admin.dto.UserDTO;
import com.tcm.platform.admin.entity.SysUser;

import java.util.List;

/**
 * 系统用户服务接口
 * 
 * <AUTHOR>
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 分页查询用户列表
     * 
     * @param pageNum  页码
     * @param pageSize 每页记录数
     * @param user     查询条件
     * @return 用户分页列表
     */
    IPage<SysUser> selectUserPage(Integer pageNum, Integer pageSize, SysUser user);

    /**
     * 根据用户ID查询用户详情
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    UserDTO selectUserById(Long userId);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    UserDTO selectUserByUsername(String username);

    /**
     * 检查用户名是否唯一
     * 
     * @param username 用户名
     * @return 结果 true-用户名已存在 false-用户名不存在
     */
    boolean checkUsernameUnique(String username);

    /**
     * 新增用户
     * 
     * @param user 用户信息
     * @return 结果
     */
    boolean insertUser(UserDTO user);

    /**
     * 修改用户
     * 
     * @param user 用户信息
     * @return 结果
     */
    boolean updateUser(UserDTO user);

    /**
     * 删除用户
     * 
     * @param userId 用户ID
     * @return 结果
     */
    boolean deleteUserById(Long userId);

    /**
     * 重置用户密码
     * 
     * @param userId   用户ID
     * @param password 密码
     * @return 结果
     */
    boolean resetPassword(Long userId, String password);

    /**
     * 修改用户状态
     * 
     * @param userId 用户ID
     * @param status 状态
     * @return 结果
     */
    boolean updateUserStatus(Long userId, Integer status);

    /**
     * 获取用户权限
     * 
     * @param userId 用户ID
     * @return 权限列表
     */
    List<String> getUserPermissions(Long userId);
}