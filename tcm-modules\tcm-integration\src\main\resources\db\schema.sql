-- 系统集成配置表
CREATE TABLE IF NOT EXISTS `sys_integration_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `system_name` varchar(100) NOT NULL COMMENT '系统名称',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `system_type` varchar(20) NOT NULL COMMENT '系统类型（HIS、LIS、PACS等）',
  `api_url` varchar(255) NOT NULL COMMENT '接口地址',
  `auth_type` varchar(20) NOT NULL COMMENT '认证类型（None、Basic、OAuth、Token等）',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `password` varchar(100) DEFAULT NULL COMMENT '密码（加密存储）',
  `token` varchar(2000) DEFAULT NULL COMMENT '令牌',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌有效期',
  `connect_timeout` int(11) DEFAULT '5000' COMMENT '连接超时时间(毫秒)',
  `read_timeout` int(11) DEFAULT '10000' COMMENT '读取超时时间(毫秒)',
  `description` varchar(500) DEFAULT NULL COMMENT '系统描述',
  `status` int(1) DEFAULT '1' COMMENT '状态（0-禁用，1-启用）',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_system_code` (`system_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='系统集成配置表';

-- 系统集成API日志表
CREATE TABLE IF NOT EXISTS `sys_integration_api_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) DEFAULT NULL COMMENT '配置ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `api_name` varchar(100) NOT NULL COMMENT '接口名称',
  `request_url` varchar(500) NOT NULL COMMENT '请求URL',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `request_headers` text COMMENT '请求头',
  `request_params` text COMMENT '请求参数',
  `response_status` int(11) DEFAULT NULL COMMENT '响应状态码',
  `response_result` text COMMENT '响应结果',
  `process_time` bigint(20) DEFAULT NULL COMMENT '处理时间(毫秒)',
  `status` int(1) DEFAULT '1' COMMENT '状态（0-失败，1-成功）',
  `error_msg` varchar(500) DEFAULT NULL COMMENT '错误消息',
  `request_ip` varchar(50) DEFAULT NULL COMMENT '请求IP',
  `operate_by` varchar(50) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_system_code` (`system_code`),
  KEY `idx_api_name` (`api_name`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='系统集成API日志表';

-- 数据同步记录表
CREATE TABLE IF NOT EXISTS `sys_data_sync_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` bigint(20) NOT NULL COMMENT '配置ID',
  `system_code` varchar(50) NOT NULL COMMENT '系统编码',
  `sync_type` varchar(50) NOT NULL COMMENT '同步类型',
  `sync_direction` varchar(20) NOT NULL COMMENT '同步方向（IN-导入，OUT-导出）',
  `total_count` int(11) DEFAULT '0' COMMENT '总记录数',
  `success_count` int(11) DEFAULT '0' COMMENT '成功记录数',
  `fail_count` int(11) DEFAULT '0' COMMENT '失败记录数',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `duration` bigint(20) DEFAULT NULL COMMENT '耗时(毫秒)',
  `status` int(1) DEFAULT '0' COMMENT '状态（0-等待，1-进行中，2-成功，3-失败，4-取消）',
  `fail_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `operate_by` varchar(50) DEFAULT NULL COMMENT '操作人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_system_code` (`system_code`),
  KEY `idx_sync_type` (`sync_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='数据同步记录表'; 