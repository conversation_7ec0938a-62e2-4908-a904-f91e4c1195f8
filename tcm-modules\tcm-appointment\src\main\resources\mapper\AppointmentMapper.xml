<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.appointment.mapper.AppointmentMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.tcm.appointment.domain.Appointment">
        <id column="id" property="id"/>
        <result column="patient_id" property="patientId"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="department_id" property="departmentId"/>
        <result column="appointment_date" property="appointmentDate"/>
        <result column="time_slot" property="timeSlot"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="symptom" property="symptom"/>
        <result column="fee" property="fee"/>
        <result column="pay_status" property="payStatus"/>
        <result column="pay_time" property="payTime"/>
        <result column="arrive_time" property="arriveTime"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="order_num" property="orderNum"/>
        <result column="notes" property="notes"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 获取医生工作时间段 -->
    <select id="getDoctorWorkTimeSlots" resultType="com.tcm.appointment.vo.TimeSlotVO">
        SELECT 
            ts.value, 
            ts.label, 
            0 as status,
            ts.type,
            CASE ts.type
                WHEN 1 THEN '上午'
                WHEN 2 THEN '下午'
                WHEN 3 THEN '晚上'
                ELSE '未知'
            END as typeName,
            ts.order_num as orderNum
        FROM 
            doctor_schedule ds
        JOIN 
            time_slot ts ON ds.time_slot_id = ts.id
        WHERE 
            ds.doctor_id = #{doctorId}
        AND 
            DATE_FORMAT(ds.schedule_date, '%Y-%m-%d') = DATE_FORMAT(#{date}, '%Y-%m-%d')
        AND 
            ds.status = 1
        ORDER BY 
            ts.order_num
    </select>
    
    <!-- 获取已预约的时间段 -->
    <select id="getBookedTimeSlots" resultType="string">
        SELECT 
            time_slot
        FROM 
            appointment
        WHERE 
            doctor_id = #{doctorId}
        AND 
            DATE_FORMAT(appointment_date, '%Y-%m-%d') = DATE_FORMAT(#{date}, '%Y-%m-%d')
        AND 
            status IN (0, 1, 2)
    </select>
    
    <!-- 检查时间段是否可用 -->
    <select id="checkTimeSlotAvailable" resultType="int">
        SELECT 
            COUNT(1)
        FROM 
            appointment
        WHERE 
            doctor_id = #{doctorId}
        AND 
            DATE_FORMAT(appointment_date, '%Y-%m-%d') = DATE_FORMAT(#{date}, '%Y-%m-%d')
        AND 
            time_slot = #{timeSlot}
        AND 
            status IN (0, 1, 2)
    </select>
    
    <!-- 获取预约统计 -->
    <select id="getAppointmentStatistics" resultType="com.tcm.appointment.vo.AppointmentStatisticsVO">
        SELECT
            COUNT(1) as totalCount,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completedCount,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as canceledCount,
            SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as noShowCount,
            IFNULL(SUM(CASE WHEN status IN (1, 2, 4) THEN 1 ELSE 0 END) / COUNT(1), 0) as appointmentRate,
            IFNULL(SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) / SUM(CASE WHEN status IN (1, 2, 4) THEN 1 ELSE 0 END), 0) as arrivalRate
        FROM
            appointment
        WHERE
            appointment_date BETWEEN #{startDate} AND #{endDate}
    </select>
    
    <!-- 按日期统计数据 -->
    <select id="getDailyStatistics" resultType="java.util.Map">
        SELECT
            DATE_FORMAT(appointment_date, '%Y-%m-%d') as date,
            COUNT(1) as total,
            SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as canceled,
            SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as noShow
        FROM
            appointment
        WHERE
            appointment_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY
            DATE_FORMAT(appointment_date, '%Y-%m-%d')
        ORDER BY
            date
    </select>
    
    <!-- 按科室统计数据 -->
    <select id="getDepartmentStatistics" resultType="java.util.Map">
        SELECT
            d.dept_name as departmentName,
            COUNT(a.id) as total,
            SUM(CASE WHEN a.status = 2 THEN 1 ELSE 0 END) as completed
        FROM
            appointment a
        JOIN
            department d ON a.department_id = d.id
        WHERE
            a.appointment_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY
            a.department_id, d.dept_name
        ORDER BY
            total DESC
    </select>
    
    <!-- 按医生统计数据 -->
    <select id="getDoctorStatistics" resultType="java.util.Map">
        SELECT
            d.doctor_name as doctorName,
            d.title as doctorTitle,
            COUNT(a.id) as total,
            SUM(CASE WHEN a.status = 2 THEN 1 ELSE 0 END) as completed
        FROM
            appointment a
        JOIN
            doctor d ON a.doctor_id = d.id
        WHERE
            a.appointment_date BETWEEN #{startDate} AND #{endDate}
        GROUP BY
            a.doctor_id, d.doctor_name, d.title
        ORDER BY
            total DESC
    </select>
    
    <!-- 获取最后插入的ID -->
    <select id="getLastInsertId" resultType="java.lang.Long">
        SELECT LAST_INSERT_ID()
    </select>
</mapper> 