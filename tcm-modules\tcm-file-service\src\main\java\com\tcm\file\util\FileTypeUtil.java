package com.tcm.file.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 文件类型工具类
 */
public class FileTypeUtil {

    /**
     * 根据MIME类型判断文件类型
     * 
     * @param mimeType MIME类型
     * @return 文件类型代码（0:普通文件 1:图片 2:视频 3:音频 4:文档）
     */
    public static int getFileTypeByMimeType(String mimeType) {
        if (StringUtils.isBlank(mimeType)) {
            return 0; // 默认为普通文件
        }
        
        mimeType = mimeType.toLowerCase();
        
        // 图片类型
        if (mimeType.startsWith("image/")) {
            return 1;
        }
        
        // 视频类型
        if (mimeType.startsWith("video/")) {
            return 2;
        }
        
        // 音频类型
        if (mimeType.startsWith("audio/")) {
            return 3;
        }
        
        // 文档类型
        if (isDocumentMimeType(mimeType)) {
            return 4;
        }
        
        // 默认为普通文件
        return 0;
    }
    
    /**
     * 判断是否为文档类型的MIME
     * 
     * @param mimeType MIME类型
     * @return 是否为文档类型
     */
    private static boolean isDocumentMimeType(String mimeType) {
        return mimeType.equals("application/pdf") ||
               mimeType.equals("application/msword") ||
               mimeType.equals("application/vnd.ms-excel") ||
               mimeType.equals("application/vnd.ms-powerpoint") ||
               mimeType.contains("officedocument.spreadsheetml") ||
               mimeType.contains("officedocument.wordprocessingml") ||
               mimeType.contains("officedocument.presentationml") ||
               mimeType.equals("text/plain") ||
               mimeType.equals("text/html") ||
               mimeType.equals("text/csv") ||
               mimeType.equals("application/rtf");
    }
} 