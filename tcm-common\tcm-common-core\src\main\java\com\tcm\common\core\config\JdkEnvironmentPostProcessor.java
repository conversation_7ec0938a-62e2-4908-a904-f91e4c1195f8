package com.tcm.common.core.config;

import com.tcm.common.core.util.JdkPathValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.ResourcePropertySource;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * JDK环境后处理器，在应用启动早期设置JDK路径
 */
public class JdkEnvironmentPostProcessor implements EnvironmentPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(JdkEnvironmentPostProcessor.class);
    private static final String TCM_JDK_PROPERTY_SOURCE = "tcmJdkPropertySource";

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        try {
            // 1. 检查当前JDK是否已经配置
            String currentJavaHome = System.getProperty("java.home");
            if (currentJavaHome != null && !currentJavaHome.isEmpty()) {
                logger.info("当前JDK路径已配置: {}", currentJavaHome);
                // 如果已经配置了JDK路径，则不需要再设置
                return;
            }

            // 2. 尝试从配置文件加载JDK配置
            Map<String, Object> jdkProps = new HashMap<>();

            // 尝试加载application-common.yml中的配置
            try {
                PropertySource<?> commonConfig = new ResourcePropertySource(
                        new ClassPathResource("application-common.yml"));

                // 获取JDK配置
                String osType = JdkPathValidator.getOsType();
                String jdkPath = null;

                // 先尝试获取通用配置
                Object homeValue = environment.getProperty("tcm.service.jdk.home");
                if (homeValue != null && !homeValue.toString().isEmpty()) {
                    jdkPath = homeValue.toString();
                }

                // 如果没有通用配置，尝试获取特定操作系统配置
                if (jdkPath == null || jdkPath.isEmpty()) {
                    switch (osType) {
                    case "windows":
                        homeValue = environment.getProperty("tcm.service.jdk.windows.home");
                        if (homeValue != null) {
                            jdkPath = homeValue.toString();
                        }
                        break;
                    case "linux":
                        homeValue = environment.getProperty("tcm.service.jdk.linux.home");
                        if (homeValue != null) {
                            jdkPath = homeValue.toString();
                        }
                        break;
                    case "mac":
                        homeValue = environment.getProperty("tcm.service.jdk.mac.home");
                        if (homeValue != null) {
                            jdkPath = homeValue.toString();
                        }
                        break;
                    default:
                        logger.warn("未知操作系统类型: {}, 无法配置JDK路径", osType);
                        break;
                    }
                }

                // 如果找到了JDK路径，验证并设置
                if (jdkPath != null && !jdkPath.isEmpty()) {
                    if (JdkPathValidator.isValidJdkPath(jdkPath)) {
                        System.setProperty("java.home", jdkPath);
                        jdkProps.put("java.home", jdkPath);
                        logger.info("已设置JDK路径: {}", jdkPath);
                    } else {
                        logger.warn("配置的JDK路径无效: {}", jdkPath);
                    }
                }
            } catch (IOException e) {
                logger.warn("加载application-common.yml失败", e);
            }

            // 将JDK配置添加到环境中
            if (!jdkProps.isEmpty()) {
                MutablePropertySources propertySources = environment.getPropertySources();
                propertySources.addFirst(new MapPropertySource(TCM_JDK_PROPERTY_SOURCE, jdkProps));
            }
        } catch (Exception e) {
            logger.error("处理JDK环境时发生错误", e);
        }
    }
}