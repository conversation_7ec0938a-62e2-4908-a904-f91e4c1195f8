package com.tcm.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.integration.dto.DataSyncRecordDTO;
import com.tcm.integration.entity.DataSyncRecord;

import java.util.Map;

/**
 * 数据同步服务接口
 *
 * <AUTHOR>
 */
public interface DataSyncService extends IService<DataSyncRecord> {

    /**
     * 分页查询数据同步记录
     *
     * @param page       页码
     * @param pageSize   每页大小
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @param status     状态
     * @return 分页结果
     */
    IPage<DataSyncRecord> page(int page, int pageSize, String systemCode, String syncType, Integer status);

    /**
     * 创建同步任务
     *
     * @param dto 同步任务信息
     * @return 任务ID
     */
    Long createSyncTask(DataSyncRecordDTO dto);

    /**
     * 执行同步任务
     *
     * @param recordId 同步记录ID
     * @return 同步结果
     */
    boolean executeSyncTask(Long recordId);

    /**
     * 获取同步详情
     *
     * @param id 同步记录ID
     * @return 同步详情
     */
    DataSyncRecord getSyncDetail(Long id);

    /**
     * 获取同步统计数据
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @return 同步统计数据
     */
    Map<String, Object> getSyncStatistics(String systemCode, String syncType);

    /**
     * 取消同步任务
     *
     * @param id 同步记录ID
     * @return 是否成功
     */
    boolean cancelSync(Long id);

    /**
     * 获取最近同步记录
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @return 最近同步记录
     */
    DataSyncRecord getLastSyncRecord(String systemCode, String syncType);

    /**
     * 数据导入
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @param params     导入参数
     * @return 同步记录ID
     */
    Long importData(String systemCode, String syncType, Map<String, Object> params);

    /**
     * 数据导出
     *
     * @param systemCode 系统编码
     * @param syncType   同步类型
     * @param params     导出参数
     * @return 同步记录ID
     */
    Long exportData(String systemCode, String syncType, Map<String, Object> params);
}