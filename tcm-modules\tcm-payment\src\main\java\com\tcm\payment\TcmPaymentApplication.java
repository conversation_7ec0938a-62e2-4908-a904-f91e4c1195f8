package com.tcm.payment;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 支付服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(exclude = {
        org.redisson.spring.starter.RedissonAutoConfiguration.class
})
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.tcm.payment.mapper")
public class TcmPaymentApplication {

    public static void main(String[] args) {
        SpringApplication.run(TcmPaymentApplication.class, args);
    }
}
