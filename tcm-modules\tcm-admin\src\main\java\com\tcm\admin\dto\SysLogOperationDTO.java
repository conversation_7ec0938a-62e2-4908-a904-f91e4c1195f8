package com.tcm.admin.dto;

import com.tcm.common.core.domain.PageQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 操作日志查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysLogOperationDTO extends PageQuery {

    /**
     * 操作模块
     */
    private String module;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 操作状态（0成功 1失败）
     */
    private Integer status;

    /**
     * 操作用户名
     */
    private String username;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 操作开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 操作结束时间
     */
    private LocalDateTime endTime;
}