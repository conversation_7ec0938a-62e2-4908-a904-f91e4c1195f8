package com.tcm.file.service;

import com.tcm.file.domain.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 文件服务接口
 */
public interface FileService {

    /**
     * 上传文件
     *
     * @param file       文件对象
     * @param module     业务模块
     * @param businessId 业务ID
     * @return 文件信息
     */
    FileInfo upload(MultipartFile file, String module, String businessId);

    /**
     * 通过输入流上传文件
     *
     * @param inputStream  输入流
     * @param originalName 原始文件名
     * @param size         文件大小
     * @param mimeType     MIME类型
     * @param module       业务模块
     * @param businessId   业务ID
     * @return 文件信息
     */
    FileInfo uploadByStream(InputStream inputStream, String originalName, long size, String mimeType, String module, String businessId);

    /**
     * 下载文件
     *
     * @param fileId 文件ID
     * @return 文件输入流
     */
    InputStream download(Long fileId);

    /**
     * 获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    FileInfo getFileInfo(Long fileId);

    /**
     * 根据业务ID获取文件列表
     *
     * @param businessId 业务ID
     * @param module     业务模块
     * @return 文件列表
     */
    List<FileInfo> listByBusinessId(String businessId, String module);

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean deleteFile(Long fileId);

    /**
     * 批量删除文件
     *
     * @param fileIds 文件ID列表
     * @return 是否成功
     */
    boolean deleteFiles(List<Long> fileIds);

    /**
     * 根据业务ID删除文件
     *
     * @param businessId 业务ID
     * @param module     业务模块
     * @return 是否成功
     */
    boolean deleteByBusinessId(String businessId, String module);

    /**
     * 更新文件状态（临时->永久）
     *
     * @param fileIds 文件ID列表
     * @return 是否成功
     */
    boolean updateStatus(List<Long> fileIds);
} 