package com.tcm.user.service;

import com.tcm.user.dto.UserDTO;
import com.tcm.user.vo.PageVO;
import com.tcm.user.vo.UserVO;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 分页获取用户列表
     */
    PageVO<UserVO> listUsers(Integer pageNum, Integer pageSize, String username, String realName);

    /**
     * 根据ID获取用户详情
     */
    UserVO getUserById(Long id);

    /**
     * 创建用户
     */
    Long createUser(UserDTO userDTO);

    /**
     * 更新用户信息
     */
    void updateUser(Long id, UserDTO userDTO);

    /**
     * 删除用户
     */
    void deleteUser(Long id);

    /**
     * 更新用户状态
     */
    void updateUserStatus(Long id, Integer status);

    /**
     * 重置用户密码
     */
    void resetPassword(Long id);
}