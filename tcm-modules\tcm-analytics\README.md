# TCM Analytics 数据分析模块

## 模块介绍
TCM Analytics 是中医智能诊疗系统（TCM System）的数据分析模块，用于处理和分析系统中的数据，生成可视化报表和仪表盘，支持决策分析。

## 主要功能
- 数据仪表盘：支持创建、配置、查看数据仪表盘
- 数据分析：支持基于Elasticsearch的数据处理和分析
- 数据可视化：支持多种图表展示
- 报表导出：支持导出分析结果

## 技术栈
- Spring Boot 2.6.15
- Spring Cloud 2021.0.5
- Spring Cloud Alibaba 2021.0.5.0
- MyBatis Plus 3.5.4
- Elasticsearch 7.14.0
- MySQL 8.2.0
- Nacos (服务注册与配置中心)

## 运行环境要求
- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Elasticsearch 7.x+
- Nacos 2.2.0+

## 快速开始

### 环境准备
1. 确保已安装JDK 17及以上版本
2. 确保已安装Maven 3.8及以上版本
3. 确保MySQL服务已启动，并执行`db/analytics_dashboard.sql`创建必要的数据库表
4. 确保Elasticsearch服务已启动，默认端口9200
5. 确保Nacos服务已启动

### 配置修改
根据实际环境修改`application.yml`中的配置：
- MySQL连接信息
- Redis连接信息
- Elasticsearch连接信息
- Nacos连接信息

### 编译与运行
#### Windows环境
```
# 直接运行启动脚本
start.bat
```

#### Linux环境
```
# 赋予执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

## API文档
启动服务后，访问以下地址查看API文档：
```
http://localhost:9102/swagger-ui.html
```

## 架构设计
本模块采用分层架构设计：
- Controller层：处理HTTP请求，提供RESTful API
- Service层：实现业务逻辑，包括数据分析处理
- Mapper层：数据访问层，与数据库交互
- Entity层：数据模型层，定义数据结构
- Repository层：Elasticsearch数据访问层

## 数据处理模块
本模块使用Elasticsearch作为数据分析引擎：
- 支持多种数据源（MySQL、文件、Elasticsearch）
- 基于Elasticsearch的全文搜索和聚合分析
- 使用Spring Data Elasticsearch简化操作
- 支持复杂查询和聚合分析
- 高性能数据检索和分析能力

## Elasticsearch索引说明
系统使用如下索引：
- tcm_analytics：存储分析数据
  - 包含仪表盘数据、查询结果等
  - 支持全文检索和聚合查询

## 已知问题
- 需要确保Elasticsearch服务正常运行
- 确保application.yml中配置了正确的Elasticsearch连接信息
- 首次运行需要手动创建索引，或在启动时自动创建

## 联系方式
如有问题，请联系开发团队：<EMAIL> 