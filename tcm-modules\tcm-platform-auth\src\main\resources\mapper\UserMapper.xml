<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.auth.mapper.UserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="UserResultMap" type="com.tcm.auth.domain.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="realName" column="real_name"/>
        <result property="mobile" column="mobile"/>
        <result property="email" column="email"/>
        <result property="gender" column="gender"/>
        <result property="avatar" column="avatar"/>
        <result property="deptId" column="dept_id"/>
        <result property="status" column="status"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="String" resultMap="UserResultMap">
        select id, username, password, real_name, mobile, email, gender, avatar,
        dept_id, status, last_login_ip, last_login_time, remark, create_by, create_time,
        update_by, update_time, del_flag
        from sys_user
        where username = #{username} and del_flag = 0
        limit 1
    </select>

    <!-- 更新用户登录信息 -->
    <update id="updateLoginInfo">
        update sys_user
        set last_login_ip = #{ip},
            last_login_time = now(),
            update_time = now()
        where id = #{userId}
    </update>
</mapper> 