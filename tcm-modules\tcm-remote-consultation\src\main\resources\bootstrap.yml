spring:
  application:
    name: tcm-remote-consultation
  profiles:
    active: dev
  config:
    import:
      - optional:classpath:/config/tcm-common.yml
  cloud:
    nacos:
      server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:public}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:public}
        file-extension: yml
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        shared-configs:
          - data-id: application-common.yml
            group: DEFAULT_GROUP
            refresh: true 