package com.tcm.gateway.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsWebFilter;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import org.springframework.web.util.pattern.PathPatternParser;

/**
 * 跨域配置
 *
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "gateway.cors")
public class CorsConfig {

    /**
     * 是否启用跨域
     */
    private boolean enabled = true;

    /**
     * 允许的源
     */
    private String allowOrigins = "*";

    /**
     * 允许的请求方法
     */
    private String allowMethods = "*";

    /**
     * 允许的请求头
     */
    private String allowHeaders = "*";

    /**
     * 暴露的响应头
     */
    private String exposeHeaders = "*";

    /**
     * 是否允许携带凭证
     */
    private boolean allowCredentials = true;

    /**
     * 预检请求的有效期，单位为秒
     */
    private long maxAge = 3600;

    /**
     * 跨域过滤器
     */
    @Bean
    public CorsWebFilter corsWebFilter() {
        if (!enabled) {
            return new CorsWebFilter(exchange -> new CorsConfiguration());
        }

        CorsConfiguration config = new CorsConfiguration();
        config.addAllowedOriginPattern(allowOrigins);
        config.addAllowedMethod(allowMethods);
        config.addAllowedHeader(allowHeaders);
        config.setAllowCredentials(allowCredentials);
        config.setMaxAge(maxAge);
        config.addExposedHeader(exposeHeaders);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource(new PathPatternParser());
        source.registerCorsConfiguration("/**", config);

        return new CorsWebFilter(source);
    }
}