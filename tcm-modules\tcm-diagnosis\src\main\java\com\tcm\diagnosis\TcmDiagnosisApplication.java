package com.tcm.diagnosis;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 诊断服务启动程序
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(exclude = {
        // RocketMQ相关
        org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class,
        // Sentinel相关
        com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration.class })
@ComponentScan({ "com.tcm.diagnosis", "com.tcm.common" })
@MapperScan("com.tcm.diagnosis.mapper")
public class TcmDiagnosisApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmDiagnosisApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 禁止SLF4J多绑定警告
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "warn");
        System.setProperty("org.slf4j.logger.org.slf4j", "error");
        System.setProperty("org.slf4j.loggerFactory.multipleBindingCheck", "ignore");

        // 解决日志冲突问题
        System.setProperty("log4j2.disableJmx", "true");
        System.setProperty("log4j2.disable", "true");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");
        System.setProperty("org.springframework.boot.logging.LoggingSystem",
                "org.springframework.boot.logging.logback.LogbackLoggingSystem");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-diagnosis");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_DIAGNOSIS_PORT") == null) {
            System.setProperty("server.port", "9330");
        }

        // 明确指定使用HikariCP数据源
        System.setProperty("spring.datasource.type", "com.zaxxer.hikari.HikariDataSource");
    }

    // 注意：TcmServiceConfig已由@EnableConfigurationProperties自动配置
    // 无需手动创建Bean
    public static void main(String[] args) {
        try {
            LOGGER.info("Starting diagnosis service module...");

            // 解决Java反射和安全限制问题
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.tryReflectionSetAccessible", "false");
            System.setProperty("illegal-access", "permit");
            System.setProperty("jdk.internal.reflect.permitAll", "true");
            System.setProperty("jdk.attach.allowAttachSelf", "true");

            // 增强处理Nacos gRPC客户端相关类缺失问题
            System.setProperty("grpc.census.enabled", "false");
            System.setProperty("io.grpc.census.enabled", "false");
            System.setProperty("io.grpc.internal.DnsNameResolverProvider.enable", "false");

            // 日志配置
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");

            // Spring Cloud配置
            System.setProperty("spring.cloud.bootstrap.enabled", "false"); // 禁用Bootstrap上下文，避免配置冲突
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false"); // 禁用配置导入检查
            System.setProperty("spring.liquibase.enabled", "false"); // 禁用Liquibase

            // 优先设置固定端口，确保Spring Boot使用这个端口启动
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.diagnosis.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_DIAGNOSIS_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "9330";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            // RocketMQ自动配置已通过注解方式排除，不需要设置系统属性
            // 使用统一配置中的RocketMQ配置
            System.setProperty("tcm.service.rocketMq.nameServerAddress", "**********:9876");

            ConfigurableApplicationContext context = SpringApplication.run(TcmDiagnosisApplication.class, args);

            // 获取实际使用的端口（包括随机分配的端口）
            String serverPort;

            // 如果PortListener已经捕获到了实际端口，则使用它
            if (actualServerPort > 0) {
                serverPort = String.valueOf(actualServerPort);
            } else {
                // 尝试从环境中获取端口
                serverPort = context.getEnvironment().getProperty("server.port");

                // 如果环境中没有端口配置，则尝试从系统属性和环境变量获取
                if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                    serverPort = System.getProperty("tcm.service.diagnosis.port");

                    // 如果系统属性中没有端口配置，则尝试从环境变量获取
                    if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                        serverPort = System.getenv("TCM_DIAGNOSIS_PORT");

                        // 如果环境变量中没有端口配置，则使用默认端口
                        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                            serverPort = "9330";
                        }
                    }
                }
            }

            // 确保端口被设置到系统属性中（使之可被其他组件获取）
            System.setProperty("server.port", serverPort);

            LOGGER.info("=====================================================");
            LOGGER.info("          Diagnosis Service Module Started!         ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            System.out.println("  _____   _____ __  __   _____  _                             _     ");
            System.out.println(" |_   _| / ____|  \\/  | |  __ \\(_)                           (_)    ");
            System.out.println("   | |  | |    | \\  / | | |  | |_  __ _  __ _ _ __   ___  ___ _ ___ ");
            System.out.println("   | |  | |    | |\\/| | | |  | | |/ _` |/ _` | '_ \\ / _ \\/ __| / __|");
            System.out.println("  _| |_ | |____| |  | | | |__| | | (_| | (_| | | | | (_) \\__ \\ \\__ \\");
            System.out.println(" |_____| \\_____|_|  |_| |_____/|_|\\__,_|\\__, |_| |_|\\___/|___/_|___/");
            System.out.println("                                          __/ |                      ");
            System.out.println("                                         |___/                       ");

            System.out.println("  Started successfully: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort);
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("Startup failed: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web server initialized with port: {}", actualServerPort);
        }
    }
}