package com.tcm.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 外部系统实体类
 */
@Data
@TableName("tcm_external_system")
@EqualsAndHashCode(callSuper = false)
public class ExternalSystem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 系统ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 系统类型（1：HIS系统，2：LIS系统，3：PACS系统，4：EMR系统，5：其他）
     */
    private Integer systemType;

    /**
     * 接口地址
     */
    private String apiUrl;

    /**
     * 认证方式（1：Basic，2：OAuth2，3：Token，4：None）
     */
    private Integer authType;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码/密钥
     */
    private String password;

    /**
     * 状态（0禁用 1启用）
     */
    private Integer status;

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout;

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌过期时间
     */
    private Date tokenExpireTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0存在 1删除）
     */
    @TableLogic
    private Integer deleted;
}