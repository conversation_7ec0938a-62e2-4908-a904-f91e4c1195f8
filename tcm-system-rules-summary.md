# TCM系统开发规则概述

## 规则分类

本项目遵循以下开发规则和原则：

1. **代码风格规则** - 统一的编码风格和命名约定
2. **提交规范** - Git提交信息的格式和内容要求
3. **Java代码规范** - Java语言的最佳实践和规范
4. **设计原则** - 系统设计和架构的基本原则
5. **设计模式与解耦原则** - 常用设计模式与减少耦合的方法
6. **安全规范** - 保障系统安全的编码和设计实践
7. **性能优化准则** - 提高系统性能的方法和实践
8. **可测试性设计** - 增强代码可测试性的设计方法
9. **微服务设计规范** - 微服务架构特有的设计原则
10. **代码一致性规范** - 确保代码在不同模块间保持一致
11. **代码注释与文档规范** - 文档和注释的标准
12. **务实原则** - 避免过度设计和不切实际的幻想
13. **项目特定规则** - 针对TCM系统的特定规则
14. **开发历史记录规则** - 记录项目开发过程的方法

## 规则要点摘要

### 代码风格规则
- 使用4个空格缩进，不使用Tab
- 类名使用PascalCase，方法名和变量名使用camelCase
- 常量使用全大写，下划线分隔单词
- 包名使用全小写
- 左大括号放在当前行末尾
- 每行最大长度为120个字符
- 所有Java类文件需要添加适当的Javadoc注释

### 提交规范
- 提交信息前缀：feat, fix, docs, style, refactor, test, chore
- 提交信息简洁明了，说明改动内容
- 较大改动拆分为多个小的提交

### 设计原则
- 遵循架构设计，保持代码风格一致
- 代码修改遵循单一职责原则，不混合多个变更
- 在设计规划时，符合"第一性原理"
- 在代码实现时，遵循"KISS原则"和"SOLID原则"
- 尽量复用已有代码，避免代码重复

### 设计模式与解耦原则
- 优先采用松耦合设计，降低组件间依赖
- 合理使用设计模式（工厂、策略、观察者等）
- 依赖倒置：高层模块不应依赖低层模块，都应依赖抽象
- 使用依赖注入实现控制反转，避免硬编码依赖
- 优先选择组合而非继承，增强灵活性

### 安全规范
- 所有用户输入必须验证和清洗，防止注入攻击
- 敏感数据不得明文存储，必须加密
- API接口必须实现访问控制和权限验证
- 避免在日志中记录敏感信息
- 使用HTTPS进行所有外部通信

### 性能优化准则
- 提前识别性能关键路径，并设计性能测试方案
- 合理使用缓存机制，减少重复计算和数据库访问
- 大批量数据操作使用批处理而非循环操作
- 避免N+1查询问题，合理设计数据库查询
- 针对高并发场景设计无状态服务

### 微服务设计规范
- 遵循领域驱动设计(DDD)原则划分微服务边界
- 每个微服务应当拥有自己的数据库或数据模式
- 服务间通信优先使用异步消息而非同步调用
- 实现服务间的容错机制，如熔断、重试、超时等
- 避免分布式事务，优先考虑最终一致性

### 代码一致性规范
- 所有微服务间保持统一的代码风格和命名约定
- 错误处理机制在所有服务中保持一致
- 所有微服务使用统一版本的公共依赖库
- API设计遵循一致的URL结构、参数命名和返回格式
- 使用统一的时间格式和时区处理（UTC优先）

### 代码注释与文档规范
- 所有代码必须有详细的注释说明
- 每个微服务模块必须生成并维护类关联图
- 关联图应随代码更新而同步更新
- 使用标准化工具（如PlantUML）生成图表
- 所有API接口必须有完整文档

### 务实原则
- 避免幻想性设计，只实现实际需求中明确要求的功能
- 不追求完美，而是追求"足够好"的解决方案
- 基于现有代码和实际情况做决策，而非假设性场景
- 优先解决已知问题，避免过度预测未来需求
- 代码和架构的复杂度应与业务复杂度匹配

### 开发历史记录规则
- 每次会话请求结束后进行总结
- 将总结内容添加到README.md文件中
- 总结内容包括：会话目的、完成任务、关键决策、技术栈、修改文件
- 使用日期作为每次总结的标题

## 文档和工具

本项目维护以下关键文档：

1. `cursor.rules` - 详细的项目规则文档
2. `README.md` - 项目说明和开发历史记录
3. 各微服务模块的PlantUML类图 - 如`gateway-model.puml`
4. 设计模式示例图 - 如`filter-factory-example.puml`

## 规则的修订和更新

这些规则应随着项目的发展而进行审查和更新。任何规则的修改都应记录在开发历史中，并通过团队讨论后实施。 