package com.tcm.security.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.common.core.domain.Result;
import com.tcm.security.model.dto.LoginDTO;
import com.tcm.security.model.vo.TokenVO;
import com.tcm.security.model.vo.UserInfoVO;
import com.tcm.security.service.AuthService;

import lombok.extern.slf4j.Slf4j;

/**
 * 认证控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<TokenVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        log.info("用户登录: {}", loginDTO.getUsername());
        return Result.success(authService.login(loginDTO));
    }
    
    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public Result<Boolean> logout() {
        return Result.success(authService.logout());
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/refresh")
    public Result<TokenVO> refreshToken(@RequestParam String refreshToken) {
        return Result.success(authService.refreshToken(refreshToken));
    }
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/user/info")
    public Result<UserInfoVO> getUserInfo() {
        return Result.success(authService.getUserInfo());
    }
    
    /**
     * 生成验证码
     */
    @GetMapping("/captcha")
    public Result<Object> captcha() {
        return Result.success(authService.generateCaptcha());
    }
}