package com.tcm.workflow.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.workflow.entity.ProcessDefinition;
import com.tcm.workflow.service.IProcessDefinitionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 流程定义控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/process/definition")
@Tag(name = "流程定义管理", description = "流程定义相关接口")
public class ProcessDefinitionController extends BaseController {

    @Autowired
    private IProcessDefinitionService processDefinitionService;

    /**
     * 查询流程定义列表
     */
    @GetMapping("/list")
    @Operation(summary = "查询流程定义列表", description = "获取流程定义列表")
    public R<List<ProcessDefinition>> list(ProcessDefinition processDefinition) {
        List<ProcessDefinition> list = processDefinitionService.selectProcessDefinitionList(processDefinition);
        return R.ok(list);
    }

    /**
     * 获取流程定义详细信息
     */
    @GetMapping("/{processDefinitionId}")
    @Operation(summary = "获取流程定义详情", description = "获取单个流程定义详细信息")
    public R<ProcessDefinition> getInfo(@PathVariable("processDefinitionId") String processDefinitionId) {
        return R.ok(processDefinitionService.selectProcessDefinitionById(processDefinitionId));
    }

    /**
     * 部署流程定义
     */
    @PostMapping("/deploy")
    @Operation(summary = "部署流程定义", description = "部署新的流程定义")
    public R<String> add(@RequestBody ProcessDefinition processDefinition) {
        String deploymentId = processDefinitionService.deployProcessDefinition(processDefinition);
        return R.ok("部署成功", deploymentId);
    }

    /**
     * 修改流程定义
     */
    @PutMapping
    @Operation(summary = "修改流程定义", description = "修改已有的流程定义")
    public R<String> edit(@RequestBody ProcessDefinition processDefinition) {
        processDefinitionService.updateProcessDefinition(processDefinition);
        return R.ok("修改成功");
    }

    /**
     * 删除流程定义
     */
    @DeleteMapping("/{processDefinitionIds}")
    @Operation(summary = "删除流程定义", description = "删除指定流程定义")
    public R<String> remove(@PathVariable String[] processDefinitionIds) {
        processDefinitionService.deleteProcessDefinitionByIds(processDefinitionIds);
        return R.ok("删除成功");
    }

    /**
     * 激活或挂起流程定义
     */
    @PutMapping("/{processDefinitionId}/{status}")
    @Operation(summary = "更新流程定义状态", description = "激活或挂起流程定义")
    public R<String> updateStatus(@PathVariable("processDefinitionId") String processDefinitionId,
            @PathVariable("status") Integer status) {
        processDefinitionService.updateProcessDefinitionStatus(processDefinitionId, status);
        return R.ok(status == 1 ? "激活成功" : "挂起成功");
    }
}