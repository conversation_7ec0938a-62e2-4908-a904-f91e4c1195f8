package com.tcm.common.security.annotation;

import java.lang.annotation.*;

/**
 * 数据权限过滤注解
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {

    /**
     * 部门表的别名
     */
    String deptAlias() default "d";

    /**
     * 用户表的别名
     */
    String userAlias() default "u";

    /**
     * 是否过滤用户权限
     */
    boolean filterUser() default false;

    /**
     * 是否过滤部门权限
     */
    boolean filterDept() default true;
} 