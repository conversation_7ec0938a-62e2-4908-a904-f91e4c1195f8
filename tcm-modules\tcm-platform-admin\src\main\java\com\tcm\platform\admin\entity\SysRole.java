package com.tcm.platform.admin.entity;

import java.time.LocalDateTime;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;

/**
 * 系统角色实体
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_role")
public class SysRole {

    /**
     * 角色ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色排序
     */
    private Integer sort;

    /**
     * 状态（0：禁用 1：启用）
     */
    private Integer status;

    /**
     * 数据权限范围（1：全部数据 2：部门及以下数据 3：本部门数据 4：自己数据）
     */
    private Integer dataScope;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志（0：正常 1：已删除）
     */
    @TableLogic
    private Integer delFlag;
}