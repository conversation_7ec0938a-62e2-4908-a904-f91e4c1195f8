package com.tcm.evaluation.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;

/**
 * OpenAPI/Swagger配置
 */
@Configuration
public class SwaggerConfig {

    /**
     * 配置OpenAPI文档信息
     * 
     * @return OpenAPI实例
     */
    @Bean
    public OpenAPI tcmEvaluationOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("TCM评估服务API文档")
                        .description("中医评估服务模块接口文档")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("TCM开发团队")
                                .email("<EMAIL>")))
                .components(new Components()
                        .addSecuritySchemes("bearer-key", 
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")))
                .addSecurityItem(new SecurityRequirement().addList("bearer-key"));
    }
} 