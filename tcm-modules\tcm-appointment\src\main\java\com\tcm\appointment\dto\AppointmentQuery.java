package com.tcm.appointment.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约查询对象
 */
@Data
public class AppointmentQuery implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 科室
     */
    private String department;
    
    /**
     * 预约类型（1-初诊 2-复诊）
     */
    private Integer type;
    
    /**
     * 预约状态（0-待支付 1-已预约 2-已到诊 3-已取消 4-已爽约）
     */
    private Integer status;
    
    /**
     * 开始日期
     */
    private Date beginDate;
    
    /**
     * 结束日期
     */
    private Date endDate;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
    
    /**
     * 获取开始日期
     * @return 开始日期
     */
    public Date getStartDate() {
        return this.beginDate;
    }
    
    /**
     * 获取结束日期
     * @return 结束日期
     */
    public Date getEndDate() {
        return this.endDate;
    }
} 