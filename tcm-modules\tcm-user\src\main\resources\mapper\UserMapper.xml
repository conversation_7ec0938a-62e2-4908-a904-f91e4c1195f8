<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.user.mapper.UserMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.user.entity.User">
        <id column="id" property="id" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="gender" property="gender" />
        <result column="avatar" property="avatar" />
        <result column="dept_id" property="deptId" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="login_ip" property="loginIp" />
        <result column="login_time" property="loginTime" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>
</mapper> 