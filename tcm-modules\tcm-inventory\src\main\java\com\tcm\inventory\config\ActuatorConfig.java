package com.tcm.inventory.config;

import org.springframework.boot.actuate.endpoint.web.EndpointMediaTypes;
import org.springframework.boot.actuate.endpoint.web.ExposableWebEndpoint;
import org.springframework.boot.actuate.endpoint.web.WebEndpointsSupplier;
import org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier;
import org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Actuator配置类，提供缺失的Endpoints相关bean
 */
@Configuration
public class ActuatorConfig {

    @Bean
    public WebEndpointsSupplier webEndpointsSupplier() {
        return () -> Collections.<ExposableWebEndpoint>emptyList();
    }
    
    @Bean
    public ServletEndpointsSupplier servletEndpointsSupplier() {
        return () -> Collections.emptyList();
    }
    
    @Bean
    public ControllerEndpointsSupplier controllerEndpointsSupplier() {
        return () -> Collections.emptyList();
    }
    
    @Bean
    public EndpointMediaTypes endpointMediaTypes() {
        List<String> mediaTypes = Arrays.asList("application/json");
        return new EndpointMediaTypes(mediaTypes, mediaTypes);
    }
} 