package com.tcm.pharmacy.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;

/**
 * Swagger配置
 * 
 * <AUTHOR>
 */
@Configuration
public class SwaggerConfig {
    
    @Bean
    public OpenAPI springShopOpenAPI() {
        // 配置认证
        List<SecurityScheme> securitySchemes = new ArrayList<>();
        SecurityScheme tokenScheme = new SecurityScheme()
                .type(SecurityScheme.Type.HTTP)
                .scheme("bearer")
                .bearerFormat("JWT")
                .in(SecurityScheme.In.HEADER)
                .name("Authorization");
        securitySchemes.add(tokenScheme);
        
        return new OpenAPI()
                .info(new Info().title("TCM Pharmacy API")
                        .description("传统中医药房服务接口文档")
                        .version("v1.0.0")
                        .contact(new Contact().name("TCM Team").email("<EMAIL>"))
                        .license(new License().name("Apache 2.0").url("https://www.apache.org/licenses/LICENSE-2.0")))
                .components(new Components().addSecuritySchemes("bearerAuth", tokenScheme))
                .addSecurityItem(new SecurityRequirement().addList("bearerAuth"));
    }
} 