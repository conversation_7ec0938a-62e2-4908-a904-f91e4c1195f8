package com.tcm.message.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.message.entity.Message;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息数据访问层
 */
@Mapper
public interface MessageMapper extends BaseMapper<Message> {

    /**
     * 获取用户未读消息数量
     *
     * @param receiverId 接收者ID
     * @return 未读消息数量
     */
    Integer countUnreadMessages(@Param("receiverId") Long receiverId);

    /**
     * 获取用户消息列表
     *
     * @param receiverId 接收者ID
     * @return 消息列表
     */
    List<Message> selectMessagesByReceiverId(@Param("receiverId") Long receiverId);

    /**
     * 标记消息为已读
     *
     * @param id 消息ID
     * @return 影响行数
     */
    int markAsRead(@Param("id") Long id);

    /**
     * 批量标记消息为已读
     *
     * @param receiverId 接收者ID
     * @return 影响行数
     */
    int markAllAsRead(@Param("receiverId") Long receiverId);
}