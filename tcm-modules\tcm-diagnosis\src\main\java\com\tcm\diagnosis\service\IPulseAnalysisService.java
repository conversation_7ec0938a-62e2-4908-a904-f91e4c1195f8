package com.tcm.diagnosis.service;

import com.tcm.diagnosis.domain.PulseAnalysis;
import com.tcm.diagnosis.vo.PulseAnalysisVO;

import java.util.List;

/**
 * 脉象分析服务接口
 */
public interface IPulseAnalysisService {

    /**
     * 分析脉诊数据
     *
     * @param dataId 脉诊数据ID
     * @return 分析结果ID
     */
    Long analyzePulseData(Long dataId);

    /**
     * 批量分析脉诊数据
     *
     * @param dataIds 脉诊数据ID列表
     * @return 是否成功
     */
    boolean analyzePulseDataBatch(List<Long> dataIds);

    /**
     * 获取脉象分析结果
     *
     * @param analysisId 分析结果ID
     * @return 脉象分析结果视图对象
     */
    PulseAnalysisVO getPulseAnalysis(Long analysisId);
    
    /**
     * 根据脉象ID获取脉象分析结果
     *
     * @param pulseId 脉象ID
     * @return 脉象分析结果视图对象
     */
    PulseAnalysisVO getPulseAnalysisByPulseId(Long pulseId);

    /**
     * 根据脉诊数据ID获取脉象分析结果
     *
     * @param dataId 脉诊数据ID
     * @return 脉象分析结果视图对象
     */
    PulseAnalysisVO getPulseAnalysisByDataId(Long dataId);
    
    /**
     * 获取最近的脉象分析结果
     *
     * @param patientId 患者ID
     * @param limit 记录数量限制
     * @return 脉象分析结果视图对象列表
     */
    List<PulseAnalysisVO> getRecentPulseAnalysis(Long patientId, Integer limit);

    /**
     * 获取诊断记录关联的脉象分析结果列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 脉象分析结果视图对象列表
     */
    List<PulseAnalysisVO> getPulseAnalysisListByDiagnosisId(Long diagnosisId);

    /**
     * 获取患者的脉象分析结果列表
     *
     * @param patientId 患者ID
     * @return 脉象分析结果视图对象列表
     */
    List<PulseAnalysisVO> getPulseAnalysisListByPatientId(Long patientId);
    


    /**
     * 删除脉象分析结果
     *
     * @param analysisId 分析结果ID
     * @return 是否成功
     */
    boolean deletePulseAnalysis(Long analysisId);

    /**
     * 导出脉象分析结果报告
     *
     * @param analysisId 分析结果ID
     * @return 报告文件路径
     */
    String exportPulseAnalysisReport(Long analysisId);
}