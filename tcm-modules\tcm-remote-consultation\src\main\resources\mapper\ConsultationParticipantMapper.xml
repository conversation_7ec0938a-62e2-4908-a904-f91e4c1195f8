<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.consultation.mapper.ConsultationParticipantMapper">
    
    <resultMap id="ConsultationParticipantResult" type="com.tcm.consultation.domain.ConsultationParticipant">
        <id property="participantId" column="participant_id"/>
        <result property="consultationId" column="consultation_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="type" column="type"/>
        <result property="status" column="status"/>
        <result property="online" column="online"/>
        <result property="joinTime" column="join_time"/>
        <result property="leaveTime" column="leave_time"/>
        <result property="opinion" column="opinion"/>
        <result property="rejectReason" column="reject_reason"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <sql id="selectConsultationParticipantVo">
        SELECT cp.participant_id, 
               cp.consultation_id, 
               cp.doctor_id, 
               cp.type, 
               cp.status, 
               cp.online, 
               cp.join_time, 
               cp.leave_time, 
               cp.opinion, 
               cp.reject_reason, 
               cp.create_by, 
               cp.create_time, 
               cp.update_by, 
               cp.update_time, 
               cp.remark, 
               cp.del_flag
        FROM tcm_consultation_participant cp
    </sql>
    
</mapper> 