package com.tcm.doctor.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.R;
import com.tcm.doctor.dto.DoctorDTO;
import com.tcm.doctor.dto.DoctorQuery;
import com.tcm.doctor.service.IDoctorService;
import com.tcm.doctor.vo.DoctorRecommendVO;
import com.tcm.doctor.vo.DoctorScheduleVO;
import com.tcm.doctor.vo.DoctorVO;
import com.tcm.doctor.vo.DoctorWorkloadVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 医生管理控制器
 */
@RestController
@RequestMapping("/doctor")
public class DoctorController {

    @Autowired
    private IDoctorService doctorService;

    /**
     * 获取医生列表
     */
    @GetMapping("/list")
    public R<IPage<DoctorVO>> list(DoctorQuery query) {
        IPage<DoctorVO> page = doctorService.listDoctors(query);
        return R.ok(page);
    }

    /**
     * 获取医生详情
     */
    @GetMapping("/{doctorId}")
    public R<DoctorVO> getInfo(@PathVariable Long doctorId) {
        DoctorVO doctor = doctorService.getDoctorById(doctorId);
        return R.ok(doctor);
    }

    /**
     * 新增医生
     */
    @PostMapping
    public R<Boolean> add(@RequestBody DoctorDTO doctorDTO) {
        boolean result = doctorService.addDoctor(doctorDTO);
        return R.ok(result);
    }

    /**
     * 修改医生
     */
    @PutMapping
    public R<Boolean> update(@RequestBody DoctorDTO doctorDTO) {
        boolean result = doctorService.updateDoctor(doctorDTO);
        return R.ok(result);
    }

    /**
     * 删除医生
     */
    @DeleteMapping("/{doctorId}")
    public R<Boolean> remove(@PathVariable Long doctorId) {
        boolean result = doctorService.deleteDoctor(doctorId);
        return R.ok(result);
    }

    /**
     * 批量删除医生
     */
    @DeleteMapping("/batch/{doctorIds}")
    public R<Boolean> removeBatch(@PathVariable Long[] doctorIds) {
        boolean result = doctorService.deleteDoctors(doctorIds);
        return R.ok(result);
    }

    /**
     * 修改医生状态
     */
    @PutMapping("/status/{doctorId}/{status}")
    public R<Boolean> updateStatus(@PathVariable Long doctorId, @PathVariable Integer status) {
        boolean result = doctorService.updateDoctorStatus(doctorId, status);
        return R.ok(result);
    }

    /**
     * 根据科室获取医生列表
     */
    @GetMapping("/department/{department}")
    public R<List<DoctorVO>> getDoctorsByDepartment(@PathVariable String department) {
        List<DoctorVO> doctors = doctorService.getDoctorsByDepartment(department);
        return R.ok(doctors);
    }

    /**
     * 根据专长获取医生列表
     */
    @GetMapping("/specialty")
    public R<List<DoctorVO>> getDoctorsBySpecialty(@RequestParam String specialty) {
        List<DoctorVO> doctors = doctorService.getDoctorsBySpecialty(specialty);
        return R.ok(doctors);
    }

    /**
     * 根据症状推荐医生
     */
    @GetMapping("/recommend")
    public R<List<DoctorRecommendVO>> recommendDoctors(
            @RequestParam String symptom,
            @RequestParam(required = false) String department,
            @RequestParam(defaultValue = "5") int limit) {
        List<DoctorRecommendVO> doctors = doctorService.recommendDoctorsBySymptom(symptom, department, limit);
        return R.ok(doctors);
    }

    /**
     * 获取医生排班
     */
    @GetMapping("/schedule/{doctorId}")
    public R<List<DoctorScheduleVO>> getSchedule(@PathVariable Long doctorId) {
        List<DoctorScheduleVO> schedule = doctorService.getDoctorSchedule(doctorId);
        return R.ok(schedule);
    }

    /**
     * 设置医生排班
     */
    @PostMapping("/schedule/{doctorId}")
    public R<Boolean> setSchedule(@PathVariable Long doctorId, @RequestBody List<DoctorScheduleVO> schedule) {
        boolean result = doctorService.setDoctorSchedule(doctorId, schedule);
        return R.ok(result);
    }

    /**
     * 获取医生工作量统计（默认过去一个月）
     */
    @GetMapping("/statistics/{doctorId}")
    public R<DoctorWorkloadVO> getWorkloadStatistics(@PathVariable Long doctorId) {
        DoctorWorkloadVO statistics = doctorService.getDoctorWorkloadStatistics(doctorId);
        return R.ok(statistics);
    }
    
    /**
     * 获取医生工作量统计（指定时间范围）
     */
    @GetMapping("/statistics/{doctorId}/{startDate}/{endDate}")
    public R<DoctorWorkloadVO> getWorkloadStatisticsByDateRange(
            @PathVariable Long doctorId,
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        DoctorWorkloadVO statistics = doctorService.getDoctorWorkloadStatistics(doctorId, startDate, endDate);
        return R.ok(statistics);
    }
    
    /**
     * 获取医生工作量统计（使用请求参数）
     */
    @GetMapping("/statistics/range/{doctorId}")
    public R<DoctorWorkloadVO> getWorkloadStatisticsByParams(
            @PathVariable Long doctorId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        DoctorWorkloadVO statistics = doctorService.getDoctorWorkloadStatistics(doctorId, startDate, endDate);
        return R.ok(statistics);
    }
}