package com.tcm.auth.service;

import com.tcm.auth.dto.LoginDTO;
import com.tcm.auth.vo.LoginUserVO;
import com.tcm.auth.vo.TokenVO;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return Token信息
     */
    TokenVO login(LoginDTO loginDTO);

    /**
     * 用户登出
     *
     * @param token 令牌
     * @return 是否成功
     */
    boolean logout(String token);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的Token信息
     */
    TokenVO refreshToken(String refreshToken);

    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息
     */
    LoginUserVO getCurrentUser();

    /**
     * 验证令牌
     *
     * @param token 令牌
     * @return 是否有效
     */
    boolean validateToken(String token);
}
