package com.tcm.assistant.exception;

/**
 * 诊断数据异常
 * 
 * <AUTHOR>
 */
public class DiagnosisDataException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public DiagnosisDataException() {
        super();
    }

    public DiagnosisDataException(String message) {
        super(message);
    }

    public DiagnosisDataException(String message, Throwable cause) {
        super(message, cause);
    }

    public DiagnosisDataException(Throwable cause) {
        super(cause);
    }
}
