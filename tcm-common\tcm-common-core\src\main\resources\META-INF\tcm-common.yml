# 统一服务配置
tcm:
  service:
    # MySQL配置
    mysql:
      host: ${TCM_MYSQL_HOST:**********}
      port: ${TCM_MYSQL_PORT:3306}
      username: ${TCM_MYSQL_USERNAME:root}
      password: ${TCM_MYSQL_PASSWORD:Kaixin207@1984}
      database: ${TCM_MYSQL_DATABASE:tcm-system}
    
    # Redis配置
    redis:
      host: ${TCM_REDIS_HOST:**********}
      port: ${TCM_REDIS_PORT:6379}
      password: ${TCM_REDIS_PASSWORD:kaixin207}
      database: 0
    
    # Elasticsearch配置
    elasticsearch:
      host: ${TCM_ES_HOST:**********}
      port: ${TCM_ES_PORT:9200}
      username: ${TCM_ES_USERNAME:elastic}
      password: ${TCM_ES_PASSWORD:kaixin207}
    
    # Neo4j配置
    neo4j:
      host: ${TCM_NEO4J_HOST:**********}
      port: ${TCM_NEO4J_PORT:7474}
      username: ${TCM_NEO4J_USERNAME:neo4j}
      password: ${TCM_NEO4J_PASSWORD:kaixin207}
    
    # Milvus配置
    milvus:
      host: ${TCM_MILVUS_HOST:**********}
      port: ${TCM_MILVUS_PORT:19530}
      username: ${TCM_MILVUS_USERNAME:root}
      password: ${TCM_MILVUS_PASSWORD:kaixin207}
    
    # MinIO配置
    minio:
      host: ${TCM_MINIO_HOST:**********}
      port: ${TCM_MINIO_PORT:9000}
      accessKey: ${TCM_MINIO_ACCESS_KEY:qztllz}
      secretKey: ${TCM_MINIO_SECRET_KEY:kaixin207}
      bucketName: ${TCM_MINIO_BUCKET:tcm-system}
      
    # Sentinel配置
    sentinel:
      host: ${TCM_SENTINEL_HOST:**********}
      port: ${TCM_SENTINEL_PORT:8080}
      username: ${TCM_SENTINEL_USERNAME:sentinel}
      password: ${TCM_SENTINEL_PASSWORD:kaixin207}
    
    # ELK配置
    elk:
      # Elasticsearch配置同上
      logstash:
        host: ${TCM_LOGSTASH_HOST:**********}
        port: ${TCM_LOGSTASH_PORT:5044}
      kibana:
        host: ${TCM_KIBANA_HOST:**********}
        port: ${TCM_KIBANA_PORT:5601}
        username: ${TCM_KIBANA_USERNAME:elastic}
        password: ${TCM_KIBANA_PASSWORD:kaixin207}
    
    # Seata配置
    seata:
      host: ${TCM_SEATA_HOST:**********}
      port: ${TCM_SEATA_PORT:8091}
      username: ${TCM_SEATA_USERNAME:seata}
      password: ${TCM_SEATA_PASSWORD:kaixin207}
      database: ${TCM_SEATA_DATABASE:seata}
    
    # Nacos配置
    nacos:
      host: ${TCM_NACOS_HOST:***********}
      port: ${TCM_NACOS_PORT:8848}
      username: ${TCM_NACOS_USERNAME:nacos}
      password: ${TCM_NACOS_PASSWORD:kaixin207}
      url: ${TCM_NACOS_URL:${tcm.service.nacos.host}:${tcm.service.nacos.port}}
      namespace: ${TCM_NACOS_NAMESPACE:}
      
    # RocketMQ配置
    rocketMq:
      host: ${TCM_ROCKETMQ_HOST:**********}
      port: ${TCM_ROCKETMQ_PORT:9876}
      username: ${TCM_ROCKETMQ_USERNAME:admin}
      password: ${TCM_ROCKETMQ_PASSWORD:admin}
      
    # 各服务端口配置
    admin:
      port: ${TCM_ADMIN_PORT:9200}
    auth:
      port: ${TCM_AUTH_PORT:9210}
    gateway:
      port: ${TCM_GATEWAY_PORT:9000}
    doctor:
      port: ${TCM_DOCTOR_PORT:9300}
    patient:
      port: ${TCM_PATIENT_PORT:9310}
    order:
      port: ${TCM_ORDER_PORT:9320}
    diagnosis:
      port: ${TCM_DIAGNOSIS_PORT:9330}
    medication:
      port: ${TCM_MEDICATION_PORT:9340}
    knowledge:
      port: ${TCM_KNOWLEDGE_PORT:9350}

# 统一Redis配置
spring:
  data:
    redis:
      host: ${TCM_REDIS_HOST:**********}
      port: ${TCM_REDIS_PORT:6379}
      password: ${TCM_REDIS_PASSWORD:kaixin207}
      database: 0

# 统一安全配置
security:
  oauth2:
    resource:
      jwt:
        key-value: tcm-auth-key

# 统一日期格式配置
spring.jackson:
  date-format: yyyy-MM-dd HH:mm:ss
  time-zone: GMT+8

# 统一异常处理配置
server:
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: never
    include-exception: false

# 统一Seata配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}-group
  service:
    vgroup-mapping:
      default: default
      tcm-prescription-group: default
      tcm-platform-auth-group: default
      tcm-platform-gateway-group: default
      tcm-platform-admin-group: default
    grouplist:
      default: ${TCM_SEATA_HOST:**********}:${TCM_SEATA_PORT:8091}
    enable-degrade: false
    disable-global-transaction: false
  transport:
    type: TCP
    server: NIO
    heartbeat: true
    enable-client-batch-send-request: false
    thread-factory:
      boss-thread-prefix: NettyBoss
      worker-thread-prefix: NettyServerNIOWorker
      server-executor-thread-prefix: NettyServerBizHandler
      share-boss-worker: false
      client-selector-thread-prefix: NettyClientSelector
      client-selector-thread-size: 1
      client-worker-thread-prefix: NettyClientWorkerThread
      boss-thread-size: 1
      worker-thread-size: default
    shutdown:
      wait: 3
    serialization: seata
    compressor: none
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ${tcm.service.nacos.url}
      namespace: ${tcm.service.nacos.namespace:}
      username: ${tcm.service.nacos.username}
      password: ${tcm.service.nacos.password}
  config:
    type: nacos
    nacos:
      server-addr: ${tcm.service.nacos.url}
      namespace: ${tcm.service.nacos.namespace:}
      group: SEATA_GROUP
      username: ${tcm.service.nacos.username}
      password: ${tcm.service.nacos.password} 