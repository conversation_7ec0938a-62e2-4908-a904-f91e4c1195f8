package com.tcm.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.core.web.page.PageDomain;
import com.tcm.inspection.domain.InspectionItem;
import com.tcm.inspection.service.InspectionItemService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 检查项目控制器
 */
@Tag(name = "检查项目管理", description = "检查项目管理相关接口")
@RestController
@RequestMapping("/inspection/item")
public class InspectionItemController extends BaseController {

    @Autowired
    private InspectionItemService inspectionItemService;

    /**
     * 获取检查项目列表
     */
    @Operation(summary = "获取检查项目列表")
    @GetMapping("/list")
    public R<List<InspectionItem>> list(InspectionItem inspectionItem) {
        List<InspectionItem> list = inspectionItemService.selectInspectionItemList(inspectionItem);
        return R.ok(list);
    }

    /**
     * 分页查询检查项目列表
     */
    @Operation(summary = "分页查询检查项目列表")
    @GetMapping("/page")
    public R<IPage<InspectionItem>> page(InspectionItem inspectionItem, PageDomain pageDomain) {
        Page<InspectionItem> page = new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        IPage<InspectionItem> pageData = inspectionItemService.selectInspectionItemPage(page, inspectionItem);
        return R.ok(pageData);
    }

    /**
     * 获取检查项目详情
     */
    @Operation(summary = "获取检查项目详情")
    @GetMapping("/{itemId}")
    public R<InspectionItem> getInfo(@Parameter(description = "检查项目ID") @PathVariable("itemId") Long itemId) {
        return R.ok(inspectionItemService.selectInspectionItemById(itemId));
    }

    /**
     * 根据分类ID查询检查项目列表
     */
    @Operation(summary = "根据分类ID查询检查项目列表")
    @GetMapping("/category/{categoryId}")
    public R<List<InspectionItem>> getItemsByCategoryId(@Parameter(description = "分类ID") @PathVariable("categoryId") Long categoryId) {
        List<InspectionItem> items = inspectionItemService.selectInspectionItemByCategoryId(categoryId);
        return R.ok(items);
    }

    /**
     * 根据科室ID查询检查项目列表
     */
    @Operation(summary = "根据科室ID查询检查项目列表")
    @GetMapping("/dept/{deptId}")
    public R<List<InspectionItem>> getItemsByDeptId(@Parameter(description = "科室ID") @PathVariable("deptId") Long deptId) {
        List<InspectionItem> items = inspectionItemService.selectInspectionItemByDeptId(deptId);
        return R.ok(items);
    }

    /**
     * 新增检查项目
     */
    @Operation(summary = "新增检查项目")
    @PostMapping
    public R<Void> add(@Valid @RequestBody InspectionItem inspectionItem) {
        // 校验项目名称和编码是否唯一
        if (inspectionItemService.checkItemNameUnique(inspectionItem)) {
            return R.fail("新增检查项目失败，项目名称已存在");
        }
        if (inspectionItemService.checkItemCodeUnique(inspectionItem)) {
            return R.fail("新增检查项目失败，项目编码已存在");
        }
        
        return toAjax(inspectionItemService.insertInspectionItem(inspectionItem));
    }

    /**
     * 修改检查项目
     */
    @Operation(summary = "修改检查项目")
    @PutMapping
    public R<Void> edit(@Valid @RequestBody InspectionItem inspectionItem) {
        // 校验项目名称和编码是否唯一
        if (inspectionItemService.checkItemNameUnique(inspectionItem)) {
            return R.fail("修改检查项目失败，项目名称已存在");
        }
        if (inspectionItemService.checkItemCodeUnique(inspectionItem)) {
            return R.fail("修改检查项目失败，项目编码已存在");
        }
        
        return toAjax(inspectionItemService.updateInspectionItem(inspectionItem));
    }

    /**
     * 删除检查项目
     */
    @Operation(summary = "删除检查项目")
    @DeleteMapping("/{itemId}")
    public R<Void> remove(@Parameter(description = "检查项目ID") @PathVariable("itemId") Long itemId) {
        return toAjax(inspectionItemService.deleteInspectionItemById(itemId));
    }

    /**
     * 批量删除检查项目
     */
    @Operation(summary = "批量删除检查项目")
    @DeleteMapping("/batch/{itemIds}")
    public R<Void> batchRemove(@Parameter(description = "检查项目ID数组，多个以逗号分隔") @PathVariable("itemIds") Long[] itemIds) {
        return toAjax(inspectionItemService.deleteInspectionItemByIds(itemIds));
    }
} 