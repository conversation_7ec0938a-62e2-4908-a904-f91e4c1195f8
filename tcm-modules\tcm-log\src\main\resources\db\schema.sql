-- 系统日志表
CREATE TABLE IF NOT EXISTS `sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `module` varchar(50) DEFAULT NULL COMMENT '操作模块',
  `operation_type` varchar(20) DEFAULT NULL COMMENT '操作类型',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `method` varchar(100) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方式',
  `request_params` text COMMENT '请求参数',
  `result` text COMMENT '返回结果',
  `status` tinyint(4) DEFAULT '0' COMMENT '操作状态（0成功 1失败）',
  `error_msg` text COMMENT '错误消息',
  `operate_time` datetime DEFAULT NULL COMMENT '操作时间',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名称',
  `ip_address` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `location` varchar(255) DEFAULT NULL COMMENT '操作地点',
  `browser` varchar(50) DEFAULT NULL COMMENT '操作浏览器',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `execute_time` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_operate_time` (`operate_time`),
  KEY `idx_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS `operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `business_id` varchar(50) DEFAULT NULL COMMENT '业务ID',
  `operation_type` tinyint(4) DEFAULT '0' COMMENT '操作类型（0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据）',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人员ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人员名称',
  `module` varchar(50) DEFAULT NULL COMMENT '操作模块',
  `content` text COMMENT '操作内容',
  `status` tinyint(4) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `operate_time` datetime DEFAULT NULL COMMENT '操作时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `location` varchar(255) DEFAULT NULL COMMENT '操作地点',
  `request_url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方式',
  `request_params` text COMMENT '请求参数',
  `method` varchar(100) DEFAULT NULL COMMENT '操作方法',
  `result` text COMMENT '返回结果',
  `error_msg` text COMMENT '错误消息',
  `execute_time` bigint(20) DEFAULT NULL COMMENT '执行时长(毫秒)',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_operate_time` (`operate_time`),
  KEY `idx_business` (`business_type`,`business_id`),
  KEY `idx_operator` (`operator_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户账号',
  `ip_address` varchar(50) DEFAULT NULL COMMENT '登录IP地址',
  `location` varchar(255) DEFAULT NULL COMMENT '登录地点',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `status` tinyint(4) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `message` varchar(255) DEFAULT NULL COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  `logout_time` datetime DEFAULT NULL COMMENT '退出时间',
  `session_id` varchar(50) DEFAULT NULL COMMENT '会话ID',
  `login_type` tinyint(4) DEFAULT '0' COMMENT '登录类型（0=网页登录，1=移动端登录，2=API登录）',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_session` (`session_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表'; 