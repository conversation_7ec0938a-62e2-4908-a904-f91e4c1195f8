<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.inspection.mapper.InspectionItemMapper">

    <resultMap type="com.tcm.inspection.domain.InspectionItem" id="InspectionItemResult">
        <id property="itemId" column="item_id"/>
        <result property="itemCode" column="item_code"/>
        <result property="itemName" column="item_name"/>
        <result property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="itemDesc" column="item_desc"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="inspectionMethod" column="inspection_method"/>
        <result property="precautions" column="precautions"/>
        <result property="referenceRange" column="reference_range"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <sql id="selectInspectionItemVo">
        select i.item_id, i.item_code, i.item_name, i.category_id, c.category_name, i.item_desc, i.price,
               i.status, i.dept_id, d.dept_name, i.inspection_method, i.precautions, i.reference_range,
               i.create_by, i.create_time, i.update_by, i.update_time, i.remark, i.del_flag
        from inspection_item i
        left join inspection_category c on i.category_id = c.category_id
        left join sys_dept d on i.dept_id = d.dept_id
    </sql>

    <select id="selectInspectionItemList" parameterType="com.tcm.inspection.domain.InspectionItem" resultMap="InspectionItemResult">
        <include refid="selectInspectionItemVo"/>
        where i.del_flag = 0
        <if test="itemCode != null and itemCode != ''">
            and i.item_code like concat('%', #{itemCode}, '%')
        </if>
        <if test="itemName != null and itemName != ''">
            and i.item_name like concat('%', #{itemName}, '%')
        </if>
        <if test="categoryId != null">
            and i.category_id = #{categoryId}
        </if>
        <if test="status != null">
            and i.status = #{status}
        </if>
        <if test="deptId != null">
            and i.dept_id = #{deptId}
        </if>
        order by i.create_time desc
    </select>

    <select id="selectInspectionItemPage" resultMap="InspectionItemResult">
        <include refid="selectInspectionItemVo"/>
        where i.del_flag = 0
        <if test="item.itemCode != null and item.itemCode != ''">
            and i.item_code like concat('%', #{item.itemCode}, '%')
        </if>
        <if test="item.itemName != null and item.itemName != ''">
            and i.item_name like concat('%', #{item.itemName}, '%')
        </if>
        <if test="item.categoryId != null">
            and i.category_id = #{item.categoryId}
        </if>
        <if test="item.status != null">
            and i.status = #{item.status}
        </if>
        <if test="item.deptId != null">
            and i.dept_id = #{item.deptId}
        </if>
        order by i.create_time desc
    </select>

    <select id="selectInspectionItemById" parameterType="Long" resultMap="InspectionItemResult">
        <include refid="selectInspectionItemVo"/>
        where i.item_id = #{itemId} and i.del_flag = 0
    </select>

    <select id="selectInspectionItemByCategoryId" parameterType="Long" resultMap="InspectionItemResult">
        <include refid="selectInspectionItemVo"/>
        where i.category_id = #{categoryId} and i.del_flag = 0 and i.status = 1
        order by i.create_time desc
    </select>

    <select id="selectInspectionItemByDeptId" parameterType="Long" resultMap="InspectionItemResult">
        <include refid="selectInspectionItemVo"/>
        where i.dept_id = #{deptId} and i.del_flag = 0 and i.status = 1
        order by i.create_time desc
    </select>

    <select id="checkItemNameUnique" parameterType="String" resultType="int">
        select count(1) from inspection_item where item_name = #{itemName} and del_flag = 0
    </select>

    <select id="checkItemCodeUnique" parameterType="String" resultType="int">
        select count(1) from inspection_item where item_code = #{itemCode} and del_flag = 0
    </select>
</mapper> 