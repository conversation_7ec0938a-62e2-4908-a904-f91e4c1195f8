package com.tcm.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.integration.entity.IntegrationApiLog;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 系统集成接口调用日志服务接口
 *
 * <AUTHOR>
 */
public interface IntegrationApiLogService extends IService<IntegrationApiLog> {

    /**
     * 分页查询接口调用日志
     *
     * @param page       页码
     * @param pageSize   每页大小
     * @param systemCode 系统编码
     * @param apiName    接口名称
     * @param status     状态
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 分页结果
     */
    IPage<IntegrationApiLog> page(int page, int pageSize, String systemCode, String apiName, Integer status,
            LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 记录接口调用日志
     *
     * @param configId       配置ID
     * @param systemCode     系统编码
     * @param apiName        接口名称
     * @param requestUrl     请求URL
     * @param requestMethod  请求方法
     * @param requestHeaders 请求头
     * @param requestParams  请求参数
     * @param responseStatus 响应状态码
     * @param responseResult 响应结果
     * @param processTime    处理时间
     * @param status         状态
     * @param errorMsg       错误消息
     * @param requestIp      请求IP
     * @param operateBy      操作人
     * @return 日志ID
     */
    Long addLog(Long configId, String systemCode, String apiName, String requestUrl, String requestMethod,
            String requestHeaders, String requestParams, Integer responseStatus, String responseResult,
            Long processTime, Integer status, String errorMsg, String requestIp, String operateBy);

    /**
     * 获取接口调用统计数据
     *
     * @param systemCode 系统编码
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 统计数据
     */
    Map<String, Object> getApiStatistics(String systemCode, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 清理过期日志
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredLogs(int days);
}