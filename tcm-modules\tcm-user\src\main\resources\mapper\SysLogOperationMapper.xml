<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.user.mapper.SysLogOperationMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.user.entity.SysLogOperation">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="username" property="username" />
        <result column="module" property="module" />
        <result column="operate_type" property="operateType" />
        <result column="description" property="description" />
        <result column="method" property="method" />
        <result column="request_url" property="requestUrl" />
        <result column="request_method" property="requestMethod" />
        <result column="request_params" property="requestParams" />
        <result column="response_result" property="responseResult" />
        <result column="ip" property="ip" />
        <result column="execution_time" property="executionTime" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="create_time" property="createTime" />
    </resultMap>
</mapper> 