package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 诊断记录视图对象
 */
@Data
public class DiagnosisRecordVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 诊断编号
     */
    private String diagnosisCode;
    
    /**
     * 诊断日期
     */
    private Date diagnosisDate;
    
    /**
     * 主诉
     */
    private String chiefComplaint;
    
    /**
     * 现病史
     */
    private String presentIllness;
    
    /**
     * 既往史
     */
    private String pastHistory;
    
    /**
     * 个人史
     */
    private String personalHistory;
    
    /**
     * 家族史
     */
    private String familyHistory;
    
    /**
     * 望诊结果
     */
    private String inspection;
    
    /**
     * 闻诊结果
     */
    private String auscultation;
    
    /**
     * 问诊结果
     */
    private String interrogation;
    
    /**
     * 切诊结果
     */
    private String palpation;
    
    /**
     * 舌象
     */
    private String tongueCondition;
    
    /**
     * 脉象
     */
    private String pulseCondition;
    
    /**
     * 中医诊断
     */
    private String tcmDiagnosis;
    
    /**
     * 西医诊断
     */
    private String westernDiagnosis;
    
    /**
     * 辨证分型
     */
    private String syndromeType;
    
    /**
     * 治疗原则
     */
    private String treatmentPrinciple;
    
    /**
     * 状态（0：草稿，1：已完成，2：已取消）
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 