package com.tcm.common.core.utils.file;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

/**
 * 文件处理工具类
 */
public class FileUtils {
    
    /**
     * 根据文件路径创建文件
     *
     * @param filePath 文件路径
     * @return 创建的文件
     * @throws IOException IO异常
     */
    public static File createFile(String filePath) throws IOException {
        File file = new File(filePath);
        if (!file.exists()) {
            // 创建父目录
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            file.createNewFile();
        }
        return file;
    }
    
    /**
     * 拷贝文件
     *
     * @param srcFile 源文件
     * @param destFile 目标文件
     * @throws IOException IO异常
     */
    public static void copyFile(File srcFile, File destFile) throws IOException {
        if (!destFile.exists()) {
            destFile.getParentFile().mkdirs();
            destFile.createNewFile();
        }
        Path source = srcFile.toPath();
        Path target = destFile.toPath();
        Files.copy(source, target, StandardCopyOption.REPLACE_EXISTING);
    }
    
    /**
     * 拷贝输入流到文件
     *
     * @param input 输入流
     * @param targetFile 目标文件
     * @throws IOException IO异常
     */
    public static void copyInputStreamToFile(InputStream input, File targetFile) throws IOException {
        if (!targetFile.exists()) {
            targetFile.getParentFile().mkdirs();
            targetFile.createNewFile();
        }
        try (OutputStream output = Files.newOutputStream(targetFile.toPath())) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = input.read(buffer)) != -1) {
                output.write(buffer, 0, bytesRead);
            }
        }
    }
    
    /**
     * 删除文件或目录
     *
     * @param file 文件或目录
     * @return 成功返回true，失败返回false
     */
    public static boolean delete(File file) {
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            if (files != null) {
                for (File f : files) {
                    delete(f);
                }
            }
        }
        return file.delete();
    }
    
    /**
     * 获取文件扩展名
     *
     * @param fileName 文件名
     * @return 文件扩展名
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
    
    /**
     * 获取文件名（不含扩展名）
     *
     * @param fileName 文件名
     * @return 不含扩展名的文件名
     */
    public static String getFileNameWithoutExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf(".") == -1) {
            return fileName;
        }
        return fileName.substring(0, fileName.lastIndexOf("."));
    }
    
    /**
     * 判断文件是否存在
     *
     * @param filePath 文件路径
     * @return 存在返回true，不存在返回false
     */
    public static boolean exists(String filePath) {
        return new File(filePath).exists();
    }
    
    /**
     * 判断文件是否存在（exist方法作为exists的别名）
     *
     * @param filePath 文件路径
     * @return 存在返回true，不存在返回false
     */
    public static boolean exist(String filePath) {
        return exists(filePath);
    }
    
    /**
     * 创建目录
     *
     * @param directoryPath 目录路径
     * @return 成功返回true，失败返回false
     */
    public static boolean createDirectory(String directoryPath) {
        File directory = new File(directoryPath);
        if (!directory.exists()) {
            return directory.mkdirs();
        }
        return true;
    }
}
