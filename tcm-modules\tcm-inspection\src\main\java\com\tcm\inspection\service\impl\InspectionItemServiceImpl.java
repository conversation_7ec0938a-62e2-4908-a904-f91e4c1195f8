package com.tcm.inspection.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.inspection.domain.InspectionItem;
import com.tcm.inspection.mapper.InspectionItemMapper;
import com.tcm.inspection.service.InspectionItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 检查项目服务实现类
 */
@Service
public class InspectionItemServiceImpl extends ServiceImpl<InspectionItemMapper, InspectionItem> implements InspectionItemService {

    @Autowired
    private InspectionItemMapper inspectionItemMapper;

    /**
     * 查询检查项目列表
     *
     * @param inspectionItem 检查项目信息
     * @return 检查项目集合
     */
    @Override
    public List<InspectionItem> selectInspectionItemList(InspectionItem inspectionItem) {
        return inspectionItemMapper.selectInspectionItemList(inspectionItem);
    }

    /**
     * 分页查询检查项目列表
     *
     * @param page           分页信息
     * @param inspectionItem 检查项目信息
     * @return 检查项目分页信息
     */
    @Override
    public IPage<InspectionItem> selectInspectionItemPage(Page<InspectionItem> page, InspectionItem inspectionItem) {
        return inspectionItemMapper.selectInspectionItemPage(page, inspectionItem);
    }

    /**
     * 根据ID查询检查项目
     *
     * @param itemId 检查项目ID
     * @return 检查项目信息
     */
    @Override
    public InspectionItem selectInspectionItemById(Long itemId) {
        return inspectionItemMapper.selectInspectionItemById(itemId);
    }

    /**
     * 根据分类ID查询检查项目列表
     *
     * @param categoryId 分类ID
     * @return 检查项目集合
     */
    @Override
    public List<InspectionItem> selectInspectionItemByCategoryId(Long categoryId) {
        return inspectionItemMapper.selectInspectionItemByCategoryId(categoryId);
    }

    /**
     * 根据科室ID查询检查项目列表
     *
     * @param deptId 科室ID
     * @return 检查项目集合
     */
    @Override
    public List<InspectionItem> selectInspectionItemByDeptId(Long deptId) {
        return inspectionItemMapper.selectInspectionItemByDeptId(deptId);
    }

    /**
     * 新增检查项目
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean insertInspectionItem(InspectionItem inspectionItem) {
        return save(inspectionItem);
    }

    /**
     * 修改检查项目
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    @Override
    @Transactional
    public boolean updateInspectionItem(InspectionItem inspectionItem) {
        return updateById(inspectionItem);
    }

    /**
     * 删除检查项目
     *
     * @param itemId 检查项目ID
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteInspectionItemById(Long itemId) {
        // 逻辑删除
        InspectionItem item = new InspectionItem();
        item.setItemId(itemId);
        item.setDelFlag(1);
        return updateById(item);
    }

    /**
     * 批量删除检查项目
     *
     * @param itemIds 需要删除的检查项目ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public boolean deleteInspectionItemByIds(Long[] itemIds) {
        // 批量逻辑删除
        InspectionItem item = new InspectionItem();
        item.setDelFlag(1);
        return update(item,
                new LambdaQueryWrapper<InspectionItem>()
                        .in(InspectionItem::getItemId, Arrays.asList(itemIds)));
    }

    /**
     * 检查项目名称是否存在
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    @Override
    public boolean checkItemNameUnique(InspectionItem inspectionItem) {
        Long itemId = inspectionItem.getItemId() == null ? -1L : inspectionItem.getItemId();
        InspectionItem info = getOne(new LambdaQueryWrapper<InspectionItem>()
                .eq(InspectionItem::getItemName, inspectionItem.getItemName())
                .eq(InspectionItem::getDelFlag, 0)
                .last("limit 1"));
        return info != null && !info.getItemId().equals(itemId);
    }

    /**
     * 检查项目编码是否存在
     *
     * @param inspectionItem 检查项目信息
     * @return 结果
     */
    @Override
    public boolean checkItemCodeUnique(InspectionItem inspectionItem) {
        Long itemId = inspectionItem.getItemId() == null ? -1L : inspectionItem.getItemId();
        InspectionItem info = getOne(new LambdaQueryWrapper<InspectionItem>()
                .eq(InspectionItem::getItemCode, inspectionItem.getItemCode())
                .eq(InspectionItem::getDelFlag, 0)
                .last("limit 1"));
        return info != null && !info.getItemId().equals(itemId);
    }
} 