package com.tcm.monitor.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;

/**
 * 系统监控控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system")
public class MonitorController {

    /**
     * 获取服务器信息
     */
    @GetMapping("/server")
    public ResponseEntity<Map<String, Object>> getServerInfo() {
        Map<String, Object> serverInfo = new HashMap<>();
        
        // 获取操作系统信息
        OperatingSystemMXBean osMXBean = ManagementFactory.getOperatingSystemMXBean();
        serverInfo.put("osName", osMXBean.getName());
        serverInfo.put("osArch", osMXBean.getArch());
        serverInfo.put("osVersion", osMXBean.getVersion());
        serverInfo.put("availableProcessors", osMXBean.getAvailableProcessors());
        serverInfo.put("systemLoadAverage", osMXBean.getSystemLoadAverage());
        
        // 获取JVM信息
        RuntimeMXBean runtimeMXBean = ManagementFactory.getRuntimeMXBean();
        serverInfo.put("jvmName", runtimeMXBean.getVmName());
        serverInfo.put("jvmVersion", runtimeMXBean.getVmVersion());
        serverInfo.put("jvmVendor", runtimeMXBean.getVmVendor());
        serverInfo.put("startTime", runtimeMXBean.getStartTime());
        serverInfo.put("uptime", runtimeMXBean.getUptime());
        
        // 获取内存信息
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        serverInfo.put("heapMemoryUsage", memoryMXBean.getHeapMemoryUsage());
        serverInfo.put("nonHeapMemoryUsage", memoryMXBean.getNonHeapMemoryUsage());
        
        return ResponseEntity.ok(serverInfo);
    }
    
    /**
     * 获取健康状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealthStatus() {
        Map<String, Object> healthStatus = new HashMap<>();
        healthStatus.put("status", "UP");
        healthStatus.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(healthStatus);
    }
} 