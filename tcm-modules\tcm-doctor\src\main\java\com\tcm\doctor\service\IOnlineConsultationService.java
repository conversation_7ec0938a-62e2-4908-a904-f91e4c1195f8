package com.tcm.doctor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.doctor.domain.OnlineConsultation;
import com.tcm.doctor.dto.ConsultationQuery;
import com.tcm.doctor.dto.OnlineConsultationDTO;
import com.tcm.doctor.vo.OnlineConsultationVO;

import java.util.List;

/**
 * 在线问诊服务接口
 */
public interface IOnlineConsultationService extends IService<OnlineConsultation> {
    
    /**
     * 分页查询问诊列表
     *
     * @param query 查询条件
     * @return 问诊列表
     */
    IPage<OnlineConsultationVO> listConsultations(ConsultationQuery query);
    
    /**
     * 根据ID查询问诊详情
     *
     * @param consultationId 问诊ID
     * @return 问诊详情
     */
    OnlineConsultationVO getConsultationById(Long consultationId);
    
    /**
     * 根据患者ID查询问诊列表
     *
     * @param patientId 患者ID
     * @return 问诊列表
     */
    List<OnlineConsultationVO> getConsultationsByPatientId(Long patientId);
    
    /**
     * 根据医生ID查询问诊列表
     *
     * @param doctorId 医生ID
     * @param status   状态（可选）
     * @return 问诊列表
     */
    List<OnlineConsultationVO> getConsultationsByDoctorId(Long doctorId, Integer status);
    
    /**
     * 新增问诊
     *
     * @param consultationDTO 问诊信息
     * @return 结果
     */
    boolean addConsultation(OnlineConsultationDTO consultationDTO);
    
    /**
     * 更新问诊状态
     *
     * @param consultationId 问诊ID
     * @param status         状态
     * @return 结果
     */
    boolean updateConsultationStatus(Long consultationId, Integer status);
    
    /**
     * 接诊
     *
     * @param consultationId 问诊ID
     * @param doctorId       医生ID
     * @return 结果
     */
    boolean acceptConsultation(Long consultationId, Long doctorId);
    
    /**
     * 完成问诊
     *
     * @param consultationId 问诊ID
     * @param doctorAdvice   医生建议
     * @param diagnosis      诊断结果
     * @param prescriptionId 处方ID
     * @return 结果
     */
    boolean finishConsultation(Long consultationId, String doctorAdvice, String diagnosis, Long prescriptionId);
    
    /**
     * 评价问诊
     *
     * @param consultationId 问诊ID
     * @param score          评分
     * @param evaluation     评价内容
     * @return 结果
     */
    boolean evaluateConsultation(Long consultationId, Integer score, String evaluation);
    
    /**
     * 更新支付状态
     *
     * @param consultationId 问诊ID
     * @param payStatus      支付状态
     * @return 结果
     */
    boolean updatePayStatus(Long consultationId, Integer payStatus);
    
    /**
     * 获取医生待接诊数量
     *
     * @param doctorId 医生ID
     * @return 待接诊数量
     */
    int countPendingConsultations(Long doctorId);
    
    /**
     * 获取医生进行中的问诊数量
     *
     * @param doctorId 医生ID
     * @return 进行中的问诊数量
     */
    int countOngoingConsultations(Long doctorId);
} 