<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tcm.schedule.mapper.SysLogOperationMapper">

    <resultMap id="SysLogOperationMap" type="com.tcm.schedule.entity.SysLogOperation">
        <id property="id" column="id"/>
        <result property="module" column="module"/>
        <result property="operateType" column="operate_type"/>
        <result property="description" column="description"/>
        <result property="method" column="method"/>
        <result property="requestUrl" column="request_url"/>
        <result property="requestMethod" column="request_method"/>
        <result property="requestParams" column="request_params"/>
        <result property="responseResult" column="response_result"/>
        <result property="executionTime" column="execution_time"/>
        <result property="ip" column="ip"/>
        <result property="location" column="location"/>
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="status" column="status"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap id="SysLogOperationVOMap" type="com.tcm.schedule.vo.SysLogOperationVO">
        <id property="id" column="id"/>
        <result property="module" column="module"/>
        <result property="operateType" column="operate_type"/>
        <result property="operateTypeDesc" column="operate_type_desc"/>
        <result property="description" column="description"/>
        <result property="method" column="method"/>
        <result property="requestUrl" column="request_url"/>
        <result property="requestMethod" column="request_method"/>
        <result property="requestParams" column="request_params"/>
        <result property="responseResult" column="response_result"/>
        <result property="executionTime" column="execution_time"/>
        <result property="ip" column="ip"/>
        <result property="location" column="location"/>
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="status" column="status"/>
        <result property="statusDesc" column="status_desc"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectSysLogOperation">
        SELECT
            id, module, operate_type,
            CASE operate_type
                WHEN 0 THEN '其他'
                WHEN 1 THEN '新增'
                WHEN 2 THEN '修改'
                WHEN 3 THEN '删除'
                WHEN 4 THEN '授权'
                WHEN 5 THEN '导出'
                WHEN 6 THEN '导入'
                WHEN 7 THEN '强退'
                WHEN 8 THEN '生成代码'
                WHEN 9 THEN '清空数据'
                ELSE '未知'
            END AS operate_type_desc,
            description, method, request_url, request_method, request_params,
            response_result, execution_time, ip, location, user_id, username, status,
            CASE status
                WHEN 0 THEN '成功'
                WHEN 1 THEN '失败'
                ELSE '未知'
            END AS status_desc,
            error_msg, create_time
        FROM sys_log_operation
    </sql>

    <select id="selectLogPage" resultMap="SysLogOperationVOMap">
        <include refid="selectSysLogOperation"/>
        <where>
            <if test="log.module != null and log.module != ''">
                AND module LIKE CONCAT('%', #{log.module}, '%')
            </if>
            <if test="log.operateType != null">
                AND operate_type = #{log.operateType}
            </if>
            <if test="log.status != null">
                AND status = #{log.status}
            </if>
            <if test="log.username != null and log.username != ''">
                AND username LIKE CONCAT('%', #{log.username}, '%')
            </if>
            <if test="log.requestUrl != null and log.requestUrl != ''">
                AND request_url LIKE CONCAT('%', #{log.requestUrl}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectLogById" resultMap="SysLogOperationVOMap">
        <include refid="selectSysLogOperation"/>
        WHERE id = #{id}
    </select>

    <delete id="deleteLogByIds">
        DELETE FROM sys_log_operation WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="cleanLog">
        TRUNCATE TABLE sys_log_operation
    </delete>
</mapper>
