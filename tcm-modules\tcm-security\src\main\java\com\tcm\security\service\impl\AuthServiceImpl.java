package com.tcm.security.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import com.tcm.common.core.exception.BusinessException;
import com.tcm.security.utils.JwtUtils;
import com.tcm.security.config.properties.SecurityProperties;
import com.tcm.security.model.dto.LoginDTO;
import com.tcm.security.model.vo.TokenVO;
import com.tcm.security.model.vo.UserInfoVO;
import com.tcm.security.service.AuthService;

import lombok.extern.slf4j.Slf4j;

/**
 * 认证服务实现
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private SecurityProperties securityProperties;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CAPTCHA_CODE_KEY = "captcha:";
    private static final String TOKEN_KEY = "token:";

    @Override
    public TokenVO login(LoginDTO loginDTO) {
        // 验证码校验
        validateCaptcha(loginDTO);

        // 用户认证
        Authentication authentication = authenticationManager
                .authenticate(new UsernamePasswordAuthenticationToken(loginDTO.getUsername(), loginDTO.getPassword()));

        // 生成token
        String username = authentication.getName();
        Long userId = Long.valueOf(username);
        String accessToken = jwtUtils.generateToken(userId, username);
        String refreshToken = UUID.randomUUID().toString();

        // 缓存刷新令牌
        redisTemplate.opsForValue().set(TOKEN_KEY + userId + ":" + refreshToken, userId,
                securityProperties.getRefreshTokenExpireMinutes(), TimeUnit.MINUTES);

        // 返回令牌信息
        return TokenVO.builder().accessToken(accessToken).refreshToken(refreshToken)
                .expiresIn(securityProperties.getTokenExpireMinutes() * 60).build();
    }

    @Override
    public Boolean logout() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            String userId = authentication.getName();
            // 删除刷新令牌
            String pattern = TOKEN_KEY + userId + ":*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            return true;
        }
        return false;
    }

    @Override
    public TokenVO refreshToken(String refreshToken) {
        if (refreshToken == null || refreshToken.isEmpty()) {
            throw new BusinessException("刷新令牌不能为空");
        }

        // 获取当前用户ID
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BusinessException("用户未登录");
        }

        String username = authentication.getName();
        Long userId = Long.valueOf(username);
        String key = TOKEN_KEY + userId + ":" + refreshToken;

        // 验证刷新令牌
        Object cachedUserId = redisTemplate.opsForValue().get(key);
        if (cachedUserId == null || !cachedUserId.toString().equals(userId.toString())) {
            throw new BusinessException("刷新令牌已过期");
        }

        // 生成新令牌
        String newAccessToken = jwtUtils.generateToken(userId, username);
        String newRefreshToken = UUID.randomUUID().toString();

        // 删除旧刷新令牌
        redisTemplate.delete(key);

        // 保存新刷新令牌
        redisTemplate.opsForValue().set(TOKEN_KEY + userId + ":" + newRefreshToken, userId,
                securityProperties.getRefreshTokenExpireMinutes(), TimeUnit.MINUTES);

        return TokenVO.builder().accessToken(newAccessToken).refreshToken(newRefreshToken)
                .expiresIn(securityProperties.getTokenExpireMinutes() * 60).build();
    }

    @Override
    public UserInfoVO getUserInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BusinessException("用户未登录");
        }

        // 这里应该从数据库或缓存中获取用户信息
        // 为了演示，使用模拟数据
        UserInfoVO userInfo = new UserInfoVO();
        userInfo.setUserId(Long.valueOf(authentication.getName()));
        userInfo.setUsername("admin");
        userInfo.setRealName("系统管理员");
        userInfo.setAvatar("https://example.com/avatar.jpg");
        userInfo.setRoles(List.of("admin"));
        userInfo.setPermissions(List.of("system:user:list", "system:user:add", "system:user:edit"));

        return userInfo;
    }

    @Override
    public Object generateCaptcha() {
        // 生成验证码
        String uuid = UUID.randomUUID().toString();
        String verifyCode = generateRandomCode(4);

        // 存储到redis
        String captchaKey = CAPTCHA_CODE_KEY + uuid;
        redisTemplate.opsForValue().set(captchaKey, verifyCode, 5, TimeUnit.MINUTES);

        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("uuid", uuid);
        result.put("img", "data:image/png;base64," + verifyCode); // 实际项目中应生成真实验证码图片

        return result;
    }

    /**
     * 校验验证码
     */
    private void validateCaptcha(LoginDTO loginDTO) {
        // 演示环境，省略验证码校验
        if (loginDTO.getCaptcha() != null && loginDTO.getUuid() != null) {
            String captchaKey = CAPTCHA_CODE_KEY + loginDTO.getUuid();
            Object codeObj = redisTemplate.opsForValue().get(captchaKey);

            if (codeObj == null) {
                throw new BusinessException("验证码已过期");
            }

            String code = codeObj.toString();
            redisTemplate.delete(captchaKey);

            if (!code.equalsIgnoreCase(loginDTO.getCaptcha())) {
                throw new BusinessException("验证码错误");
            }
        }
    }

    /**
     * 生成随机验证码
     */
    private String generateRandomCode(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append((int) (Math.random() * 10));
        }
        return sb.toString();
    }
}