package com.tcm.doctor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.doctor.domain.DoctorEvaluation;
import com.tcm.doctor.vo.DoctorEvaluationVO;
import com.tcm.doctor.vo.DoctorWorkloadVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 医生评价Mapper接口
 */
@Mapper
public interface DoctorEvaluationMapper extends BaseMapper<DoctorEvaluation> {
    
    /**
     * 查询医生评价列表
     *
     * @param page     分页参数
     * @param doctorId 医生ID
     * @return 医生评价列表
     */
    IPage<DoctorEvaluationVO> selectDoctorEvaluationList(Page<DoctorEvaluation> page,
                                                       @Param("doctorId") Long doctorId);
    
    /**
     * 根据ID查询医生评价详情
     *
     * @param evaluationId 评价ID
     * @return 医生评价详情
     */
    DoctorEvaluationVO selectDoctorEvaluationById(@Param("evaluationId") Long evaluationId);
    
    /**
     * 根据诊断ID查询医生评价
     *
     * @param diagnosisId 诊断ID
     * @return 医生评价
     */
    DoctorEvaluationVO selectDoctorEvaluationByDiagnosisId(@Param("diagnosisId") Long diagnosisId);
    
    /**
     * 计算医生评分
     *
     * @param doctorId 医生ID
     * @return 平均评分
     */
    Double calculateDoctorAverageScore(@Param("doctorId") Long doctorId);
    
    /**
     * 获取医生评价数量
     *
     * @param doctorId 医生ID
     * @return 评价数量
     */
    Integer countDoctorEvaluation(@Param("doctorId") Long doctorId);
    
    /**
     * 查询医生工作量统计
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 医生工作量统计
     */
    List<DoctorWorkloadVO> selectDoctorWorkloadList(@Param("beginDate") Date beginDate,
                                                  @Param("endDate") Date endDate);
} 