package com.tcm.analytics.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.analytics.entity.AnalyticsDashboard;

/**
 * 分析仪表盘服务接口
 */
public interface AnalyticsDashboardService extends IService<AnalyticsDashboard> {

    /**
     * 创建仪表盘
     * 
     * @param dashboard 仪表盘信息
     * @return 仪表盘ID
     */
    Long createDashboard(AnalyticsDashboard dashboard);

    /**
     * 更新仪表盘
     * 
     * @param dashboard 仪表盘信息
     * @return 是否更新成功
     */
    boolean updateDashboard(AnalyticsDashboard dashboard);

    /**
     * 获取仪表盘详情
     * 
     * @param dashboardId 仪表盘ID
     * @return 仪表盘详情
     */
    AnalyticsDashboard getDashboardDetail(Long dashboardId);

    /**
     * 分页查询仪表盘列表
     * 
     * @param page   分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    Page<AnalyticsDashboard> listDashboardsByPage(Page<AnalyticsDashboard> page, Map<String, Object> params);

    /**
     * 根据创建者获取仪表盘列表
     * 
     * @param creator 创建者
     * @return 仪表盘列表
     */
    List<AnalyticsDashboard> listDashboardsByCreator(String creator);

    /**
     * 获取仪表盘数据
     * 
     * @param dashboardId 仪表盘ID
     * @param params      查询参数
     * @return 仪表盘数据
     */
    Map<String, Object> getDashboardData(Long dashboardId, Map<String, Object> params);

    /**
     * 添加组件到仪表盘
     * 
     * @param dashboardId     仪表盘ID
     * @param componentConfig 组件配置
     * @return 组件ID
     */
    String addComponent(Long dashboardId, Map<String, Object> componentConfig);

    /**
     * 更新仪表盘组件
     * 
     * @param dashboardId     仪表盘ID
     * @param componentId     组件ID
     * @param componentConfig 组件配置
     * @return 是否更新成功
     */
    boolean updateComponent(Long dashboardId, String componentId, Map<String, Object> componentConfig);

    /**
     * 删除仪表盘组件
     * 
     * @param dashboardId 仪表盘ID
     * @param componentId 组件ID
     * @return 是否删除成功
     */
    boolean removeComponent(Long dashboardId, String componentId);

    /**
     * 导出仪表盘配置
     * 
     * @param dashboardId 仪表盘ID
     * @return 导出的配置
     */
    String exportDashboardConfig(Long dashboardId);

    /**
     * 导入仪表盘配置
     * 
     * @param config        配置内容
     * @param dashboardName 仪表盘名称
     * @return 导入的仪表盘ID
     */
    Long importDashboardConfig(String config, String dashboardName);

    /**
     * 删除仪表盘
     * 
     * @param dashboardId 仪表盘ID
     * @return 是否删除成功
     */
    boolean deleteDashboard(Long dashboardId);

    /**
     * 根据类型获取仪表盘列表
     * 
     * @param type 仪表盘类型
     * @return 仪表盘列表
     */
    List<AnalyticsDashboard> getDashboardsByType(Integer type);

    /**
     * 搜索仪表盘
     * 
     * @param keyword 关键字
     * @return 仪表盘列表
     */
    List<AnalyticsDashboard> searchDashboards(String keyword);

    /**
     * 获取仪表盘统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getDashboardStats();
}