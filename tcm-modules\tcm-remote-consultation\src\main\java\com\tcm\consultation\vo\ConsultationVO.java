package com.tcm.consultation.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 远程会诊视图对象
 */
@Data
public class ConsultationVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 会诊记录ID
     */
    private Long consultationId;
    
    /**
     * 会诊编号
     */
    private String consultationCode;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 主诊医生ID
     */
    private Long primaryDoctorId;
    
    /**
     * 主诊医生姓名
     */
    private String primaryDoctorName;
    
    /**
     * 主诊医生职称
     */
    private String primaryDoctorTitle;
    
    /**
     * 会诊主题
     */
    private String title;
    
    /**
     * 会诊类型（1：病例分析会诊，2：实时远程会诊，3：MDT多学科会诊）
     */
    private Integer type;
    
    /**
     * 会诊类型名称
     */
    private String typeName;
    
    /**
     * 会诊预约时间
     */
    private Date appointmentTime;
    
    /**
     * 实际开始时间
     */
    private Date startTime;
    
    /**
     * 实际结束时间
     */
    private Date endTime;
    
    /**
     * 会诊时长（分钟）
     */
    private Integer duration;
    
    /**
     * 会诊状态（0：待确认，1：已确认，2：进行中，3：已完成，4：已取消）
     */
    private Integer status;
    
    /**
     * 会诊状态名称
     */
    private String statusName;
    
    /**
     * 会诊内容摘要
     */
    private String summary;
    
    /**
     * 诊断ID
     */
    private Long diagnosisId;
    
    /**
     * 诊断名称
     */
    private String diagnosisName;
    
    /**
     * 会议室ID
     */
    private String roomId;
    
    /**
     * 会议密码
     */
    private String password;
    
    /**
     * 录制视频URL
     */
    private String videoUrl;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 参与人列表
     */
    private List<ParticipantVO> participants;
    
    /**
     * 获取类型名称
     */
    public String getTypeName() {
        if (type == null) {
            return "";
        }
        switch (type) {
            case 1: return "病例分析会诊";
            case 2: return "实时远程会诊";
            case 3: return "MDT多学科会诊";
            default: return "未知类型";
        }
    }
    
    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 0: return "待确认";
            case 1: return "已确认";
            case 2: return "进行中";
            case 3: return "已完成";
            case 4: return "已取消";
            default: return "未知状态";
        }
    }
} 