-- 创建数据分析仪表盘表
CREATE TABLE IF NOT EXISTS `analytics_dashboard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '仪表盘ID',
  `name` varchar(100) NOT NULL COMMENT '仪表盘名称',
  `type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '仪表盘类型（0:系统预设, 1:用户自定义）',
  `layout` text COMMENT '仪表盘布局（JSON格式）',
  `data_source` varchar(50) DEFAULT NULL COMMENT '数据源类型（mysql, elastic, file等）',
  `query_config` text COMMENT '查询配置',
  `components_config` json DEFAULT NULL COMMENT '组件配置（JSON格式）',
  `refresh_interval` int(11) DEFAULT '60' COMMENT '刷新间隔（秒）',
  `auto_refresh` tinyint(4) DEFAULT '0' COMMENT '是否自动刷新（0:否, 1:是）',
  `scope` tinyint(4) DEFAULT '0' COMMENT '权限范围（0:私有, 1:部门, 2:全局）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `creator` varchar(50) DEFAULT NULL COMMENT '创建者',
  `order_num` int(11) DEFAULT '0' COMMENT '排序号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标记（0:未删除, 1:已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_creator` (`creator`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据分析仪表盘表'; 