package com.tcm.statistics.controller;

import java.time.LocalDate;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.common.core.domain.Result;
import com.tcm.statistics.domain.ChartDataDTO;
import com.tcm.statistics.domain.DoctorPrescriptionStatsDTO;
import com.tcm.statistics.domain.MedicineUsageDTO;
import com.tcm.statistics.domain.PrescriptionRationalityReportDTO;
import com.tcm.statistics.service.ChartGeneratorService;
import com.tcm.statistics.service.PrescriptionStatisticsService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

/**
 * 处方统计控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v1/statistics/prescriptions")
@Tag(name = "处方统计分析", description = "处方统计分析接口")
@Slf4j
public class PrescriptionStatisticsController {

    @Autowired
    private PrescriptionStatisticsService statisticsService;
    
    @Autowired
    private ChartGeneratorService chartService;
    
    /**
     * 获取医生处方统计数据
     */
    @GetMapping("/doctors/{doctorId}")
    @Operation(summary = "获取医生处方统计数据", description = "根据医生ID和日期范围获取处方统计数据")
    public Result<DoctorPrescriptionStatsDTO> getDoctorStats(
            @Parameter(description = "医生ID") @PathVariable Long doctorId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("获取医生处方统计数据: doctorId={}, startDate={}, endDate={}", doctorId, startDate, endDate);
        DoctorPrescriptionStatsDTO stats = statisticsService.analyzeDoctorPrescriptions(
            doctorId, startDate, endDate);
        return Result.success(stats);
    }
    
    /**
     * 获取医生处方统计图表 - 证型分布
     */
    @GetMapping("/doctors/{doctorId}/charts/syndrome-distribution")
    @Operation(summary = "获取医生处方统计图表 - 证型分布", description = "获取医生处方中的证型分布饼图数据")
    public Result<ChartDataDTO> getSyndromeDistributionChart(
            @Parameter(description = "医生ID") @PathVariable Long doctorId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("获取医生处方证型分布图表: doctorId={}, startDate={}, endDate={}", doctorId, startDate, endDate);
        DoctorPrescriptionStatsDTO stats = statisticsService.analyzeDoctorPrescriptions(
            doctorId, startDate, endDate);
        
        ChartDataDTO chartData = chartService.generatePieChart(
            "证型分布", 
            stats.getSyndromeDistribution(),
            "syndrome",
            "count");
        
        return Result.success(chartData);
    }
    
    /**
     * 获取医生处方统计图表 - 常用药物TOP10
     */
    @GetMapping("/doctors/{doctorId}/charts/top-medicines")
    @Operation(summary = "获取医生处方统计图表 - 常用药物TOP10", description = "获取医生处方中常用药物TOP10条形图数据")
    public Result<ChartDataDTO> getTopMedicinesChart(
            @Parameter(description = "医生ID") @PathVariable Long doctorId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("获取医生处方常用药物TOP10图表: doctorId={}, startDate={}, endDate={}", doctorId, startDate, endDate);
        DoctorPrescriptionStatsDTO stats = statisticsService.analyzeDoctorPrescriptions(
            doctorId, startDate, endDate);
        
        ChartDataDTO chartData = chartService.generateBarChart(
            "常用药物TOP10",
            stats.getTopMedicines(),
            "medicineName",
            "count");
        
        return Result.success(chartData);
    }
    
    /**
     * 获取药物使用趋势
     */
    @GetMapping("/medicines/{medicineName}/trend")
    @Operation(summary = "获取药物使用趋势", description = "获取指定药物在日期范围内的使用趋势")
    public Result<ChartDataDTO> getMedicineTrend(
            @Parameter(description = "药物名称") @PathVariable String medicineName,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("获取药物使用趋势: medicineName={}, startDate={}, endDate={}", medicineName, startDate, endDate);
        List<MedicineUsageDTO> trend = statisticsService.analyzeMedicineTrend(
            medicineName, startDate, endDate);
        
        ChartDataDTO chartData = chartService.generateLineChart(
            medicineName + "使用趋势",
            trend,
            "medicineName",
            "count");
        
        return Result.success(chartData);
    }
    
    /**
     * 获取处方合理性评估报告
     */
    @GetMapping("/rationality")
    @Operation(summary = "获取处方合理性评估报告", description = "根据日期范围和科室ID获取处方合理性评估报告")
    public Result<PrescriptionRationalityReportDTO> getRationalityReport(
            @Parameter(description = "科室ID") @RequestParam(required = false) Long departmentId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("获取处方合理性评估报告: departmentId={}, startDate={}, endDate={}", departmentId, startDate, endDate);
        PrescriptionRationalityReportDTO report = statisticsService.analyzeRationality(
            startDate, endDate, departmentId);
        return Result.success(report);
    }
    
    /**
     * 获取处方合理性问题分布图表
     */
    @GetMapping("/rationality/charts/issue-distribution")
    @Operation(summary = "获取处方合理性问题分布图表", description = "获取处方合理性问题分布饼图数据")
    public Result<ChartDataDTO> getIssueDistributionChart(
            @Parameter(description = "科室ID") @RequestParam(required = false) Long departmentId,
            @Parameter(description = "开始日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        
        log.info("获取处方合理性问题分布图表: departmentId={}, startDate={}, endDate={}", departmentId, startDate, endDate);
        PrescriptionRationalityReportDTO report = statisticsService.analyzeRationality(
            startDate, endDate, departmentId);
        
        ChartDataDTO chartData = chartService.generatePieChart(
            "处方问题分布", 
            report.getIssueDistribution(),
            "issueType",
            "count");
        
        return Result.success(chartData);
    }
} 