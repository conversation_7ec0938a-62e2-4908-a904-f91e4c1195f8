<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.inspection.mapper.InspectionCategoryMapper">

    <!-- 检查分类结果映射 -->
    <resultMap id="CategoryResult" type="com.tcm.inspection.domain.InspectionCategory">
        <id property="categoryId" column="category_id"/>
        <result property="categoryCode" column="category_code"/>
        <result property="categoryName" column="category_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="description" column="description"/>
        <result property="orderNum" column="order_num"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="selectCategoryVo">
        select 
            category_id, category_code, category_name, parent_id, description,
            order_num, status, create_by, create_time, update_by, update_time, 
            remark, del_flag
        from inspection_category
    </sql>
    
    <!-- 查询检查分类列表 -->
    <select id="selectInspectionCategoryList" parameterType="com.tcm.inspection.domain.InspectionCategory" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>
            del_flag = 0
            <if test="categoryName != null and categoryName != ''">
                AND category_name like concat('%', #{categoryName}, '%')
            </if>
            <if test="categoryCode != null and categoryCode != ''">
                AND category_code like concat('%', #{categoryCode}, '%')
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        order by order_num
    </select>
    
    <!-- 分页查询检查分类列表 -->
    <select id="selectInspectionCategoryPage" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        <where>
            del_flag = 0
            <if test="category.categoryName != null and category.categoryName != ''">
                AND category_name like concat('%', #{category.categoryName}, '%')
            </if>
            <if test="category.categoryCode != null and category.categoryCode != ''">
                AND category_code like concat('%', #{category.categoryCode}, '%')
            </if>
            <if test="category.parentId != null">
                AND parent_id = #{category.parentId}
            </if>
            <if test="category.status != null">
                AND status = #{category.status}
            </if>
        </where>
        order by order_num
    </select>
    
    <!-- 根据ID查询检查分类 -->
    <select id="selectInspectionCategoryById" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where category_id = #{categoryId} and del_flag = 0
    </select>
    
    <!-- 根据父分类ID查询检查分类列表 -->
    <select id="selectInspectionCategoryByParentId" parameterType="Long" resultMap="CategoryResult">
        <include refid="selectCategoryVo"/>
        where parent_id = #{parentId} and del_flag = 0
        order by order_num
    </select>
    
    <!-- 检查分类名称是否存在 -->
    <select id="checkCategoryNameUnique" parameterType="String" resultType="int">
        select count(1) from inspection_category
        where category_name = #{categoryName} and del_flag = 0
    </select>
    
    <!-- 检查分类编码是否存在 -->
    <select id="checkCategoryCodeUnique" parameterType="String" resultType="int">
        select count(1) from inspection_category
        where category_code = #{categoryCode} and del_flag = 0
    </select>
</mapper> 