package com.tcm.international;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 国际化服务启动类
 */
@EnableDiscoveryClient(autoRegister = false)
@SpringBootApplication(exclude = {
    org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration.class,
    org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration.class,
    org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class
})
@MapperScan("com.tcm.international.mapper")
public class TcmInternationalApplication {
    
    static {
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j2.statusLogger.level", "OFF");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("org.springframework.boot.logging.LoggingSystem", "none");
        System.setProperty("spring.main.banner-mode", "off");
        System.setProperty("logging.level.root", "WARN");
        System.setProperty("slf4j.provider", "ch.qos.logback.classic.spi.LogbackServiceProvider");
        System.setProperty("logging.config", "classpath:logback-spring.xml");
        System.setProperty("logback.configurationFile", "logback-spring.xml");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");
    }

    public static void main(String[] args) {
        try {
            System.out.println("Starting International Service...");
            
            // Configure logging
            System.setProperty("org.springframework.boot.logging.LoggingSystem", "none");
            System.setProperty("slf4j.provider", "ch.qos.logback.classic.spi.LogbackServiceProvider");
            System.setProperty("logging.config", "classpath:logback-spring.xml");
            System.setProperty("log4j2.disable", "true");
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml");
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");
            System.setProperty("spring.main.banner-mode", "off");
            
            // Application config
            System.setProperty("spring.application.name", "tcm-international");
            System.setProperty("spring.liquibase.enabled", "false");
            System.setProperty("spring.main.allow-bean-definition-overriding", "true");
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");
            System.setProperty("nacos.client.naming.grpc.enabled", "false");
            System.setProperty("nacos.client.config.grpc.enabled", "false");
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");
            
            // Start application
            SpringApplication app = new SpringApplication(TcmInternationalApplication.class);
            app.run(args);
            
            // Simple success message without concatenation
            System.out.println("\n=================================================");
            System.out.println("TCM-INTERNATIONAL Service Started Successfully!");
            System.out.println("Port: " + System.getProperty("server.port"));
            System.out.println("Status: Running");
            System.out.println("=================================================\n");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
