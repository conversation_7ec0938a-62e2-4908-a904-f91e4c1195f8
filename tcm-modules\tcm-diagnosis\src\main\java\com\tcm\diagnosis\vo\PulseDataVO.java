package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 脉诊数据视图对象
 */
@Data
public class PulseDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据ID
     */
    private Long id;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 脉象位置（寸/关/尺）
     */
    private String pulsePosition;

    /**
     * 波形数据（用于前端绘图）
     */
    private List<Double> waveform;

    /**
     * 特征数据
     */
    private Map<String, Object> features;

    /**
     * 采集时间
     */
    private Date captureTime;

    /**
     * 采样率（Hz）
     */
    private Integer sampleRate;

    /**
     * 采样时长（秒）
     */
    private Integer sampleDuration;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 状态（0：未分析，1：已分析）
     */
    private Integer status;
}