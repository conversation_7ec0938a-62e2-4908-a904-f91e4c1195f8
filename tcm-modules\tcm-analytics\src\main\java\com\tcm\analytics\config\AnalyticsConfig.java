package com.tcm.analytics.config;

import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 数据分析配置类
 */
@Configuration
public class AnalyticsConfig {

    @Value("${analytics.thread.core-size:4}")
    private int corePoolSize;

    @Value("${analytics.thread.max-size:8}")
    private int maxPoolSize;

    @Value("${analytics.thread.queue-capacity:100}")
    private int queueCapacity;

    @Value("${analytics.thread.keep-alive-seconds:60}")
    private int keepAliveSeconds;

    @Value("${analytics.batch.size:1000}")
    private int batchSize;

    /**
     * 配置数据处理线程池
     */
    @Bean
    public ThreadPoolTaskExecutor analyticsThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("analytics-thread-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 批处理大小配置
     */
    @Bean
    public Integer analyticsBatchSize() {
        return batchSize;
    }
}