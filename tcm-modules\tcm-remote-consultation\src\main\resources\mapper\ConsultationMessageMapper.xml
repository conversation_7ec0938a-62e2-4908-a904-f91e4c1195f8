<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.consultation.mapper.ConsultationMessageMapper">
    
    <resultMap id="ConsultationMessageResult" type="com.tcm.consultation.domain.ConsultationMessage">
        <id property="messageId" column="message_id"/>
        <result property="consultationId" column="consultation_id"/>
        <result property="senderId" column="sender_id"/>
        <result property="senderName" column="sender_name"/>
        <result property="senderType" column="sender_type"/>
        <result property="messageType" column="message_type"/>
        <result property="content" column="content"/>
        <result property="mediaUrl" column="media_url"/>
        <result property="fileName" column="file_name"/>
        <result property="fileSize" column="file_size"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <sql id="selectConsultationMessageVo">
        SELECT cm.message_id, 
               cm.consultation_id, 
               cm.sender_id, 
               cm.sender_name, 
               cm.sender_type, 
               cm.message_type, 
               cm.content, 
               cm.media_url, 
               cm.file_name, 
               cm.file_size, 
               cm.create_by, 
               cm.create_time, 
               cm.update_by, 
               cm.update_time, 
               cm.remark, 
               cm.del_flag
        FROM tcm_consultation_message cm
    </sql>
    
</mapper> 