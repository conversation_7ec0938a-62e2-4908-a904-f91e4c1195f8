<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入Spring Boot默认的基础配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 定义变量，可以通过application.yml中的logging.file.path覆盖 -->
    <property name="LOG_FILE_PATH" value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}/logs}}"/>
    <property name="LOG_FILE_NAME" value="${LOG_FILE_NAME:-tcm-medication}"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_PATH}/${LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE_PATH}/${LOG_FILE_NAME}-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 设置日志级别 -->
    <logger name="com.tcm" level="DEBUG"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework.security" level="INFO"/>
    <logger name="com.alibaba.nacos" level="ERROR"/>
    <logger name="com.alibaba.nacos.shaded.io.grpc" level="ERROR"/>
    <logger name="com.alibaba.nacos.shaded.io.perfmark" level="ERROR"/>
    <logger name="com.alibaba.nacos.common.remote.client" level="ERROR"/>
    <logger name="com.alibaba.nacos.shaded.io.grpc.netty" level="ERROR"/>
    <logger name="com.alibaba.cloud.nacos" level="ERROR"/>
    <logger name="com.alibaba.nacos.client.naming" level="ERROR"/>
    <logger name="org.springframework.cloud" level="DEBUG"/>
    <logger name="org.springframework.boot.autoconfigure" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    
    <!-- 根日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
