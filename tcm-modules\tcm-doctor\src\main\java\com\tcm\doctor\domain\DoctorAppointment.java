package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 医生预约实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_doctor_appointment")
public class DoctorAppointment extends BaseEntity {
    
    /**
     * 预约ID
     */
    @TableId
    private Long appointmentId;
    
    /**
     * 排班ID
     */
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 预约日期
     */
    private Date appointmentDate;
    
    /**
     * 预约时段
     */
    private Integer periodType;
    
    /**
     * 预约号序
     */
    private Integer sequenceNumber;
    
    /**
     * 主诉
     */
    private String chiefComplaint;
    
    /**
     * 病情描述
     */
    private String illnessDesc;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 预约状态（0-待就诊 1-已就诊 2-已取消 3-爽约）
     */
    private Integer status;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 取消时间
     */
    private Date cancelTime;
    
    /**
     * 就诊时间
     */
    private Date visitTime;
    
    /**
     * 就诊记录ID（关联诊断记录）
     */
    private Long diagnosisId;
} 