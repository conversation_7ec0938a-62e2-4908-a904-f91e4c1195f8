package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.diagnosis.domain.SyndromeIdentification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 辨证结果Mapper接口
 */
@Mapper
public interface SyndromeIdentificationMapper extends BaseMapper<SyndromeIdentification> {

    /**
     * 根据诊断ID查询辨证结果
     *
     * @param diagnosisId 诊断记录ID
     * @return 辨证结果
     */
    SyndromeIdentification selectSyndromeByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据证型查询辨证结果列表
     *
     * @param pattern 证型
     * @return 辨证结果列表
     */
    List<SyndromeIdentification> selectSyndromeByPattern(@Param("pattern") String pattern);

    /**
     * 根据患者ID查询辨证结果列表
     *
     * @param patientId 患者ID
     * @return 辨证结果列表
     */
    List<SyndromeIdentification> selectSyndromeByPatientId(@Param("patientId") Long patientId);

    /**
     * 查询与指定辨证结果相似的案例
     *
     * @param syndromeId 辨证结果ID
     * @param limit      限制数量
     * @return 相似辨证结果列表
     */
    List<SyndromeIdentification> selectSimilarSyndromes(@Param("syndromeId") Long syndromeId,
            @Param("limit") Integer limit);
}