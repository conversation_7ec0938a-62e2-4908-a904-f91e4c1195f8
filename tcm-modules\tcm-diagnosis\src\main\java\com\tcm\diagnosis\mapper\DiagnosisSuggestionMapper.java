package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.diagnosis.domain.DiagnosisSuggestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 诊断建议Mapper接口
 */
@Mapper
public interface DiagnosisSuggestionMapper extends BaseMapper<DiagnosisSuggestion> {

    /**
     * 根据诊断ID查询诊断建议列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 诊断建议列表
     */
    List<DiagnosisSuggestion> selectSuggestionByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据诊断ID和建议类型查询诊断建议列表
     *
     * @param diagnosisId    诊断记录ID
     * @param suggestionType 建议类型
     * @return 诊断建议列表
     */
    List<DiagnosisSuggestion> selectSuggestionByType(@Param("diagnosisId") Long diagnosisId,
            @Param("suggestionType") String suggestionType);

    /**
     * 根据诊断ID查询已采纳的诊断建议列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 已采纳的诊断建议列表
     */
    List<DiagnosisSuggestion> selectAdoptedSuggestion(@Param("diagnosisId") Long diagnosisId);
}