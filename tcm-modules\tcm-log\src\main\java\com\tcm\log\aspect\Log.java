package com.tcm.log.aspect;

import java.lang.annotation.*;

/**
 * 自定义操作日志注解
 * 
 * <AUTHOR>
 */
@Target({ ElementType.PARAMETER, ElementType.METHOD })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    /**
     * 模块
     */
    String module() default "";

    /**
     * 业务类型
     */
    String businessType() default "";
    
    /**
     * 业务ID
     */
    String businessId() default "";

    /**
     * 操作类型
     */
    int operationType() default 0;

    /**
     * 日志内容
     */
    String content() default "";
    
    /**
     * 保存请求参数
     */
    boolean saveRequestData() default true;
    
    /**
     * 保存响应数据
     */
    boolean saveResponseData() default true;
} 