package com.tcm.diagnosis.service;

import com.tcm.diagnosis.domain.DiagnosisSuggestion;
import com.tcm.diagnosis.dto.DiagnosisDataDTO;
import com.tcm.diagnosis.vo.DiagnosisSuggestionVO;
import com.tcm.diagnosis.vo.KnowledgeItemVO;

import java.util.List;

/**
 * 诊断建议服务接口
 */
public interface IDiagnosisSuggestionService {

    /**
     * 生成诊断建议
     *
     * @param diagnosisDataDTO 诊断数据
     * @return 建议ID列表
     */
    List<Long> generateSuggestions(DiagnosisDataDTO diagnosisDataDTO);

    /**
     * 获取诊断建议
     *
     * @param suggestionId 建议ID
     * @return 诊断建议视图对象
     */
    DiagnosisSuggestionVO getSuggestion(Long suggestionId);

    /**
     * 获取诊断记录的建议列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 诊断建议视图对象列表
     */
    List<DiagnosisSuggestionVO> getSuggestionsByDiagnosisId(Long diagnosisId);

    /**
     * 获取诊断记录的特定类型建议
     *
     * @param diagnosisId    诊断记录ID
     * @param suggestionType 建议类型
     * @return 诊断建议视图对象列表
     */
    List<DiagnosisSuggestionVO> getSuggestionsByType(Long diagnosisId, String suggestionType);

    /**
     * 医生采纳建议
     *
     * @param suggestionId 建议ID
     * @param doctorId     医生ID
     * @return 是否成功
     */
    boolean adoptSuggestion(Long suggestionId, Long doctorId);

    /**
     * 医生拒绝建议
     *
     * @param suggestionId 建议ID
     * @param doctorId     医生ID
     * @param reason       拒绝原因
     * @return 是否成功
     */
    boolean rejectSuggestion(Long suggestionId, Long doctorId, String reason);

    /**
     * 修改建议内容
     *
     * @param suggestionId 建议ID
     * @param content      新的建议内容
     * @param doctorId     医生ID
     * @return 是否成功
     */
    boolean updateSuggestion(Long suggestionId, String content, Long doctorId);

    /**
     * 获取相关知识条目
     *
     * @param keyword 关键词
     * @param limit   限制数量
     * @return 知识条目视图对象列表
     */
    List<KnowledgeItemVO> getRelatedKnowledge(String keyword, Integer limit);

    /**
     * 导出诊断建议报告
     *
     * @param diagnosisId 诊断记录ID
     * @return 报告文件路径
     */
    String exportSuggestionReport(Long diagnosisId);
}