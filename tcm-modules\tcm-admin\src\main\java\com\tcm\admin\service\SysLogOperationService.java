package com.tcm.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.admin.dto.SysLogOperationDTO;
import com.tcm.admin.entity.SysLogOperation;
import com.tcm.admin.vo.SysLogOperationVO;
import com.tcm.common.core.domain.PageVO;

/**
 * 系统操作日志服务接口
 */
public interface SysLogOperationService extends IService<SysLogOperation> {

    /**
     * 保存操作日志
     *
     * @param sysLogOperation 操作日志信息
     */
    void saveLog(SysLogOperation sysLogOperation);

    /**
     * 查询系统操作日志分页列表
     *
     * @param dto 查询条件
     * @return 分页列表
     */
    PageVO<SysLogOperationVO> selectLogPage(SysLogOperationDTO dto);

    /**
     * 查询操作日志详细信息
     *
     * @param id 日志ID
     * @return 操作日志信息
     */
    SysLogOperationVO getLogById(Long id);

    /**
     * 清空操作日志
     * 
     * @return 结果
     */
    boolean cleanLog();

    /**
     * 批量删除操作日志
     *
     * @param ids 日志ID数组
     * @return 结果
     */
    boolean deleteLogByIds(Long[] ids);
}