package com.tcm.diagnosis.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 辨证数据传输对象
 */
@Data
public class SyndromeIdentificationDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    
    /**
     * 辨证ID（更新时使用）
     */
    private Long syndromeId;

    /**
     * 诊断记录ID
     */
    @NotNull(message = "诊断记录ID不能为空")
    private Long diagnosisId;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 望诊结果
     */
    private String inspection;

    /**
     * 闻诊结果
     */
    private String auscultation;

    /**
     * 问诊结果
     */
    private String interrogation;

    /**
     * 切诊结果
     */
    private String palpation;

    /**
     * 舌象图像ID列表
     */
    private List<Long> tongueImageIds;

    /**
     * 脉诊数据ID列表
     */
    private List<Long> pulseDataIds;

    /**
     * 症状列表（名称-程度）
     */
    private Map<String, String> symptoms;
    
    /**
     * 体征列表（名称-程度）
     */
    private Map<String, String> signs;

    /**
     * 体质信息
     */
    private String constitution;

    /**
     * 其他临床信息
     */
    private Map<String, Object> clinicalInfo;

    /**
     * 辨证方法（规则/AI/混合）
     */
    private String identificationMethod;
    
    /**
     * 疾病类型
     */
    private String diseaseType;
    
    /**
     * 证型类型
     */
    private String syndromeType;
    
    /**
     * 病机学说明
     */
    private String pathogenesis;
    
    /**
     * 分析结果（JSON格式）
     */
    private String analysisResult;
    
    /**
     * 治疗原则
     */
    private String treatmentPrinciple;
}