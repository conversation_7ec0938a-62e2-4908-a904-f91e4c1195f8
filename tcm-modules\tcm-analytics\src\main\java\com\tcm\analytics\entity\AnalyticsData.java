package com.tcm.analytics.entity;

import java.util.Date;
import java.util.Map;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import lombok.Data;

/**
 * 分析数据实体类 用于在Elasticsearch中存储和检索数据
 */
@Data
@Document(indexName = "tcm_analytics")
@ConditionalOnProperty(name = "spring.elasticsearch.enabled", havingValue = "true", matchIfMissing = false)
public class AnalyticsData {

    @Id
    private String id;

    @Field(type = FieldType.Keyword)
    private String dataSource;

    @Field(type = FieldType.Keyword)
    private String dataType;

    @Field(type = FieldType.Keyword)
    private String creator;

    @Field(type = FieldType.Date)
    private Date createTime;

    @Field(type = FieldType.Date)
    private Date updateTime;

    @Field(type = FieldType.Object)
    private Map<String, Object> data;

    @Field(type = FieldType.Text)
    private String queryConfig;

    @Field(type = FieldType.Keyword)
    private String dashboardId;

    @Field(type = FieldType.Text)
    private String description;
}