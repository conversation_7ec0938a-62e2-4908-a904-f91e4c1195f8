package com.tcm.device.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;

/**
 * 设备配置类
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "device")
public class DeviceConfig {

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 3000;

    /**
     * 心跳检测间隔（毫秒）
     */
    private int heartbeatInterval = 30000;

    /**
     * 重连尝试次数
     */
    private int retryAttempts = 3;
}