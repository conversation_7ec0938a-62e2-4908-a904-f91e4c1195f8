package com.tcm.assistant.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.tcm.assistant.model.dto.DiagnosisDataDTO;
import com.tcm.assistant.model.dto.ClinicalDataDTO;
import com.tcm.assistant.model.dto.MedicalImageDTO;
import com.tcm.assistant.model.vo.DiagnosisSuggestionVO;
import com.tcm.assistant.model.vo.ImageAnalysisVO;
import com.tcm.assistant.client.DeepSeekApiClient;

import java.util.Map;
import java.util.HashMap;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;
import java.time.Instant;

/**
 * AI辅助诊疗服务
 */
@Service
@Slf4j
public class DiagnosisAssistantService {

    @Autowired
    private DeepSeekApiClient deepSeekApiClient;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 分析医疗图像
     */
    public ImageAnalysisVO analyzeImage(MedicalImageDTO imageDTO) {
        log.info("处理图像分析请求，类型: {}", imageDTO.getImageType());

        long startTime = System.currentTimeMillis();

        try {
            // 调用DeepSeek API进行图像分析
            Map<String, Object> result = deepSeekApiClient.analyzeImage(
                    imageDTO.getImageType(),
                    imageDTO.getImageData());

            // 转换结果
            ImageAnalysisVO vo = new ImageAnalysisVO();
            vo.setImageType(imageDTO.getImageType());
            vo.setProcessingTime(System.currentTimeMillis() - startTime);

            if (result != null) {
                if (result.containsKey("analysis_result")) {
                    vo.setAnalysisResult((String) result.get("analysis_result"));
                }

                if (result.containsKey("features")) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> features = (Map<String, Object>) result.get("features");
                    vo.setFeatures(features);

                    // 舌象特殊处理
                    if ("TONGUE".equals(imageDTO.getImageType())) {
                        vo.setTongueColor((String) features.getOrDefault("tongue_color", ""));
                        vo.setCoatingColor((String) features.getOrDefault("coating_color", ""));
                        vo.setTongueShape((String) features.getOrDefault("tongue_shape", ""));
                        vo.setCoatingThickness((String) features.getOrDefault("coating_thickness", ""));
                    }
                }

                if (result.containsKey("confidence")) {
                    vo.setConfidence((Double) result.get("confidence"));
                }
            }

            return vo;
        } catch (Exception e) {
            log.error("图像分析处理失败", e);

            // 发生错误时返回基本信息
            ImageAnalysisVO fallback = new ImageAnalysisVO();
            fallback.setImageType(imageDTO.getImageType());
            fallback.setAnalysisResult("图像分析暂时不可用: " + e.getMessage());
            fallback.setProcessingTime(System.currentTimeMillis() - startTime);
            fallback.setConfidence(0.0);
            return fallback;
        }
    }

    /**
     * 获取诊断建议
     */
    public DiagnosisSuggestionVO getDiagnosisSuggestion(DiagnosisDataDTO dto) {
        log.info("处理诊断建议请求：{}", dto);

        try {
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("patient_id", dto.getPatientId());
            requestData.put("doctor_id", dto.getDoctorId());
            requestData.put("tcm_syndromes", dto.getTcmSyndromes());
            requestData.put("symptoms", dto.getSymptoms());
            requestData.put("tongue_description", dto.getTongueDescription());
            requestData.put("pulse_description", dto.getPulseDescription());
            requestData.put("inquiry_info", dto.getInquiryInfo());

            // 调用AI服务获取诊断建议
            Map<String, Object> response = deepSeekApiClient.getDiagnosisSuggestion(requestData);

            DiagnosisSuggestionVO result = new DiagnosisSuggestionVO();

            if (response != null) {
                result.setSuggestion((String) response.getOrDefault("suggestion", "暂无诊断建议"));
                result.setTreatmentPlan((String) response.getOrDefault("treatment_plan", ""));

                if (response.containsKey("confidence")) {
                    result.setConfidence((Double) response.get("confidence"));
                }

                // 相关知识
                if (response.containsKey("related_knowledge")) {
                    @SuppressWarnings("unchecked")
                    List<Object> knowledge = (List<Object>) response.get("related_knowledge");
                    result.setRelatedKnowledge(knowledge);
                }

                // 相似病例
                if (response.containsKey("similar_cases")) {
                    @SuppressWarnings("unchecked")
                    List<Object> cases = (List<Object>) response.get("similar_cases");
                    result.setSimilarCases(cases);
                }
            } else {
                result.setSuggestion("AI诊断服务暂时不可用");
            }

            return result;
        } catch (Exception e) {
            log.error("获取诊断建议失败", e);
            DiagnosisSuggestionVO fallback = new DiagnosisSuggestionVO();
            fallback.setSuggestion("AI诊断服务出错: " + e.getMessage());
            return fallback;
        }
    }

    /**
     * 获取治疗建议
     */
    public Object getTreatmentSuggestion(DiagnosisDataDTO dto) {
        log.info("处理治疗建议请求：{}", dto);

        try {
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("patient_id", dto.getPatientId());
            requestData.put("doctor_id", dto.getDoctorId());
            requestData.put("tcm_syndromes", dto.getTcmSyndromes());
            requestData.put("symptoms", dto.getSymptoms());

            // 调用AI服务获取治疗建议
            Map<String, Object> response = deepSeekApiClient.getDiagnosisSuggestion(requestData);

            return response != null ? response.getOrDefault("treatment_plan", "暂无治疗建议") : "AI治疗建议服务暂时不可用";
        } catch (Exception e) {
            log.error("获取治疗建议失败", e);
            return "AI治疗建议服务出错: " + e.getMessage();
        }
    }

    /**
     * 获取相似病例
     */
    public Object getSimilarCases(DiagnosisDataDTO dto) {
        log.info("处理相似病例请求：{}", dto);

        try {
            // 构建请求参数
            Map<String, Object> query = new HashMap<>();
            query.put("symptoms", dto.getSymptoms());
            query.put("tcm_syndromes", dto.getTcmSyndromes());

            // 调用相似病例搜索服务
            return deepSeekApiClient.getSimilarCases(query);
        } catch (Exception e) {
            log.error("获取相似病例失败", e);
            return Collections.singletonMap("error", "获取相似病例失败: " + e.getMessage());
        }
    }
}