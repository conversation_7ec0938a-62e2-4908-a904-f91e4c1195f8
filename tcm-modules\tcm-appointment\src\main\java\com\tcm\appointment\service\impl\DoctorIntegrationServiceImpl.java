package com.tcm.appointment.service.impl;

import com.tcm.appointment.service.IDoctorIntegrationService;
import com.tcm.appointment.vo.DoctorScheduleVO;
import com.tcm.appointment.vo.DoctorVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.List;

/**
 * 医生模块集成服务实现类
 */
@Slf4j
@Service
public class DoctorIntegrationServiceImpl implements IDoctorIntegrationService {

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 获取医生信息
     *
     * @param doctorId 医生ID
     * @return 医生信息
     */
    @Override
    public DoctorVO getDoctorInfo(Long doctorId) {
        log.info("调用医生服务获取医生信息，doctorId: {}", doctorId);
        try {
            // 实际项目中应使用微服务调用或Feign客户端
            // 这里为简化实现，使用RestTemplate模拟调用
            String url = "http://tcm-doctor/doctor/" + doctorId;
            return restTemplate.getForObject(url, DoctorVO.class);
        } catch (Exception e) {
            log.error("获取医生信息失败", e);
            return null;
        }
    }

    /**
     * 获取科室下的所有医生
     *
     * @param departmentId 科室ID
     * @return 医生列表
     */
    @Override
    public List<DoctorVO> getDoctorsByDepartment(Long departmentId) {
        log.info("调用医生服务获取科室下的医生列表，departmentId: {}", departmentId);
        try {
            String url = "http://tcm-doctor/doctor/list?departmentId=" + departmentId;
            return restTemplate.getForObject(url, List.class);
        } catch (Exception e) {
            log.error("获取科室医生列表失败", e);
            return null;
        }
    }

    /**
     * 获取医生排班信息
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 排班信息
     */
    @Override
    public List<DoctorScheduleVO> getDoctorSchedule(Long doctorId, Date date) {
        log.info("调用医生服务获取排班信息，doctorId: {}, date: {}", doctorId, date);
        try {
            String url = "http://tcm-doctor/doctor/schedule?doctorId=" + doctorId + "&date=" + date.getTime();
            return restTemplate.getForObject(url, List.class);
        } catch (Exception e) {
            log.error("获取医生排班信息失败", e);
            return null;
        }
    }

    /**
     * 更新医生排班信息（如减少可预约数）
     *
     * @param scheduleId   排班ID
     * @param decrementNum 减少数量
     * @return 操作结果
     */
    @Override
    public boolean updateScheduleAppointmentLimit(Long scheduleId, Integer decrementNum) {
        log.info("调用医生服务更新排班可预约数，scheduleId: {}, decrementNum: {}", scheduleId, decrementNum);
        try {
            String url = "http://tcm-doctor/doctor/schedule/update-limit?scheduleId=" + scheduleId + "&decrementNum=" + decrementNum;
            return Boolean.TRUE.equals(restTemplate.postForObject(url, null, Boolean.class));
        } catch (Exception e) {
            log.error("更新医生排班可预约数失败", e);
            return false;
        }
    }
} 