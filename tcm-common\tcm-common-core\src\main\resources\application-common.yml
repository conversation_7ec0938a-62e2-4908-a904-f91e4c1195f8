# 通用配置
spring:
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ${tcm.service.mysql.url}
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    druid:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 20
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true

  # Redis配置
  data:
    redis:
      host: ${tcm.service.redis.host}
      port: ${tcm.service.redis.port}
      password: ${tcm.service.redis.password}
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池最大连接数
          max-active: 200
          # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
          # 连接池中的最大空闲连接
          max-idle: 10
          # 连接池中的最小空闲连接
          min-idle: 0

# Elasticsearch配置
elasticsearch:
  host: **********
  port: 9200
  username: elastic
  password: kaixin207
  connectTimeout: 5000
  socketTimeout: 5000

# Neo4j配置
neo4j:
  uri: bolt://**********:7687
  username: neo4j
  password: kaixin207

# Milvus配置
milvus:
  host: **********
  port: 19530
  username: root
  password: kaixin207

# MinIO配置
minio:
  endpoint: http://**********:9000
  accessKey: qztllz
  secretKey: kaixin207
  bucketName: tcm-system

# Sentinel配置
spring.cloud.sentinel:
  transport:
    dashboard: ${tcm.service.sentinel.dashboardUrl}
  eager: true
  datasource:
    ds:
      nacos:
        server-addr: ${tcm.service.nacos.url}
        dataId: sentinel-rules
        groupId: DEFAULT_GROUP
        rule-type: flow

# Seata配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}-group
  enable-auto-data-source-proxy: true
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ${tcm.service.nacos.url}
      username: ${tcm.service.nacos.username}
      password: ${tcm.service.nacos.password}
  config:
    type: nacos
    nacos:
      server-addr: ${tcm.service.nacos.url}
      group: SEATA_GROUP
      username: ${tcm.service.nacos.username}
      password: ${tcm.service.nacos.password}
      data-id: seata-server.properties

# RocketMQ配置
rocketmq:
  name-server: ${tcm.service.rocketMq.nameServerAddress}
  producer:
    group: ${spring.application.name}-producer-group
    send-message-timeout: 3000

# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    com.tcm: debug
    org.springframework: warn

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.**.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Token配置
token:
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyzTCMSystem
  # 令牌有效期（默认30分钟）
  expireTime: 30

# 统一服务配置
tcm:
  service:
    # MySQL配置
    mysql:
      host: **********
      port: 3306
      username: root
      password: Kaixin207@1984
      database: tcm-system
      url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/${tcm.service.mysql.database}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true
      
    # Redis配置
    redis:
      host: **********
      port: 6379
      password: kaixin207
      database: 0
      
    # Elasticsearch配置
    elasticsearch:
      host: **********
      port: 9200
      username: elastic
      password: kaixin207
      
    # Neo4j配置
    neo4j:
      host: **********
      port: 7474
      boltPort: 7687
      username: neo4j
      password: kaixin207
      
    # Milvus配置
    milvus:
      host: **********
      port: 19530
      username: root
      password: kaixin207
      
    # MinIO配置
    minio:
      host: **********
      port: 9000
      accessKey: qztllz
      secretKey: kaixin207
      bucketName: tcm-system
      
    # Sentinel配置
    sentinel:
      host: **********
      port: 8080
      username: sentinel
      password: kaixin207
      
    # ELK配置
    elk:
      elasticsearch:
        host: **********
        port: 9200
        username: elastic
        password: kaixin207
      logstash:
        host: **********
        port: 5044
      kibana:
        host: **********
        port: 5601
        username: elastic
        password: kaixin207
        
    # Seata配置
    seata:
      host: **********
      port: 8091
      username: seata
      password: kaixin207
      database: seata
      
    # Nacos配置
    nacos:
      host: ***********
      port: 8848
      username: nacos
      password: kaixin207
      
    # RocketMQ配置
    rocketMq:
      host: **********
      port: 9876
      username: admin
      password: admin
      nameServerAddress: ${tcm.service.rocketMq.host}:${tcm.service.rocketMq.port}
      
    # JDK配置
    jdk:
      # 默认JDK路径，可通过环境变量JDK_HOME覆盖
      home: ${JDK_HOME:}
      # 特定操作系统的JDK路径
      windows: 
        home: ${WINDOWS_JDK_HOME:C:\Users\<USER>\jdk\java}
      linux:
        home: ${LINUX_JDK_HOME:/home/<USER>/jdk/java/}
      mac:
        home: ${MAC_JDK_HOME:/Library/Java/JavaVirtualMachines/jdk1.8.0_301.jdk/Contents/Home} 