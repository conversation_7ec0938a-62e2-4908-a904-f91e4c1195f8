package com.tcm.message.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.common.core.domain.R;
import com.tcm.message.entity.Message;
import com.tcm.message.service.MessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息控制器
 */
@RestController
@RequestMapping("/message")
@Tag(name = "消息管理", description = "消息管理相关接口")
public class MessageController {

    @Autowired
    private MessageService messageService;

    /**
     * 发送消息
     */
    @PostMapping("/send")
    @Operation(summary = "发送消息", description = "发送单条消息")
    public R<Boolean> sendMessage(@RequestBody Message message) {
        return R.ok(messageService.sendMessage(message));
    }

    /**
     * 批量发送消息
     */
    @PostMapping("/batchSend")
    @Operation(summary = "批量发送消息", description = "向多个接收者发送相同内容的消息")
    public R<Boolean> batchSendMessage(@RequestBody Message message, @RequestParam List<Long> receiverIds) {
        return R.ok(messageService.batchSendMessage(message, receiverIds));
    }

    /**
     * 获取消息列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取消息列表", description = "分页获取指定用户的消息列表")
    public R<Page<Message>> getMessageList(
            @Parameter(description = "接收者ID") @RequestParam Long receiverId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页条数") @RequestParam(defaultValue = "10") Integer pageSize) {
        Page<Message> page = new Page<>(pageNum, pageSize);
        return R.ok(messageService.getMessageList(receiverId, page));
    }

    /**
     * 获取未读消息数量
     */
    @GetMapping("/unreadCount")
    @Operation(summary = "获取未读消息数量", description = "获取指定用户的未读消息数量")
    public R<Integer> getUnreadMessageCount(@Parameter(description = "接收者ID") @RequestParam Long receiverId) {
        return R.ok(messageService.getUnreadMessageCount(receiverId));
    }

    /**
     * 标记消息为已读
     */
    @PutMapping("/read/{id}")
    @Operation(summary = "标记消息为已读", description = "将指定消息标记为已读状态")
    public R<Boolean> markAsRead(@Parameter(description = "消息ID") @PathVariable Long id) {
        return R.ok(messageService.markAsRead(id));
    }

    /**
     * 标记所有消息为已读
     */
    @PutMapping("/readAll")
    @Operation(summary = "标记所有消息为已读", description = "将指定用户的所有未读消息标记为已读状态")
    public R<Boolean> markAllAsRead(@Parameter(description = "接收者ID") @RequestParam Long receiverId) {
        return R.ok(messageService.markAllAsRead(receiverId));
    }

    /**
     * 删除消息
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除消息", description = "删除指定消息")
    public R<Boolean> deleteMessage(@Parameter(description = "消息ID") @PathVariable Long id) {
        return R.ok(messageService.deleteMessage(id));
    }

    /**
     * 获取消息详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取消息详情", description = "获取指定消息的详细信息")
    public R<Message> getMessageDetail(@Parameter(description = "消息ID") @PathVariable Long id) {
        return R.ok(messageService.getById(id));
    }
}