package com.tcm.assistant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 医疗图像分析DTO
 */
@Data
@Schema(description = "医疗图像分析请求数据")
public class MedicalImageDTO {

    @Schema(description = "图像类型：TONGUE-舌象，FACE-面诊，PULSE-脉图，OTHER-其他", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imageType;

    @Schema(description = "图像数据（Base64编码）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String imageData;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "医生ID")
    private Long doctorId;

    @Schema(description = "诊断会话ID")
    private Long diagnosisSessionId;

    @Schema(description = "额外分析参数")
    private String analysisParams;
}