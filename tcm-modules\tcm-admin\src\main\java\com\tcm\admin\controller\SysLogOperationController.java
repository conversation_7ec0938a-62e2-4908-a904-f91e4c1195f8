package com.tcm.admin.controller;

import com.tcm.admin.dto.SysLogOperationDTO;
import com.tcm.admin.service.SysLogOperationService;
import com.tcm.admin.vo.SysLogOperationVO;
import com.tcm.common.core.domain.PageVO;
import com.tcm.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 系统操作日志控制器
 */
@Tag(name = "操作日志接口")
@RestController
@RequestMapping("/operlog")
@RequiredArgsConstructor
public class SysLogOperationController {

    private final SysLogOperationService sysLogOperationService;

    /**
     * 获取操作日志列表
     */
    @Operation(summary = "获取操作日志列表")
    @GetMapping("/list")
    public Result<PageVO<SysLogOperationVO>> list(SysLogOperationDTO dto) {
        PageVO<SysLogOperationVO> pageVO = sysLogOperationService.selectLogPage(dto);
        return Result.success(pageVO);
    }

    /**
     * 获取操作日志详细信息
     */
    @Operation(summary = "获取操作日志详细信息")
    @GetMapping("/{id}")
    public Result<SysLogOperationVO> getInfo(@Parameter(description = "日志ID") @PathVariable Long id) {
        SysLogOperationVO sysLogOperation = sysLogOperationService.getLogById(id);
        return Result.success(sysLogOperation);
    }

    /**
     * 删除操作日志
     */
    @Operation(summary = "删除操作日志")
    @DeleteMapping("/{ids}")
    public Result<?> remove(@Parameter(description = "日志ID串，多个以逗号分隔") @PathVariable Long[] ids) {
        return sysLogOperationService.deleteLogByIds(ids) ? Result.success() : Result.error("删除操作日志失败");
    }

    /**
     * 清空操作日志
     */
    @Operation(summary = "清空操作日志")
    @DeleteMapping("/clean")
    public Result<?> clean() {
        return sysLogOperationService.cleanLog() ? Result.success() : Result.error("清空操作日志失败");
    }
}