package com.tcm.doctor.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 在线问诊数据传输对象
 */
@Data
public class OnlineConsultationDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 问诊ID
     */
    private Long consultationId;
    
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;
    
    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;
    
    /**
     * 问诊标题
     */
    @NotBlank(message = "问诊标题不能为空")
    @Size(max = 100, message = "问诊标题长度不能超过100个字符")
    private String title;
    
    /**
     * 问诊类型（1-图文咨询 2-语音咨询 3-视频咨询）
     */
    @NotNull(message = "问诊类型不能为空")
    private Integer type;
    
    /**
     * 主诉
     */
    @NotBlank(message = "主诉不能为空")
    @Size(max = 200, message = "主诉长度不能超过200个字符")
    private String chiefComplaint;
    
    /**
     * 病情描述
     */
    @Size(max = 500, message = "病情描述长度不能超过500个字符")
    private String illnessDesc;
    
    /**
     * 患病时长
     */
    @Size(max = 50, message = "患病时长长度不能超过50个字符")
    private String illnessDuration;
    
    /**
     * 既往病史
     */
    @Size(max = 500, message = "既往病史长度不能超过500个字符")
    private String medicalHistory;
    
    /**
     * 药物过敏史
     */
    @Size(max = 200, message = "药物过敏史长度不能超过200个字符")
    private String allergicHistory;
    
    /**
     * 问诊图片（多个以逗号分隔）
     */
    private String images;
    
    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    private Double payAmount;
} 