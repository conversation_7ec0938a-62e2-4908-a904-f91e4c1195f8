# Tomcat
server:
  port: 9084

# Spring
spring:
  application:
    # 应用名称
    name: tcm-appointment
  profiles:
    # 环境配置
    active: dev
  cloud:
    compatibility-verifier:
      enabled: false
    # Nacos配置使用统一模式
    nacos:
      config:
        # 配置中心地址
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-common.${spring.cloud.nacos.config.file-extension}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        import-check:
          enabled: false   # 禁用配置导入检查
  config:
    import:
      - optional:classpath:/application-common.yml 