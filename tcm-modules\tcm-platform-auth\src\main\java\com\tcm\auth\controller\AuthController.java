package com.tcm.auth.controller;

import com.tcm.auth.dto.LoginDTO;
import com.tcm.auth.service.AuthService;
import com.tcm.auth.vo.LoginUserVO;
import com.tcm.auth.vo.TokenVO;
import com.tcm.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 */
@Tag(name = "认证接口")
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    /**
     * 登录
     */
    @Operation(summary = "用户登录")
    @PostMapping("/login")
    public Result<TokenVO> login(@Valid @RequestBody LoginDTO loginDTO) {
        TokenVO tokenVO = authService.login(loginDTO);
        return Result.success(tokenVO);
    }

    /**
     * 刷新令牌
     */
    @Operation(summary = "刷新令牌")
    @PostMapping("/refresh")
    public Result<TokenVO> refresh(@Parameter(description = "刷新令牌") @RequestParam String refreshToken) {
        TokenVO tokenVO = authService.refreshToken(refreshToken);
        return Result.success(tokenVO);
    }

    /**
     * 获取用户信息
     */
    @Operation(summary = "获取登录用户信息")
    @GetMapping("/info")
    public Result<LoginUserVO> info(@Parameter(description = "访问令牌") @RequestParam String token) {
        LoginUserVO userVO = authService.getUserInfo(token);
        return Result.success(userVO);
    }

    /**
     * 登出
     */
    @Operation(summary = "用户登出")
    @PostMapping("/logout")
    public Result<?> logout(@Parameter(description = "访问令牌") @RequestParam String token) {
        authService.logout(token);
        return Result.success();
    }
}