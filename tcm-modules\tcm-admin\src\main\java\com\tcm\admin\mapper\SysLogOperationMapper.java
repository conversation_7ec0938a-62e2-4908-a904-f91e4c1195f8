package com.tcm.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.admin.entity.SysLogOperation;
import com.tcm.admin.vo.SysLogOperationVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统操作日志Mapper接口
 */
@Mapper
public interface SysLogOperationMapper extends BaseMapper<SysLogOperation> {

    /**
     * 查询操作日志分页列表
     *
     * @param page            分页参数
     * @param sysLogOperation 查询参数
     * @return 分页结果
     */
    IPage<SysLogOperationVO> selectLogPage(Page<SysLogOperation> page, @Param("log") SysLogOperation sysLogOperation);

    /**
     * 根据ID查询操作日志
     *
     * @param id 日志ID
     * @return 日志信息
     */
    SysLogOperationVO selectLogById(@Param("id") Long id);

    /**
     * 批量删除操作日志
     *
     * @param ids 日志ID数组
     * @return 删除结果
     */
    int deleteLogByIds(@Param("ids") Long[] ids);

    /**
     * 清空操作日志
     */
    void cleanLog();
}