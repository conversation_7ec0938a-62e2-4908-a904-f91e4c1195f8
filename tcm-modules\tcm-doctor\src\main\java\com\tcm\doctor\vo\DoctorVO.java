package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生视图对象
 */
@Data
public class DoctorVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生编号
     */
    private String doctorCode;
    
    /**
     * 职称 (1-主任医师 2-副主任医师 3-主治医师 4-住院医师)
     */
    private Integer title;
    
    /**
     * 职称名称
     */
    private String titleName;
    
    /**
     * 专长
     */
    private String specialty;
    
    /**
     * 简介
     */
    private String introduction;
    
    /**
     * 所属医院
     */
    private String hospital;
    
    /**
     * 所属科室
     */
    private String department;
    
    /**
     * 工作年限
     */
    private Integer workYears;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 