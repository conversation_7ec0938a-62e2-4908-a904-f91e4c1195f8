package com.tcm.doctor.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.R;
import com.tcm.doctor.dto.AppointmentQuery;
import com.tcm.doctor.dto.DoctorAppointmentDTO;
import com.tcm.doctor.service.IDoctorAppointmentService;
import com.tcm.doctor.vo.DoctorAppointmentVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 医生预约控制器
 */
@RestController
@RequestMapping("/doctor/appointment")
public class DoctorAppointmentController {
    
    @Autowired
    private IDoctorAppointmentService appointmentService;
    
    /**
     * 查询预约列表
     */
    @GetMapping("/list")
    public R<IPage<DoctorAppointmentVO>> list(AppointmentQuery query) {
        IPage<DoctorAppointmentVO> page = appointmentService.listAppointments(query);
        return R.ok(page);
    }
    
    /**
     * 获取预约详情
     */
    @GetMapping("/{appointmentId}")
    public R<DoctorAppointmentVO> getInfo(@PathVariable Long appointmentId) {
        DoctorAppointmentVO appointment = appointmentService.getAppointmentById(appointmentId);
        return R.ok(appointment);
    }
    
    /**
     * 根据患者ID查询预约列表
     */
    @GetMapping("/patient/{patientId}")
    public R<List<DoctorAppointmentVO>> getAppointmentsByPatient(@PathVariable Long patientId) {
        List<DoctorAppointmentVO> appointments = appointmentService.getAppointmentsByPatientId(patientId);
        return R.ok(appointments);
    }
    
    /**
     * 根据医生ID和日期查询预约列表
     */
    @GetMapping("/doctor/{doctorId}/date/{date}")
    public R<List<DoctorAppointmentVO>> getAppointmentsByDoctorAndDate(
            @PathVariable Long doctorId,
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        List<DoctorAppointmentVO> appointments = appointmentService.getAppointmentsByDoctorIdAndDate(doctorId, date);
        return R.ok(appointments);
    }
    
    /**
     * 新增预约
     */
    @PostMapping
    public R<Boolean> add(@RequestBody @Valid DoctorAppointmentDTO appointmentDTO) {
        boolean result = appointmentService.addAppointment(appointmentDTO);
        return R.ok(result);
    }
    
    /**
     * 更新预约状态
     */
    @PutMapping("/status/{appointmentId}/{status}")
    public R<Boolean> updateStatus(@PathVariable Long appointmentId, @PathVariable Integer status) {
        boolean result = appointmentService.updateAppointmentStatus(appointmentId, status);
        return R.ok(result);
    }
    
    /**
     * 取消预约
     */
    @PutMapping("/cancel/{appointmentId}")
    public R<Boolean> cancel(@PathVariable Long appointmentId, @RequestParam String cancelReason) {
        boolean result = appointmentService.cancelAppointment(appointmentId, cancelReason);
        return R.ok(result);
    }
    
    /**
     * 更新就诊信息
     */
    @PutMapping("/visit/{appointmentId}/{diagnosisId}")
    public R<Boolean> updateVisitInfo(@PathVariable Long appointmentId, @PathVariable Long diagnosisId) {
        boolean result = appointmentService.updateVisitInfo(appointmentId, diagnosisId);
        return R.ok(result);
    }
    
    /**
     * 检查能否预约
     */
    @GetMapping("/check/{scheduleId}")
    public R<Boolean> checkAppointmentAvailable(@PathVariable Long scheduleId) {
        boolean result = appointmentService.checkAppointmentAvailable(scheduleId);
        return R.ok(result);
    }
} 