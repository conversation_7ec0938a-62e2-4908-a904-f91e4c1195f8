package com.tcm.workflow.dto;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流程部署数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDeployDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程定义键
     */
    @NotBlank(message = "流程定义键不能为空")
    private String processDefinitionKey;

    /**
     * 流程定义名称
     */
    @NotBlank(message = "流程定义名称不能为空")
    private String processDefinitionName;

    /**
     * 流程分类
     */
    private String category;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 资源名称
     */
    @NotBlank(message = "资源名称不能为空")
    private String resourceName;

    /**
     * 流程XML内容
     */
    @NotBlank(message = "流程XML内容不能为空")
    private String processXml;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否主流程
     */
    private Boolean mainProcess = true;
}