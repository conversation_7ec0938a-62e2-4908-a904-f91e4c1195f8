package com.tcm.doctor.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 医生预约数据传输对象
 */
@Data
public class DoctorAppointmentDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预约ID
     */
    private Long appointmentId;
    
    /**
     * 排班ID
     */
    @NotNull(message = "排班ID不能为空")
    private Long scheduleId;
    
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;
    
    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;
    
    /**
     * 预约日期
     */
    @NotNull(message = "预约日期不能为空")
    private Date appointmentDate;
    
    /**
     * 预约时段
     */
    @NotNull(message = "预约时段不能为空")
    private Integer periodType;
    
    /**
     * 主诉
     */
    @NotBlank(message = "主诉不能为空")
    @Size(max = 200, message = "主诉长度不能超过200个字符")
    private String chiefComplaint;
    
    /**
     * 病情描述
     */
    @Size(max = 500, message = "病情描述长度不能超过500个字符")
    private String illnessDesc;
    
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String contactPhone;
    
    /**
     * 预约状态（0-待就诊 1-已就诊 2-已取消 3-爽约）
     */
    private Integer status;
    
    /**
     * 取消原因
     */
    @Size(max = 200, message = "取消原因长度不能超过200个字符")
    private String cancelReason;
} 