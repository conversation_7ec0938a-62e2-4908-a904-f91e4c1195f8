package com.tcm.diagnosis.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 舌象图像采集数据传输对象
 */
@Data
public class TongueImageCaptureDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 图像数据（Base64编码）
     */
    @NotBlank(message = "图像数据不能为空")
    private String imageData;

    /**
     * 设备信息
     */
    private String deviceInfo;

    /**
     * 备注
     */
    private String remark;
}