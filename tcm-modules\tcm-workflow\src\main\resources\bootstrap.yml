spring:  
  application:  
    name: tcm-workflow  
  profiles:  
    active: dev  
  cloud:  
    compatibility-verifier:  
      enabled: false  
    nacos:
      config:
        server-addr: ${tcm.service.nacos.host:${TCM_NACOS_HOST:***********}}:${tcm.service.nacos.port:${TCM_NACOS_PORT:8848}}
        username: ${tcm.service.nacos.username:${TCM_NACOS_USERNAME:nacos}}
        password: ${tcm.service.nacos.password:${TCM_NACOS_PASSWORD:kaixin207}}
        namespace: ${tcm.service.nacos.namespace:}
        file-extension: yml
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: tcm-common.yml
            group: DEFAULT_GROUP
            refresh: true
        timeout: 10000
    # 移除Nacos配置，避免与application.yml冲突  
    # 完全移除Nacos配置，防止与application.yml中的配置冲突 
