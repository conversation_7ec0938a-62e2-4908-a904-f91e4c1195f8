package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 相似病例视图对象
 */
@Data
public class SimilarCaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 病例ID
     */
    private Long caseId;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 患者信息（脱敏）
     */
    private Map<String, Object> patientInfo;

    /**
     * 诊断医生姓名
     */
    private String doctorName;

    /**
     * 诊断日期
     */
    private Date diagnosisDate;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 疾病类型
     */
    private String diseaseType;
    
    /**
     * 证型
     */
    private String syndromeType;

    /**
     * 主要症状
     */
    private List<String> mainSymptoms;

    /**
     * 舌象特征
     */
    private String tongueCharacteristics;

    /**
     * 脉象特征
     */
    private String pulseCharacteristics;

    /**
     * 中医诊断
     */
    private String tcmDiagnosis;

    /**
     * 治疗方案
     */
    private String treatmentPlan;
    
    /**
     * 治疗方法
     */
    private String treatmentMethod;

    /**
     * 处方信息
     */
    private String prescription;

    /**
     * 治疗效果
     */
    private String treatmentEffect;
    
    /**
     * 有效性评分（0-10）
     */
    private Integer effectiveness;

    /**
     * 相似度（0-100）
     */
    private Integer similarity;
}