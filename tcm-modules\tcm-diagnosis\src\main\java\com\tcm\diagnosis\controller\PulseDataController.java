package com.tcm.diagnosis.controller;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.log.annotation.Log;
import com.tcm.common.log.enums.BusinessType;
import com.tcm.common.security.annotation.RequiresPermissions;
import com.tcm.diagnosis.dto.PulseDataCaptureDTO;
import com.tcm.diagnosis.service.IPulseDataService;
import com.tcm.diagnosis.vo.PulseDataVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 脉诊数据控制器
 */
@RestController
@RequestMapping("/pulse/data")
public class PulseDataController extends BaseController {

    @Autowired
    private IPulseDataService pulseDataService;

    /**
     * 上传脉诊数据
     */
    @PostMapping("/upload")
    @RequiresPermissions("diagnosis:pulse:upload")
    @Log(title = "脉诊数据", businessType = BusinessType.INSERT)
    public R<Long> uploadPulseData(@Validated @RequestBody PulseDataCaptureDTO pulseDataDTO) {
        return R.ok(pulseDataService.uploadPulseData(pulseDataDTO));
    }

    /**
     * 获取脉诊数据详情
     */
    @GetMapping("/{dataId}")
    @RequiresPermissions("diagnosis:pulse:query")
    public R<PulseDataVO> getPulseData(@PathVariable Long dataId) {
        return R.ok(pulseDataService.getPulseData(dataId));
    }

    /**
     * 获取诊断记录关联的脉诊数据列表
     */
    @GetMapping("/list/diagnosis/{diagnosisId}")
    @RequiresPermissions("diagnosis:pulse:list")
    public R<List<PulseDataVO>> getPulseDataListByDiagnosisId(@PathVariable Long diagnosisId) {
        return R.ok(pulseDataService.getPulseDataListByDiagnosisId(diagnosisId));
    }

    /**
     * 获取患者的脉诊数据列表
     */
    @GetMapping("/list/patient/{patientId}")
    @RequiresPermissions("diagnosis:pulse:list")
    public R<List<PulseDataVO>> getPulseDataListByPatientId(@PathVariable Long patientId) {
        return R.ok(pulseDataService.getPulseDataListByPatientId(patientId));
    }

    /**
     * 删除脉诊数据
     */
    @DeleteMapping("/{dataId}")
    @RequiresPermissions("diagnosis:pulse:remove")
    @Log(title = "脉诊数据", businessType = BusinessType.DELETE)
    public R<Boolean> deletePulseData(@PathVariable Long dataId) {
        return R.ok(pulseDataService.deletePulseData(dataId));
    }

    /**
     * 获取最近的脉诊数据列表
     */
    @GetMapping("/recent/{patientId}/{limit}")
    @RequiresPermissions("diagnosis:pulse:list")
    public R<List<PulseDataVO>> getRecentPulseData(@PathVariable Long patientId, @PathVariable Integer limit) {
        return R.ok(pulseDataService.getRecentPulseData(patientId, limit));
    }
}