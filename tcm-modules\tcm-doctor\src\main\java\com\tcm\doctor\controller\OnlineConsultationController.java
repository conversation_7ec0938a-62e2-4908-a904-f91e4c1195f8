package com.tcm.doctor.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.R;
import com.tcm.doctor.dto.ConsultationQuery;
import com.tcm.doctor.dto.OnlineConsultationDTO;
import com.tcm.doctor.service.IOnlineConsultationService;
import com.tcm.doctor.vo.OnlineConsultationVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 在线问诊控制器
 */
@RestController
@RequestMapping("/doctor/consultation")
public class OnlineConsultationController {
    
    @Autowired
    private IOnlineConsultationService consultationService;
    
    /**
     * 查询问诊列表
     */
    @GetMapping("/list")
    public R<IPage<OnlineConsultationVO>> list(ConsultationQuery query) {
        IPage<OnlineConsultationVO> page = consultationService.listConsultations(query);
        return R.ok(page);
    }
    
    /**
     * 获取问诊详情
     */
    @GetMapping("/{consultationId}")
    public R<OnlineConsultationVO> getInfo(@PathVariable Long consultationId) {
        OnlineConsultationVO consultation = consultationService.getConsultationById(consultationId);
        return R.ok(consultation);
    }
    
    /**
     * 根据患者ID查询问诊列表
     */
    @GetMapping("/patient/{patientId}")
    public R<List<OnlineConsultationVO>> getConsultationsByPatient(@PathVariable Long patientId) {
        List<OnlineConsultationVO> consultations = consultationService.getConsultationsByPatientId(patientId);
        return R.ok(consultations);
    }
    
    /**
     * 根据医生ID查询问诊列表
     */
    @GetMapping("/doctor/{doctorId}")
    public R<List<OnlineConsultationVO>> getConsultationsByDoctor(
            @PathVariable Long doctorId,
            @RequestParam(required = false) Integer status) {
        List<OnlineConsultationVO> consultations = consultationService.getConsultationsByDoctorId(doctorId, status);
        return R.ok(consultations);
    }
    
    /**
     * 新增问诊
     */
    @PostMapping
    public R<Boolean> add(@RequestBody @Valid OnlineConsultationDTO consultationDTO) {
        boolean result = consultationService.addConsultation(consultationDTO);
        return R.ok(result);
    }
    
    /**
     * 更新问诊状态
     */
    @PutMapping("/status/{consultationId}/{status}")
    public R<Boolean> updateStatus(@PathVariable Long consultationId, @PathVariable Integer status) {
        boolean result = consultationService.updateConsultationStatus(consultationId, status);
        return R.ok(result);
    }
    
    /**
     * 接诊
     */
    @PutMapping("/accept/{consultationId}/{doctorId}")
    public R<Boolean> accept(@PathVariable Long consultationId, @PathVariable Long doctorId) {
        boolean result = consultationService.acceptConsultation(consultationId, doctorId);
        return R.ok(result);
    }
    
    /**
     * 完成问诊
     */
    @PutMapping("/finish/{consultationId}")
    public R<Boolean> finish(
            @PathVariable Long consultationId,
            @RequestParam String doctorAdvice,
            @RequestParam String diagnosis,
            @RequestParam(required = false) Long prescriptionId) {
        boolean result = consultationService.finishConsultation(consultationId, doctorAdvice, diagnosis, prescriptionId);
        return R.ok(result);
    }
    
    /**
     * 评价问诊
     */
    @PutMapping("/evaluate/{consultationId}")
    public R<Boolean> evaluate(
            @PathVariable Long consultationId,
            @RequestParam Integer score,
            @RequestParam String evaluation) {
        boolean result = consultationService.evaluateConsultation(consultationId, score, evaluation);
        return R.ok(result);
    }
    
    /**
     * 更新支付状态
     */
    @PutMapping("/pay/{consultationId}/{payStatus}")
    public R<Boolean> updatePayStatus(@PathVariable Long consultationId, @PathVariable Integer payStatus) {
        boolean result = consultationService.updatePayStatus(consultationId, payStatus);
        return R.ok(result);
    }
    
    /**
     * 获取医生待接诊数量
     */
    @GetMapping("/count/pending/{doctorId}")
    public R<Integer> countPendingConsultations(@PathVariable Long doctorId) {
        int count = consultationService.countPendingConsultations(doctorId);
        return R.ok(count);
    }
    
    /**
     * 获取医生进行中的问诊数量
     */
    @GetMapping("/count/ongoing/{doctorId}")
    public R<Integer> countOngoingConsultations(@PathVariable Long doctorId) {
        int count = consultationService.countOngoingConsultations(doctorId);
        return R.ok(count);
    }
} 