<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.patient.mapper.PatientMapper">

    <resultMap id="PatientResult" type="com.tcm.patient.domain.Patient">
        <id property="patientId" column="patient_id"/>
        <result property="userId" column="user_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientCode" column="patient_code"/>
        <result property="gender" column="gender"/>
        <result property="age" column="age"/>
        <result property="birthday" column="birthday"/>
        <result property="idCard" column="id_card"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="bloodType" column="blood_type"/>
        <result property="allergies" column="allergies"/>
        <result property="familyHistory" column="family_history"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="address" column="address"/>
        <result property="emergencyContact" column="emergency_contact"/>
        <result property="emergencyPhone" column="emergency_phone"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <resultMap id="PatientVOResult" type="com.tcm.patient.vo.PatientVO">
        <id property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientCode" column="patient_code"/>
        <result property="gender" column="gender"/>
        <result property="genderName" column="gender_name"/>
        <result property="age" column="age"/>
        <result property="birthday" column="birthday"/>
        <result property="idCard" column="id_card"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="height" column="height"/>
        <result property="weight" column="weight"/>
        <result property="bloodType" column="blood_type"/>
        <result property="allergies" column="allergies"/>
        <result property="familyHistory" column="family_history"/>
        <result property="medicalHistory" column="medical_history"/>
        <result property="address" column="address"/>
        <result property="emergencyContact" column="emergency_contact"/>
        <result property="emergencyPhone" column="emergency_phone"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <sql id="selectPatientVo">
        SELECT p.patient_id,
               p.user_id,
               p.patient_name,
               p.patient_code,
               p.gender,
               CASE p.gender
                   WHEN 0 THEN '男'
                   WHEN 1 THEN '女'
                   ELSE '未知'
               END AS gender_name,
               p.age,
               p.birthday,
               p.id_card,
               p.phone,
               p.email,
               p.height,
               p.weight,
               p.blood_type,
               p.allergies,
               p.family_history,
               p.medical_history,
               p.address,
               p.emergency_contact,
               p.emergency_phone,
               p.status,
               p.create_time,
               p.remark
        FROM tcm_patient p
    </sql>
    
    <select id="selectPatientList" resultMap="PatientVOResult">
        <include refid="selectPatientVo"/>
        <where>
            p.del_flag = 0
            <if test="query.patientName != null and query.patientName != ''">
                AND p.patient_name like concat('%', #{query.patientName}, '%')
            </if>
            <if test="query.patientCode != null and query.patientCode != ''">
                AND p.patient_code = #{query.patientCode}
            </if>
            <if test="query.gender != null">
                AND p.gender = #{query.gender}
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND p.phone like concat('%', #{query.phone}, '%')
            </if>
            <if test="query.idCard != null and query.idCard != ''">
                AND p.id_card like concat('%', #{query.idCard}, '%')
            </if>
            <if test="query.ageBegin != null">
                AND p.age >= #{query.ageBegin}
            </if>
            <if test="query.ageEnd != null">
                AND p.age &lt;= #{query.ageEnd}
            </if>
            <if test="query.bloodType != null and query.bloodType != ''">
                AND p.blood_type = #{query.bloodType}
            </if>
            <if test="query.status != null">
                AND p.status = #{query.status}
            </if>
        </where>
        ORDER BY p.create_time DESC
    </select>
    
    <select id="selectPatientById" parameterType="Long" resultMap="PatientVOResult">
        <include refid="selectPatientVo"/>
        WHERE p.patient_id = #{patientId} AND p.del_flag = 0
    </select>
    
    <select id="selectPatientByUserId" parameterType="Long" resultMap="PatientVOResult">
        <include refid="selectPatientVo"/>
        WHERE p.user_id = #{userId} AND p.del_flag = 0
    </select>
    
    <select id="selectPatientByIdCard" parameterType="String" resultMap="PatientVOResult">
        <include refid="selectPatientVo"/>
        WHERE p.id_card = #{idCard} AND p.del_flag = 0
    </select>
    
</mapper> 