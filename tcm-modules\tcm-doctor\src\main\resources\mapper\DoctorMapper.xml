<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.doctor.mapper.DoctorMapper">

    <resultMap id="DoctorResult" type="com.tcm.doctor.domain.Doctor">
        <id property="doctorId" column="doctor_id"/>
        <result property="userId" column="user_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="doctorCode" column="doctor_code"/>
        <result property="title" column="title"/>
        <result property="specialty" column="specialty"/>
        <result property="introduction" column="introduction"/>
        <result property="licenseNo" column="license_no"/>
        <result property="hospital" column="hospital"/>
        <result property="department" column="department"/>
        <result property="workYears" column="work_years"/>
        <result property="avatar" column="avatar"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <resultMap id="DoctorVOResult" type="com.tcm.doctor.vo.DoctorVO">
        <id property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="doctorCode" column="doctor_code"/>
        <result property="title" column="title"/>
        <result property="titleName" column="title_name"/>
        <result property="specialty" column="specialty"/>
        <result property="introduction" column="introduction"/>
        <result property="hospital" column="hospital"/>
        <result property="department" column="department"/>
        <result property="workYears" column="work_years"/>
        <result property="avatar" column="avatar"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="createTime" column="create_time"/>
    </resultMap>
    
    <sql id="selectDoctorVo">
        SELECT d.doctor_id,
               d.user_id,
               d.doctor_name,
               d.doctor_code,
               d.title,
               CASE d.title
                   WHEN 1 THEN '主任医师'
                   WHEN 2 THEN '副主任医师'
                   WHEN 3 THEN '主治医师'
                   WHEN 4 THEN '住院医师'
                   ELSE '其他'
               END AS title_name,
               d.specialty,
               d.introduction,
               d.license_no,
               d.hospital,
               d.department,
               d.work_years,
               d.avatar,
               d.phone,
               d.email,
               d.status,
               d.create_time,
               d.remark
        FROM tcm_doctor d
    </sql>
    
    <select id="selectDoctorList" parameterType="com.tcm.doctor.dto.DoctorQuery" resultMap="DoctorVOResult">
        <include refid="selectDoctorVo"/>
        <where>
            d.del_flag = 0
            <if test="query.doctorName != null and query.doctorName != ''">
                AND d.doctor_name like concat('%', #{query.doctorName}, '%')
            </if>
            <if test="query.doctorCode != null and query.doctorCode != ''">
                AND d.doctor_code = #{query.doctorCode}
            </if>
            <if test="query.title != null">
                AND d.title = #{query.title}
            </if>
            <if test="query.specialty != null and query.specialty != ''">
                AND d.specialty like concat('%', #{query.specialty}, '%')
            </if>
            <if test="query.hospital != null and query.hospital != ''">
                AND d.hospital like concat('%', #{query.hospital}, '%')
            </if>
            <if test="query.department != null and query.department != ''">
                AND d.department = #{query.department}
            </if>
            <if test="query.status != null">
                AND d.status = #{query.status}
            </if>
        </where>
        ORDER BY d.create_time DESC
    </select>
    
    <select id="selectDoctorById" parameterType="Long" resultMap="DoctorVOResult">
        <include refid="selectDoctorVo"/>
        WHERE d.doctor_id = #{doctorId} AND d.del_flag = 0
    </select>
    
    <select id="selectDoctorByDepartment" parameterType="String" resultMap="DoctorVOResult">
        <include refid="selectDoctorVo"/>
        WHERE d.department = #{department} AND d.status = 0 AND d.del_flag = 0
        ORDER BY d.title ASC, d.work_years DESC
    </select>
    
    <select id="selectDoctorBySpecialty" parameterType="String" resultMap="DoctorVOResult">
        <include refid="selectDoctorVo"/>
        WHERE d.specialty like concat('%', #{specialty}, '%') AND d.status = 0 AND d.del_flag = 0
        ORDER BY d.title ASC, d.work_years DESC
    </select>
    
</mapper> 