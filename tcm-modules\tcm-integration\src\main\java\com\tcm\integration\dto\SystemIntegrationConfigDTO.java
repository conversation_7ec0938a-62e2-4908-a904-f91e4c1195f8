package com.tcm.integration.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 系统集成配置DTO
 *
 * <AUTHOR>
 */
@Data
public class SystemIntegrationConfigDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 系统名称
     */
    @NotBlank(message = "系统名称不能为空")
    private String systemName;

    /**
     * 系统编码
     */
    @NotBlank(message = "系统编码不能为空")
    private String systemCode;

    /**
     * 系统类型（HIS、LIS、PACS等）
     */
    @NotBlank(message = "系统类型不能为空")
    private String systemType;

    /**
     * 接口地址
     */
    @NotBlank(message = "接口地址不能为空")
    private String apiUrl;

    /**
     * 认证类型（None、Basic、OAuth、Token等）
     */
    @NotBlank(message = "认证类型不能为空")
    private String authType;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 令牌
     */
    private String token;

    /**
     * 连接超时时间(毫秒)
     */
    private Integer connectTimeout;

    /**
     * 读取超时时间(毫秒)
     */
    private Integer readTimeout;

    /**
     * 系统描述
     */
    private String description;

    /**
     * 状态（0-禁用，1-启用）
     */
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 备注
     */
    private String remark;
}