package com.tcm.gateway.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

/**
 * 通用配置类，用于加载通用配置文件中的属性
 * 
 * <AUTHOR>
 */
@Configuration
@PropertySource(value = "classpath:/META-INF/tcm-common.yml", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "tcm.service")
public class CommonConfig {

    private Redis redis;

    public static class Redis {
        private String host;
        private int port;
        private String password;
        private int database;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public int getDatabase() {
            return database;
        }

        public void setDatabase(int database) {
            this.database = database;
        }
    }

    public Redis getRedis() {
        return redis;
    }

    public void setRedis(Redis redis) {
        this.redis = redis;
    }
}