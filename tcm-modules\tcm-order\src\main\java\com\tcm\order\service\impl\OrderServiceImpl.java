package com.tcm.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.order.entity.Order;
import com.tcm.order.entity.OrderItem;
import com.tcm.order.mapper.OrderItemMapper;
import com.tcm.order.mapper.OrderMapper;
import com.tcm.order.service.IOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单服务实现类
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements IOrderService {

    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private OrderItemMapper orderItemMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createOrder(Order order) {
        // 设置初始订单状态
        if (order.getOrderStatus() == null) {
            order.setOrderStatus(0); // 待支付
        }
        if (order.getPaymentStatus() == null) {
            order.setPaymentStatus(0); // 未支付
        }
        
        // 生成订单编号（实际项目中可能需要更复杂的生成规则）
        String orderNo = "TCM" + System.currentTimeMillis();
        order.setOrderNo(orderNo);
        
        // 设置创建时间
        order.setCreateTime(LocalDateTime.now());
        
        // 保存订单
        this.save(order);
        
        return order.getOrderId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(Long orderId, Integer status) {
        Order order = new Order();
        order.setOrderId(orderId);
        order.setOrderStatus(status);
        order.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payOrder(Long orderId, Integer paymentMethod, String transactionNo) {
        // 查询订单
        Order order = this.getById(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            return false;
        }
        
        // 检查订单状态
        if (order.getOrderStatus() != 0 || order.getPaymentStatus() != 0) {
            log.error("订单状态不正确，无法支付，orderId: {}, status: {}, paymentStatus: {}", 
                    orderId, order.getOrderStatus(), order.getPaymentStatus());
            return false;
        }
        
        // 更新订单支付信息
        LocalDateTime paymentTime = LocalDateTime.now();
        int rows = orderMapper.updateOrderPaymentStatus(orderId, 1, transactionNo, paymentTime);
        
        // 更新订单状态
        if (rows > 0) {
            Order updateOrder = new Order();
            updateOrder.setOrderId(orderId);
            updateOrder.setOrderStatus(1); // 已支付
            updateOrder.setPaymentMethod(paymentMethod);
            updateOrder.setPaymentTime(paymentTime);
            updateOrder.setUpdateTime(paymentTime);
            
            return this.updateById(updateOrder);
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(Long orderId, String cancelReason) {
        // 查询订单
        Order order = this.getById(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            return false;
        }
        
        // 检查订单状态
        if (order.getOrderStatus() > 2) {
            log.error("订单状态不正确，无法取消，orderId: {}, status: {}", orderId, order.getOrderStatus());
            return false;
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus(5); // 已取消
        updateOrder.setCancelReason(cancelReason);
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(updateOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundOrder(Long orderId, String refundAmount, String refundReason) {
        // 查询订单
        Order order = this.getById(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            return false;
        }
        
        // 检查订单状态
        if (order.getOrderStatus() != 1 && order.getOrderStatus() != 2 && order.getOrderStatus() != 3) {
            log.error("订单状态不正确，无法退款，orderId: {}, status: {}", orderId, order.getOrderStatus());
            return false;
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus(6); // 已退款
        updateOrder.setPaymentStatus(2); // 已退款
        updateOrder.setRefundReason(refundReason);
        updateOrder.setRefundTime(LocalDateTime.now());
        // 这里实际项目中需要处理退款金额的转换
        // updateOrder.setRefundAmount(new BigDecimal(refundAmount));
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(updateOrder);
    }

    @Override
    public IPage<Order> getOrderPage(Page<Order> page, Integer orderType, Integer status, String keyword) {
        return orderMapper.selectOrderPage(page, orderType, status, keyword);
    }

    @Override
    public List<Map<String, Object>> getOrderStatistics(LocalDateTime startTime, LocalDateTime endTime) {
        return orderMapper.selectOrderStatistics(startTime, endTime);
    }

    @Override
    public List<Order> getPatientOrders(Long patientId, Integer status) {
        return orderMapper.selectPatientOrders(patientId, status);
    }

    @Override
    public List<Order> getDoctorOrders(Long doctorId, Integer status) {
        return orderMapper.selectDoctorOrders(doctorId, status);
    }

    @Override
    public Order getOrderDetail(Long orderId) {
        // 查询订单基本信息
        Order order = this.getById(orderId);
        if (order == null) {
            return null;
        }
        
        // 查询订单明细
        List<OrderItem> items = orderItemMapper.selectByOrderId(orderId);
        // 实际项目中可能需要将items放入order对象中
        
        return order;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deliveryOrder(Long orderId, String expressCompany, String expressNo) {
        // 查询订单
        Order order = this.getById(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            return false;
        }
        
        // 检查订单状态
        if (order.getOrderStatus() != 2) {
            log.error("订单状态不正确，无法发货，orderId: {}, status: {}", orderId, order.getOrderStatus());
            return false;
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus(3); // 待取药/待收货
        updateOrder.setExpressCompany(expressCompany);
        updateOrder.setExpressNo(expressNo);
        updateOrder.setDeliveryTime(LocalDateTime.now());
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(updateOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean receiveOrder(Long orderId) {
        // 查询订单
        Order order = this.getById(orderId);
        if (order == null) {
            log.error("订单不存在，orderId: {}", orderId);
            return false;
        }
        
        // 检查订单状态
        if (order.getOrderStatus() != 3) {
            log.error("订单状态不正确，无法确认收货，orderId: {}, status: {}", orderId, order.getOrderStatus());
            return false;
        }
        
        // 更新订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus(4); // 已完成
        updateOrder.setReceiveTime(LocalDateTime.now());
        updateOrder.setUpdateTime(LocalDateTime.now());
        
        return this.updateById(updateOrder);
    }
}
