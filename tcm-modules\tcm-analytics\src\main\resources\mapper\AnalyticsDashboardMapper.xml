<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.analytics.mapper.AnalyticsDashboardMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.analytics.entity.AnalyticsDashboard">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="layout" property="layout" />
        <result column="data_source" property="dataSource" />
        <result column="query_config" property="queryConfig" />
        <result column="refresh_interval" property="refreshInterval" />
        <result column="auto_refresh" property="autoRefresh" />
        <result column="scope" property="scope" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="order_num" property="orderNum" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
    </resultMap>
    
    <!-- 自定义查询映射结果，包含JSON类型处理 -->
    <resultMap id="DashboardResultMap" type="com.tcm.analytics.entity.AnalyticsDashboard" extends="BaseResultMap">
        <result column="components_config" property="componentsConfig" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, layout, data_source, query_config, components_config, refresh_interval, auto_refresh, scope, create_time, update_time, creator, order_num, remark, del_flag
    </sql>

    <!-- 根据ID查询仪表盘 -->
    <select id="selectById" resultMap="DashboardResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM analytics_dashboard
        WHERE id = #{id} AND del_flag = 0
    </select>
</mapper> 