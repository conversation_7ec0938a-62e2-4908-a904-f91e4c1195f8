package com.tcm.analytics.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.analytics.entity.AnalyticsTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 分析任务Mapper接口
 */
@Mapper
public interface AnalyticsTaskMapper extends BaseMapper<AnalyticsTask> {

    /**
     * 查询待执行的任务
     * 
     * @param limit 限制数量
     * @return 任务列表
     */
    @Select("SELECT * FROM analytics_task WHERE task_status = 0 AND del_flag = 0 ORDER BY create_time LIMIT #{limit}")
    List<AnalyticsTask> selectPendingTasks(@Param("limit") int limit);
    
    /**
     * 更新任务状态
     * 
     * @param id 任务ID
     * @param status 状态
     * @return 更新行数
     */
    @Update("UPDATE analytics_task SET task_status = #{status} WHERE id = #{id}")
    int updateTaskStatus(@Param("id") Long id, @Param("status") Integer status);
    
    /**
     * 按类型统计任务数量
     * 
     * @param taskType 任务类型
     * @return 任务数量
     */
    @Select("SELECT COUNT(*) FROM analytics_task WHERE task_type = #{taskType} AND del_flag = 0")
    int countByTaskType(@Param("taskType") Integer taskType);
    
    /**
     * 根据创建者查询任务
     * 
     * @param creator 创建者
     * @return 任务列表
     */
    @Select("SELECT * FROM analytics_task WHERE creator = #{creator} AND del_flag = 0 ORDER BY create_time DESC")
    List<AnalyticsTask> selectByCreator(@Param("creator") String creator);
} 