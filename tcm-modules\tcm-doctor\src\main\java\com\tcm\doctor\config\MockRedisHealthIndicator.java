package com.tcm.doctor.config;

import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

/**
 * 自定义Redis健康指示器，用于模拟模式
 * 当系统配置为使用模拟Redis时（tcm.mock.redis=true），该健康指示器将替代默认的Redis健康指示器
 * 使用与Spring Boot相同的bean名称，确保能够替代原有健康检查器
 * 始终返回UP状态，避免因Redis连接问题导致的健康检查警告
 */
@Component("redisHealthIndicator")
@Primary
@ConditionalOnProperty(name = "tcm.mock.redis", havingValue = "true")
public class MockRedisHealthIndicator extends AbstractHealthIndicator {

    /**
     * 实现健康检查逻辑
     * 在模拟模式下，始终返回健康状态，不会尝试连接真实的Redis服务器
     */
    @Override
    protected void doHealthCheck(Health.Builder builder) {
        // 在模拟模式下，始终返回健康状态
        builder.up()
               .withDetail("mode", "mocked")
               .withDetail("version", "模拟Redis服务")
               .withDetail("description", "Redis模拟模式已激活，未连接实际Redis服务器");
    }
}
