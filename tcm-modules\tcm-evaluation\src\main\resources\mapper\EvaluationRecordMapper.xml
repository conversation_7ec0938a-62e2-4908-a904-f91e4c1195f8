<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.evaluation.mapper.EvaluationRecordMapper">
    
    <resultMap id="evaluationRecordResultMap" type="com.tcm.evaluation.domain.EvaluationRecord">
        <id property="id" column="id"/>
        <result property="formId" column="form_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="evaluationType" column="evaluation_type"/>
        <result property="status" column="status"/>
        <result property="evaluationResult" column="evaluation_result" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="userAnswers" column="user_answers" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="score" column="score"/>
        <result property="summary" column="summary"/>
        <result property="recommendations" column="recommendations"/>
        <result property="startTime" column="start_time"/>
        <result property="completeTime" column="complete_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="version" column="version"/>
    </resultMap>
    
    <sql id="selectEvaluationRecordVo">
        select id, form_id, patient_id, doctor_id, evaluation_type, status, evaluation_result, 
        user_answers, score, summary, recommendations, start_time, complete_time, 
        create_by, create_time, update_by, update_time, del_flag, version
        from eval_record
    </sql>
    
    <select id="selectByPatientId" resultMap="evaluationRecordResultMap">
        <include refid="selectEvaluationRecordVo"/>
        where del_flag = 0 
        and patient_id = #{patientId}
        order by id desc
    </select>
    
    <select id="selectByDoctorId" resultMap="evaluationRecordResultMap">
        <include refid="selectEvaluationRecordVo"/>
        where del_flag = 0 
        and doctor_id = #{doctorId}
        order by id desc
    </select>
    
    <select id="selectByFormId" resultMap="evaluationRecordResultMap">
        <include refid="selectEvaluationRecordVo"/>
        where del_flag = 0 
        and form_id = #{formId}
        order by id desc
    </select>
    
    <select id="selectByTypeAndStatus" resultMap="evaluationRecordResultMap">
        <include refid="selectEvaluationRecordVo"/>
        where del_flag = 0 
        and evaluation_type = #{evaluationType}
        and status = #{status}
        order by id desc
    </select>
    
</mapper> 