package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.ServiceException;
import com.tcm.common.core.utils.DateUtils;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.common.core.utils.file.FileUtils;
import com.tcm.diagnosis.domain.TongueAnalysis;
import com.tcm.diagnosis.domain.TongueImage;
import com.tcm.diagnosis.mapper.TongueAnalysisMapper;
import com.tcm.diagnosis.mapper.TongueImageMapper;
import com.tcm.diagnosis.service.ITongueAnalysisService;
import com.tcm.diagnosis.vo.TongueAnalysisVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 舌象分析服务实现类
 */
@Slf4j
@Service
public class TongueAnalysisServiceImpl extends ServiceImpl<TongueAnalysisMapper, TongueAnalysis>
        implements ITongueAnalysisService {

    @Value("${tcm.upload.path:/tcm/upload}")
    private String uploadPath;

    @Value("${tcm.ai.model.tongue.version:v1.0}")
    private String modelVersion;

    @Autowired
    private TongueAnalysisMapper tongueAnalysisMapper;

    @Autowired
    private TongueImageMapper tongueImageMapper;

    /**
     * 异步任务执行器
     */
    private final Executor executor = Executors.newFixedThreadPool(5);

    /**
     * 分析舌象图像
     *
     * @param imageId 图像ID
     * @return 分析结果ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long analyzeTongueImage(Long imageId) {
        // 查询舌象图像信息
        TongueImage tongueImage = tongueImageMapper.selectById(imageId);
        if (tongueImage == null) {
            throw new ServiceException("舌象图像不存在");
        }

        // 检查是否已分析
        LambdaQueryWrapper<TongueAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TongueAnalysis::getImageId, imageId);
        TongueAnalysis existAnalysis = getOne(queryWrapper);
        if (existAnalysis != null) {
            return existAnalysis.getAnalysisId();
        }

        // 获取图像文件路径
        String imagePath = uploadPath + "/" + tongueImage.getImagePath();
        if (!FileUtils.exist(imagePath)) {
            throw new ServiceException("图像文件不存在");
        }

        // 调用舌象分析AI模型
        TongueAnalysisResult analysisResult = analyzeWithAIModel(new File(imagePath));

        // 保存分析结果
        TongueAnalysis tongueAnalysis = new TongueAnalysis();
        tongueAnalysis.setImageId(imageId);
        tongueAnalysis.setPatientId(tongueImage.getPatientId());
        tongueAnalysis.setDoctorId(tongueImage.getDoctorId());
        tongueAnalysis.setTongueColor(analysisResult.getTongueColor());
        tongueAnalysis.setTongueShape(analysisResult.getTongueShape());
        tongueAnalysis.setCoatingColor(analysisResult.getCoatingColor());
        tongueAnalysis.setCoatingThickness(analysisResult.getCoatingThickness());
        tongueAnalysis.setTongueCharacteristics(analysisResult.getTongueCharacteristics());
        tongueAnalysis.setAnalysisResult(analysisResult.getResultJson());
        tongueAnalysis.setClinicalImplications(analysisResult.getClinicalImplications());
        tongueAnalysis.setAnalysisTime(new Date());
        tongueAnalysis.setModelVersion(modelVersion);
        tongueAnalysis.setConfidence(analysisResult.getConfidence());
        tongueAnalysis.setCreateBy(String.valueOf(tongueImage.getDoctorId()));

        // 保存到数据库
        save(tongueAnalysis);

        // 更新图像状态为已分析
        tongueImage.setStatus(1);
        tongueImageMapper.updateById(tongueImage);

        return tongueAnalysis.getAnalysisId();
    }

    /**
     * 批量分析舌象图像
     *
     * @param imageIds 图像ID列表
     * @return 是否成功
     */
    @Override
    public boolean analyzeTongueImageBatch(List<Long> imageIds) {
        if (imageIds == null || imageIds.isEmpty()) {
            return false;
        }

        // 使用CompletableFuture进行异步处理
        List<CompletableFuture<Long>> futures = imageIds.stream()
                .map(imageId -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return analyzeTongueImage(imageId);
                    } catch (Exception e) {
                        log.error("批量分析舌象图像失败，图像ID: {}, 错误: {}", imageId, e.getMessage());
                        return null;
                    }
                }, executor))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return true;
    }

    /**
     * 获取舌象分析结果
     *
     * @param analysisId 分析结果ID
     * @return 舌象分析结果视图对象
     */
    @Override
    public TongueAnalysisVO getTongueAnalysis(Long analysisId) {
        TongueAnalysis tongueAnalysis = getById(analysisId);
        if (tongueAnalysis == null) {
            throw new ServiceException("分析结果不存在");
        }
        return convertToVO(tongueAnalysis);
    }

    /**
     * 根据图像ID获取舌象分析结果
     *
     * @param imageId 图像ID
     * @return 舌象分析结果视图对象
     */
    @Override
    public TongueAnalysisVO getTongueAnalysisByImageId(Long imageId) {
        TongueAnalysis tongueAnalysis = tongueAnalysisMapper.selectTongueAnalysisByImageId(imageId);
        if (tongueAnalysis == null) {
            throw new ServiceException("该图像尚未进行分析");
        }
        return convertToVO(tongueAnalysis);
    }

    /**
     * 获取诊断记录关联的舌象分析结果列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 舌象分析结果视图对象列表
     */
    @Override
    public List<TongueAnalysisVO> getTongueAnalysisListByDiagnosisId(Long diagnosisId) {
        List<TongueAnalysis> analysisList = tongueAnalysisMapper.selectTongueAnalysisByDiagnosisId(diagnosisId);
        return convertToVOList(analysisList);
    }

    /**
     * 获取患者的舌象分析结果列表
     *
     * @param patientId 患者ID
     * @return 舌象分析结果视图对象列表
     */
    @Override
    public List<TongueAnalysisVO> getTongueAnalysisListByPatientId(Long patientId) {
        List<TongueAnalysis> analysisList = tongueAnalysisMapper.selectTongueAnalysisByPatientId(patientId);
        return convertToVOList(analysisList);
    }

    /**
     * 删除舌象分析结果
     *
     * @param analysisId 分析结果ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTongueAnalysis(Long analysisId) {
        TongueAnalysis tongueAnalysis = getById(analysisId);
        if (tongueAnalysis == null) {
            throw new ServiceException("分析结果不存在");
        }

        // 更新图像状态为未分析
        Long imageId = tongueAnalysis.getImageId();
        TongueImage tongueImage = tongueImageMapper.selectById(imageId);
        if (tongueImage != null) {
            tongueImage.setStatus(0);
            tongueImageMapper.updateById(tongueImage);
        }

        // 删除分析结果
        return removeById(analysisId);
    }

    /**
     * 导出舌象分析结果报告
     *
     * @param analysisId 分析结果ID
     * @return 报告文件路径
     */
    @Override
    public String exportTongueAnalysisReport(Long analysisId) {
        TongueAnalysis tongueAnalysis = getById(analysisId);
        if (tongueAnalysis == null) {
            throw new ServiceException("分析结果不存在");
        }

        // 获取舌象图像信息
        TongueImage tongueImage = tongueImageMapper.selectById(tongueAnalysis.getImageId());
        if (tongueImage == null) {
            throw new ServiceException("舌象图像不存在");
        }

        // TODO: 实现报告生成逻辑
        // 1. 生成包含图像和分析结果的报告文件
        // 2. 返回报告文件的下载路径

        return "reports/tongue/" + DateUtils.datePath() + "/" + analysisId + ".pdf";
    }

    /**
     * 将实体转换为视图对象
     *
     * @param tongueAnalysis 舌象分析结果实体
     * @return 舌象分析结果视图对象
     */
    private TongueAnalysisVO convertToVO(TongueAnalysis tongueAnalysis) {
        if (tongueAnalysis == null) {
            return null;
        }

        TongueAnalysisVO vo = new TongueAnalysisVO();
        vo.setId(tongueAnalysis.getAnalysisId());
        vo.setImageId(tongueAnalysis.getImageId());
        vo.setPatientId(tongueAnalysis.getPatientId());
        vo.setTongueColor(tongueAnalysis.getTongueColor());
        vo.setTongueColorDesc(describeTongueColor(tongueAnalysis.getTongueColor()));
        vo.setTongueShape(tongueAnalysis.getTongueShape());
        vo.setTongueShapeDesc(describeTongueShape(tongueAnalysis.getTongueShape()));
        vo.setCoatingColor(tongueAnalysis.getCoatingColor());
        vo.setCoatingColorDesc(describeCoatingColor(tongueAnalysis.getCoatingColor()));
        vo.setCoatingThickness(tongueAnalysis.getCoatingThickness());
        vo.setCoatingThicknessDesc(describeCoatingThickness(tongueAnalysis.getCoatingThickness()));
        vo.setTongueCharacteristics(tongueAnalysis.getTongueCharacteristics());
        vo.setClinicalImplications(tongueAnalysis.getClinicalImplications());
        vo.setAnalysisTime(tongueAnalysis.getAnalysisTime());
        vo.setConfidence(tongueAnalysis.getConfidence());

        // 获取缩略图
        TongueImage tongueImage = tongueImageMapper.selectById(tongueAnalysis.getImageId());
        if (tongueImage != null) {
            vo.setThumbnail(tongueImage.getThumbnailPath());
        }

        // TODO: 通过远程调用获取患者姓名
        // vo.setPatientName(remotePatientService.getPatientNameById(tongueAnalysis.getPatientId()));

        return vo;
    }

    /**
     * 将实体列表转换为视图对象列表
     *
     * @param tongueAnalysisList 舌象分析结果实体列表
     * @return 舌象分析结果视图对象列表
     */
    private List<TongueAnalysisVO> convertToVOList(List<TongueAnalysis> tongueAnalysisList) {
        if (tongueAnalysisList == null || tongueAnalysisList.isEmpty()) {
            return new ArrayList<>();
        }

        return tongueAnalysisList.stream()
                .filter(Objects::nonNull)
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    /**
     * 调用AI模型进行舌象分析
     *
     * @param imageFile 图像文件
     * @return 分析结果
     */
    private TongueAnalysisResult analyzeWithAIModel(File imageFile) {
        // TODO: 实现AI模型调用逻辑
        // 此处为模拟实现，实际项目中应调用AI服务API或加载AI模型进行处理

        TongueAnalysisResult result = new TongueAnalysisResult();
        result.setTongueColor("淡红");
        result.setTongueShape("正常");
        result.setCoatingColor("薄白");
        result.setCoatingThickness("薄");
        result.setTongueCharacteristics("舌体微胖");
        result.setClinicalImplications("脾胃虚弱");
        result.setResultJson(
                "{\"tongueColor\":\"淡红\",\"tongueShape\":\"正常\",\"coatingColor\":\"薄白\",\"coatingThickness\":\"薄\",\"features\":[\"舌体微胖\"],\"implications\":\"脾胃虚弱\"}");
        result.setConfidence(85);

        return result;
    }

    /**
     * 描述舌色
     *
     * @param tongueColor 舌色代码
     * @return 舌色描述
     */
    private String describeTongueColor(String tongueColor) {
        if (StringUtils.isEmpty(tongueColor)) {
            return "";
        }

        // 舌色描述映射
        switch (tongueColor) {
            case "淡白":
                return "舌色淡白，多为气血两虚";
            case "淡红":
                return "舌色淡红，为正常舌色";
            case "红":
                return "舌色红，多为热证";
            case "绛":
                return "舌色深红，多为热入营血或热盛";
            case "紫":
                return "舌色紫暗，多为血瘀";
            case "青":
                return "舌色青黯，多为寒凝血瘀";
            default:
                return tongueColor;
        }
    }

    /**
     * 描述舌形
     *
     * @param tongueShape 舌形代码
     * @return 舌形描述
     */
    private String describeTongueShape(String tongueShape) {
        if (StringUtils.isEmpty(tongueShape)) {
            return "";
        }

        // 舌形描述映射
        switch (tongueShape) {
            case "正常":
                return "舌形正常，柔软灵活";
            case "胖大":
                return "舌体胖大，多为湿盛或脾虚";
            case "瘦薄":
                return "舌体瘦薄，多为气血不足";
            case "点刺":
                return "舌面有红点突起，多为热毒";
            case "裂纹":
                return "舌面有裂纹，多为津液不足";
            case "齿痕":
                return "舌边有齿痕，多为脾虚湿盛";
            default:
                return tongueShape;
        }
    }

    /**
     * 描述舌苔颜色
     *
     * @param coatingColor 舌苔颜色代码
     * @return 舌苔颜色描述
     */
    private String describeCoatingColor(String coatingColor) {
        if (StringUtils.isEmpty(coatingColor)) {
            return "";
        }

        // 舌苔颜色描述映射
        switch (coatingColor) {
            case "薄白":
                return "舌苔薄白，多为正常";
            case "厚白":
                return "舌苔厚白，多为寒证或湿盛";
            case "黄":
                return "舌苔黄，多为热证";
            case "灰黑":
                return "舌苔灰黑，多为寒盛或热极";
            case "腻":
                return "舌苔腻，多为湿浊内蕴";
            default:
                return coatingColor;
        }
    }

    /**
     * 描述舌苔厚度
     *
     * @param coatingThickness 舌苔厚度代码
     * @return 舌苔厚度描述
     */
    private String describeCoatingThickness(String coatingThickness) {
        if (StringUtils.isEmpty(coatingThickness)) {
            return "";
        }

        // 舌苔厚度描述映射
        switch (coatingThickness) {
            case "无苔":
                return "舌无苔，多为胃肠热盛或阴液严重不足";
            case "薄":
                return "舌苔薄，多为正常或病情轻";
            case "厚":
                return "舌苔厚，多为病情重";
            case "腻":
                return "舌苔厚腻，多为湿浊内蕴";
            case "剥脱":
                return "舌苔剥脱，多为胃阴不足";
            default:
                return coatingThickness;
        }
    }

    /**
     * 舌象分析结果内部类
     */
    private static class TongueAnalysisResult {
        private String tongueColor;
        private String tongueShape;
        private String coatingColor;
        private String coatingThickness;
        private String tongueCharacteristics;
        private String clinicalImplications;
        private String resultJson;
        private Integer confidence;

        public String getTongueColor() {
            return tongueColor;
        }

        public void setTongueColor(String tongueColor) {
            this.tongueColor = tongueColor;
        }

        public String getTongueShape() {
            return tongueShape;
        }

        public void setTongueShape(String tongueShape) {
            this.tongueShape = tongueShape;
        }

        public String getCoatingColor() {
            return coatingColor;
        }

        public void setCoatingColor(String coatingColor) {
            this.coatingColor = coatingColor;
        }

        public String getCoatingThickness() {
            return coatingThickness;
        }

        public void setCoatingThickness(String coatingThickness) {
            this.coatingThickness = coatingThickness;
        }

        public String getTongueCharacteristics() {
            return tongueCharacteristics;
        }

        public void setTongueCharacteristics(String tongueCharacteristics) {
            this.tongueCharacteristics = tongueCharacteristics;
        }

        public String getClinicalImplications() {
            return clinicalImplications;
        }

        public void setClinicalImplications(String clinicalImplications) {
            this.clinicalImplications = clinicalImplications;
        }

        public String getResultJson() {
            return resultJson;
        }

        public void setResultJson(String resultJson) {
            this.resultJson = resultJson;
        }

        public Integer getConfidence() {
            return confidence;
        }

        public void setConfidence(Integer confidence) {
            this.confidence = confidence;
        }
    }
}