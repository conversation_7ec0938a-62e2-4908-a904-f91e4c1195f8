package com.tcm.common.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 中医系统服务配置 集中管理所有第三方服务的连接信息
 */
@ConfigurationProperties(prefix = "tcm.service")
public class TcmServiceConfig {

    /** MySQL 配置 */
    private Mysql mysql = new Mysql();

    /** Redis 配置 */
    private Redis redis = new Redis();

    /** Elasticsearch 配置 */
    private Elasticsearch elasticsearch = new Elasticsearch();

    /** Neo4j 配置 */
    private Neo4j neo4j = new Neo4j();

    /** Milvus 配置 */
    private Milvus milvus = new Milvus();

    /** MinIO 配置 */
    private Minio minio = new Minio();

    /** Sentinel 配置 */
    private Sentinel sentinel = new Sentinel();

    /** ELK 配置 */
    private Elk elk = new Elk();

    /** Seata 配置 */
    private Seata seata = new Seata();

    /** Nacos 配置 */
    private Nacos nacos = new Nacos();

    /** RocketMQ 配置 */
    private RocketMq rocketMq = new RocketMq();

    /** JDK 配置 */
    private Jdk jdk = new Jdk();

    public static class Mysql {
        private String host = "**********";
        private int port = 3306;
        private String username = "root";
        private String password = "Kaixin207@1984";
        private String database = "tcm-system";
        private String url;

        public String getUrl() {
            // 如果url已设置，则直接返回，否则构建默认URL
            if (url != null && !url.isEmpty()) {
                return url;
            }
            return "jdbc:mysql://" + host + ":" + port + "/" + database
                    + "?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8";
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }
    }

    public static class Redis {
        private String host = "**********";
        private int port = 6379;
        private String password = "kaixin207";
        private int database = 0;

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public int getDatabase() {
            return database;
        }

        public void setDatabase(int database) {
            this.database = database;
        }
    }

    public static class Elasticsearch {
        private String host = "**********";
        private int port = 9200;
        private String username = "elastic";
        private String password = "kaixin207";

        public String getUrl() {
            return "http://" + host + ":" + port;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Neo4j {
        private String host = "**********";
        private int port = 7474; // HTTP端口
        private int boltPort = 7687; // Bolt端口
        private String username = "neo4j";
        private String password = "kaixin207";

        public String getUrl() {
            // 使用Bolt端口(7687)而不是HTTP端口(7474)
            return "bolt://" + host + ":" + boltPort;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Milvus {
        private String host = "**********";
        private int port = 19530;
        private String username = "root";
        private String password = "kaixin207";

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Minio {
        private String host = "**********";
        private int port = 9000;
        private String accessKey = "qztllz";
        private String secretKey = "kaixin207";
        private String bucketName = "tcm-system";

        public String getUrl() {
            return "http://" + host + ":" + port;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getAccessKey() {
            return accessKey;
        }

        public void setAccessKey(String accessKey) {
            this.accessKey = accessKey;
        }

        public String getSecretKey() {
            return secretKey;
        }

        public void setSecretKey(String secretKey) {
            this.secretKey = secretKey;
        }

        public String getBucketName() {
            return bucketName;
        }

        public void setBucketName(String bucketName) {
            this.bucketName = bucketName;
        }
    }

    public static class Sentinel {
        private String host = "**********";
        private int port = 8080;
        private String username = "sentinel";
        private String password = "kaixin207";

        public String getDashboardUrl() {
            return host + ":" + port;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Elk {
        private Elasticsearch elasticsearch = new Elasticsearch();
        private Logstash logstash = new Logstash();
        private Kibana kibana = new Kibana();

        public static class Logstash {
            private String host = "**********";
            private int port = 5044;

            public String getHost() {
                return host;
            }

            public void setHost(String host) {
                this.host = host;
            }

            public int getPort() {
                return port;
            }

            public void setPort(int port) {
                this.port = port;
            }
        }

        public static class Kibana {
            private String host = "**********";
            private int port = 5601;
            private String username = "elastic";
            private String password = "kaixin207";

            public String getUrl() {
                return "http://" + host + ":" + port;
            }

            public String getHost() {
                return host;
            }

            public void setHost(String host) {
                this.host = host;
            }

            public int getPort() {
                return port;
            }

            public void setPort(int port) {
                this.port = port;
            }

            public String getUsername() {
                return username;
            }

            public void setUsername(String username) {
                this.username = username;
            }

            public String getPassword() {
                return password;
            }

            public void setPassword(String password) {
                this.password = password;
            }
        }

        public Elasticsearch getElasticsearch() {
            return elasticsearch;
        }

        public void setElasticsearch(Elasticsearch elasticsearch) {
            this.elasticsearch = elasticsearch;
        }

        public Logstash getLogstash() {
            return logstash;
        }

        public void setLogstash(Logstash logstash) {
            this.logstash = logstash;
        }

        public Kibana getKibana() {
            return kibana;
        }

        public void setKibana(Kibana kibana) {
            this.kibana = kibana;
        }
    }

    public static class Seata {
        private String host = "**********";
        private int port = 8091;
        private String username = "seata";
        private String password = "kaixin207";
        private String database = "seata";

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getDatabase() {
            return database;
        }

        public void setDatabase(String database) {
            this.database = database;
        }
    }

    public static class Nacos {
        private String host = "***********";
        private int port = 8848;
        private String username = "nacos";
        private String password = "kaixin207";

        public String getUrl() {
            return host + ":" + port;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class RocketMq {
        private String host = "**********";
        private int port = 9876;
        private String username = "admin";
        private String password = "admin";

        public String getNameServerAddress() {
            return host + ":" + port;
        }

        public String getHost() {
            return host;
        }

        public void setHost(String host) {
            this.host = host;
        }

        public int getPort() {
            return port;
        }

        public void setPort(int port) {
            this.port = port;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }
    }

    public static class Jdk {
        private String home = "C:\\Users\\<USER>\\jdk\\java";
        private OsConfig windows = new OsConfig();
        private OsConfig linux = new OsConfig();

        public static class OsConfig {
            private String home;

            public String getHome() {
                return home;
            }

            public void setHome(String home) {
                this.home = home;
            }
        }

        /**
         * 根据当前操作系统获取合适的JDK路径
         * 
         * @return JDK路径
         */
        public String getOsSpecificHome() {
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains("win")) {
                return windows.getHome() != null ? windows.getHome() : home;
            } else if (osName.contains("linux") || osName.contains("unix")) {
                return linux.getHome() != null ? linux.getHome() : home;
            }
            return home;
        }

        public String getHome() {
            return home;
        }

        public void setHome(String home) {
            this.home = home;
        }

        public OsConfig getWindows() {
            return windows;
        }

        public void setWindows(OsConfig windows) {
            this.windows = windows;
        }

        public OsConfig getLinux() {
            return linux;
        }

        public void setLinux(OsConfig linux) {
            this.linux = linux;
        }
    }

    public Mysql getMysql() {
        return mysql;
    }

    public void setMysql(Mysql mysql) {
        this.mysql = mysql;
    }

    public Redis getRedis() {
        return redis;
    }

    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    public Elasticsearch getElasticsearch() {
        return elasticsearch;
    }

    public void setElasticsearch(Elasticsearch elasticsearch) {
        this.elasticsearch = elasticsearch;
    }

    public Neo4j getNeo4j() {
        return neo4j;
    }

    public void setNeo4j(Neo4j neo4j) {
        this.neo4j = neo4j;
    }

    public Milvus getMilvus() {
        return milvus;
    }

    public void setMilvus(Milvus milvus) {
        this.milvus = milvus;
    }

    public Minio getMinio() {
        return minio;
    }

    public void setMinio(Minio minio) {
        this.minio = minio;
    }

    public Sentinel getSentinel() {
        return sentinel;
    }

    public void setSentinel(Sentinel sentinel) {
        this.sentinel = sentinel;
    }

    public Elk getElk() {
        return elk;
    }

    public void setElk(Elk elk) {
        this.elk = elk;
    }

    public Seata getSeata() {
        return seata;
    }

    public void setSeata(Seata seata) {
        this.seata = seata;
    }

    public Nacos getNacos() {
        return nacos;
    }

    public void setNacos(Nacos nacos) {
        this.nacos = nacos;
    }

    public RocketMq getRocketMq() {
        return rocketMq;
    }

    public void setRocketMq(RocketMq rocketMq) {
        this.rocketMq = rocketMq;
    }

    public Jdk getJdk() {
        return jdk;
    }

    public void setJdk(Jdk jdk) {
        this.jdk = jdk;
    }
}