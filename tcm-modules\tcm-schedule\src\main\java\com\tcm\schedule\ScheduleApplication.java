package com.tcm.schedule;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 排班模块启动类
 */
@SpringBootApplication(exclude = {
        org.redisson.spring.starter.RedissonAutoConfiguration.class,
        com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration.class,
        com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration.class,
        com.alibaba.cloud.nacos.NacosConfigAutoConfiguration.class,
        com.alibaba.cloud.nacos.registry.NacosServiceRegistryAutoConfiguration.class,
        com.alibaba.cloud.nacos.endpoint.NacosConfigEndpointAutoConfiguration.class,
        com.alibaba.cloud.nacos.endpoint.NacosDiscoveryEndpointAutoConfiguration.class,
        org.springframework.cloud.client.serviceregistry.AutoServiceRegistrationAutoConfiguration.class,
        org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration.class
})
public class ScheduleApplication {
    public static void main(String[] args) {
        SpringApplication.run(ScheduleApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  排班模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
