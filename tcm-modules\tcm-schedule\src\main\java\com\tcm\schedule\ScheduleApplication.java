package com.tcm.schedule;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

import com.tcm.common.security.annotation.EnableCustomConfig;
import com.tcm.common.security.annotation.EnableTcmFeignClients;

/**
 * 排班模块启动类
 */
@EnableCustomConfig
@EnableTcmFeignClients
@EnableDiscoveryClient
@SpringBootApplication
public class ScheduleApplication {
    public static void main(String[] args) {
        SpringApplication.run(ScheduleApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  排班模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
