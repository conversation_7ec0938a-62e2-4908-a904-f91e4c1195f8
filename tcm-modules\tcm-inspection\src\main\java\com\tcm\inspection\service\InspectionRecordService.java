package com.tcm.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.inspection.domain.InspectionRecord;

import java.util.List;

/**
 * 检查记录服务接口
 */
public interface InspectionRecordService extends IService<InspectionRecord> {

    /**
     * 查询检查记录列表
     *
     * @param inspectionRecord 检查记录信息
     * @return 检查记录集合
     */
    List<InspectionRecord> selectInspectionRecordList(InspectionRecord inspectionRecord);

    /**
     * 分页查询检查记录列表
     *
     * @param page             分页信息
     * @param inspectionRecord 检查记录信息
     * @return 检查记录分页信息
     */
    IPage<InspectionRecord> selectInspectionRecordPage(Page<InspectionRecord> page, InspectionRecord inspectionRecord);

    /**
     * 根据ID查询检查记录
     *
     * @param recordId 检查记录ID
     * @return 检查记录信息
     */
    InspectionRecord selectInspectionRecordById(Long recordId);

    /**
     * 根据检查单号查询检查记录
     *
     * @param inspectionNo 检查单号
     * @return 检查记录信息
     */
    InspectionRecord selectInspectionRecordByInspectionNo(String inspectionNo);

    /**
     * 根据患者ID查询检查记录列表
     *
     * @param patientId 患者ID
     * @return 检查记录集合
     */
    List<InspectionRecord> selectInspectionRecordByPatientId(Long patientId);

    /**
     * 新增检查记录
     *
     * @param inspectionRecord 检查记录信息
     * @return 结果
     */
    boolean insertInspectionRecord(InspectionRecord inspectionRecord);

    /**
     * 修改检查记录
     *
     * @param inspectionRecord 检查记录信息
     * @return 结果
     */
    boolean updateInspectionRecord(InspectionRecord inspectionRecord);

    /**
     * 删除检查记录
     *
     * @param recordId 检查记录ID
     * @return 结果
     */
    boolean deleteInspectionRecordById(Long recordId);

    /**
     * 批量删除检查记录
     *
     * @param recordIds 需要删除的检查记录ID数组
     * @return 结果
     */
    boolean deleteInspectionRecordByIds(Long[] recordIds);

    /**
     * 更新检查结果
     *
     * @param inspectionRecord 检查记录信息
     * @return 结果
     */
    boolean updateInspectionResult(InspectionRecord inspectionRecord);

    /**
     * 取消检查记录
     *
     * @param recordId 检查记录ID
     * @param reason   取消原因
     * @return 结果
     */
    boolean cancelInspectionRecord(Long recordId, String reason);

    /**
     * 生成检查单号
     *
     * @return 检查单号
     */
    String generateInspectionNo();
} 