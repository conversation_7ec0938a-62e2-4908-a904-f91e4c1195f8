package com.tcm.common.core.interceptor;

import com.tcm.common.core.annotation.Idempotent;
import com.tcm.common.core.constant.HttpStatus;
import com.tcm.common.core.exception.BusinessException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.lang.reflect.Method;

/**
 * 幂等性拦截器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IdempotentInterceptor implements HandlerInterceptor {

    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        Idempotent idempotent = method.getAnnotation(Idempotent.class);
        if (idempotent == null) {
            return true;
        }

        // 生成幂等性KEY：prefix + method + url + ip + uid(如果登录) + body(如果POST)
        String url = request.getRequestURI();
        String methodName = method.getDeclaringClass().getName() + "." + method.getName();
        String ip = getIpAddress(request);
        // TODO: 从安全上下文获取用户ID，这里临时用IP代替
        String userId = "anonymous";

        // 获取请求体中的参数，对POST请求进行处理
        StringBuilder paramBuilder = new StringBuilder();
        if (request.getContentLength() > 0 && "POST".equalsIgnoreCase(request.getMethod())) {
            try {
                String body = request.getReader().lines().reduce("", (accumulator, actual) -> accumulator + actual);
                paramBuilder.append(body);
            } catch (Exception e) {
                log.error("读取请求体失败", e);
            }
        } else {
            // 处理GET请求参数
            request.getParameterMap().forEach((key, values) -> {
                for (String value : values) {
                    paramBuilder.append(key).append("=").append(value).append("&");
                }
            });
        }

        // 使用MD5生成唯一键
        String params = paramBuilder.toString();
        String key = idempotent.prefix() + DigestUtils.md5DigestAsHex(
                (methodName + url + ip + userId + params).getBytes());

        ValueOperations<String, Object> operations = redisTemplate.opsForValue();
        
        // 如果key已存在，说明是重复提交
        Boolean isAbsent = operations.setIfAbsent(key, "1", 
                idempotent.expireTime(), idempotent.timeUnit());
        
        if (Boolean.FALSE.equals(isAbsent)) {
            log.warn("幂等性校验不通过: {}", key);
            throw new BusinessException(HttpStatus.ERROR, idempotent.message());
        }
        
        return true;
    }

    /**
     * 获取IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
} 