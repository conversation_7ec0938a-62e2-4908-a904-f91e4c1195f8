package com.tcm.prescription.controller;

import com.tcm.common.core.domain.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.tcm.prescription.service.IPrescriptionStatService;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 处方统计控制器
 */
@RestController
@RequestMapping("/prescription/stat")
public class PrescriptionStatController {

    @Autowired
    private IPrescriptionStatService prescriptionStatService;

    /**
     * 获取医生处方数量
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处方数量
     */
    @GetMapping("/count")
    public R<Long> getPrescriptionCount(
            @RequestParam("doctorId") Long doctorId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Long count = prescriptionStatService.getPrescriptionCount(doctorId, startDate, endDate);
        return R.ok(count);
    }

    /**
     * 获取医生处方总金额
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处方总金额
     */
    @GetMapping("/amount")
    public R<BigDecimal> getPrescriptionAmount(
            @RequestParam("doctorId") Long doctorId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        BigDecimal amount = prescriptionStatService.getPrescriptionAmount(doctorId, startDate, endDate);
        return R.ok(amount);
    }
}
