package com.tcm.consultation.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 远程会诊消息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_consultation_message")
public class ConsultationMessage extends BaseEntity {

    /**
     * 消息ID
     */
    @TableId
    private Long messageId;

    /**
     * 会诊记录ID
     */
    private Long consultationId;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者姓名
     */
    private String senderName;

    /**
     * 发送者类型（1：主诊医生，2：会诊医生，3：专家，4：系统）
     */
    private Integer senderType;

    /**
     * 消息类型（1：文本，2：图片，3：文件，4：系统通知）
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 媒体资源URL
     */
    private String mediaUrl;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件大小（KB）
     */
    private Long fileSize;
}