package com.tcm.appointment.service;

import com.tcm.appointment.domain.Appointment;
import com.tcm.appointment.dto.AppointmentDTO;
import com.tcm.appointment.dto.AppointmentQuery;
import com.tcm.appointment.mapper.AppointmentMapper;
import com.tcm.appointment.service.impl.AppointmentServiceImpl;
import com.tcm.appointment.vo.AppointmentVO;
import com.tcm.appointment.vo.TimeSlotVO;
import com.tcm.common.core.exception.BusinessException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 预约服务测试类
 */
@ExtendWith(MockitoExtension.class)
public class AppointmentServiceTest {

    @Mock
    private AppointmentMapper appointmentMapper;

    @InjectMocks
    private AppointmentServiceImpl appointmentService;

    @BeforeEach
    void setUp() {
        // 设置属性值
        ReflectionTestUtils.setField(appointmentService, "allowMultiplePerTimeSlot", false);
        ReflectionTestUtils.setField(appointmentService, "appointmentAdvanceDays", 14);
    }

    @Test
    @DisplayName("测试获取可用时间段")
    void testGetAvailableTimeSlots() {
        // 准备测试数据
        Long doctorId = 1L;
        Date date = new Date();

        TimeSlotVO slot1 = new TimeSlotVO();
        slot1.setValue("08:00-08:30");
        slot1.setLabel("上午 08:00-08:30");
        slot1.setStatus(0);
        slot1.setType(1);
        slot1.setTypeName("上午");
        slot1.setOrderNum(1);

        TimeSlotVO slot2 = new TimeSlotVO();
        slot2.setValue("08:30-09:00");
        slot2.setLabel("上午 08:30-09:00");
        slot2.setStatus(0);
        slot2.setType(1);
        slot2.setTypeName("上午");
        slot2.setOrderNum(2);

        List<TimeSlotVO> workTimeSlots = Arrays.asList(slot1, slot2);
        List<String> bookedTimeSlots = Arrays.asList("08:00-08:30");

        // 模拟appointmentMapper的行为
        when(appointmentMapper.getDoctorWorkTimeSlots(doctorId, date)).thenReturn(workTimeSlots);
        when(appointmentMapper.getBookedTimeSlots(doctorId, date)).thenReturn(bookedTimeSlots);

        // 调用测试方法
        List<TimeSlotVO> availableTimeSlots = appointmentService.getAvailableTimeSlots(doctorId, date);

        // 验证结果
        assertNotNull(availableTimeSlots);
        assertEquals(1, availableTimeSlots.size());
        assertEquals("08:30-09:00", availableTimeSlots.get(0).getValue());

        // 验证方法调用
        verify(appointmentMapper, times(1)).getDoctorWorkTimeSlots(doctorId, date);
        verify(appointmentMapper, times(1)).getBookedTimeSlots(doctorId, date);
    }

    @Test
    @DisplayName("测试创建预约")
    void testCreateAppointment() {
        // 准备测试数据
        AppointmentDTO dto = new AppointmentDTO();
        dto.setPatientId(1L);
        dto.setDoctorId(1L);
        // 由于AppointmentDTO没有setDepartmentId方法,需要移除该行或使用正确的setter方法
        dto.setAppointmentDate(new Date());
        dto.setTimeSlot("08:00-08:30");
        dto.setType(1);
        dto.setSymptom("测试症状");
        dto.setFee(50.0);

        // 模拟appointmentMapper的行为
        when(appointmentMapper.checkTimeSlotAvailable(anyLong(), any(Date.class), anyString())).thenReturn(0);
        when(appointmentMapper.insert(any(Appointment.class))).thenReturn(1);
        when(appointmentMapper.getLastInsertId()).thenReturn(456L); // 修改为其他测试值

        // 调用测试方法
        Long appointmentId = appointmentService.createAppointment(dto);

        // 验证结果
        assertNotNull(appointmentId);
        assertEquals(123L, appointmentId);

        // 验证方法调用
        verify(appointmentMapper, times(1)).checkTimeSlotAvailable(eq(dto.getDoctorId()), any(Date.class),
                eq(dto.getTimeSlot()));
        verify(appointmentMapper, times(1)).insert(any(Appointment.class));
        verify(appointmentMapper, times(1)).getLastInsertId();
    }

    @Test
    @DisplayName("测试创建预约时间段已被占用")
    void testCreateAppointmentTimeSlotUnavailable() {
        // 准备测试数据
        AppointmentDTO dto = new AppointmentDTO();
        dto.setPatientId(1L);
        dto.setDoctorId(1L);
        // 由于 AppointmentDTO 没有 setDepartmentId 方法，此行应该移除
        dto.setAppointmentDate(new Date());
        dto.setTimeSlot("08:00-08:30");
        dto.setType(1);
        dto.setSymptom("测试症状");
        dto.setFee(50.0);

        // 模拟appointmentMapper的行为，返回已存在记录
        when(appointmentMapper.checkTimeSlotAvailable(anyLong(), any(Date.class), anyString())).thenReturn(1);

        // 验证抛出异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            appointmentService.createAppointment(dto);
        });

        assertEquals("该时间段已被预约", exception.getMessage());

        // 验证方法调用
        verify(appointmentMapper, times(1)).checkTimeSlotAvailable(eq(dto.getDoctorId()), any(Date.class),
                eq(dto.getTimeSlot()));
    }
}