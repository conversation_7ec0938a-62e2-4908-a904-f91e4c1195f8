package com.tcm.analytics.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * 分析模型实体类
 */
@Data
@TableName("analytics_model")
public class AnalyticsModel {

    /**
     * 模型ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 模型类型（0:回归模型, 1:分类模型, 2:聚类模型, 3:自定义模型）
     */
    private Integer modelType;

    /**
     * 模型版本
     */
    private String modelVersion;

    /**
     * 模型路径
     */
    private String modelPath;

    /**
     * 模型参数（JSON格式）
     */
    private String modelParams;

    /**
     * 模型描述
     */
    private String modelDesc;

    /**
     * 训练数据描述
     */
    private String trainingDataDesc;

    /**
     * 精度
     */
    private Double accuracy;

    /**
     * 召回率
     */
    private Double recall;

    /**
     * F1值
     */
    private Double f1Score;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 模型状态（0:未训练, 1:训练中, 2:已训练, 3:训练失败）
     */
    private Integer modelStatus;

    /**
     * 模型指标（JSON格式）
     */
    private String modelMetrics;

    /**
     * 模型训练开始时间
     */
    private LocalDateTime trainStartTime;

    /**
     * 模型训练结束时间
     */
    private LocalDateTime trainEndTime;

    /**
     * 删除标记（0:未删除, 1:已删除）
     */
    private Integer delFlag;

    /**
     * 用户ID
     */
    private Long userId;

    // 兼容旧代码的方法

    /**
     * 获取模型类型
     */
    public String getType() {
        if (modelType == null)
            return null;
        switch (modelType) {
        case 0:
            return "REGRESSION";
        case 1:
            return "CLASSIFICATION";
        case 2:
            return "CLUSTERING";
        case 3:
            return "CUSTOM";
        default:
            return "UNKNOWN";
        }
    }

    /**
     * 获取模型状态
     */
    public String getStatus() {
        if (modelStatus == null)
            return null;
        switch (modelStatus) {
        case 0:
            return "UNTRAINED";
        case 1:
            return "TRAINING";
        case 2:
            return "COMPLETED";
        case 3:
            return "FAILED";
        default:
            return "UNKNOWN";
        }
    }

    /**
     * 设置模型状态
     */
    public void setStatus(String status) {
        if (status == null)
            return;
        switch (status) {
        case "UNTRAINED":
            this.modelStatus = 0;
            break;
        case "TRAINING":
            this.modelStatus = 1;
            break;
        case "COMPLETED":
            this.modelStatus = 2;
            break;
        case "FAILED":
            this.modelStatus = 3;
            break;
        default:
            this.modelStatus = 0;
        }
    }

    /**
     * 获取模型名称
     */
    public String getName() {
        return this.modelName;
    }

    /**
     * 设置错误信息
     */
    public void setErrorMessage(String message) {
        this.modelDesc = message;
    }

    /**
     * 设置模型参数
     */
    public void setParameters(String parameters) {
        this.modelParams = parameters;
    }

    /**
     * 设置模型指标
     */
    public void setMetrics(String metrics) {
        this.modelMetrics = metrics;
    }
}