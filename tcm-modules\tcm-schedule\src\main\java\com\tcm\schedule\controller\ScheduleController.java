package com.tcm.schedule.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 排班控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/schedule")
public class ScheduleController {

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Schedule service is running");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取医生排班信息
     */
    @GetMapping("/doctor")
    public ResponseEntity<Map<String, Object>> getDoctorSchedule(String doctorId) {
        Map<String, Object> response = new HashMap<>();
        
        // 模拟数据
        List<Map<String, Object>> scheduleList = new ArrayList<>();
        
        LocalDate today = LocalDate.now();
        
        for (int i = 0; i < 7; i++) {
            Map<String, Object> schedule = new HashMap<>();
            schedule.put("date", today.plusDays(i).toString());
            schedule.put("morning", i % 2 == 0);
            schedule.put("afternoon", i % 3 == 0);
            schedule.put("evening", i % 5 == 0);
            scheduleList.add(schedule);
        }
        
        response.put("doctorId", doctorId);
        response.put("doctorName", "张医生");
        response.put("schedules", scheduleList);
        response.put("timestamp", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建排班信息
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createSchedule(@RequestBody Map<String, Object> scheduleRequest) {
        Map<String, Object> response = new HashMap<>();
        
        // 模拟保存排班信息的逻辑
        response.put("success", true);
        response.put("message", "排班信息创建成功");
        response.put("scheduleId", System.currentTimeMillis());
        response.put("doctorId", scheduleRequest.get("doctorId"));
        response.put("timestamp", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取科室排班信息
     */
    @GetMapping("/department")
    public ResponseEntity<Map<String, Object>> getDepartmentSchedule(String departmentId) {
        Map<String, Object> response = new HashMap<>();
        
        // 模拟数据
        List<Map<String, Object>> doctorSchedules = new ArrayList<>();
        
        for (int i = 1; i <= 5; i++) {
            Map<String, Object> doctorSchedule = new HashMap<>();
            doctorSchedule.put("doctorId", "D" + i);
            doctorSchedule.put("doctorName", "医生" + i);
            
            List<Map<String, Object>> schedules = new ArrayList<>();
            LocalDate today = LocalDate.now();
            
            for (int j = 0; j < 7; j++) {
                Map<String, Object> schedule = new HashMap<>();
                schedule.put("date", today.plusDays(j).toString());
                schedule.put("morning", (i + j) % 2 == 0);
                schedule.put("afternoon", (i + j) % 3 == 0);
                schedule.put("evening", (i + j) % 5 == 0);
                schedules.add(schedule);
            }
            
            doctorSchedule.put("schedules", schedules);
            doctorSchedules.add(doctorSchedule);
        }
        
        response.put("departmentId", departmentId);
        response.put("departmentName", "中医内科");
        response.put("doctorSchedules", doctorSchedules);
        response.put("timestamp", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(response);
    }
} 