package com.tcm.schedule.controller;

import java.time.LocalDateTime;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.common.core.annotation.Log;
import com.tcm.common.core.domain.PageVO;
import com.tcm.common.core.domain.Result;
import com.tcm.schedule.dto.ScheduleDTO;
import com.tcm.schedule.entity.Schedule;
import com.tcm.schedule.service.ScheduleService;
import com.tcm.schedule.vo.ScheduleVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * 排班控制器
 */
@Tag(name = "排班接口")
@RestController
@RequestMapping("/api/schedule")
@RequiredArgsConstructor
public class ScheduleController {

    private final ScheduleService scheduleService;

    /**
     * 获取排班列表
     */
    @Operation(summary = "获取排班列表")
    @GetMapping("/list")
    public Result<PageVO<ScheduleVO>> list(ScheduleDTO dto) {
        PageVO<ScheduleVO> pageVO = scheduleService.selectSchedulePage(dto);
        return Result.success(pageVO);
    }

    /**
     * 获取排班详细信息
     */
    @Operation(summary = "获取排班详细信息")
    @GetMapping("/{id}")
    public Result<ScheduleVO> getInfo(@Parameter(description = "排班ID") @PathVariable Long id) {
        ScheduleVO schedule = scheduleService.getScheduleById(id);
        return Result.success(schedule);
    }

    /**
     * 新增排班
     */
    @Operation(summary = "新增排班")
    @Log(module = "排班管理", operateType = 1, description = "新增排班")
    @PostMapping
    public Result<?> add(@RequestBody Schedule schedule) {
        return scheduleService.addSchedule(schedule) ? Result.success() : Result.error("新增排班失败");
    }

    /**
     * 修改排班
     */
    @Operation(summary = "修改排班")
    @Log(module = "排班管理", operateType = 2, description = "修改排班")
    @PutMapping
    public Result<?> edit(@RequestBody Schedule schedule) {
        return scheduleService.updateSchedule(schedule) ? Result.success() : Result.error("修改排班失败");
    }

    /**
     * 删除排班
     */
    @Operation(summary = "删除排班")
    @Log(module = "排班管理", operateType = 3, description = "删除排班")
    @DeleteMapping("/{ids}")
    public Result<?> remove(@Parameter(description = "排班ID串，多个以逗号分隔") @PathVariable Long[] ids) {
        return scheduleService.deleteScheduleByIds(ids) ? Result.success() : Result.error("删除排班失败");
    }

    /**
     * 查询医生排班列表
     */
    @Operation(summary = "查询医生排班列表")
    @GetMapping("/doctor/{doctorId}")
    public Result<List<ScheduleVO>> getScheduleByDoctorId(@Parameter(description = "医生ID") @PathVariable Long doctorId,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beginDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        List<ScheduleVO> list = scheduleService.getScheduleByDoctorId(doctorId, beginDate, endDate);
        return Result.success(list);
    }

    /**
     * 查询科室排班列表
     */
    @Operation(summary = "查询科室排班列表")
    @GetMapping("/dept/{deptId}")
    public Result<List<ScheduleVO>> getScheduleByDeptId(@Parameter(description = "科室ID") @PathVariable Long deptId,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime beginDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endDate) {
        List<ScheduleVO> list = scheduleService.getScheduleByDeptId(deptId, beginDate, endDate);
        return Result.success(list);
    }

    /**
     * 更新排班预约数
     */
    @Operation(summary = "更新排班预约数")
    @Log(module = "排班管理", operateType = 2, description = "更新排班预约数")
    @PutMapping("/appointment/{id}")
    public Result<?> updateAppointmentCount(@Parameter(description = "排班ID") @PathVariable Long id) {
        return scheduleService.updateAppointmentCount(id) ? Result.success() : Result.error("更新排班预约数失败");
    }
}
