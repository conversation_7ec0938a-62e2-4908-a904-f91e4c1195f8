package com.tcm.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单明细实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tcm_order_item")
public class OrderItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单明细ID
     */
    @TableId(value = "item_id", type = IdType.AUTO)
    private Long itemId;

    /**
     * 订单ID
     */
    private Long orderId;
    
    /**
     * 商品ID（药材ID或服务ID）
     */
    private Long productId;
    
    /**
     * 商品类型：1-中药材 2-西药 3-成药 4-诊疗服务 5-其他
     */
    private Integer productType;
    
    /**
     * 商品名称
     */
    private String productName;
    
    /**
     * 商品规格
     */
    private String productSpec;
    
    /**
     * 商品单位
     */
    private String productUnit;
    
    /**
     * 商品数量
     */
    private BigDecimal quantity;
    
    /**
     * 商品单价
     */
    private BigDecimal unitPrice;
    
    /**
     * 商品总价
     */
    private BigDecimal totalPrice;
    
    /**
     * 药材用法
     */
    private String usage;
    
    /**
     * 用药顺序：先煎/后下/包煎
     */
    private String usageOrder;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
