package com.tcm.gateway.filter;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 认证过滤器
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "gateway")
public class AuthFilter implements GlobalFilter, Ordered {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 白名单路径
     */
    private List<String> whiteList;

    /**
     * 黑名单IP
     */
    private List<String> blackList;

    /**
     * 认证令牌前缀
     */
    private static final String TOKEN_PREFIX = "Bearer ";

    /**
     * 认证令牌键名
     */
    private static final String TOKEN_KEY = "Authorization";

    /**
     * Redis令牌前缀
     */
    private static final String REDIS_TOKEN_KEY = "auth:token:";

    /**
     * 路径匹配器
     */
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();
        String path = request.getURI().getPath();
        String clientIp = getIpAddress(request);

        // 1. 检查黑名单IP
        if (isBlackListIp(clientIp)) {
            log.warn("黑名单IP访问: {}", clientIp);
            return unauthorizedResponse(response, "您的IP已被列入黑名单");
        }

        // 2. 白名单直接放行
        if (isWhiteListPath(path)) {
            return chain.filter(exchange);
        }

        // 3. 获取并校验Token
        String token = getToken(request);
        if (!StringUtils.hasText(token)) {
            log.warn("认证失败: Token缺失, URI: {}", path);
            return unauthorizedResponse(response, "请先登录");
        }

        try {
            // 4. 验证Token有效性（通过查询Redis）
            Object userId = redisTemplate.opsForValue().get(REDIS_TOKEN_KEY + token);
            if (userId == null) {
                log.warn("认证失败: Token无效或已过期, URI: {}", path);
                return unauthorizedResponse(response, "登录已过期，请重新登录");
            }

            // 5. 延长令牌有效期（可选）
            redisTemplate.expire(REDIS_TOKEN_KEY + token, 30, TimeUnit.MINUTES);

            // 6. 将用户信息传递到下游服务
            ServerHttpRequest newRequest = request.mutate()
                    .header("X-User-Id", userId.toString())
                    .build();
            ServerWebExchange newExchange = exchange.mutate()
                    .request(newRequest)
                    .build();

            return chain.filter(newExchange);
        } catch (Exception e) {
            log.error("认证异常: ", e);
            return unauthorizedResponse(response, "认证异常，请重新登录");
        }
    }

    @Override
    public int getOrder() {
        // 设置过滤器优先级，数值越小优先级越高
        return -100;
    }

    /**
     * 未授权响应
     */
    private Mono<Void> unauthorizedResponse(ServerHttpResponse response, String message) {
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);

        String body = "{\"code\":401,\"message\":\"" + message + "\"}";
        DataBuffer buffer = response.bufferFactory().wrap(body.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 获取请求令牌
     */
    private String getToken(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(TOKEN_KEY);
        if (StringUtils.hasText(token) && token.startsWith(TOKEN_PREFIX)) {
            return token.substring(TOKEN_PREFIX.length());
        }
        return null;
    }

    /**
     * 判断是否为白名单路径
     */
    private boolean isWhiteListPath(String path) {
        if (whiteList == null || whiteList.isEmpty()) {
            return false;
        }

        for (String pattern : whiteList) {
            if (pathMatcher.match(pattern, path)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为黑名单IP
     */
    private boolean isBlackListIp(String ip) {
        return blackList != null && blackList.contains(ip);
    }

    /**
     * 获取请求IP地址
     */
    private String getIpAddress(ServerHttpRequest request) {
        String ip = request.getHeaders().getFirst("X-Forwarded-For");
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("Proxy-Client-IP");
        }
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeaders().getFirst("WL-Proxy-Client-IP");
        }
        if (!StringUtils.hasText(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddress().getAddress().getHostAddress();
        }

        // 对于通过多个代理的情况，第一个IP为客户端真实IP
        if (StringUtils.hasText(ip) && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }
}