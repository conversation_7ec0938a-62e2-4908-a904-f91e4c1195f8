# Cursor规则

## 代码风格规则
- 使用4个空格进行缩进，不使用Tab
- 类名使用PascalCase命名法
- 方法名和变量名使用camelCase命名法
- 常量使用全大写，下划线分隔单词
- 包名使用全小写
- 左大括号放在当前行末尾
- 每行最大长度为120个字符
- 所有Java类文件需要添加适当的Javadoc注释
- 使用UTF-8编码
- 不要硬编码
- 每次中文回复
- 尽可能使用统一配置

## 提交规范
- 提交信息前缀规范：
  - feat: 新功能
  - fix: 修复问题
  - docs: 文档变更
  - style: 代码格式修改
  - refactor: 代码重构
  - test: 测试用例相关变化
  - chore: 其他变更
- 提交信息应当简洁明了，说明改动内容
- 较大改动应当拆分为多个小的提交

## 通用配置管理规则
- 所有服务通用配置必须统一存放在`tcm-common.yml`中
- 严格禁止在模块配置中使用硬编码的服务地址（如localhost）
- 所有模块必须在`bootstrap.yml`中引入通用配置：
  ```yaml
  spring:
    cloud:
      nacos:
        config:
          shared-configs:
            - data-id: tcm-common.yml
              group: DEFAULT_GROUP
              refresh: true
  ```
- 服务地址引用必须使用`${tcm.service.xxx}`格式：
  - 数据库：`${tcm.service.mysql.host}`
  - Redis：`${tcm.service.redis.host}`
  - Nacos：`${tcm.service.nacos.host}`
- 配置优先级依次为：环境变量 > 模块配置 > 通用配置 > 默认值
- 每个模块必须添加Nacos配置中心依赖：
  ```xml
  <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
  </dependency>
  ```
- 动态配置必须使用`@RefreshScope`注解实现自动刷新
- 禁止重复定义通用配置中已有的配置项
- 模块特有配置放在各自的`application.yml`中
- 敏感配置（密码、密钥等）优先使用环境变量方式注入
- 配置变更需通过配置中心，不允许直接修改部署环境文件

## Java代码规范
- 避免使用原始类型
- 优先使用接口而非实现类
- 使用Optional处理可能为null的返回值
- 遵循SOLID原则
- 使用@Override注解标记重写方法
- 合理处理异常，不应捕获异常后不处理
- 避免过度设计，保持代码简洁

## 设计原则
- 遵循架构设计，保持代码风格一致
- 代码修改遵循单一职责原则，不混合多个变更
- 在进行代码设计规划时，符合"第一性原理"：
  - 回归问题本质，从基本原理出发
  - 避免基于类比或先例的设计，追求从根本上解决问题
  - 质疑和验证每个设计假设
- 在代码实现时，遵循"KISS原则"（Keep It Simple, Stupid）：
  - 保持代码简单明了，避免不必要的复杂性
  - 优先选择最简单的解决方案
  - 避免过度工程化和过早优化
- 遵循"SOLID原则"：
  - 单一职责原则（S）：一个类只负责一个功能领域
  - 开闭原则（O）：对扩展开放，对修改关闭
  - 里氏替换原则（L）：子类可以完全替代父类
  - 接口隔离原则（I）：使用多个专门的接口，而不是单一的总接口
  - 依赖倒置原则（D）：依赖于抽象而不是具体实现
- 尽量复用已有代码，避免代码重复（DRY原则：Don't Repeat Yourself）
- 优先考虑组合而非继承
- 设计接口时保持简洁，减少依赖

## 安全规范
- 所有用户输入必须进行验证和清洗，防止注入攻击
- 敏感数据（如密码、令牌）不得明文存储，必须加密
- API接口必须实现适当的访问控制和权限验证
- 避免在日志中记录敏感信息
- 定期更新依赖库，修复已知安全漏洞
- 实现适当的异常处理，避免泄露技术细节
- 使用HTTPS进行所有外部通信
- 实现API请求限流机制，防止DoS攻击
- 会话管理遵循最小权限原则

## 性能优化准则
- 提前识别性能关键路径，并设计性能测试方案
- 合理使用缓存机制，减少重复计算和数据库访问
- 大批量数据操作使用批处理而非循环操作
- 避免N+1查询问题，合理设计数据库查询
- 懒加载非立即需要的资源
- 微服务间通信尽量减少数据传输量
- 定期进行性能分析和优化
- 针对高并发场景设计无状态服务
- 使用异步处理长时间运行的任务

## 可测试性设计
- 代码设计时考虑可测试性，便于单元测试
- 业务逻辑与外部依赖解耦，便于模拟测试
- 编写有意义的单元测试，覆盖核心业务逻辑
- 避免对静态方法和单例的直接依赖
- 使用依赖注入而非硬编码依赖
- 使用测试驱动开发(TDD)方法开发关键功能
- 保持测试代码的简洁和可维护性
- 建立自动化测试流程，确保代码质量

## 微服务设计规范
- 遵循领域驱动设计(DDD)原则划分微服务边界
- 每个微服务应当拥有自己的数据库或数据模式
- 服务间通信优先使用异步消息而非同步调用
- 实现服务间的容错机制，如熔断、重试、超时等
- 采用API网关统一服务入口，处理通用关注点
- 实现分布式追踪，便于问题排查
- 避免分布式事务，优先考虑最终一致性
- 设计幂等API，确保重复请求安全处理

## 代码一致性规范
- 所有微服务间保持统一的代码风格和命名约定
- 错误处理机制在所有服务中保持一致，使用统一的异常体系
- 所有微服务使用统一版本的公共依赖库，避免依赖冲突
- API设计遵循一致的URL结构、参数命名和返回格式
- 所有服务使用统一的日志格式和日志级别标准
- 配置管理采用一致的方法和结构
- 使用统一的时间格式和时区处理（UTC优先）
- 数据模型在概念上保持一致，使用统一的命名和关系表示
- 所有服务采用一致的度量指标和监控方式
- 建立通用代码模板和生成工具，确保新代码的一致性
- 版本控制和发布流程对所有服务保持一致
- 所有服务共享基础设施层代码，避免重复实现

## 代码注释与文档规范
- 所有代码必须有详细的注释说明，包括：
  - 类级注释：说明类的用途、责任和使用示例
  - 方法级注释：描述方法功能、参数、返回值和异常
  - 复杂逻辑注释：解释复杂算法或业务规则的实现
  - 字段注释：说明重要字段的含义和用途
- 注释应当解释"为什么"而不仅仅是"是什么"
- 每个微服务模块必须生成并维护类关联图，包括：
  - 模块内部类关系图（UML类图）
  - 服务间依赖关系图
  - 重要业务流程时序图
- 关联图应随代码更新而同步更新
- 使用标准化工具（如PlantUML）生成图表，确保风格一致
- 所有API接口必须有完整文档，包括请求参数、返回值和错误码
- 使用统一的API文档工具（如Swagger/OpenAPI）
- 项目README文件应包含系统架构图和部署指南
- 代码中的TODO/FIXME注释必须包含创建日期和负责人
- 维护详细的变更日志，记录所有重要变更
- 定期审查和更新文档，确保文档与代码同步

## 务实原则
- 避免幻想性设计，只实现实际需求中明确要求的功能
- 不追求完美，而是追求"足够好"的解决方案
- 基于现有代码和实际情况做决策，而非假设性场景
- 优先解决已知问题，避免过度预测未来需求
- 明确区分"必须实现"和"可能需要"的功能
- 保持增量式开发，小步迭代而非大规模重构
- 代码和架构的复杂度应与业务复杂度匹配，不过度设计
- 技术选型要基于团队实际技能和项目实际需求
- 在文档中区分已实现功能和计划功能，避免混淆
- 定期审视并移除未被使用的代码和功能

## 设计模式与解耦原则
- 优先采用松耦合设计，降低组件间依赖
- 合理使用设计模式解决常见问题，但避免过度使用：
  - 工厂模式：封装对象创建逻辑，提供统一接口
  - 策略模式：封装算法族，支持算法切换
  - 观察者模式：实现松耦合的事件通知
  - 装饰器模式：动态扩展功能，避免继承滥用
  - 适配器模式：统一不同接口，减少外部依赖影响
  - 模板方法：定义算法骨架，允许子类定义具体步骤
  - 代理模式：控制对对象的访问，添加间接层
- 依赖倒置：高层模块不应依赖低层模块，都应依赖抽象
- 使用依赖注入实现控制反转，避免硬编码依赖
- 接口隔离：类不应被迫依赖它们不使用的方法
- 单一职责：一个类只有一个引起变化的原因
- 合理使用领域事件实现系统间通信，避免直接调用
- 使用接口而非实现，隐藏实现细节
- 遵循"最少知识原则"（迪米特法则），减少类间耦合
- 优先选择组合而非继承，增强灵活性
- 避免使用全局状态和单例，优先使用依赖注入

## 项目特定规则
- 微服务命名使用tcm-{服务名}格式
- 使用nacos进行服务发现和配置管理
- API版本控制使用URI路径方式
- 所有REST API返回统一的响应格式
- 使用枚举类型表示状态和类型
- 合理使用Spring注解减少样板代码

## 开发历史记录规则
- 每次会话请求结束后进行总结
- 无论是生成新文件还是修改了原有内容都需要总结
- 将总结的内容以添加方式写入到README.md文件中（说明文件中的内容是累计增加的）
- 总结的内容必须包括：
  1. 会话的主要目的
  2. 完成的主要任务
  3. 关键决策和解决方案
  4. 使用的技术栈
  5. 修改了哪些文件
- 使用日期作为每次总结的标题（格式：## YYYY-MM-DD 开发记录）
- 保持总结内容简洁明了，重点突出 


You are an expert in Java programming, Spring Boot, Spring Framework, Maven, JUnit, and related Java technologies.

Code Style and Structure
- Write clean, efficient, and well-documented Java code with accurate Spring Boot examples.
- Use Spring Boot best practices and conventions throughout your code.
- Implement RESTful API design patterns when creating web services.
- Use descriptive method and variable names following camelCase convention.
- Structure Spring Boot applications: controllers, services, repositories, models, configurations.

Spring Boot Specifics
- Use Spring Boot starters for quick project setup and dependency management.
- Implement proper use of annotations (e.g., @SpringBootApplication, @RestController, @Service).
- Utilize Spring Boot's auto-configuration features effectively.
- Implement proper exception handling using @ControllerAdvice and @ExceptionHandler.

Naming Conventions
- Use PascalCase for class names (e.g., UserController, OrderService).
- Use camelCase for method and variable names (e.g., findUserById, isOrderValid).
- Use ALL_CAPS for constants (e.g., MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE).

Java and Spring Boot Usage
- Use Java 17 or later features when applicable (e.g., records, sealed classes, pattern matching).
- Leverage Spring Boot 3.x features and best practices.
- Use Spring Data JPA for database operations when applicable.
- Implement proper validation using Bean Validation (e.g., @Valid, custom validators).

Configuration and Properties
- Use application.properties or application.yml for configuration.
- Implement environment-specific configurations using Spring Profiles.
- Use @ConfigurationProperties for type-safe configuration properties.

Dependency Injection and IoC
- Use constructor injection over field injection for better testability.
- Leverage Spring's IoC container for managing bean lifecycles.

Testing
- Write unit tests using JUnit 5 and Spring Boot Test.
- Use MockMvc for testing web layers.
- Implement integration tests using @SpringBootTest.
- Use @DataJpaTest for repository layer tests.

Performance and Scalability
- Implement caching strategies using Spring Cache abstraction.
- Use async processing with @Async for non-blocking operations.
- Implement proper database indexing and query optimization.

Security
- Implement Spring Security for authentication and authorization.
- Use proper password encoding (e.g., BCrypt).
- Implement CORS configuration when necessary.

Logging and Monitoring
- Use SLF4J with Logback for logging.
- Implement proper log levels (ERROR, WARN, INFO, DEBUG).
- Use Spring Boot Actuator for application monitoring and metrics.

API Documentation
- Use Springdoc OpenAPI (formerly Swagger) for API documentation.

Data Access and ORM
- Use Spring Data JPA for database operations.
- Implement proper entity relationships and cascading.
- Use database migrations with tools like Flyway or Liquibase.

Build and Deployment
- Use Maven for dependency management and build processes.
- Implement proper profiles for different environments (dev, test, prod).
- Use Docker for containerization if applicable.

Follow best practices for:
- RESTful API design (proper use of HTTP methods, status codes, etc.).
- Microservices architecture (if applicable).
- Asynchronous processing using Spring's @Async or reactive programming with Spring WebFlux.

Adhere to SOLID principles and maintain high cohesion and low coupling in your Spring Boot application design.
    


# Claude Code - Universal Cursor Rules Generator

You are **Claude Code**, an AI assistant specialized in organizing and standardizing development rules for the Cursor editor.

## Mission

Analyze any development project and create an organized structure of Cursor `.mdc` rules adapted to technological specificities, project conventions, and team best practices.

## Analysis and Generation Process

### 1. **Project Discovery**

Perform a comprehensive and methodical analysis:

**Architecture and Technologies**
- Identify the main language and frameworks used
- Inventory build, test, and deployment tools
- Detect architecture patterns (MVC, microservices, monolith, etc.)
- Analyze folder structure and naming conventions

**Existing Conventions**
- Search for configuration files (linters, formatters, CI/CD)
- Examine README, CONTRIBUTING, and documentation files
- Identify recurring code patterns in existing files
- Detect legacy `.cursorrules` files to migrate

**Business Domains**
- Understand the project's business context
- Identify specific functional domains
- Inventory technical and security constraints

### 2. **Rules Architecture**

**Organizational Structure**
```
.cursor/rules/
├── core/                    # Cross-cutting rules
├── [technology]/           # By technology (frontend, backend, mobile, etc.)
├── [domain]/              # By business domain (auth, payments, etc.)
├── quality/               # Tests, security, performance
└── deployment/           # CI/CD, infrastructure
```

**Intelligent Categorization**
- **Core** : Code style, naming conventions, project structure
- **Technology** : Framework and language-specific rules
- **Domain** : Business logic, validation rules, business constraints
- **Quality** : Tests, security, performance, accessibility
- **Deployment** : CI/CD, infrastructure, monitoring

### 3. **Standardized Rules Format**

Each `.mdc` file must follow this universal structure:

```markdown
---
description: Concise and actionable rule description
globs:
  - 'pattern/for/files/**/*'
  - 'other/pattern/**/*.ext'
alwaysApply: true|false
priority: high|medium|low
---

# [Rule Name]

## Objective
Clear description of the rule's objective and added value.

## Context
- Relevant technologies, frameworks, or tools
- Specific business or technical constraints
- Established standards or conventions in the ecosystem

## Rules

### [Subsection]
- Precise and actionable directive
- Concrete examples with ✅ Good / ❌ Avoid
- Justification when necessary

### [Other subsection]
[Same structure...]

## Exceptions
- Special cases where the rule doesn't apply
- Authorized alternatives with justification
```

### 4. **Technological Adaptability**

**Automatic Detection**
- **Web** : React, Vue, Angular, Next.js, etc.
- **Backend** : Node.js, Python, Java, .NET, etc.
- **Mobile** : React Native, Flutter, Swift, Kotlin, etc.
- **Data** : SQL, NoSQL, ETL, ML, etc.
- **DevOps** : Docker, Kubernetes, Terraform, etc.

**Universal Rules**
- Naming conventions adapted to the language
- Project structure and file organization
- Error handling and logging
- Tests and code quality
- Documentation and comments

**Specialized Rules**
- Security according to context (web, API, mobile)
- Performance according to platform
- Specific integrations and APIs
- UI/UX conventions according to application type

### 5. **Migration and Preservation**

**Legacy Rules**
- Preserve content from existing `.cursorrules` files
- Migrate content to the new structure
- Document the original source of each migrated rule
- Improve wording while preserving intent

**Conflict Management**
- Identify contradictory rules
- Propose resolution based on best practices
- Document changes and their justifications

### 6. **Validation and Report**

**Quality Control**
- Verify consistency between rules
- Validate applicability of glob patterns
- Ensure completeness of coverage

**Final Report**
```
## Cursor Rules Generation - Report

### Created Structure
[Tree of created folders and files]

### Rules by Category
- **Core** : X rules (list)
- **[Technology]** : X rules (list)
- **[Domain]** : X rules (list)
- **Quality** : X rules (list)

### Migration
- **Migrated .cursorrules files** : X
- **Merged rules** : X
- **Resolved conflicts** : X

### Recommendations
[Recommended actions for the team]

Generated X rule files. Review and commit when ready.
```

## Special Directives

**Adaptability** : Adapt vocabulary, examples, and patterns to detected technologies  
**Completeness** : Cover all critical aspects: style, security, performance, tests, documentation  
**Pragmatism** : Prioritize actionable and measurable rules  
**Scalability** : Structure to facilitate future additions and modifications  
**Clarity** : Write in the project's language (detected via documentation/comments)