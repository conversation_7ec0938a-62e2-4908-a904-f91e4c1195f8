package com.tcm.doctor.constant;

/**
 * 服务常量配置
 * 集中管理各服务配置常量，便于统一维护
 */
public class ServiceConstants {
    // 应用配置
    public static final String SERVICE_NAME = "tcm-doctor";
    public static final String SERVICE_PORT = "9360";
    
    // Nacos配置
    public static final String NACOS_SERVER_ADDR = "***********:8848";
    public static final String NACOS_USERNAME = "nacos";
    public static final String NACOS_PASSWORD = "kaixin207";
    public static final String NACOS_NAMESPACE = "public";
    public static final String NACOS_GROUP = "DEFAULT_GROUP";
    
    // MySQL数据库配置
    public static final String MYSQL_HOST = "**********";
    public static final String MYSQL_PORT = "3306";
    public static final String MYSQL_DATABASE = "tcm-system";
    public static final String MYSQL_USERNAME = "root";
    public static final String MYSQL_PASSWORD = "Kaixin207@1984";
    public static final String MYSQL_URL = "jdbc:mysql://" + MYSQL_HOST + ":" + MYSQL_PORT + "/" + MYSQL_DATABASE + "?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true";
    
    // RocketMQ配置
    public static final String ROCKETMQ_NAME_SERVER = "**********:9876";
    public static final String ROCKETMQ_PRODUCER_GROUP = "doctor-producer-group";
    
    // Redis配置 - 使用统一配置的地址
    public static final String REDIS_HOST;
    
    static {
        // 从环境变量中读取Redis地址，如果不存在则使用默认地址
        String configuredHost = System.getenv("TCM_REDIS_HOST");
        if (configuredHost == null || configuredHost.trim().isEmpty()) {
            configuredHost = "**********"; // 默认地址
        }
        REDIS_HOST = configuredHost;
    }
    public static final String REDIS_PORT = "6379";
    public static final String REDIS_PASSWORD = "kaixin207";
    public static final String REDIS_DATABASE = "0";
    public static final String REDIS_TIMEOUT = "5000";
    
    // 日志配置
    public static final String LOG_LEVEL_ROOT = "WARN";
    public static final String LOG_LEVEL_TCM = "INFO";
    public static final String LOG_PATTERN_CONSOLE = "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx";
}
