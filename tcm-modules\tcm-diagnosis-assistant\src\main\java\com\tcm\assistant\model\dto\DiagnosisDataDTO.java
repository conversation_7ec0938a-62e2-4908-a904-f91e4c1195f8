package com.tcm.assistant.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.List;

/**
 * 诊断数据DTO
 */
@Data
@Schema(description = "诊断数据请求")
public class DiagnosisDataDTO {

    /**
     * 患者ID
     */
    @Schema(description = "患者ID")
    private Long patientId;

    /**
     * 医生ID
     */
    @Schema(description = "医生ID")
    private Long doctorId;

    /**
     * 诊断ID
     */
    @Schema(description = "诊断ID")
    private Long diagnosisId;

    /**
     * 证型列表
     */
    @Schema(description = "中医证型列表")
    private List<String> tcmSyndromes;

    /**
     * 症状列表
     */
    @Schema(description = "症状列表")
    private List<String> symptoms;

    /**
     * 舌象描述
     */
    @Schema(description = "舌象描述")
    private String tongueDescription;

    /**
     * 脉象描述
     */
    @Schema(description = "脉象描述")
    private String pulseDescription;

    /**
     * 问诊信息
     */
    @Schema(description = "问诊信息")
    private String inquiryInfo;
}