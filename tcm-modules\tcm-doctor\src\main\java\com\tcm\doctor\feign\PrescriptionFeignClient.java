package com.tcm.doctor.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 处方服务远程调用接口
 */
@FeignClient(name = "tcm-prescription", fallback = PrescriptionFeignFallback.class)
public interface PrescriptionFeignClient {

    /**
     * 获取医生处方数量
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处方数量
     */
    @GetMapping("/prescription/stat/count")
    Long getPrescriptionCount(@RequestParam("doctorId") Long doctorId,
                              @RequestParam("startDate") LocalDate startDate,
                              @RequestParam("endDate") LocalDate endDate);

    /**
     * 获取医生处方总金额
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处方总金额
     */
    @GetMapping("/prescription/stat/amount")
    BigDecimal getPrescriptionAmount(@RequestParam("doctorId") Long doctorId,
                                     @RequestParam("startDate") LocalDate startDate,
                                     @RequestParam("endDate") LocalDate endDate);
}
