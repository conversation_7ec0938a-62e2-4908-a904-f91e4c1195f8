package com.tcm.workflow.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程任务实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wf_task")
public class Task extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @TableId(value = "task_id", type = IdType.ASSIGN_ID)
    private String taskId;

    /** 任务名称 */
    private String taskName;

    /** 任务定义Key */
    private String taskDefinitionKey;

    /** 流程实例ID */
    private String processInstanceId;

    /** 流程定义ID */
    private String processDefinitionId;

    /** 流程定义KEY */
    private String processDefinitionKey;

    /** 流程定义名称 */
    private String processDefinitionName;

    /** 执行ID */
    private String executionId;

    /** 任务处理人ID */
    private String assignee;

    /** 任务处理人名称 */
    private String assigneeName;

    /** 表单Key */
    private String formKey;

    /** 表单数据 */
    private String formData;

    /** 业务Key */
    private String businessKey;

    /** 父任务ID */
    private String parentTaskId;

    /** 任务创建时间 */
    private Date createTime;

    /** 任务开始时间 */
    private Date startTime;

    /** 任务完成时间 */
    private Date completeTime;

    /** 任务到期时间 */
    private Date dueDate;

    /** 任务优先级 */
    private Integer priority;

    /** 任务类型 */
    private String taskType;

    /** 任务状态：1待处理，2进行中，3已完成，4已取消 */
    private Integer status;

    /** 处理结果 */
    private String result;

    /** 处理意见 */
    private String comment;

    /** 租户ID */
    private String tenantId;
}