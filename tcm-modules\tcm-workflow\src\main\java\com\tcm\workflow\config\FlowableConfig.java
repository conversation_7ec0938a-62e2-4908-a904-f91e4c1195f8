package com.tcm.workflow.config;

import org.flowable.common.engine.impl.persistence.StrongUuidGenerator;
import org.flowable.spring.SpringProcessEngineConfiguration;
import org.flowable.spring.boot.EngineConfigurationConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Flowable工作流配置
 * 
 * <AUTHOR>
 */
@Configuration
public class FlowableConfig implements EngineConfigurationConfigurer<SpringProcessEngineConfiguration> {

    @Override
    public void configure(SpringProcessEngineConfiguration engineConfiguration) {
        // 设置字体
        engineConfiguration.setActivityFontName("宋体");
        engineConfiguration.setLabelFontName("宋体");
        engineConfiguration.setAnnotationFontName("宋体");

        // 设置自动部署资源目录
        engineConfiguration.setDeploymentMode("single-resource");

        // 设置自动创建表
        engineConfiguration.setDatabaseSchemaUpdate("true");

        // 禁用JOB执行器（可根据需要调整）
        engineConfiguration.setAsyncExecutorActivate(false);

        // 启用SQL日志记录（Flowable 6.8.0版本中正确的方法）
        engineConfiguration.setEnableDatabaseEventLogging(false);

        // 添加EventListener（可选）
        // engineConfiguration.addEventListener(new CustomProcessEventListener());

        // 添加自定义表单引擎配置（可选）
        // engineConfiguration.setFormEngineConfigurator(new
        // CustomFormEngineConfigurator());
    }

    /**
     * 自定义ID生成器，使用Flowable自带的UUID生成器
     */
    @Bean
    public StrongUuidGenerator idGenerator() {
        return new StrongUuidGenerator();
    }
}