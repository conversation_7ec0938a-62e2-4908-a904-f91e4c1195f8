package com.tcm.admin.controller;

import com.tcm.admin.dto.DeptDTO;
import com.tcm.admin.service.DeptService;
import com.tcm.admin.vo.DeptVO;
import com.tcm.admin.vo.TreeSelectVO;
import com.tcm.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门管理控制器
 */
@Tag(name = "部门管理接口")
@RestController
@RequestMapping("/dept")
@RequiredArgsConstructor
public class DeptController {

    private final DeptService deptService;

    /**
     * 获取部门列表
     */
    @Operation(summary = "获取部门列表")
    @GetMapping("/list")
    public Result<List<DeptVO>> list(DeptDTO deptDTO) {
        List<DeptVO> depts = deptService.selectDeptList(deptDTO);
        return Result.success(depts);
    }

    /**
     * 查询部门详细信息
     */
    @Operation(summary = "根据部门ID获取详细信息")
    @GetMapping("/{deptId}")
    public Result<DeptVO> getInfo(@Parameter(description = "部门ID") @PathVariable Long deptId) {
        DeptVO dept = deptService.getDeptById(deptId);
        return Result.success(dept);
    }

    /**
     * 获取部门下拉树列表
     */
    @Operation(summary = "获取部门下拉树列表")
    @GetMapping("/treeselect")
    public Result<List<TreeSelectVO>> treeselect(DeptDTO deptDTO) {
        List<DeptVO> depts = deptService.selectDeptList(deptDTO);
        return Result.success(deptService.buildDeptTreeSelect(depts));
    }

    /**
     * 加载对应角色部门列表树
     */
    @Operation(summary = "加载对应角色部门列表树")
    @GetMapping("/roleDeptTreeselect/{roleId}")
    public Result<?> roleDeptTreeselect(@Parameter(description = "角色ID") @PathVariable Long roleId) {
        List<DeptVO> depts = deptService.selectDeptList(new DeptDTO());
        List<TreeSelectVO> treeList = deptService.buildDeptTreeSelect(depts);
        List<Long> checkedKeys = deptService.selectDeptListByRoleId(roleId);

        // 创建包含树和选中键的Map数据结构
        java.util.Map<String, Object> data = new java.util.HashMap<>();
        data.put("treeList", treeList);
        data.put("checkedKeys", checkedKeys);

        return Result.success(data);
    }

    /**
     * 新增部门
     */
    @Operation(summary = "新增部门")
    @PostMapping
    public Result<?> add(@Validated @RequestBody DeptDTO deptDTO) {
        if (!deptService.checkDeptNameUnique(deptDTO)) {
            return Result.error("新增部门'" + deptDTO.getDeptName() + "'失败，部门名称已存在");
        }
        return deptService.insertDept(deptDTO) ? Result.success() : Result.error("新增部门失败");
    }

    /**
     * 修改部门
     */
    @Operation(summary = "修改部门")
    @PutMapping
    public Result<?> edit(@Validated @RequestBody DeptDTO deptDTO) {
        if (!deptService.checkDeptNameUnique(deptDTO)) {
            return Result.error("修改部门'" + deptDTO.getDeptName() + "'失败，部门名称已存在");
        }
        return deptService.updateDept(deptDTO) ? Result.success() : Result.error("修改部门失败");
    }

    /**
     * 删除部门
     */
    @Operation(summary = "删除部门")
    @DeleteMapping("/{deptId}")
    public Result<?> remove(@Parameter(description = "部门ID") @PathVariable Long deptId) {
        return deptService.deleteDeptById(deptId) ? Result.success() : Result.error("删除部门失败");
    }
}