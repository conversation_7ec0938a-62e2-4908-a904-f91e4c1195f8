package com.tcm.schedule.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.domain.PageVO;
import com.tcm.schedule.dto.SysLogOperationDTO;
import com.tcm.schedule.entity.SysLogOperation;
import com.tcm.schedule.mapper.SysLogOperationMapper;
import com.tcm.schedule.service.SysLogOperationService;
import com.tcm.schedule.vo.SysLogOperationVO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 系统操作日志服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysLogOperationServiceImpl extends ServiceImpl<SysLogOperationMapper, SysLogOperation>
        implements SysLogOperationService {

    /**
     * 保存操作日志
     *
     * @param sysLogOperation 操作日志信息
     */
    @Override
    public void saveLog(SysLogOperation sysLogOperation) {
        // 异步保存日志
        save(sysLogOperation);
    }

    /**
     * 查询系统操作日志分页列表
     *
     * @param dto 查询条件
     * @return 分页列表
     */
    @Override
    public PageVO<SysLogOperationVO> selectLogPage(SysLogOperationDTO dto) {
        SysLogOperation sysLogOperation = new SysLogOperation();
        sysLogOperation.setModule(dto.getModule());
        sysLogOperation.setOperateType(dto.getOperateType());
        sysLogOperation.setStatus(dto.getStatus());
        sysLogOperation.setUsername(dto.getUsername());
        sysLogOperation.setRequestUrl(dto.getRequestUrl());

        Page<SysLogOperation> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        IPage<SysLogOperationVO> pageData = baseMapper.selectLogPage(page, sysLogOperation);

        return new PageVO<>(pageData.getRecords(), pageData.getTotal(), dto.getPageSize(), dto.getPageNum());
    }

    /**
     * 查询操作日志详细信息
     *
     * @param id 日志ID
     * @return 操作日志信息
     */
    @Override
    public SysLogOperationVO getLogById(Long id) {
        return baseMapper.selectLogById(id);
    }

    /**
     * 清空操作日志
     *
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cleanLog() {
        baseMapper.cleanLog();
        return true;
    }

    /**
     * 批量删除操作日志
     *
     * @param ids 日志ID数组
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLogByIds(Long[] ids) {
        return baseMapper.deleteLogByIds(ids) > 0;
    }
}
