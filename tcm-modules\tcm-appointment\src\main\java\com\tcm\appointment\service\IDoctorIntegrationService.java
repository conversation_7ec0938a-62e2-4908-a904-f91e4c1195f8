package com.tcm.appointment.service;

import com.tcm.appointment.vo.DoctorScheduleVO;
import com.tcm.appointment.vo.DoctorVO;

import java.util.Date;
import java.util.List;

/**
 * 医生模块集成服务接口
 */
public interface IDoctorIntegrationService {
    
    /**
     * 获取医生信息
     *
     * @param doctorId 医生ID
     * @return 医生信息
     */
    DoctorVO getDoctorInfo(Long doctorId);
    
    /**
     * 获取科室下的所有医生
     *
     * @param departmentId 科室ID
     * @return 医生列表
     */
    List<DoctorVO> getDoctorsByDepartment(Long departmentId);
    
    /**
     * 获取医生排班信息
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 排班信息
     */
    List<DoctorScheduleVO> getDoctorSchedule(Long doctorId, Date date);
    
    /**
     * 更新医生排班信息（如减少可预约数）
     *
     * @param scheduleId    排班ID
     * @param decrementNum  减少数量
     * @return 操作结果
     */
    boolean updateScheduleAppointmentLimit(Long scheduleId, Integer decrementNum);
} 