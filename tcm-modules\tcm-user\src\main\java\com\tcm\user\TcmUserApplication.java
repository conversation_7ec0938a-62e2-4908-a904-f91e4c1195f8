package com.tcm.user;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 用户服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@ComponentScan(basePackages = { "com.tcm.user" })
@MapperScan("com.tcm.user.mapper")
public class TcmUserApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmUserApplication.class);

    public static void main(String[] args) {
        try {
            // 设置默认配置
            System.setProperty("spring.application.name", "tcm-user");
            System.setProperty("server.port", "9313");
            System.setProperty("spring.main.allow-bean-definition-overriding", "true");

            // 启动应用程序
            SpringApplication.run(TcmUserApplication.class, args);

            LOGGER.info("=====================================================");
            LOGGER.info("           TCM User Service Started!         ");
            LOGGER.info("=====================================================");
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}