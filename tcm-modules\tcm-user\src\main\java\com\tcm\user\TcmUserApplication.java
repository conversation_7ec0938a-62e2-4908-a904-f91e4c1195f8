package com.tcm.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 用户服务启动类
 */
@SpringBootApplication(exclude = { ErrorMvcAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration.class })
@EnableDiscoveryClient
@MapperScan("com.tcm.user.mapper")
@ComponentScan(basePackages = { "com.tcm" })
public class TcmUserApplication {
    public static void main(String[] args) {
        SpringApplication.run(TcmUserApplication.class, args);
    }
}