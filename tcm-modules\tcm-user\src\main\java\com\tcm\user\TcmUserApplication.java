package com.tcm.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 用户服务启动类
 */
@SpringBootApplication
@EnableDiscoveryClient
@MapperScan("com.tcm.user.mapper")
@ComponentScan(basePackages = { "com.tcm" })
public class TcmUserApplication {
    public static void main(String[] args) {
        try {
            SpringApplication.run(TcmUserApplication.class, args);
            System.out.println("TCM用户服务启动成功！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}