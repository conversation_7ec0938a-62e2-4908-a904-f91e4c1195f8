package com.tcm.diagnosis.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 诊断建议视图对象
 */
@Data
public class DiagnosisSuggestionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 建议ID
     */
    private Long suggestionId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 建议标题
     */
    private String suggestionTitle;
    
    /**
     * 建议内容
     */
    private String suggestion;
    
    /**
     * 建议内容（用于兼容性）
     */
    private String suggestionContent;

    /**
     * 建议类型（诊断建议/治疗建议/用药建议/注意事项等）
     */
    private String suggestionType;

    /**
     * 建议依据
     */
    private String basis;
    
    /**
     * 状态（0：待处理，1：已采纳，2：已拒绝）
     */
    private Integer status;
    
    /**
     * 是否修改（0：未修改，1：已修改）
     */
    private Integer isModified;

    /**
     * 相似病例列表
     */
    private List<SimilarCaseVO> similarCases;

    /**
     * 关联知识条目
     */
    private List<KnowledgeItemVO> relatedKnowledge;

    /**
     * 关联知识条目（JSON格式）
     */
    private String relatedKnowledgeJson;

    /**
     * 参考文献
     */
    private List<String> references;
    
    /**
     * 参考来源
     */
    private String referencesText;

    /**
     * 生成方法（AI/知识库/医生）
     */
    private String generationMethod;

    /**
     * 生成时间
     */
    private Date generationTime;

    /**
     * 置信度（0-100）
     */
    private Integer confidence;

    /**
     * 是否医生采纳（0：未采纳，1：已采纳）
     */
    private Integer doctorAdopted;

    /**
     * 采纳时间
     */
    private Date adoptTime;
    
    /**
     * 采纳人
     */
    private String adoptBy;
    
    /**
     * 拒绝时间
     */
    private Date rejectTime;
    
    /**
     * 拒绝人
     */
    private String rejectBy;
    
    /**
     * 拒绝原因
     */
    private String rejectReason;
    
    /**
     * 创建时间
     */
    private Date createTime;
}