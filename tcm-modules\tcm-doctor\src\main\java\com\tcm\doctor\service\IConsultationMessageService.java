package com.tcm.doctor.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.doctor.domain.ConsultationMessage;
import com.tcm.doctor.dto.ConsultationMessageDTO;
import com.tcm.doctor.vo.ConsultationMessageVO;

import java.util.List;

/**
 * 问诊消息服务接口
 */
public interface IConsultationMessageService extends IService<ConsultationMessage> {
    
    /**
     * 查询问诊消息列表
     *
     * @param consultationId 问诊ID
     * @return 消息列表
     */
    List<ConsultationMessageVO> listMessages(Long consultationId);
    
    /**
     * 根据ID查询消息详情
     *
     * @param messageId 消息ID
     * @return 消息详情
     */
    ConsultationMessageVO getMessageById(Long messageId);
    
    /**
     * 发送消息
     *
     * @param messageDTO 消息信息
     * @return 结果
     */
    boolean sendMessage(ConsultationMessageDTO messageDTO);
    
    /**
     * 发送系统消息
     *
     * @param consultationId 问诊ID
     * @param content        消息内容
     * @return 结果
     */
    boolean sendSystemMessage(Long consultationId, String content);
    
    /**
     * 更新消息已读状态
     *
     * @param messageId 消息ID
     * @return 结果
     */
    boolean readMessage(Long messageId);
    
    /**
     * 批量更新消息已读状态
     *
     * @param consultationId 问诊ID
     * @param receiverId     接收者ID
     * @return 结果
     */
    boolean readAllMessages(Long consultationId, Long receiverId);
    
    /**
     * 撤回消息
     *
     * @param messageId 消息ID
     * @param senderId  发送者ID
     * @return 结果
     */
    boolean recallMessage(Long messageId, Long senderId);
    
    /**
     * 统计未读消息数
     *
     * @param consultationId 问诊ID
     * @param receiverId     接收者ID
     * @return 未读消息数
     */
    int countUnreadMessages(Long consultationId, Long receiverId);
} 