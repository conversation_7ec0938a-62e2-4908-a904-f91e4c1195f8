<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tcm-modules</artifactId>
        <groupId>com.tcm</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tcm-report</artifactId>

    <description>
        tcm-report 报表模块
    </description>

    <dependencies>
        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringBoot 拦截器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- 阿里数据库连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>

        <!-- 验证码 -->
        <dependency>
            <groupId>pro.fessional</groupId>
            <artifactId>kaptcha</artifactId>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- TCM Common -->
        <dependency>
            <groupId>com.tcm</groupId>
            <artifactId>tcm-common-core</artifactId>
        </dependency>

        <!-- TCM Common DataSource -->
        <dependency>
            <groupId>com.tcm</groupId>
            <artifactId>tcm-common-datasource</artifactId>
        </dependency>

        <!-- TCM Common Log -->
        <dependency>
            <groupId>com.tcm</groupId>
            <artifactId>tcm-common-log</artifactId>
        </dependency>

        <!-- TCM Common Swagger -->
        <dependency>
            <groupId>com.tcm</groupId>
            <artifactId>tcm-common-swagger</artifactId>
        </dependency>

        <!-- TCM Common Security -->
        <dependency>
            <groupId>com.tcm</groupId>
            <artifactId>tcm-common-security</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                    <mainClass>com.tcm.report.TcmReportApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
