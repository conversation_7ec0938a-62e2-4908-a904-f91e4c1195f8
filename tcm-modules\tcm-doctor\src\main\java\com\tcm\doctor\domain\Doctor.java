package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医生实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_doctor")
public class Doctor extends BaseEntity {
    
    /**
     * 医生ID
     */
    @TableId
    private Long doctorId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生编号
     */
    private String doctorCode;
    
    /**
     * 职称 (1-主任医师 2-副主任医师 3-主治医师 4-住院医师)
     */
    private Integer title;
    
    /**
     * 专长
     */
    private String specialty;
    
    /**
     * 简介
     */
    private String introduction;
    
    /**
     * 执业证书编号
     */
    private String licenseNo;
    
    /**
     * 所属医院
     */
    private String hospital;
    
    /**
     * 所属科室
     */
    private String department;
    
    /**
     * 工作年限
     */
    private Integer workYears;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 状态（0正常 1停用）
     */
    private Integer status;
} 