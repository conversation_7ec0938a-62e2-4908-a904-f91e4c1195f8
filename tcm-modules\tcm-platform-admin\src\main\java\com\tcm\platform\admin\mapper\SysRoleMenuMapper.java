package com.tcm.platform.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.platform.admin.entity.SysRoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色菜单关联Mapper接口
 * 
 * <AUTHOR>
 */
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {

    /**
     * 批量新增角色菜单关联
     * 
     * @param roleMenus 角色菜单列表
     * @return 结果
     */
    int batchInsert(@Param("list") List<SysRoleMenu> roleMenus);

    /**
     * 通过角色ID删除角色菜单关联
     * 
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 通过菜单ID删除角色菜单关联
     * 
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteByMenuId(@Param("menuId") Long menuId);
}