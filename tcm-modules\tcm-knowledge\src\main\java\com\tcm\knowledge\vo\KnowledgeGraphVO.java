package com.tcm.knowledge.vo;

import lombok.Data;
import java.util.List;

/**
 * 知识图谱VO
 */
@Data
public class KnowledgeGraphVO {
    /**
     * 节点列表
     */
    private List<NodeVO> nodes;
    
    /**
     * 关系列表
     */
    private List<RelationVO> relations;
    
    /**
     * 节点VO
     */
    @Data
    public static class NodeVO {
        /**
         * 节点ID
         */
        private String id;
        
        /**
         * 节点名称
         */
        private String name;
        
        /**
         * 节点类型（疾病、症状、方剂、药物等）
         */
        private String type;
        
        /**
         * 节点属性
         */
        private List<AttributeVO> attributes;
    }
    
    /**
     * 关系VO
     */
    @Data
    public static class RelationVO {
        /**
         * 关系ID
         */
        private String id;
        
        /**
         * 源节点ID
         */
        private String source;
        
        /**
         * 目标节点ID
         */
        private String target;
        
        /**
         * 关系类型
         */
        private String type;
        
        /**
         * 关系描述
         */
        private String description;
    }
    
    /**
     * 属性VO
     */
    @Data
    public static class AttributeVO {
        /**
         * 属性名
         */
        private String name;
        
        /**
         * 属性值
         */
        private String value;
    }
}
