package com.tcm.knowledge.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 医案VO
 */
@Data
public class MedicalCaseVO {
    /**
     * 医案ID
     */
    private Long id;
    
    /**
     * 医案标题
     */
    private String title;
    
    /**
     * 医生名称
     */
    private String doctorName;
    
    /**
     * 病患情况
     */
    private String patientInfo;
    
    /**
     * 主诉
     */
    private String chiefComplaint;
    
    /**
     * 望诊结果
     */
    private String inspection;
    
    /**
     * 闻诊结果
     */
    private String auscultation;
    
    /**
     * 问诊结果
     */
    private String interrogation;
    
    /**
     * 切诊结果
     */
    private String palpation;
    
    /**
     * 辨证论治
     */
    private String dialecticalTreatment;
    
    /**
     * 处方
     */
    private String prescription;
    
    /**
     * 效果评价
     */
    private String evaluation;
    
    /**
     * 标签
     */
    private List<String> tags;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
