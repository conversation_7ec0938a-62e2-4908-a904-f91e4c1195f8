package com.tcm.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.integration.dto.SystemIntegrationConfigDTO;
import com.tcm.integration.entity.SystemIntegrationConfig;
import com.tcm.integration.mapper.SystemIntegrationConfigMapper;
import com.tcm.integration.service.SystemIntegrationConfigService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;

/**
 * 系统集成配置服务实现类
 *
 * <AUTHOR>
 */
@Service
public class SystemIntegrationConfigServiceImpl extends
        ServiceImpl<SystemIntegrationConfigMapper, SystemIntegrationConfig> implements SystemIntegrationConfigService {

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public SystemIntegrationConfig getById(Long id) {
        return super.getById(id);
    }

    @Override
    public IPage<SystemIntegrationConfig> page(int page, int pageSize, String systemName, String systemCode,
            String systemType) {
        LambdaQueryWrapper<SystemIntegrationConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.hasText(systemName)) {
            queryWrapper.like(SystemIntegrationConfig::getSystemName, systemName);
        }
        if (StringUtils.hasText(systemCode)) {
            queryWrapper.eq(SystemIntegrationConfig::getSystemCode, systemCode);
        }
        if (StringUtils.hasText(systemType)) {
            queryWrapper.eq(SystemIntegrationConfig::getSystemType, systemType);
        }
        queryWrapper.orderByDesc(SystemIntegrationConfig::getCreateTime);
        return page(new Page<>(page, pageSize), queryWrapper);
    }

    @Override
    public SystemIntegrationConfig getBySystemCode(String systemCode) {
        return baseMapper.selectBySystemCode(systemCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(SystemIntegrationConfigDTO dto) {
        // 检查系统编码是否已存在
        LambdaQueryWrapper<SystemIntegrationConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemIntegrationConfig::getSystemCode, dto.getSystemCode());
        if (count(queryWrapper) > 0) {
            throw new RuntimeException("系统编码已存在");
        }

        SystemIntegrationConfig config = new SystemIntegrationConfig();
        BeanUtils.copyProperties(dto, config);

        // 设置默认值
        if (config.getConnectTimeout() == null) {
            config.setConnectTimeout(5000); // 默认5秒连接超时
        }
        if (config.getReadTimeout() == null) {
            config.setReadTimeout(10000); // 默认10秒读取超时
        }

        config.setCreateTime(LocalDateTime.now());
        return save(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(SystemIntegrationConfigDTO dto) {
        if (dto.getId() == null) {
            throw new RuntimeException("ID不能为空");
        }

        // 检查是否存在
        SystemIntegrationConfig existConfig = getById(dto.getId());
        if (existConfig == null) {
            throw new RuntimeException("系统配置不存在");
        }

        // 检查系统编码是否已被其他记录使用
        LambdaQueryWrapper<SystemIntegrationConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemIntegrationConfig::getSystemCode, dto.getSystemCode())
                .ne(SystemIntegrationConfig::getId, dto.getId());
        if (count(queryWrapper) > 0) {
            throw new RuntimeException("系统编码已被其他记录使用");
        }

        SystemIntegrationConfig config = new SystemIntegrationConfig();
        BeanUtils.copyProperties(dto, config);
        config.setUpdateTime(LocalDateTime.now());

        // 如果密码字段为空，保留原密码
        if (!StringUtils.hasText(config.getPassword())) {
            config.setPassword(existConfig.getPassword());
        }

        return updateById(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(Long id) {
        // 检查是否存在
        if (getById(id) == null) {
            throw new RuntimeException("系统配置不存在");
        }
        return removeById(id);
    }

    @Override
    public boolean enable(Long id) {
        SystemIntegrationConfig config = getById(id);
        if (config == null) {
            throw new RuntimeException("系统配置不存在");
        }
        config.setStatus(1);
        config.setUpdateTime(LocalDateTime.now());
        return updateById(config);
    }

    @Override
    public boolean disable(Long id) {
        SystemIntegrationConfig config = getById(id);
        if (config == null) {
            throw new RuntimeException("系统配置不存在");
        }
        config.setStatus(0);
        config.setUpdateTime(LocalDateTime.now());
        return updateById(config);
    }

    @Override
    public boolean testConnection(Long id) {
        SystemIntegrationConfig config = getById(id);
        if (config == null) {
            throw new RuntimeException("系统配置不存在");
        }

        try {
            // 简单的连接测试，使用GET请求访问系统API地址
            ResponseEntity<String> response = restTemplate.getForEntity(
                    config.getApiUrl(), String.class);

            // 返回2xx状态码视为连接成功
            return response.getStatusCode().is2xxSuccessful();
        } catch (Exception e) {
            // 连接失败
            return false;
        }
    }
}