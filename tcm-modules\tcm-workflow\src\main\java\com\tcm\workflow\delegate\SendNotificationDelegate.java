package com.tcm.workflow.delegate;

import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.JavaDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 发送通知的服务任务实现
 * 
 * <AUTHOR>
 */
@Component
public class SendNotificationDelegate implements JavaDelegate {

    private static final Logger LOGGER = LoggerFactory.getLogger(SendNotificationDelegate.class);

    @Override
    public void execute(DelegateExecution execution) {
        // 获取流程变量
        String assignee = (String) execution.getVariable("assignee");
        String taskId = execution.getCurrentActivityId();
        String processInstanceId = execution.getProcessInstanceId();

        // 记录日志
        LOGGER.info("Sending notification for task [{}] to user [{}] in process instance [{}]", taskId, assignee,
                processInstanceId);

        // 实际通知逻辑（这里可以集成短信、邮件等通知服务）
        // 模拟通知发送
        boolean success = sendNotification(assignee, taskId, processInstanceId);

        // 设置结果变量
        execution.setVariable("notificationSent", success);
    }

    /**
     * 发送通知的实际实现
     * 
     * @param assignee          处理人
     * @param taskId            任务ID
     * @param processInstanceId 流程实例ID
     * @return 是否发送成功
     */
    private boolean sendNotification(String assignee, String taskId, String processInstanceId) {
        // 模拟通知发送，实际项目中可以调用消息服务、邮件服务等
        LOGGER.info("Message to [{}]: You have a new task [{}] in process [{}]", assignee, taskId, processInstanceId);
        return true;
    }
}