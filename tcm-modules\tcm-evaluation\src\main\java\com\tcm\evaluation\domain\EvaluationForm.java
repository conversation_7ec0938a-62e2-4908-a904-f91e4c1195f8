package com.tcm.evaluation.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;

import lombok.Data;

/**
 * 评估表单实体
 */
@Data
@TableName(value = "eval_form", autoResultMap = true)
public class EvaluationForm {
    
    /**
     * 表单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 表单名称
     */
    private String name;
    
    /**
     * 表单类型
     */
    private String type;
    
    /**
     * 评估类型
     */
    private String evaluationType;
    
    /**
     * 表单描述
     */
    private String description;
    
    /**
     * 表单结构，JSON格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object formStructure;
    
    /**
     * 表单模板，JSON格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object formTemplate;
    
    /**
     * 评分规则，JSON格式
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object scoringRules;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 创建者
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新者
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 删除标志（0代表未删除，1代表已删除）
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 版本号
     */
    @Version
    private Integer version;
} 