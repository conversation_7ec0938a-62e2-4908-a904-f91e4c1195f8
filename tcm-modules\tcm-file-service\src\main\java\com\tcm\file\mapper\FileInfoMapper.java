package com.tcm.file.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.file.domain.FileInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件信息数据访问层
 */
@Mapper
public interface FileInfoMapper extends BaseMapper<FileInfo> {

    /**
     * 根据业务ID查询文件列表
     *
     * @param businessId 业务ID
     * @param module     业务模块
     * @return 文件列表
     */
    List<FileInfo> selectByBusinessId(@Param("businessId") String businessId, @Param("module") String module);

    /**
     * 根据MD5值查询文件信息
     *
     * @param md5 文件MD5值
     * @return 文件信息
     */
    FileInfo selectByMd5(@Param("md5") String md5);

    /**
     * 批量更新文件状态
     *
     * @param fileIds 文件ID列表
     * @param status  状态值
     * @return 更新行数
     */
    int updateStatusBatch(@Param("fileIds") List<Long> fileIds, @Param("status") Integer status);

    /**
     * 根据业务ID删除文件
     *
     * @param businessId 业务ID
     * @param module     业务模块
     * @return 删除行数
     */
    int deleteByBusinessId(@Param("businessId") String businessId, @Param("module") String module);
} 