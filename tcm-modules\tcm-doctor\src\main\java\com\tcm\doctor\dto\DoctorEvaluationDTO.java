package com.tcm.doctor.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 医生评价数据传输对象
 */
@Data
public class DoctorEvaluationDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 评价ID
     */
    private Long evaluationId;
    
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;
    
    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;
    
    /**
     * 诊断记录ID
     */
    @NotNull(message = "诊断记录ID不能为空")
    private Long diagnosisId;
    
    /**
     * 评分（1-5分）
     */
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最小为1分")
    @Max(value = 5, message = "评分最大为5分")
    private Integer score;
    
    /**
     * 评价内容
     */
    @NotNull(message = "评价内容不能为空")
    @Size(min = 5, max = 500, message = "评价内容长度在5-500个字符之间")
    private String content;
    
    /**
     * 是否匿名（0-否 1-是）
     */
    private Integer anonymous;
    
    /**
     * 医生回复
     */
    @Size(max = 500, message = "回复内容长度不能超过500个字符")
    private String reply;
} 