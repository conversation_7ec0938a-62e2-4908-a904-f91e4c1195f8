# 开发环境配置
tcm:
  service:
    # Nacos配置
    nacos:
      host: localhost
      port: 8848
      username: nacos
      password: nacos
      namespace: 
    # MySQL配置
    mysql:
      host: localhost
      port: 3306
      database: tcm
      username: root
      password: root
    # Redis配置
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
    # RocketMQ配置
    rocketMq:
      host: localhost
      port: 9876
    # 服务端口
    inspection:
      port: 9308

# 日志配置
logging:
  level:
    root: info
    com.tcm: debug
    org.springframework.cloud.alibaba.nacos: debug

# 开启Actuator端点
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      enabled: true
      show-details: always

# Nacos服务发现配置
spring:
  cloud:
    nacos:
      discovery:
        enabled: true
        register-enabled: true
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        # 服务注册IP
        ip: 127.0.0.1
        # 心跳间隔
        heart-beat-interval: 5000
        # 心跳超时
        heart-beat-timeout: 15000
        # 元数据
        metadata:
          preserved.heart.beat.interval: 5000
          preserved.heart.beat.timeout: 15000
          preserved.ip.delete.timeout: 15000 