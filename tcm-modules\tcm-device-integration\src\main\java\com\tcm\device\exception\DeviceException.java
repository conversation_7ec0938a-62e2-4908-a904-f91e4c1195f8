package com.tcm.device.exception;

/**
 * 设备异常类
 * 
 * <AUTHOR>
 */
public class DeviceException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    private Integer code;
    private String message;

    public DeviceException(String message) {
        this.message = message;
    }

    public DeviceException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    public DeviceException(String message, Throwable e) {
        super(message, e);
        this.message = message;
    }

    public DeviceException(String message, Integer code, Throwable e) {
        super(message, e);
        this.message = message;
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }
}