package com.tcm.consultation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.consultation.domain.ConsultationParticipant;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 远程会诊参与人Mapper接口
 */
public interface ConsultationParticipantMapper extends BaseMapper<ConsultationParticipant> {

    /**
     * 根据会诊ID和用户ID查询参与者记录
     */
    @Select("SELECT * FROM tcm_consultation_participant WHERE consultation_id = #{consultationId} AND doctor_id = #{userId} LIMIT 1")
    ConsultationParticipant selectByConsultationAndUser(@Param("consultationId") Long consultationId,
            @Param("userId") Long userId);

    /**
     * 根据用户ID查询医生姓名
     */
    @Select("SELECT d.doctor_name FROM tcm_doctor d WHERE d.user_id = #{userId} LIMIT 1")
    String selectDoctorNameByUserId(@Param("userId") Long userId);
}