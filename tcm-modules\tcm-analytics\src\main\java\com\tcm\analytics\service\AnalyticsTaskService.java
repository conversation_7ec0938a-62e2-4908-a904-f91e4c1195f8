package com.tcm.analytics.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.analytics.entity.AnalyticsTask;

/**
 * 分析任务服务接口
 */
public interface AnalyticsTaskService extends IService<AnalyticsTask> {

    /**
     * 创建分析任务
     * 
     * @param task 任务信息
     * @return 创建的任务
     */
    AnalyticsTask createTask(AnalyticsTask task);

    /**
     * 取消分析任务
     * 
     * @param id 任务ID
     * @return 是否成功
     */
    boolean cancelTask(Long id);

    /**
     * 获取任务状态
     * 
     * @param id 任务ID
     * @return 任务状态
     */
    String getTaskStatus(Long id);

    /**
     * 获取任务结果
     * 
     * @param id 任务ID
     * @return 任务结果
     */
    Map<String, Object> getTaskResult(Long id);

    /**
     * 获取用户所有任务
     * 
     * @param userId 用户ID
     * @return 任务列表
     */
    List<AnalyticsTask> getUserTasks(Long userId);

    /**
     * 获取任务详情
     * 
     * @param taskId 任务ID
     * @return 任务详情
     */
    AnalyticsTask getTaskDetail(Long taskId);

    /**
     * 分页查询任务列表
     * 
     * @param page   分页参数
     * @param params 查询参数
     * @return 任务列表
     */
    Page<AnalyticsTask> listTasksByPage(Page<AnalyticsTask> page, Map<String, Object> params);

    /**
     * 根据类型获取任务列表
     * 
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<AnalyticsTask> listTasksByType(Integer taskType);

    /**
     * 根据创建者获取任务列表
     * 
     * @param creator 创建者
     * @return 任务列表
     */
    List<AnalyticsTask> listTasksByCreator(String creator);

    /**
     * 删除任务
     * 
     * @param taskId 任务ID
     * @return 删除结果
     */
    boolean deleteTask(Long taskId);
}