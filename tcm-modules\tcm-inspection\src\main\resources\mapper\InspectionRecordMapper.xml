<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.inspection.mapper.InspectionRecordMapper">

    <!-- 检查记录结果映射 -->
    <resultMap id="RecordResult" type="com.tcm.inspection.domain.InspectionRecord">
        <id property="recordId" column="record_id"/>
        <result property="inspectionNo" column="inspection_no"/>
        <result property="patientId" column="patient_id"/>
        <result property="itemId" column="item_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="applyTime" column="apply_time"/>
        <result property="executeTime" column="execute_time"/>
        <result property="result" column="result"/>
        <result property="conclusion" column="conclusion"/>
        <result property="filePaths" column="file_paths"/>
        <result property="status" column="status"/>
        <result property="executorId" column="executor_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <!-- 关联字段 -->
        <result property="patientName" column="patient_name"/>
        <result property="itemName" column="item_name"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="deptName" column="dept_name"/>
        <result property="executorName" column="executor_name"/>
    </resultMap>
    
    <!-- 通用查询结果列 -->
    <sql id="selectRecordVo">
        select 
            r.record_id, r.inspection_no, r.patient_id, r.item_id, r.doctor_id, r.dept_id, r.apply_time,
            r.execute_time, r.result, r.conclusion, r.file_paths, r.status, r.executor_id,
            r.create_by, r.create_time, r.update_by, r.update_time, r.remark, r.del_flag,
            i.item_name, d.dept_name
        from inspection_record r
        left join inspection_item i on r.item_id = i.item_id
        left join sys_dept d on r.dept_id = d.dept_id
    </sql>
    
    <!-- 查询检查记录列表 -->
    <select id="selectInspectionRecordList" parameterType="com.tcm.inspection.domain.InspectionRecord" resultMap="RecordResult">
        <include refid="selectRecordVo"/>
        <where>
            r.del_flag = 0
            <if test="inspectionNo != null and inspectionNo != ''">
                AND r.inspection_no like concat('%', #{inspectionNo}, '%')
            </if>
            <if test="patientId != null">
                AND r.patient_id = #{patientId}
            </if>
            <if test="itemId != null">
                AND r.item_id = #{itemId}
            </if>
            <if test="doctorId != null">
                AND r.doctor_id = #{doctorId}
            </if>
            <if test="deptId != null">
                AND r.dept_id = #{deptId}
            </if>
            <if test="status != null">
                AND r.status = #{status}
            </if>
            <if test="applyTime != null">
                AND r.apply_time &gt;= #{applyTime}
            </if>
            <if test="executeTime != null">
                AND r.execute_time &gt;= #{executeTime}
            </if>
        </where>
        order by r.create_time desc
    </select>
    
    <!-- 分页查询检查记录列表 -->
    <select id="selectInspectionRecordPage" resultMap="RecordResult">
        <include refid="selectRecordVo"/>
        <where>
            r.del_flag = 0
            <if test="record.inspectionNo != null and record.inspectionNo != ''">
                AND r.inspection_no like concat('%', #{record.inspectionNo}, '%')
            </if>
            <if test="record.patientId != null">
                AND r.patient_id = #{record.patientId}
            </if>
            <if test="record.itemId != null">
                AND r.item_id = #{record.itemId}
            </if>
            <if test="record.doctorId != null">
                AND r.doctor_id = #{record.doctorId}
            </if>
            <if test="record.deptId != null">
                AND r.dept_id = #{record.deptId}
            </if>
            <if test="record.status != null">
                AND r.status = #{record.status}
            </if>
            <if test="record.applyTime != null">
                AND r.apply_time &gt;= #{record.applyTime}
            </if>
            <if test="record.executeTime != null">
                AND r.execute_time &gt;= #{record.executeTime}
            </if>
        </where>
        order by r.create_time desc
    </select>
    
    <!-- 根据ID查询检查记录 -->
    <select id="selectInspectionRecordById" parameterType="Long" resultMap="RecordResult">
        <include refid="selectRecordVo"/>
        where r.record_id = #{recordId} and r.del_flag = 0
    </select>
    
    <!-- 根据检查单号查询检查记录 -->
    <select id="selectInspectionRecordByInspectionNo" parameterType="String" resultMap="RecordResult">
        <include refid="selectRecordVo"/>
        where r.inspection_no = #{inspectionNo} and r.del_flag = 0
    </select>
    
    <!-- 根据患者ID查询检查记录列表 -->
    <select id="selectInspectionRecordByPatientId" parameterType="Long" resultMap="RecordResult">
        <include refid="selectRecordVo"/>
        where r.patient_id = #{patientId} and r.del_flag = 0
        order by r.create_time desc
    </select>
</mapper> 