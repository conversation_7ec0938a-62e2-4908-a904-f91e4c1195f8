<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.platform.admin.mapper.SysUserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="UserResultMap" type="com.tcm.platform.admin.entity.SysUser">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="realName" column="real_name"/>
        <result property="mobile" column="mobile"/>
        <result property="email" column="email"/>
        <result property="gender" column="gender"/>
        <result property="avatar" column="avatar"/>
        <result property="deptId" column="dept_id"/>
        <result property="status" column="status"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 查询字段 -->
    <sql id="selectUserVo">
        select u.id, u.username, u.password, u.real_name, u.mobile, u.email, u.gender, u.avatar,
        u.dept_id, u.status, u.last_login_ip, u.last_login_time, u.remark, u.create_by, u.create_time,
        u.update_by, u.update_time, u.del_flag, d.dept_name
        from sys_user u
        left join sys_dept d on u.dept_id = d.id
    </sql>

    <!-- 分页查询用户列表 -->
    <select id="selectUserPage" resultMap="UserResultMap">
        <include refid="selectUserVo"/>
        where u.del_flag = 0
        <if test="user.username != null and user.username != ''">
            AND u.username like concat('%', #{user.username}, '%')
        </if>
        <if test="user.realName != null and user.realName != ''">
            AND u.real_name like concat('%', #{user.realName}, '%')
        </if>
        <if test="user.status != null">
            AND u.status = #{user.status}
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            AND u.mobile like concat('%', #{user.mobile}, '%')
        </if>
        <if test="user.deptId != null">
            AND (u.dept_id = #{user.deptId} OR u.dept_id IN (
                select t.id from sys_dept t where find_in_set(#{user.deptId}, t.ancestors)
            ))
        </if>
        order by u.create_time desc
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="String" resultMap="UserResultMap">
        <include refid="selectUserVo"/>
        where u.username = #{username} and u.del_flag = 0
        limit 1
    </select>

    <!-- 更新用户状态 -->
    <update id="updateStatus">
        update sys_user set status = #{status}, update_time = now() where id = #{userId}
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        update sys_user set password = #{password}, update_time = now() where id = #{userId}
    </update>
</mapper> 