package com.tcm.doctor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.doctor.domain.DoctorEvaluation;
import com.tcm.doctor.dto.DoctorEvaluationDTO;
import com.tcm.doctor.mapper.DoctorEvaluationMapper;
import com.tcm.doctor.service.IDoctorEvaluationService;
import com.tcm.doctor.vo.DoctorEvaluationVO;
import com.tcm.doctor.vo.DoctorWorkloadVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 医生评价服务实现类
 */
@Service
public class DoctorEvaluationServiceImpl extends ServiceImpl<DoctorEvaluationMapper, DoctorEvaluation> implements IDoctorEvaluationService {

    @Autowired
    private DoctorEvaluationMapper doctorEvaluationMapper;

    @Override
    public IPage<DoctorEvaluationVO> listDoctorEvaluations(Long doctorId, int pageNum, int pageSize) {
        Page<DoctorEvaluation> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<DoctorEvaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoctorEvaluation::getDoctorId, doctorId)
                    .orderByDesc(DoctorEvaluation::getCreateTime);
        
        IPage<DoctorEvaluation> evaluationPage = page(page, queryWrapper);
        
        return evaluationPage.convert(evaluation -> {
            DoctorEvaluationVO vo = new DoctorEvaluationVO();
            BeanUtils.copyProperties(evaluation, vo);
            return vo;
        });
    }

    @Override
    public DoctorEvaluationVO getDoctorEvaluationById(Long evaluationId) {
        DoctorEvaluation evaluation = getById(evaluationId);
        if (evaluation == null) {
            return null;
        }
        
        DoctorEvaluationVO vo = new DoctorEvaluationVO();
        BeanUtils.copyProperties(evaluation, vo);
        return vo;
    }

    @Override
    public DoctorEvaluationVO getDoctorEvaluationByDiagnosisId(Long diagnosisId) {
        LambdaQueryWrapper<DoctorEvaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoctorEvaluation::getDiagnosisId, diagnosisId);
        
        DoctorEvaluation evaluation = getOne(queryWrapper);
        if (evaluation == null) {
            return null;
        }
        
        DoctorEvaluationVO vo = new DoctorEvaluationVO();
        BeanUtils.copyProperties(evaluation, vo);
        return vo;
    }

    @Override
    public Double calculateDoctorAverageScore(Long doctorId) {
        LambdaQueryWrapper<DoctorEvaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoctorEvaluation::getDoctorId, doctorId)
                    .select(DoctorEvaluation::getScore);
        
        List<DoctorEvaluation> evaluations = list(queryWrapper);
        if (evaluations.isEmpty()) {
            return 0.0;
        }
        
        double sum = evaluations.stream()
                               .mapToDouble(evaluation -> evaluation.getScore())
                               .sum();
        
        return sum / evaluations.size();
    }
    
    @Override
    public BigDecimal getAverageDoctorRating(Long doctorId, LocalDate startDate, LocalDate endDate) {
        // 将LocalDate转换为Date以适配旧代码
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        LambdaQueryWrapper<DoctorEvaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoctorEvaluation::getDoctorId, doctorId)
                    .ge(DoctorEvaluation::getCreateTime, startDateTime)
                    .lt(DoctorEvaluation::getCreateTime, endDateTime)
                    .select(DoctorEvaluation::getScore);
        
        List<DoctorEvaluation> evaluations = list(queryWrapper);
        if (evaluations.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        double sum = evaluations.stream()
                              .mapToDouble(evaluation -> evaluation.getScore())
                              .sum();
        
        BigDecimal avgRating = new BigDecimal(sum / evaluations.size());
        return avgRating.setScale(1, RoundingMode.HALF_UP);
    }

    @Override
    public Integer countDoctorEvaluation(Long doctorId) {
        LambdaQueryWrapper<DoctorEvaluation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoctorEvaluation::getDoctorId, doctorId);
        
        // 将long转为int再装箱为Integer
        return Math.toIntExact(count(queryWrapper));
    }

    @Override
    public boolean addDoctorEvaluation(DoctorEvaluationDTO evaluationDTO) {
        DoctorEvaluation evaluation = new DoctorEvaluation();
        BeanUtils.copyProperties(evaluationDTO, evaluation);
        evaluation.setCreateTime(new Date());
        
        return save(evaluation);
    }

    @Override
    public boolean replyDoctorEvaluation(Long evaluationId, String reply) {
        DoctorEvaluation evaluation = getById(evaluationId);
        if (evaluation == null) {
            return false;
        }
        
        evaluation.setReply(reply);
        // 医生评价实体中没有setReplyTime方法，移除此行
        // 如果需要记录回复时间，可以在DoctorEvaluation类中添加replyTime字段
        
        return updateById(evaluation);
    }

    @Override
    public boolean updateDoctorEvaluationStatus(Long evaluationId, Integer status) {
        DoctorEvaluation evaluation = getById(evaluationId);
        if (evaluation == null) {
            return false;
        }
        
        evaluation.setStatus(status);
        
        return updateById(evaluation);
    }

    @Override
    public List<DoctorWorkloadVO> getDoctorWorkloadList(Date beginDate, Date endDate) {
        // 这里需要实现从数据库查询医生工作量统计的逻辑
        // 暂时返回空列表
        return new ArrayList<>();
    }
}
