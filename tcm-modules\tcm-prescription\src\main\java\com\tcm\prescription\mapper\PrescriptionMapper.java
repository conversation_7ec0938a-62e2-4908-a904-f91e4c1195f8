package com.tcm.prescription.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.prescription.domain.Prescription;
import com.tcm.prescription.dto.PrescriptionQuery;
import com.tcm.prescription.vo.PrescriptionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 处方Mapper接口
 */
@Mapper
public interface PrescriptionMapper extends BaseMapper<Prescription> {
    
    /**
     * 查询处方列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 处方列表
     */
    IPage<PrescriptionVO> selectPrescriptionList(Page<Prescription> page, @Param("query") PrescriptionQuery query);
    
    /**
     * 根据ID查询处方详情
     *
     * @param prescriptionId 处方ID
     * @return 处方详情
     */
    PrescriptionVO selectPrescriptionById(@Param("prescriptionId") Long prescriptionId);
    
    /**
     * 根据诊断记录ID查询处方列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 处方列表
     */
    List<PrescriptionVO> selectPrescriptionByDiagnosisId(@Param("diagnosisId") Long diagnosisId);
    
    /**
     * 根据患者ID查询处方列表
     *
     * @param patientId 患者ID
     * @return 处方列表
     */
    List<PrescriptionVO> selectPrescriptionByPatientId(@Param("patientId") Long patientId);
    
    /**
     * 根据医生ID查询处方列表
     *
     * @param doctorId 医生ID
     * @return 处方列表
     */
    List<PrescriptionVO> selectPrescriptionByDoctorId(@Param("doctorId") Long doctorId);
    
    /**
     * 统计医生处方数量
     *
     * @param params 参数Map，包含doctorId、startDate、endDate
     * @return 处方数量
     */
    Long countPrescriptionsByDoctor(Map<String, Object> params);
    
    /**
     * 统计医生处方金额总和
     *
     * @param params 参数Map，包含doctorId、startDate、endDate
     * @return 处方金额总和
     */
    BigDecimal sumPrescriptionAmountByDoctor(Map<String, Object> params);
} 