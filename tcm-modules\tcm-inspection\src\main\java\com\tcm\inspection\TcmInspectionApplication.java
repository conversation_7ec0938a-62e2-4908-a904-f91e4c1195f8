package com.tcm.inspection;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 中医检查服务启动类
 */
@SpringBootApplication(exclude = {
        org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration.class,

        org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class })
@EnableDiscoveryClient
@EnableFeignClients
@ComponentScan(basePackages = { "com.tcm.inspection", "com.tcm.common" })
@MapperScan("com.tcm.inspection.mapper")
public class TcmInspectionApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmInspectionApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // ===== SLF4J日志冲突解决 =====
        // 1. 确保使用logback作为唯一的SLF4J实现
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "WARN");
        System.setProperty("org.slf4j.simpleLogger.showThreadName", "false");
        System.setProperty("org.slf4j.simpleLogger.showLogName", "false");
        System.setProperty("org.slf4j.simpleLogger.showShortLogName", "true");

        // 2. 彻底禁用Log4j
        System.setProperty("log4j2.disable.jmx", "true");
        System.setProperty("log4j.configuration", "log4j-disabled.properties");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");

        // 3. 强制所有其他日志框架重定向到SLF4J
        System.setProperty("org.jboss.logging.provider", "slf4j");
        System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SLF4JLogFactory");

        // ===== 类加载器配置 =====
        System.setProperty("spring.classloader.overrideStandardClassLoader", "false");
        System.setProperty("spring.boot.register-shutdown-hook", "false");

        // ===== PerfMark相关冲突解决 =====
        // 彻底禁用PerfMark实现，避免类加载问题
        System.setProperty("io.perfmark.PerfMark.impl", "io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.impl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.startEnabled", "false");

        // 设置一个假的类名，防止通过Class.forName()加载原始类
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.impl.SecretPerfMarkImpl$PerfMarkImpl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");

        // 禁用gRPC通信，强制使用HTTP
        System.setProperty("nacos.client.naming.grpc.enabled", "false");
        System.setProperty("nacos.client.config.grpc.enabled", "false");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-inspection");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_INSPECTION_PORT") == null) {
            System.setProperty("server.port", "${tcm.service.inspection.port:9308}");
        }
    }

    public static void main(String[] args) {
        try {
            // 打印当前工作目录
            LOGGER.info("当前工作目录: {}", System.getProperty("user.dir"));

            // 配置日志相关设置
            configureLogging();

            // 配置应用设置
            configureApplicationSettings();

            // 配置服务端口
            String fixedPort = configureServerPort();

            // 启动应用程序
            ConfigurableApplicationContext context = SpringApplication.run(TcmInspectionApplication.class, args);

            // 处理和显示启动信息
            displayStartupInfo(context, fixedPort);
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 配置日志相关设置
     */
    private static void configureLogging() {
        // 1.1 清理并禁用所有可能的Log4j相关配置
        System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
        System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
        System.setProperty("log4j2.statusLogger.level", "OFF"); // 禁用Log4j2状态日志

        // 1.2 防止SLF4J日志绑定冲突
        try {
            // 尝试卸载可能已经加载的SLF4J绑定
            Class.forName("org.slf4j.LoggerFactory").getDeclaredMethod("reset").invoke(null);
        } catch (Exception e) {
            // 忽略任何反射错误，继续执行
        }

        // 1.3 配置日志级别和其他设置
        System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
        System.setProperty("logging.level.root", "INFO"); // 设置根日志级别
        System.setProperty("logging.level.com.tcm", "DEBUG"); // 设置TCM模块日志级别
        System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅
    }

    /**
     * 配置应用程序设置
     */
    private static void configureApplicationSettings() {
        // 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
        System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
        System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

        // Nacos配置 - 彻底解决Nacos连接问题
        // 禁用Nacos配置导入检查，以避免启动错误
        System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

        // 设置类加载器
        System.setProperty("java.system.class.loader", "org.springframework.boot.loader.LaunchedURLClassLoader");
        System.setProperty("spring.devtools.restart.enabled", "false");

        // 设置统一配置文件
        System.setProperty("spring.config.import", "optional:classpath:/config/tcm-common.yml");

        // 解决Java反射和安全限制问题
        System.setProperty("java.net.preferIPv4Stack", "true");
        System.setProperty("io.netty.tryReflectionSetAccessible", "true");

        // 强制禁用所有netty unsafe访问
        System.setProperty("io.netty.noUnsafe", "true");

        // 禁用Spring Boot Actuator相关功能
        System.setProperty("management.endpoints.enabled-by-default", "false");
        System.setProperty("management.endpoint.health.enabled", "false");
        System.setProperty("management.endpoint.info.enabled", "false");
        System.setProperty("management.endpoints.web.exposure.include", "");
        System.setProperty("management.endpoints.jmx.exposure.include", "");
        System.setProperty("spring.jmx.enabled", "false");

        // 启用MyBatis映射器扫描的详细日志
        System.setProperty("logging.level.org.mybatis.spring.mapper", "DEBUG");

        // 显式设置mybatis-plus的配置
        System.setProperty("mybatis-plus.mapper-locations", "classpath*:/mapper/**/*.xml");
        System.setProperty("mybatis-plus.type-aliases-package", "com.tcm.inspection.domain");
        System.setProperty("mybatis-plus.global-config.db-config.id-type", "AUTO");
    }

    /**
     * 配置服务器端口
     * 
     * @return 配置的端口
     */
    private static String configureServerPort() {
        // 优先设置固定端口，确保Spring Boot使用这个端口启动
        String fixedPort = System.getProperty("server.port");
        if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
            fixedPort = System.getProperty("tcm.service.inspection.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getenv("TCM_INSPECTION_PORT");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = "${tcm.service.inspection.port:9308}";
                }
            }
            System.setProperty("server.port", fixedPort);
        }

        // 打印将要使用的端口
        LOGGER.info("启动服务器使用的端口: {}", System.getProperty("server.port"));

        return fixedPort;
    }

    /**
     * 显示启动信息
     * 
     * @param context        Spring应用上下文
     * @param configuredPort 配置的端口
     */
    private static void displayStartupInfo(ConfigurableApplicationContext context, String configuredPort) {
        // 获取实际使用的端口（包括随机分配的端口）
        String serverPort = determineActualServerPort(context, configuredPort);

        // 确保端口被设置到系统属性中（使之可被其他组件获取）
        System.setProperty("server.port", serverPort);

        LOGGER.info("=====================================================");
        LOGGER.info("         TCM Inspection Service 已启动!             ");
        LOGGER.info("=====================================================");
        LOGGER.info("应用名称: {}", context.getEnvironment().getProperty("spring.application.name"));
        LOGGER.info("应用端口: {}", serverPort);

        // 安全地获取激活的 profile
        String[] activeProfiles = context.getEnvironment().getActiveProfiles();
        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
        LOGGER.info("激活的配置文件: {}", activeProfile);

        printLogo();

        System.out.println("  启动成功: http://" + (System.getProperty("server.address", "localhost")) + ":" + serverPort
                + context.getEnvironment().getProperty("server.servlet.context-path", ""));
        System.out.println("=====================================================================");
    }

    /**
     * 确定实际使用的服务器端口
     * 
     * @param context        Spring应用上下文
     * @param configuredPort 配置的端口
     * @return 实际使用的端口
     */
    private static String determineActualServerPort(ConfigurableApplicationContext context, String configuredPort) {
        // 如果PortListener已经捕获到了实际端口，则使用它
        if (actualServerPort > 0) {
            return String.valueOf(actualServerPort);
        }

        // 尝试从环境中获取端口
        String serverPort = context.getEnvironment().getProperty("server.port");

        // 如果环境中没有端口配置，则使用配置的端口
        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
            return configuredPort;
        }

        return serverPort;
    }

    /**
     * 打印应用Logo
     */
    private static void printLogo() {
        System.out.println("  _____   _____ __  __   _____                           _   _             ");
        System.out.println(" |_   _| / ____|  \\/  | |_   _|                         | | (_)            ");
        System.out.println("   | |  | |    | \\  / |   | |  _ __  ___ _ __   ___  ___| |_ _  ___  _ __  ");
        System.out.println("   | |  | |    | |\\/| |   | | | '_ \\/ __| '_ \\ / _ \\/ __| __| |/ _ \\| '_ \\ ");
        System.out.println("  _| |_ | |____| |  | |  _| |_| | | \\__ \\ |_) |  __/ (__| |_| | (_) | | | |");
        System.out.println(" |_____| \\_____|_|  |_| |_____|_| |_|___/ .__/ \\___|\\___|\\__|_|\\___/|_| |_|");
        System.out.println("                                        | |                                ");
        System.out.println("                                        |_|                                ");
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web服务器已初始化，使用端口: {}", actualServerPort);
        }
    }
}