package com.tcm.prescription.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 处方数据传输对象
 */
@Data
public class PrescriptionDTO {

    /**
     * 处方ID（更新时需要）
     */
    private Long prescriptionId;
    
    /**
     * 诊断记录ID
     */
    @NotNull(message = "诊断记录ID不能为空")
    private Long diagnosisId;
    
    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;
    
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;
    
    /**
     * 处方类型（1：中药方剂，2：中成药，3：西药）
     */
    @NotNull(message = "处方类型不能为空")
    private Integer type;
    
    /**
     * 处方名称
     */
    @NotBlank(message = "处方名称不能为空")
    private String name;
    
    /**
     * 用药天数
     */
    @NotNull(message = "用药天数不能为空")
    private Integer days;
    
    /**
     * 剂数
     */
    @NotNull(message = "剂数不能为空")
    private Integer doses;
    
    /**
     * 用药方法
     */
    @NotBlank(message = "用药方法不能为空")
    private String usageMethod;
    
    /**
     * 用药频次
     */
    @NotBlank(message = "用药频次不能为空")
    private String frequency;
    
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    
    /**
     * 煎药方式
     */
    private String decoctionMethod;
    
    /**
     * 医嘱
     */
    private String advice;
    
    /**
     * 处方药品列表
     */
    private List<PrescriptionMedicineDTO> medicines;
} 