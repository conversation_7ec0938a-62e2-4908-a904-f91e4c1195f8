package com.tcm.diagnosis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 舌象分析结果实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_tongue_analysis")
public class TongueAnalysis extends BaseEntity {

    /**
     * 分析ID
     */
    @TableId
    private Long analysisId;

    /**
     * 图像ID
     */
    private Long imageId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 舌色
     */
    private String tongueColor;

    /**
     * 舌形
     */
    private String tongueShape;

    /**
     * 舌苔颜色
     */
    private String coatingColor;

    /**
     * 舌苔厚度
     */
    private String coatingThickness;

    /**
     * 舌态特征
     */
    private String tongueCharacteristics;

    /**
     * 分析结果详情（JSON格式）
     */
    private String analysisResult;

    /**
     * 临床意义
     */
    private String clinicalImplications;

    /**
     * 分析时间
     */
    private Date analysisTime;

    /**
     * 分析模型版本
     */
    private String modelVersion;

    /**
     * 分析置信度（0-100）
     */
    private Integer confidence;
}