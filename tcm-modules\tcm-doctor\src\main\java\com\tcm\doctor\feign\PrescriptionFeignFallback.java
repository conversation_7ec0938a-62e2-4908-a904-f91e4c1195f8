package com.tcm.doctor.feign;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 处方服务远程调用降级处理
 */
@Component
public class PrescriptionFeignFallback implements PrescriptionFeignClient {
    
    private static final Logger log = LoggerFactory.getLogger(PrescriptionFeignFallback.class);

    @Override
    public Long getPrescriptionCount(Long doctorId, LocalDate startDate, LocalDate endDate) {
        log.error("远程调用处方服务获取处方数量失败，doctorId: {}, 时间范围: {} - {}", doctorId, startDate, endDate);
        return 0L;
    }

    @Override
    public BigDecimal getPrescriptionAmount(Long doctorId, LocalDate startDate, LocalDate endDate) {
        log.error("远程调用处方服务获取处方金额失败，doctorId: {}, 时间范围: {} - {}", doctorId, startDate, endDate);
        return BigDecimal.ZERO;
    }
}
