package com.tcm.user.exception;

import com.tcm.user.constant.HttpStatus;

/**
 * 业务异常
 */
public class BusinessException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 空构造方法，避免反序列化问题
     */
    public BusinessException() {
    }

    /**
     * 构造函数
     * 
     * @param message 错误提示
     */
    public BusinessException(String message) {
        this.message = message;
        this.code = HttpStatus.ERROR;
    }

    /**
     * 构造函数
     * 
     * @param code    错误码
     * @param message 错误提示
     */
    public BusinessException(Integer code, String message) {
        this.message = message;
        this.code = code;
    }

    /**
     * 构造函数
     * 
     * @param code    错误码
     * @param message 错误提示
     */
    public BusinessException(Integer code, String message, String detailMessage) {
        this.message = message;
        this.code = code;
        this.detailMessage = detailMessage;
    }

    /**
     * 构造函数
     * 
     * @param httpStatus 错误状态码
     * @param message    错误提示
     */
    public BusinessException(int httpStatus, String message) {
        this.message = message;
        this.code = httpStatus;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    public String getDetailMessage() {
        return detailMessage;
    }

    public BusinessException setDetailMessage(String detailMessage) {
        this.detailMessage = detailMessage;
        return this;
    }
}