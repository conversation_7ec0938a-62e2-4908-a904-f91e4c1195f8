package com.tcm.doctor.feign;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单服务远程调用降级处理工厂
 * 使用FallbackFactory可以获取到导致降级的原因
 */
@Component
public class OrderFeignFallbackFactory implements FallbackFactory<OrderFeignClient> {
    
    private static final Logger log = LoggerFactory.getLogger(OrderFeignFallbackFactory.class);

    @Override
    public OrderFeignClient create(Throwable cause) {
        return new OrderFeignClient() {
            @Override
            public BigDecimal getTotalAmount(Long doctorId, LocalDate startDate, LocalDate endDate) {
                log.error("远程调用订单服务获取收费总金额失败，doctorId: {}, 时间范围: {} - {}, 原因: {}", 
                        doctorId, startDate, endDate, cause.getMessage());
                return BigDecimal.ZERO;
            }

            @Override
            public BigDecimal getRevisitRate(Long doctorId, LocalDate startDate, LocalDate endDate) {
                log.error("远程调用订单服务获取复诊率失败，doctorId: {}, 时间范围: {} - {}, 原因: {}", 
                        doctorId, startDate, endDate, cause.getMessage());
                return BigDecimal.ZERO;
            }
        };
    }
}
