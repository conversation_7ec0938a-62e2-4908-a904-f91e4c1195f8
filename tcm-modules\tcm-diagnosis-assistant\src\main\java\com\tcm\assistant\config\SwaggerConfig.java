package com.tcm.assistant.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;

/**
 * OpenAPI配置
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("AI辅助诊疗服务API文档")
                        .description("中医智能诊疗系统-AI辅助诊疗服务接口文档")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("TCM Team")
                                .email("<EMAIL>")));
    }
}