package com.tcm.admin.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 树形选择视图对象
 */
@Data
@NoArgsConstructor
public class TreeSelectVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 节点ID
     */
    private Long id;

    /**
     * 节点标签
     */
    private String label;

    /**
     * 子节点
     */
    private List<TreeSelectVO> children;

    /**
     * 构造函数
     *
     * @param menuVO 菜单视图对象
     */
    public TreeSelectVO(MenuVO menuVO) {
        this.id = menuVO.getId();
        this.label = menuVO.getMenuName();
        if (menuVO.getChildren() != null && !menuVO.getChildren().isEmpty()) {
            this.children = menuVO.getChildren().stream().map(TreeSelectVO::new).collect(Collectors.toList());
        }
    }

    /**
     * 构造函数
     *
     * @param deptVO 部门视图对象
     */
    public TreeSelectVO(DeptVO deptVO) {
        this.id = deptVO.getId();
        this.label = deptVO.getDeptName();
        if (deptVO.getChildren() != null && !deptVO.getChildren().isEmpty()) {
            this.children = deptVO.getChildren().stream().map(TreeSelectVO::new).collect(Collectors.toList());
        }
    }
}