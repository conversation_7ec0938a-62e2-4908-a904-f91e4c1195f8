package com.tcm.auth.service.impl;

import com.tcm.auth.dto.LoginDTO;
import com.tcm.auth.service.AuthService;
import com.tcm.auth.vo.LoginUserVO;
import com.tcm.auth.vo.TokenVO;
import com.tcm.common.core.constant.HttpStatus;
import com.tcm.common.core.exception.BusinessException;
import com.tcm.common.security.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final JwtUtils jwtUtils;
    private final RedisTemplate<String, Object> redisTemplate;

    // 用户信息Redis前缀
    private static final String REDIS_USER_KEY = "auth:user:";
    // 刷新令牌Redis前缀
    private static final String REDIS_REFRESH_TOKEN = "auth:refresh:";
    // 令牌过期时间（秒）
    private static final long TOKEN_EXPIRE = 86400L;
    // 刷新令牌过期时间（秒）
    private static final long REFRESH_TOKEN_EXPIRE = 604800L;

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @Override
    public TokenVO login(LoginDTO loginDTO) {
        // 验证码校验（可选）
        // validateCaptcha(loginDTO.getCaptcha(), loginDTO.getCaptchaKey());

        // 用户认证，这里简单判断（实际项目中应从数据库校验）
        // TODO: 从数据库查询用户并校验密码
        if (!"admin".equals(loginDTO.getUsername()) || !"123456".equals(loginDTO.getPassword())) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "用户名或密码错误");
        }

        // 生成令牌
        Long userId = 1L; // 模拟用户ID
        String token = jwtUtils.generateToken(loginDTO.getUsername(), userId);
        String refreshToken = UUID.randomUUID().toString();

        // 创建用户信息
        LoginUserVO userVO = createUserInfo(userId, loginDTO.getUsername());

        // 存储用户信息到Redis
        redisTemplate.opsForValue().set(REDIS_USER_KEY + userId, userVO, TOKEN_EXPIRE, TimeUnit.SECONDS);
        // 存储刷新令牌到Redis
        redisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN + refreshToken, userId, REFRESH_TOKEN_EXPIRE,
                TimeUnit.SECONDS);

        // 返回令牌
        return TokenVO.builder()
                .token(token)
                .refreshToken(refreshToken)
                .tokenType("Bearer")
                .expiration(TOKEN_EXPIRE)
                .build();
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新令牌
     */
    @Override
    public TokenVO refreshToken(String refreshToken) {
        if (!StringUtils.hasText(refreshToken)) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "刷新令牌不能为空");
        }

        // 从Redis获取用户ID
        Object userId = redisTemplate.opsForValue().get(REDIS_REFRESH_TOKEN + refreshToken);
        if (userId == null) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "刷新令牌已过期");
        }

        // 从Redis获取用户信息
        LoginUserVO userVO = (LoginUserVO) redisTemplate.opsForValue().get(REDIS_USER_KEY + userId);
        if (userVO == null) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "用户信息已过期，请重新登录");
        }

        // 生成新的访问令牌
        String token = jwtUtils.generateToken(userVO.getUsername(), userVO.getId());
        String newRefreshToken = UUID.randomUUID().toString();

        // 删除旧的刷新令牌
        redisTemplate.delete(REDIS_REFRESH_TOKEN + refreshToken);

        // 更新Redis中的用户信息和刷新令牌
        redisTemplate.opsForValue().set(REDIS_USER_KEY + userId, userVO, TOKEN_EXPIRE, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(REDIS_REFRESH_TOKEN + newRefreshToken, userId, REFRESH_TOKEN_EXPIRE,
                TimeUnit.SECONDS);

        // 返回新令牌
        return TokenVO.builder()
                .token(token)
                .refreshToken(newRefreshToken)
                .tokenType("Bearer")
                .expiration(TOKEN_EXPIRE)
                .build();
    }

    /**
     * 获取登录用户信息
     *
     * @param token 访问令牌
     * @return 用户信息
     */
    @Override
    public LoginUserVO getUserInfo(String token) {
        if (!StringUtils.hasText(token)) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "令牌不能为空");
        }

        // 验证令牌有效性
        if (jwtUtils.isTokenExpired(token)) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "令牌已过期");
        }

        // 从令牌中获取用户ID
        Long userId = jwtUtils.getUserIdFromToken(token);

        // 从Redis获取用户信息
        LoginUserVO userVO = (LoginUserVO) redisTemplate.opsForValue().get(REDIS_USER_KEY + userId);
        if (userVO == null) {
            throw new BusinessException(HttpStatus.UNAUTHORIZED, "用户信息已过期，请重新登录");
        }

        return userVO;
    }

    /**
     * 用户登出
     *
     * @param token 访问令牌
     */
    @Override
    public void logout(String token) {
        if (!StringUtils.hasText(token)) {
            return;
        }

        try {
            // 从令牌中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);

            // 删除Redis中的用户信息
            redisTemplate.delete(REDIS_USER_KEY + userId);

            // 通过用户ID查找并删除刷新令牌
            // 实际项目中可能需要维护Token与用户ID之间的双向映射关系
        } catch (Exception e) {
            log.error("登出异常", e);
        }
    }

    /**
     * 验证码校验（实际项目中需要实现）
     */
    private void validateCaptcha(String captcha, String captchaKey) {
        if (!StringUtils.hasText(captcha) || !StringUtils.hasText(captchaKey)) {
            throw new BusinessException(HttpStatus.BAD_REQUEST, "验证码不能为空");
        }

        // TODO: 从Redis获取验证码并校验
        // 如果验证码无效或已过期，抛出异常
    }

    /**
     * 创建用户信息（模拟，实际项目中应从数据库获取）
     */
    private LoginUserVO createUserInfo(Long userId, String username) {
        LoginUserVO userVO = new LoginUserVO();
        userVO.setId(userId);
        userVO.setUsername(username);
        userVO.setRealName("系统管理员");
        userVO.setMobile("***********");
        userVO.setEmail("<EMAIL>");
        userVO.setAvatar("https://example.com/avatar.jpg");

        // 设置角色
        List<String> roles = new ArrayList<>();
        roles.add("admin");
        userVO.setRoles(roles);

        // 设置权限
        List<String> permissions = new ArrayList<>();
        permissions.add("*:*:*"); // 所有权限
        userVO.setPermissions(permissions);

        return userVO;
    }
}
