package com.tcm.statistics.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 证型分布数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyndromeDistributionDTO {
    
    /**
     * 证型名称
     */
    private String syndrome;
    
    /**
     * 出现次数
     */
    private Long count;
    
    /**
     * 占比百分比
     */
    private Double percentage;
} 