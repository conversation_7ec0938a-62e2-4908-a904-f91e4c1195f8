package com.tcm.gateway.config;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 网关路由配置
 *
 * <AUTHOR>
 */
@Configuration
public class RouterConfig {

    /**
     * 配置路由规则
     * 注意：这里的路由规则优先级高于配置文件中的路由规则
     */
    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
                // 健康检查路由
                .route("health_check_route", r -> r.path("/actuator/**")
                        .filters(f -> f.stripPrefix(0))
                        .uri("lb://tcm-platform-gateway"))
                .build();
    }
}