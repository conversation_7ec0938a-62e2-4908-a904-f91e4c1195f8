package com.tcm.inventory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.inventory.entity.InventoryItem;
import com.tcm.inventory.mapper.InventoryItemMapper;
import com.tcm.inventory.service.IInventoryItemService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 库存项目服务实现类
 */
@Service
public class InventoryItemServiceImpl extends ServiceImpl<InventoryItemMapper, InventoryItem> implements IInventoryItemService {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(InventoryItemServiceImpl.class);
    
    @Override
    public InventoryItem getByItemCode(String itemCode) {
        LOGGER.info("根据药品编码查询库存项目: {}", itemCode);
        LambdaQueryWrapper<InventoryItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InventoryItem::getItemCode, itemCode);
        return getOne(wrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuantity(Long itemId, int quantity) {
        LOGGER.info("更新库存数量: itemId={}, quantity={}", itemId, quantity);
        InventoryItem item = getById(itemId);
        if (item == null) {
            LOGGER.warn("库存项目不存在: {}", itemId);
            return false;
        }
        
        // 计算新的库存数量
        int newQuantity = item.getQuantity() + quantity;
        if (newQuantity < 0) {
            LOGGER.warn("库存不足，无法更新: itemId={}, currentQuantity={}, changeQuantity={}", 
                    itemId, item.getQuantity(), quantity);
            return false;
        }
        
        // 更新库存数量
        item.setQuantity(newQuantity);
        
        // 根据库存数量更新库存状态
        if (newQuantity == 0) {
            item.setStatus(2); // 缺货
        } else if (newQuantity < 10) {
            item.setStatus(1); // 低库存
        } else {
            item.setStatus(0); // 正常
        }
        
        return updateById(item);
    }
} 