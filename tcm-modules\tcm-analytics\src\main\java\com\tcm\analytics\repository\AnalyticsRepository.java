package com.tcm.analytics.repository;

import java.util.List;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import com.tcm.analytics.entity.AnalyticsData;

/**
 * 分析数据仓库接口
 */
@Repository
@ConditionalOnProperty(name = "spring.elasticsearch.enabled", havingValue = "true", matchIfMissing = false)
public interface AnalyticsRepository extends ElasticsearchRepository<AnalyticsData, String> {

    /**
     * 根据仪表盘ID查找数据
     * 
     * @param dashboardId 仪表盘ID
     * @return 数据列表
     */
    List<AnalyticsData> findByDashboardId(String dashboardId);

    /**
     * 根据数据源类型查找数据
     * 
     * @param dataSource 数据源类型
     * @return 数据列表
     */
    List<AnalyticsData> findByDataSource(String dataSource);

    /**
     * 根据创建者查找数据
     * 
     * @param creator 创建者
     * @return 数据列表
     */
    List<AnalyticsData> findByCreator(String creator);

    /**
     * 根据仪表盘ID和数据源类型查找数据
     * 
     * @param dashboardId 仪表盘ID
     * @param dataSource  数据源类型
     * @return 数据列表
     */
    List<AnalyticsData> findByDashboardIdAndDataSource(String dashboardId, String dataSource);
}