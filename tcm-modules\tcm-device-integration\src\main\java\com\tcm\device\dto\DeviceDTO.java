package com.tcm.device.dto;

import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 设备数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class DeviceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    private Long id;

    /**
     * 设备名称
     */
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 100, message = "设备名称长度不能超过100个字符")
    private String deviceName;

    /**
     * 设备编码
     */
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 50, message = "设备编码长度不能超过50个字符")
    private String deviceCode;

    /**
     * 设备类型
     */
    @NotBlank(message = "设备类型不能为空")
    @Size(max = 50, message = "设备类型长度不能超过50个字符")
    private String deviceType;

    /**
     * 设备型号
     */
    @Size(max = 100, message = "设备型号长度不能超过100个字符")
    private String model;

    /**
     * 厂商
     */
    @Size(max = 100, message = "厂商长度不能超过100个字符")
    private String manufacturer;

    /**
     * IP地址
     */
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    private String ipAddress;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 通信协议
     */
    @Size(max = 50, message = "通信协议长度不能超过50个字符")
    private String protocol;

    /**
     * 设备位置
     */
    @Size(max = 100, message = "设备位置长度不能超过100个字符")
    private String location;

    /**
     * 设备描述
     */
    @Size(max = 500, message = "设备描述长度不能超过500个字符")
    private String description;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}