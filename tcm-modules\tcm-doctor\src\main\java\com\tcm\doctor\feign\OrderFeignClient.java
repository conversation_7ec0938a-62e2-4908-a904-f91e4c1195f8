package com.tcm.doctor.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单服务远程调用接口
 */
@FeignClient(name = "tcm-order", fallbackFactory = OrderFeignFallbackFactory.class)
public interface OrderFeignClient {

    /**
     * 获取医生收费总金额
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收费总金额
     */
    @GetMapping("/order/stat/amount")
    BigDecimal getTotalAmount(@RequestParam("doctorId") Long doctorId,
                              @RequestParam("startDate") LocalDate startDate,
                              @RequestParam("endDate") LocalDate endDate);

    /**
     * 获取医生复诊率
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 复诊率（百分比）
     */
    @GetMapping("/order/stat/revisit-rate")
    BigDecimal getRevisitRate(@RequestParam("doctorId") Long doctorId,
                              @RequestParam("startDate") LocalDate startDate,
                              @RequestParam("endDate") LocalDate endDate);
}
