package com.tcm.file.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * 文件工具类
 */
@Slf4j
public class FileUtils {

    /**
     * 计算文件的MD5值
     * 
     * @param inputStream 文件输入流
     * @return MD5值
     */
    public static String calculateMd5(InputStream inputStream) throws IOException, NoSuchAlgorithmException {
        // 将输入流内容读入字节数组，以便重用
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        IOUtils.copy(inputStream, byteArrayOutputStream);
        byte[] data = byteArrayOutputStream.toByteArray();
        
        // 重置输入流，以便后续使用
        if (inputStream.markSupported()) {
            inputStream.reset();
        } else {
            // 如果不支持重置，则创建新的ByteArrayInputStream并替换原始输入流
            ByteArrayInputStream newInputStream = new ByteArrayInputStream(data);
            IOUtils.closeQuietly(inputStream);
            inputStream = newInputStream;
        }
        
        // 计算MD5
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(data);
        byte[] digest = md.digest();
        
        // 将字节转换为十六进制字符串
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }
        
        return sb.toString();
    }
    
    /**
     * 获取安全的文件名
     * 
     * @param fileName 原始文件名
     * @return 安全的文件名
     */
    public static String getSafeFileName(String fileName) {
        if (fileName == null) {
            return null;
        }
        
        // 移除路径信息，只保留文件名
        fileName = FilenameUtils.getName(fileName);
        
        // 替换不安全的字符
        return fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
    }
    
    /**
     * 根据文件扩展名判断是否为图片
     * 
     * @param extension 文件扩展名
     * @return 是否为图片
     */
    public static boolean isImage(String extension) {
        if (extension == null) {
            return false;
        }
        
        extension = extension.toLowerCase();
        return "jpg".equals(extension) || 
               "jpeg".equals(extension) || 
               "png".equals(extension) || 
               "gif".equals(extension) || 
               "bmp".equals(extension) || 
               "webp".equals(extension);
    }
    
    /**
     * 根据文件扩展名判断是否为视频
     * 
     * @param extension 文件扩展名
     * @return 是否为视频
     */
    public static boolean isVideo(String extension) {
        if (extension == null) {
            return false;
        }
        
        extension = extension.toLowerCase();
        return "mp4".equals(extension) || 
               "avi".equals(extension) || 
               "mov".equals(extension) || 
               "wmv".equals(extension) || 
               "flv".equals(extension) || 
               "mkv".equals(extension);
    }
    
    /**
     * 根据文件扩展名判断是否为音频
     * 
     * @param extension 文件扩展名
     * @return 是否为音频
     */
    public static boolean isAudio(String extension) {
        if (extension == null) {
            return false;
        }
        
        extension = extension.toLowerCase();
        return "mp3".equals(extension) || 
               "wav".equals(extension) || 
               "ogg".equals(extension) || 
               "flac".equals(extension) || 
               "aac".equals(extension);
    }
} 