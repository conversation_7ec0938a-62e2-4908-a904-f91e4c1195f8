package com.tcm.admin.service;

import com.tcm.admin.dto.LoginDTO;
import com.tcm.admin.vo.LoginUserVO;
import com.tcm.admin.vo.TokenVO;

/**
 * 认证服务接口
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    TokenVO login(LoginDTO loginDTO);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新令牌
     */
    TokenVO refreshToken(String refreshToken);

    /**
     * 获取登录用户信息
     *
     * @param token 访问令牌
     * @return 用户信息
     */
    LoginUserVO getUserInfo(String token);

    /**
     * 用户登出
     *
     * @param token 访问令牌
     */
    void logout(String token);
}