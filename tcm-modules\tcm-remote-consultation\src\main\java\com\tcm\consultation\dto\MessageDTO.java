package com.tcm.consultation.dto;

import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会诊消息数据传输对象
 */
@Data
public class MessageDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private Long id;

    /**
     * 会诊ID
     */
    private Long consultationId;

    /**
     * 发送者ID
     */
    private Long senderId;

    /**
     * 发送者姓名
     */
    private String senderName;

    /**
     * 发送者类型：1-主诊医生，2-会诊医生，3-患者，4-系统
     */
    private Integer senderType;

    /**
     * 消息类型：1-文本，2-图片，3-文件，4-系统通知，5-控制命令
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 附件URL
     */
    private String attachmentUrl;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}