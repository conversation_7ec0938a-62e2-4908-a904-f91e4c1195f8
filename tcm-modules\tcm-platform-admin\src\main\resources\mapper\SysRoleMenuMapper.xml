<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.platform.admin.mapper.SysRoleMenuMapper">

    <!-- 批量新增角色菜单关联 -->
    <insert id="batchInsert">
        insert into sys_role_menu(role_id, menu_id, create_by, create_time) values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.menuId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 通过角色ID删除角色菜单关联 -->
    <delete id="deleteByRoleId">
        delete from sys_role_menu where role_id = #{roleId}
    </delete>

    <!-- 通过菜单ID删除角色菜单关联 -->
    <delete id="deleteByMenuId">
        delete from sys_role_menu where menu_id = #{menuId}
    </delete>
</mapper> 