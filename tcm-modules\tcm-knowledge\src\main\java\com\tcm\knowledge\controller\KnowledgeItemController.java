package com.tcm.knowledge.controller;

import com.tcm.knowledge.dto.KnowledgeItemDTO;
import com.tcm.knowledge.service.IKnowledgeItemService;
import com.tcm.knowledge.vo.KnowledgeItemVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/knowledge/item")
public class KnowledgeItemController {
    @Autowired
    private IKnowledgeItemService knowledgeItemService;

    @PostMapping
    public Long add(@RequestBody KnowledgeItemDTO dto) {
        return knowledgeItemService.addKnowledgeItem(dto);
    }

    @PutMapping("/{id}")
    public boolean update(@PathVariable Long id, @RequestBody KnowledgeItemDTO dto) {
        return knowledgeItemService.updateKnowledgeItem(id, dto);
    }

    @DeleteMapping("/{id}")
    public boolean delete(@PathVariable Long id) {
        return knowledgeItemService.deleteKnowledgeItem(id);
    }

    @GetMapping("/{id}")
    public KnowledgeItemVO get(@PathVariable Long id) {
        return knowledgeItemService.getKnowledgeItem(id);
    }

    @GetMapping("/list")
    public List<KnowledgeItemVO> list(@RequestParam(required = false) String type,
            @RequestParam(required = false) String keyword) {
        return knowledgeItemService.listKnowledgeItems(type, keyword);
    }
}