package com.tcm.patient.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.patient.domain.Patient;
import com.tcm.patient.dto.PatientDTO;
import com.tcm.patient.dto.PatientQuery;
import com.tcm.patient.vo.PatientVO;

/**
 * 患者服务接口
 */
public interface IPatientService extends IService<Patient> {
    
    /**
     * 分页查询患者列表
     *
     * @param query 查询条件
     * @return 患者列表
     */
    IPage<PatientVO> listPatients(PatientQuery query);
    
    /**
     * 根据ID查询患者详情
     *
     * @param patientId 患者ID
     * @return 患者详情
     */
    PatientVO getPatientById(Long patientId);
    
    /**
     * 根据用户ID查询患者信息
     *
     * @param userId 用户ID
     * @return 患者信息
     */
    PatientVO getPatientByUserId(Long userId);
    
    /**
     * 根据身份证号查询患者信息
     *
     * @param idCard 身份证号
     * @return 患者信息
     */
    PatientVO getPatientByIdCard(String idCard);
    
    /**
     * 新增患者
     *
     * @param patientDTO 患者信息
     * @return 结果
     */
    boolean addPatient(PatientDTO patientDTO);
    
    /**
     * 修改患者
     *
     * @param patientDTO 患者信息
     * @return 结果
     */
    boolean updatePatient(PatientDTO patientDTO);
    
    /**
     * 删除患者
     *
     * @param patientId 患者ID
     * @return 结果
     */
    boolean deletePatient(Long patientId);
    
    /**
     * 批量删除患者
     *
     * @param patientIds 患者ID数组
     * @return 结果
     */
    boolean deletePatients(Long[] patientIds);
    
    /**
     * 修改患者状态
     *
     * @param patientId 患者ID
     * @param status    状态
     * @return 结果
     */
    boolean updatePatientStatus(Long patientId, Integer status);
} 