package com.tcm.user.controller;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.user.dto.UserDTO;
import com.tcm.user.service.UserService;
import com.tcm.user.vo.PageVO;
import com.tcm.user.vo.UserVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
@Tag(name = "用户管理", description = "用户相关接口")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * 分页获取用户列表
     */
    @GetMapping("/list")
    @Operation(summary = "分页获取用户列表", description = "支持根据用户名和真实姓名筛选")
    public R<PageVO<UserVO>> list(@Parameter(description = "页码，从1开始") @RequestParam(defaultValue = "1") Integer pageNum,
            @Parameter(description = "每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "真实姓名") @RequestParam(required = false) String realName) {
        PageVO<UserVO> pageVO = userService.listUsers(pageNum, pageSize, username, realName);
        return R.ok(pageVO);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详情")
    public R<UserVO> getUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        UserVO userVO = userService.getUserById(id);
        return R.ok(userVO);
    }

    /**
     * 创建用户
     */
    @PostMapping
    @Operation(summary = "创建用户", description = "创建新用户")
    public R<Long> createUser(@Valid @RequestBody UserDTO userDTO) {
        Long userId = userService.createUser(userDTO);
        return R.ok("创建成功", userId);
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新用户", description = "更新用户信息")
    public R<Void> updateUser(@Parameter(description = "用户ID") @PathVariable Long id,
            @Valid @RequestBody UserDTO userDTO) {
        userService.updateUser(id, userDTO);
        return R.ok("更新成功");
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    public R<Void> deleteUser(@Parameter(description = "用户ID") @PathVariable Long id) {
        userService.deleteUser(id);
        return R.ok("删除成功");
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status/{status}")
    @Operation(summary = "更新用户状态", description = "启用或禁用用户")
    public R<Void> updateUserStatus(@Parameter(description = "用户ID") @PathVariable Long id,
            @Parameter(description = "用户状态：0正常，1停用") @PathVariable Integer status) {
        userService.updateUserStatus(id, status);
        return R.ok(status == 0 ? "启用成功" : "禁用成功");
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/password/reset")
    @Operation(summary = "重置用户密码", description = "将用户密码重置为默认密码")
    public R<Void> resetPassword(@Parameter(description = "用户ID") @PathVariable Long id) {
        userService.resetPassword(id);
        return R.ok("密码重置成功");
    }
}