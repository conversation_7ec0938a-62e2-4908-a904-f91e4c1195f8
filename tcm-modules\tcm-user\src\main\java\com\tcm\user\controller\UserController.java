package com.tcm.user.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.user.dto.UserDTO;
import com.tcm.user.service.UserService;
import com.tcm.user.vo.PageVO;
import com.tcm.user.vo.R;
import com.tcm.user.vo.UserVO;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class UserController extends BaseController {

    @Autowired
    private UserService userService;

    /**
     * 分页获取用户列表
     */
    @GetMapping("/list")
    public R<PageVO<UserVO>> list(@RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName) {
        PageVO<UserVO> pageVO = userService.listUsers(pageNum, pageSize, username, realName);
        return R.ok(pageVO);
    }

    /**
     * 获取用户详情
     */
    @GetMapping("/{id}")
    public R<UserVO> getUser(@PathVariable Long id) {
        UserVO userVO = userService.getUserById(id);
        return R.ok(userVO);
    }

    /**
     * 创建用户
     */
    @PostMapping
    public R<Long> createUser(@RequestBody UserDTO userDTO) {
        Long userId = userService.createUser(userDTO);
        return R.ok("创建成功", userId);
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    public R<String> updateUser(@PathVariable Long id, @RequestBody UserDTO userDTO) {
        userService.updateUser(id, userDTO);
        return R.ok("更新成功");
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public R<String> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return R.ok("删除成功");
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status/{status}")
    public R<String> updateUserStatus(@PathVariable Long id, @PathVariable Integer status) {
        userService.updateUserStatus(id, status);
        return R.ok(status == 0 ? "启用成功" : "禁用成功");
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/password/reset")
    public R<String> resetPassword(@PathVariable Long id) {
        userService.resetPassword(id);
        return R.ok("密码重置成功");
    }
}