package com.tcm.user.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class UserController {

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "User service is running");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        Map<String, Object> response = new HashMap<>();
        
        String username = loginRequest.get("username");
        String password = loginRequest.get("password");
        
        // 模拟登录逻辑
        if ("admin".equals(username) && "password".equals(password)) {
            response.put("success", true);
            response.put("message", "登录成功");
            response.put("token", UUID.randomUUID().toString());
            response.put("userId", "U001");
            response.put("username", username);
            response.put("roles", List.of("ADMIN", "USER"));
        } else {
            response.put("success", false);
            response.put("message", "用户名或密码错误");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getUserInfo(String userId) {
        Map<String, Object> response = new HashMap<>();
        
        // 模拟用户数据
        response.put("userId", userId);
        response.put("username", "张三");
        response.put("email", "<EMAIL>");
        response.put("phone", "13800138000");
        response.put("roles", List.of("USER"));
        response.put("permissions", List.of("VIEW_PROFILE", "EDIT_PROFILE"));
        response.put("lastLoginTime", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public ResponseEntity<Map<String, Object>> getUserList() {
        Map<String, Object> response = new HashMap<>();
        
        // 模拟用户列表数据
        List<Map<String, Object>> userList = new ArrayList<>();
        
        for (int i = 1; i <= 10; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("userId", "U00" + i);
            user.put("username", "用户" + i);
            user.put("email", "user" + i + "@example.com");
            user.put("phone", "1380013800" + i);
            user.put("status", i % 3 == 0 ? "禁用" : "正常");
            user.put("createTime", LocalDateTime.now().minusDays(i).toString());
            
            userList.add(user);
        }
        
        response.put("total", userList.size());
        response.put("users", userList);
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建用户
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createUser(@RequestBody Map<String, Object> userRequest) {
        Map<String, Object> response = new HashMap<>();
        
        // 模拟创建用户逻辑
        response.put("success", true);
        response.put("message", "用户创建成功");
        response.put("userId", UUID.randomUUID().toString());
        response.put("username", userRequest.get("username"));
        response.put("createTime", LocalDateTime.now().toString());
        
        return ResponseEntity.ok(response);
    }
} 