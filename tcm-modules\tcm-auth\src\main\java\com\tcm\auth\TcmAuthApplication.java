package com.tcm.auth;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 认证授权服务启动类
 */
@SpringBootApplication
@ComponentScan(basePackages = { "com.tcm.auth", "com.tcm.common" })
@MapperScan("com.tcm.auth.mapper")
public class TcmAuthApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmAuthApplication.class);

    public static void main(String[] args) {
        try {

            // 启动应用程序
            SpringApplication.run(TcmAuthApplication.class, args);
            System.out.println("(♥◠‿◠)ﾉﾞ  认证授权模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                    " .-------.       ____     __        \n" +
                    " |  _ _   \\      \\   \\   /  /    \n" +
                    " | ( ' )  |       \\  _. /  '       \n" +
                    " |(_ o _) /        _( )_ .'         \n" +
                    " | (_,_).' __  ___(_ o _)'          \n" +
                    " |  |\\ \\  |  ||   |(_,_)'         \n" +
                    " |  | \\ `'   /|   `-'  /           \n" +
                    " |  |  \\    /  \\      /           \n" +
                    " ''-'   `'-'    `-..-'              ");

        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
