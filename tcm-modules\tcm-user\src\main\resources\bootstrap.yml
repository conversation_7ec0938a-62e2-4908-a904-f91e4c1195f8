spring:
  application:
    name: tcm-user
  profiles:
    active: dev
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        shared-configs:
          - data-id: tcm-common.yml
            group: DEFAULT_GROUP
            refresh: true 