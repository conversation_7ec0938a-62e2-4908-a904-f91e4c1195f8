<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>tcm-system</artifactId>
        <groupId>com.tcm</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>tcm-modules</artifactId>
    <packaging>pom</packaging>

    <name>tcm-modules</name>
    <description>业务模块</description>

    <modules>
        <module>tcm-doctor</module>
        <module>tcm-patient</module>
        <module>tcm-diagnosis</module>
        <module>tcm-prescription</module>
        <module>tcm-knowledge</module>
        <module>tcm-appointment</module>
        <module>tcm-statistics</module>
        <module>tcm-analytics</module>
        <!-- 订单管理模块 -->
        <module>tcm-order</module>
        <!-- 平台相关模块 -->
        <module>tcm-platform-admin</module>
        <module>tcm-platform-auth</module>
        <module>tcm-platform-gateway</module>
        <!-- AI与智能诊疗相关模块 -->
        <module>tcm-diagnosis-assistant</module>
        <module>tcm-remote-consultation</module>
        <module>tcm-inspection</module>
        <!-- 药物管理模块 -->
        <module>tcm-medication</module>
        <!-- 国际化支持 -->
        <module>tcm-international</module>
        <!-- 集成模块 -->
        <module>tcm-device-integration</module>
        <module>tcm-integration</module>
        <!-- 安全与隐私保护 -->
        <module>tcm-security</module>
        <!-- 系统日志模块 -->
        <module>tcm-log</module>
        <!-- 库存管理模块 -->
        <module>tcm-inventory</module>
        <!-- 文件服务模块 -->
        <module>tcm-file-service</module>
        <!-- 评估模块 -->
        <module>tcm-evaluation</module>
        <!-- 消息模块 -->
        <module>tcm-message</module>
        <!-- 监控模块 -->
        <module>tcm-monitor</module>
        <!-- 通知模块 -->
        <module>tcm-notification</module>
        <!-- 护理模块 -->
        <module>tcm-nursing</module>
        <!-- 排班模块 -->
        <module>tcm-schedule</module>
        <!-- 用户模块 -->
        <module>tcm-user</module>
        <!-- 工作流模块 -->
        <module>tcm-workflow</module>
    </modules>

    <properties>
        <spring-cloud.version>2021.0.5</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
    </properties>
    
    <!-- 公共依赖，所有子模块都会继承 -->
    <dependencies>
        <!-- 排除冲突的SLF4J实现，解决多绑定警告 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- Validation API -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        
        <!-- Hibernate Validator -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>6.2.5.Final</version>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.28</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>
    
    <dependencyManagement>
        <dependencies>
            <!-- 公共模块依赖 -->
            <dependency>
                <groupId>com.tcm</groupId>
                <artifactId>tcm-common-core</artifactId>
                <version>${tcm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tcm</groupId>
                <artifactId>tcm-common-redis</artifactId>
                <version>${tcm.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tcm</groupId>
                <artifactId>tcm-common-security</artifactId>
                <version>${tcm.version}</version>
            </dependency>
            
            <!-- Spring Cloud 相关依赖 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter</artifactId>
                <version>3.1.5</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>3.1.5</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-loadbalancer</artifactId>
                <version>3.1.5</version>
            </dependency>
            
            <!-- Spring Cloud Alibaba 相关依赖 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
            </dependency>
            
            <!-- Bean Validation API -->
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>2.0.1.Final</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>6.2.5.Final</version>
            </dependency>
            
            <!-- Spring Integration -->
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-core</artifactId>
                <version>5.5.15</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.integration</groupId>
                <artifactId>spring-integration-http</artifactId>
                <version>5.5.15</version>
            </dependency>
            
            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.28</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
