package com.tcm.integration.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统集成接口调用日志实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("sys_integration_api_log")
public class IntegrationApiLog {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 关联的系统配置ID
     */
    private Long configId;

    /**
     * 系统编码
     */
    private String systemCode;

    /**
     * 接口名称
     */
    private String apiName;

    /**
     * 请求URL
     */
    private String requestUrl;

    /**
     * 请求方法（GET、POST、PUT、DELETE等）
     */
    private String requestMethod;

    /**
     * 请求头信息
     */
    private String requestHeaders;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 响应状态码
     */
    private Integer responseStatus;

    /**
     * 响应结果
     */
    private String responseResult;

    /**
     * 处理时间(毫秒)
     */
    private Long processTime;

    /**
     * 请求状态（0-失败，1-成功）
     */
    private Integer status;

    /**
     * 错误消息
     */
    private String errorMsg;

    /**
     * 请求IP
     */
    private String requestIp;

    /**
     * 操作人
     */
    private String operateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}