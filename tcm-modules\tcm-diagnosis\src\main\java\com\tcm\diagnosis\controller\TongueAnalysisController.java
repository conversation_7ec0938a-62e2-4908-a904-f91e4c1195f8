package com.tcm.diagnosis.controller;

import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.log.annotation.Log;
import com.tcm.common.log.enums.BusinessType;
import com.tcm.common.security.annotation.RequiresPermissions;
import com.tcm.diagnosis.service.ITongueAnalysisService;
import com.tcm.diagnosis.vo.TongueAnalysisVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 舌象分析控制器
 */
@RestController
@RequestMapping("/tongue/analysis")
public class TongueAnalysisController extends BaseController {

    @Autowired
    private ITongueAnalysisService tongueAnalysisService;

    /**
     * 分析舌象图像
     */
    @PostMapping("/analyze/{imageId}")
    @RequiresPermissions("diagnosis:tongue:analyze")
    @Log(title = "舌象分析", businessType = BusinessType.INSERT)
    public R<Long> analyzeTongueImage(@PathVariable Long imageId) {
        return R.ok(tongueAnalysisService.analyzeTongueImage(imageId));
    }

    /**
     * 批量分析舌象图像
     */
    @PostMapping("/analyze/batch")
    @RequiresPermissions("diagnosis:tongue:analyze")
    @Log(title = "舌象分析", businessType = BusinessType.INSERT)
    public R<Boolean> analyzeTongueImageBatch(@RequestBody List<Long> imageIds) {
        return R.ok(tongueAnalysisService.analyzeTongueImageBatch(imageIds));
    }

    /**
     * 获取舌象分析结果
     */
    @GetMapping("/{analysisId}")
    @RequiresPermissions("diagnosis:tongue:query")
    public R<TongueAnalysisVO> getTongueAnalysis(@PathVariable Long analysisId) {
        return R.ok(tongueAnalysisService.getTongueAnalysis(analysisId));
    }

    /**
     * 根据图像ID获取舌象分析结果
     */
    @GetMapping("/image/{imageId}")
    @RequiresPermissions("diagnosis:tongue:query")
    public R<TongueAnalysisVO> getTongueAnalysisByImageId(@PathVariable Long imageId) {
        return R.ok(tongueAnalysisService.getTongueAnalysisByImageId(imageId));
    }

    /**
     * 获取诊断记录关联的舌象分析结果列表
     */
    @GetMapping("/list/diagnosis/{diagnosisId}")
    @RequiresPermissions("diagnosis:tongue:list")
    public R<List<TongueAnalysisVO>> getTongueAnalysisListByDiagnosisId(@PathVariable Long diagnosisId) {
        return R.ok(tongueAnalysisService.getTongueAnalysisListByDiagnosisId(diagnosisId));
    }

    /**
     * 获取患者的舌象分析结果列表
     */
    @GetMapping("/list/patient/{patientId}")
    @RequiresPermissions("diagnosis:tongue:list")
    public R<List<TongueAnalysisVO>> getTongueAnalysisListByPatientId(@PathVariable Long patientId) {
        return R.ok(tongueAnalysisService.getTongueAnalysisListByPatientId(patientId));
    }

    /**
     * 删除舌象分析结果
     */
    @DeleteMapping("/{analysisId}")
    @RequiresPermissions("diagnosis:tongue:remove")
    @Log(title = "舌象分析", businessType = BusinessType.DELETE)
    public R<Boolean> deleteTongueAnalysis(@PathVariable Long analysisId) {
        return R.ok(tongueAnalysisService.deleteTongueAnalysis(analysisId));
    }

    /**
     * 导出舌象分析结果报告
     */
    @GetMapping("/export/{analysisId}")
    @RequiresPermissions("diagnosis:tongue:export")
    @Log(title = "舌象分析", businessType = BusinessType.EXPORT)
    public R<String> exportTongueAnalysisReport(@PathVariable Long analysisId) {
        return R.ok(tongueAnalysisService.exportTongueAnalysisReport(analysisId));
    }
}