package com.tcm.medication.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 中药配伍禁忌实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("tcm_medicine_incompatibility")
public class MedicineIncompatibility {

    /**
     * 配伍禁忌ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 药品ID A
     */
    private Long medicineIdA;

    /**
     * 药品名称 A
     */
    private String medicineNameA;

    /**
     * 药品ID B
     */
    private Long medicineIdB;

    /**
     * 药品名称 B
     */
    private String medicineNameB;

    /**
     * 禁忌类型（1：相反，2：相恶，3：相畏，4：相杀）
     */
    private Integer incompatibilityType;

    /**
     * 禁忌描述
     */
    private String description;

    /**
     * 理论依据
     */
    private String theoreticalBasis;

    /**
     * 源出处
     */
    private String source;

    /**
     * 状态（0：无效，1：有效）
     */
    private Integer status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;
}