package com.tcm.platform.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.platform.admin.entity.SysDept;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统部门Mapper接口
 * 
 * <AUTHOR>
 */
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 查询部门管理数据
     * 
     * @param dept 部门信息
     * @return 部门信息集合
     */
    List<SysDept> selectDeptList(SysDept dept);

    /**
     * 根据角色ID查询部门树信息
     * 
     * @param roleId 角色ID
     * @return 部门列表
     */
    List<Long> selectDeptIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 修改部门状态
     * 
     * @param deptId 部门ID
     * @param status 部门状态
     * @return 影响的行数
     */
    int updateStatus(@Param("deptId") Long deptId, @Param("status") Integer status);

    /**
     * 查询部门是否存在用户
     * 
     * @param deptId 部门ID
     * @return 结果
     */
    int checkDeptExistUser(@Param("deptId") Long deptId);

    /**
     * 查询所有子部门ID
     * 
     * @param deptId 部门ID
     * @return 部门ID列表
     */
    List<Long> selectChildrenDeptById(@Param("deptId") Long deptId);
}