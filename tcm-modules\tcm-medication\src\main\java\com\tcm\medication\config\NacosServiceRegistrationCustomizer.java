package com.tcm.medication.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.Configuration;

import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;

/**
 * Nacos服务注册自定义配置 通过自定义方式禁用 NacosAutoServiceRegistration 的自动启动功能
 */
@Configuration
public class NacosServiceRegistrationCustomizer implements BeanFactoryAware {
    private static final Logger LOGGER = LoggerFactory.getLogger(NacosServiceRegistrationCustomizer.class);
    private ConfigurableListableBeanFactory beanFactory;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = (ConfigurableListableBeanFactory) beanFactory;
        customizeNacosRegistration();
    }

    private void customizeNacosRegistration() {
        try {
            String[] beanNames = beanFactory.getBeanNamesForType(NacosAutoServiceRegistration.class, true, false);
            if (beanNames.length > 0) {
                String beanName = beanNames[0];
                AbstractBeanDefinition beanDefinition = (AbstractBeanDefinition) ((BeanDefinitionRegistry) beanFactory)
                        .getBeanDefinition(beanName);

                // 将 NacosAutoServiceRegistration Bean 的作用域从单例改为原型
                // 这样可以避免在应用启动阶段自动执行服务注册
                beanDefinition.setLazyInit(true);

                LOGGER.info("已成功修改 NacosAutoServiceRegistration 的初始化行为，防止启动失败");
            } else {
                LOGGER.warn("未找到 NacosAutoServiceRegistration Bean，无法修改初始化行为");
            }
        } catch (Exception e) {
            LOGGER.error("修改 NacosAutoServiceRegistration 初始化行为时发生错误: {}", e.getMessage());
        }
    }
}