package com.tcm.integration.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 数据同步记录DTO
 *
 * <AUTHOR>
 */
@Data
public class DataSyncRecordDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联的系统配置ID
     */
    @NotNull(message = "系统配置ID不能为空")
    private Long configId;

    /**
     * 系统编码
     */
    @NotBlank(message = "系统编码不能为空")
    private String systemCode;

    /**
     * 同步类型（患者信息、药品信息、检查结果等）
     */
    @NotBlank(message = "同步类型不能为空")
    private String syncType;

    /**
     * 同步方向（IN-导入数据，OUT-导出数据）
     */
    @NotBlank(message = "同步方向不能为空")
    private String syncDirection;

    /**
     * 备注
     */
    private String remark;
}