package com.tcm.user.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.user.entity.UserRole;

/**
 * 用户角色关联服务接口
 */
public interface UserRoleService extends IService<UserRole> {

    /**
     * 为用户分配角色
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     */
    void assignRolesToUser(Long userId, List<Long> roleIds);

    /**
     * 删除用户的所有角色
     *
     * @param userId 用户ID
     */
    void deleteUserRoles(Long userId);
}