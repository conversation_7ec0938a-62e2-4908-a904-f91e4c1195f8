package com.tcm.evaluation.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.evaluation.domain.EvaluationRecord;

/**
 * 评估记录Mapper接口
 */
@Mapper
public interface EvaluationRecordMapper extends BaseMapper<EvaluationRecord> {
    
    /**
     * 根据患者ID查询评估记录，并分页
     * 
     * @param page 分页参数
     * @param patientId 患者ID
     * @return 分页结果
     */
    IPage<EvaluationRecord> selectByPatientId(Page<EvaluationRecord> page, @Param("patientId") Long patientId);
    
    /**
     * 根据医生ID查询评估记录，并分页
     * 
     * @param page 分页参数
     * @param doctorId 医生ID
     * @return 分页结果
     */
    IPage<EvaluationRecord> selectByDoctorId(Page<EvaluationRecord> page, @Param("doctorId") Long doctorId);
    
    /**
     * 根据表单ID查询评估记录，并分页
     * 
     * @param page 分页参数
     * @param formId 表单ID
     * @return 分页结果
     */
    IPage<EvaluationRecord> selectByFormId(Page<EvaluationRecord> page, @Param("formId") Long formId);
    
    /**
     * 根据评估类型和状态查询评估记录，并分页
     * 
     * @param page 分页参数
     * @param evaluationType 评估类型
     * @param status 评估状态
     * @return 分页结果
     */
    IPage<EvaluationRecord> selectByTypeAndStatus(Page<EvaluationRecord> page, 
            @Param("evaluationType") String evaluationType, 
            @Param("status") String status);
} 