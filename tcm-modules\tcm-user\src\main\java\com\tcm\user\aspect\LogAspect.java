package com.tcm.user.aspect;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcm.common.core.annotation.Log;
import com.tcm.user.entity.SysLogOperation;
import com.tcm.user.service.SysLogOperationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 操作日志切面
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class LogAspect {

    private final SysLogOperationService sysLogOperationService;
    private final ObjectMapper objectMapper;

    // 线程变量，用于记录操作开始时间
    private static final ThreadLocal<Long> TIME_THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 配置切入点
     */
    @Pointcut("@annotation(com.tcm.common.core.annotation.Log)")
    public void logPointCut() {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     * @param result    返回结果
     */
    @AfterReturning(pointcut = "logPointCut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        handleLog(joinPoint, null, result);
    }

    /**
     * 异常通知
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(pointcut = "logPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        handleLog(joinPoint, e, null);
    }

    /**
     * 处理日志记录
     *
     * @param joinPoint 切点
     * @param e         异常
     * @param result    返回结果
     */
    protected void handleLog(JoinPoint joinPoint, Exception e, Object result) {
        try {
            long executionTime = System.currentTimeMillis() - TIME_THREAD_LOCAL.get();
            TIME_THREAD_LOCAL.remove();

            // 获取日志注解
            Log logAnnotation = getLogAnnotation(joinPoint);
            if (logAnnotation == null) {
                return;
            }

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                    .getRequestAttributes();
            if (attributes == null) {
                return;
            }
            HttpServletRequest request = attributes.getRequest();

            // 创建日志对象
            SysLogOperation operLog = new SysLogOperation();
            operLog.setStatus(e == null ? 0 : 1);
            operLog.setErrorMsg(e != null ? e.getMessage() : "");
            operLog.setModule(logAnnotation.module());
            operLog.setOperateType(logAnnotation.operateType());
            operLog.setDescription(logAnnotation.description());
            operLog.setMethod(joinPoint.getSignature().toString());
            operLog.setRequestUrl(request.getRequestURI());
            operLog.setRequestMethod(request.getMethod());
            operLog.setIp(getIpAddress(request));
            operLog.setUsername("admin"); // 实际项目中应从安全上下文中获取当前用户
            operLog.setUserId(1L); // 实际项目中应从安全上下文中获取当前用户ID
            operLog.setExecutionTime(executionTime);
            operLog.setCreateTime(LocalDateTime.now());

            // 设置请求参数
            if (logAnnotation.isSaveRequestData()) {
                setRequestParams(joinPoint, operLog);
            }

            // 设置返回值
            if (logAnnotation.isSaveResponseData() && result != null) {
                operLog.setResponseResult(objectMapper.writeValueAsString(result));
            }

            // 保存日志
            sysLogOperationService.saveLog(operLog);
        } catch (Exception ex) {
            log.error("保存操作日志异常", ex);
        }
    }

    /**
     * 获取注解
     *
     * @param joinPoint 切点
     * @return 日志注解
     */
    private Log getLogAnnotation(JoinPoint joinPoint) {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        return method.getAnnotation(Log.class);
    }

    /**
     * 获取请求参数
     *
     * @param joinPoint 切点
     * @param operLog   日志对象
     */
    private void setRequestParams(JoinPoint joinPoint, SysLogOperation operLog) {
        try {
            String params = "";
            if (joinPoint.getArgs().length > 0) {
                Object[] args = joinPoint.getArgs();
                Map<String, Object> paramsMap = new HashMap<>(args.length);
                for (int i = 0; i < args.length; i++) {
                    if (args[i] != null && !isIgnoreParams(args[i])) {
                        paramsMap.put("arg" + i, args[i]);
                    }
                }
                if (!paramsMap.isEmpty()) {
                    params = objectMapper.writeValueAsString(paramsMap);
                }
            }
            operLog.setRequestParams(params);
        } catch (JsonProcessingException e) {
            log.error("解析请求参数异常", e);
        }
    }

    /**
     * 判断是否忽略参数
     *
     * @param arg 参数
     * @return 是否忽略
     */
    private boolean isIgnoreParams(Object arg) {
        Class<?> clazz = arg.getClass();
        // 忽略HttpServletRequest、HttpServletResponse等类型参数
        return clazz.isAssignableFrom(HttpServletRequest.class);
    }

    /**
     * 获取IP地址
     *
     * @param request 请求
     * @return IP地址
     */
    private String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}