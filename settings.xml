<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" 
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地仓库的位置 -->
    <localRepository>C:/Users/<USER>/.m2/repository</localRepository>
  
    <!-- Apache Maven 配置 -->
    <pluginGroups/>
    <proxies/>

    
    <!-- 国内镜像配置 -->
    <mirrors>
        <!-- 阿里云镜像 -->
        <mirror>
            <id>aliyunmaven</id>
            <mirrorOf>*</mirrorOf>
            <name>阿里云公共仓库</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </mirror>
        <!-- 阿里云镜像 - 备用 -->
        <mirror>
            <id>aliyun-central</id>
            <name><PERSON>yun Maven Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <!-- 华为云镜像 -->
        <mirror>
            <id>huaweicloud</id>
            <name>Huawei Cloud Maven</name>
            <url>https://repo.huaweicloud.com/repository/maven/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <!-- 腾讯云镜像 -->
        <mirror>
            <id>tencent</id>
            <name>Tencent Maven Mirror</name>
            <url>https://mirrors.cloud.tencent.com/nexus/repository/maven-public/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <!-- 网易镜像 -->
        <mirror>
            <id>netease</id>
            <name>NetEase Maven Mirror</name>
            <url>https://mirrors.163.com/maven/repository/maven-public/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <!-- 中科大镜像 -->
        <mirror>
            <id>ustc</id>
            <name>USTC Maven Mirror</name>
            <url>https://mirrors.ustc.edu.cn/maven/maven2/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <!-- 清华大学镜像 -->
        <mirror>
            <id>tsinghua</id>
            <name>Tsinghua Maven Mirror</name>
            <url>https://mirrors.tuna.tsinghua.edu.cn/maven/</url>
            <mirrorOf>central</mirrorOf>
        </mirror>
        <!-- JitPack镜像 -->
        <mirror>
            <id>jitpack</id>
            <name>JitPack Maven Repository</name>
            <url>https://jitpack.io</url>
            <mirrorOf>jitpack.io</mirrorOf>
        </mirror>
    </mirrors>

    <!-- 配置: java8, 先从阿里云下载, 没有再去私服下载  -->
    <!-- 20190929 hepengju 测试结果: 影响下载顺序的是profiles标签的配置顺序(后面配置的ali仓库先下载), 而不是activeProfiles的顺序 -->
    <profiles>
        <!-- 全局JDK1.8配置 -->
        <profile>
            <id>jdk1.8</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>

        
        <!-- Nexus私服配置: 第三方jar包下载, 比如oracle的jdbc驱动等 -->
        <profile>
            <id>dev</id>
            <repositories>
                <repository>
                    <id>nexus</id>
                    <url>http://nexus.hepengju.cn:8081/nexus/content/groups/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>public</id>
                    <name>Public Repositories</name>
                    <url>http://nexus.hepengju.cn:8081/nexus/content/groups/public/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        
        <!-- 阿里云配置: 提高国内的jar包下载速度 -->
        <profile>
            <id>ali</id>
            <repositories>
                <repository>
                    <id>alimaven</id>
                    <name>aliyun maven</name>
                    <url>https://maven.aliyun.com/repository/public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>alimaven</id>
                    <name>aliyun maven</name>
                    <url>https://maven.aliyun.com/repository/public/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
        
        <!-- 华为云配置 -->
        <profile>
            <id>huawei</id>
            <repositories>
                <repository>
                    <id>huaweicloud</id>
                    <name>huawei maven</name>
                    <url>https://repo.huaweicloud.com/repository/maven/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>huaweicloud</id>
                    <name>huawei maven</name>
                    <url>https://repo.huaweicloud.com/repository/maven/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- 清华大学配置 -->
        <profile>
            <id>tsinghua</id>
            <repositories>
                <repository>
                    <id>tsinghua</id>
                    <name>Tsinghua Maven Mirror</name>
                    <url>https://mirrors.tuna.tsinghua.edu.cn/maven/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>tsinghua</id>
                    <name>Tsinghua Maven Mirror</name>
                    <url>https://mirrors.tuna.tsinghua.edu.cn/maven/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- USTC配置 -->
        <profile>
            <id>ustc</id>
            <repositories>
                <repository>
                    <id>ustc</id>
                    <name>USTC Maven Mirror</name>
                    <url>https://mirrors.ustc.edu.cn/maven/maven2/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>ustc</id>
                    <name>USTC Maven Mirror</name>
                    <url>https://mirrors.ustc.edu.cn/maven/maven2/</url>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>
    
    <!-- 激活配置 --> 
    <activeProfiles>
        <activeProfile>jdk1.8</activeProfile>
        <activeProfile>ali</activeProfile>
        <activeProfile>huawei</activeProfile>
        <activeProfile>tsinghua</activeProfile>
        <activeProfile>ustc</activeProfile>
    </activeProfiles>
</settings>
