server:
  port: ${tcm.service.appointment.port:${TCM_APPOINTMENT_PORT:9103}}
  servlet:
    context-path: /appointment

spring:
  application:
    name: tcm-appointment
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  autoconfigure:
    exclude:
      # RocketMQ相关
      - org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration
      # Sentinel相关
      - com.alibaba.cloud.sentinel.datasource.config.NacosDataSourceAutoConfiguration
      # Liquibase相关
      - org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration
      # JMX相关自动配置 - 解决ParameterValueMapper问题
      - org.springframework.boot.actuate.autoconfigure.endpoint.jmx.JmxEndpointAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.export.jmx.JmxMetricsExportAutoConfiguration
      # Endpoint相关
      - org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration
      # 管理上下文
      - org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration
      # 健康检查
      - org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration
      # 监控指标
      - org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.web.servlet.WebMvcMetricsAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.metrics.SystemMetricsAutoConfiguration
      - org.springframework.boot.actuate.autoconfigure.scheduling.ScheduledTasksEndpointAutoConfiguration
  
  # Redis配置，使用统一配置中的属性
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    password: ${tcm.service.redis.password}
    database: ${tcm.service.redis.database:0}
    
  # 数据库配置，使用统一配置中的属性
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/${tcm.service.mysql.database}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 900000
      auto-commit: true
      connection-test-query: SELECT 1 FROM DUAL
  
  # 禁用Liquibase
  liquibase:
    enabled: false
    
  # 配置中心和注册中心配置
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        enabled: true
        # 禁用监控相关功能
        metadata:
          "[preserved.heart.beat.interval]": 10000
          "[preserved.heart.beat.timeout]": 30000
          "[preserved.ip.delete.timeout]": 30000
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false   # 禁用配置导入检查
        # Nacos配置
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: application-common.yml
            refresh: true
        # 设置访问超时时间
        timeout: 10000
    # 禁用Sentinel
    sentinel:
      enabled: false

  # 任务调度配置
  task:
    execution:
      pool:
        core-size: 10
        max-size: 50
        queue-capacity: 1000
        keep-alive: 60s
      thread-name-prefix: appointment-task-

# 预约模块配置
appointment:
  allow-multiple-per-time-slot: false
  # 预约提前天数
  advance-days: 14
  # 爽约计入黑名单次数
  no-show-limit: 3

# 公共配置参考块 - 这些配置应该定义在application-common.yml中
# 实现真正的统一配置方式
# tcm:
#   service:
#     # MySQL配置
#     mysql:
#       host: ${MYSQL_HOST:**********}
#       port: ${MYSQL_PORT:3306}
#       username: ${MYSQL_USERNAME:root}
#       password: ${MYSQL_PASSWORD:Kaixin207@1984}
#       database: ${MYSQL_DATABASE:tcm-system}
#     # Redis配置
#     redis:
#       host: ${REDIS_HOST:**********}
#       port: ${REDIS_PORT:6379}
#       password: ${REDIS_PASSWORD:kaixin207}
#       database: ${REDIS_DATABASE:0}
#     # Nacos配置
#     nacos:
#       host: ${NACOS_HOST:***********}
#       port: ${NACOS_PORT:8848}
#       namespace: ${NACOS_NAMESPACE:}
#       username: ${NACOS_USERNAME:nacos}
#       password: ${NACOS_PASSWORD:kaixin207}
#     # RocketMQ配置
#     rocketMq:
#       host: ${ROCKETMQ_HOST:**********}
#       port: ${ROCKETMQ_PORT:9876}

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.appointment.domain
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.tcm.appointment: debug
    org.springframework: warn

# 标准Spring RocketMQ配置 - 使用统一配置中的属性
rocketmq:
  name-server: ${tcm.service.rocketMq.host}:${tcm.service.rocketMq.port}
  producer:
    group: ${spring.application.name}-producer-group
    retry-times-when-send-failed: 0
    retry-times-when-send-async-failed: 0
    retry-next-server: false