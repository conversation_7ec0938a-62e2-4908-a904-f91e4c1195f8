{"configurations": [{"type": "java", "name": "TcmScheduleApplication", "request": "launch", "mainClass": "com.tcm.schedule.TcmScheduleApplication", "projectName": "tcm-schedule"}, {"type": "java", "name": "TcmStatisticsApplication", "request": "launch", "mainClass": "com.tcm.statistics.TcmStatisticsApplication", "projectName": "com.tcm-tcm-statistics"}, {"type": "java", "name": "TcmUserApplication", "request": "launch", "mainClass": "com.tcm.user.TcmUserApplication", "projectName": "tcm-user"}, {"type": "java", "name": "TcmDeviceIntegrationApplication", "request": "launch", "mainClass": "com.tcm.device.TcmDeviceIntegrationApplication", "projectName": "com.tcm-tcm-device-integration"}, {"type": "java", "name": "TcmWorkflowApplication", "request": "launch", "mainClass": "com.tcm.workflow.TcmWorkflowApplication", "projectName": "tcm-workflow"}, {"type": "java", "name": "TcmAppointmentApplication", "request": "launch", "mainClass": "com.tcm.TcmAppointmentApplication", "projectName": "tcm-appointment"}, {"type": "java", "name": "TcmAnalyticsApplication", "request": "launch", "mainClass": "com.tcm.analytics.TcmAnalyticsApplication", "projectName": "tcm-analytics"}, {"type": "java", "name": "SimpleApp", "request": "launch", "mainClass": "com.tcm.security.SimpleApp", "projectName": "tcm-security"}, {"type": "java", "name": "TcmSecurityPreBootstrap", "request": "launch", "mainClass": "com.tcm.security.TcmSecurityPreBootstrap", "projectName": "tcm-security"}, {"type": "java", "name": "TcmSecurityNoLoggingApp", "request": "launch", "mainClass": "com.tcm.security.TcmSecurityNoLoggingApp", "projectName": "tcm-security"}, {"type": "java", "name": "TcmSecurityLauncher", "request": "launch", "mainClass": "com.tcm.security.TcmSecurityLauncher", "projectName": "tcm-security"}, {"type": "java", "name": "StandaloneSecurity", "request": "launch", "mainClass": "com.tcm.security.StandaloneSecurity", "projectName": "tcm-security"}, {"type": "java", "name": "SecurityApp", "request": "launch", "mainClass": "com.tcm.security.SecurityApp", "projectName": "tcm-security"}, {"type": "java", "name": "SimpleSecurityApp", "request": "launch", "mainClass": "com.tcm.security.SimpleSecurityApp", "projectName": "tcm-security"}, {"type": "java", "name": "FixLoggingConflicts", "request": "launch", "mainClass": "com.tcm.security.FixLoggingConflicts", "projectName": "tcm-security"}, {"type": "java", "name": "TcmSecurityApplication", "request": "launch", "mainClass": "com.tcm.security.TcmSecurityApplication", "projectName": "tcm-security"}, {"type": "java", "name": "TcmRemoteConsultationApplication", "request": "launch", "mainClass": "com.tcm.consultation.TcmRemoteConsultationApplication", "projectName": "tcm-remote-consultation"}, {"type": "java", "name": "TcmPrescriptionApplication", "request": "launch", "mainClass": "com.tcm.prescription.TcmPrescriptionApplication", "projectName": "tcm-prescription"}, {"type": "java", "name": "TcmPlatformAuthApplication", "request": "launch", "mainClass": "com.tcm.auth.TcmPlatformAuthApplication", "projectName": "tcm-platform-auth"}, {"type": "java", "name": "TcmPlatformAdminApplication", "request": "launch", "mainClass": "com.tcm.platform.admin.TcmPlatformAdminApplication", "projectName": "tcm-platform-admin"}, {"type": "java", "name": "TcmPatientApplication", "request": "launch", "mainClass": "com.tcm.patient.TcmPatientApplication", "projectName": "tcm-patient"}, {"type": "java", "name": "TcmOrderApplication", "request": "launch", "mainClass": "com.tcm.order.TcmOrderApplication", "projectName": "tcm-order"}, {"type": "java", "name": "TcmMedicationApplication", "request": "launch", "mainClass": "com.tcm.medication.TcmMedicationApplication", "projectName": "tcm-medication"}, {"type": "java", "name": "KnowledgeApplication", "request": "launch", "mainClass": "com.tcm.knowledge.KnowledgeApplication", "projectName": "tcm-knowledge"}, {"type": "java", "name": "TcmIntegrationApplication", "request": "launch", "mainClass": "com.tcm.integration.TcmIntegrationApplication", "projectName": "tcm-integration"}, {"type": "java", "name": "TcmInternationalApplication", "request": "launch", "mainClass": "com.tcm.international.TcmInternationalApplication", "projectName": "tcm-international"}, {"type": "java", "name": "TcmI18nApplication", "request": "launch", "mainClass": "com.tcm.i18n.TcmI18nApplication", "projectName": "tcm-i18n"}, {"type": "java", "name": "TcmDoctorApplication", "request": "launch", "mainClass": "com.tcm.doctor.TcmDoctorApplication", "projectName": "tcm-doctor"}, {"type": "java", "name": "TcmDiagnosisAssistantApplication", "request": "launch", "mainClass": "com.tcm.assistant.TcmDiagnosisAssistantApplication", "projectName": "tcm-diagnosis-assistant"}, {"type": "java", "name": "TcmDiagnosisApplication", "request": "launch", "mainClass": "com.tcm.diagnosis.TcmDiagnosisApplication", "projectName": "tcm-diagnosis"}, {"type": "java", "name": "TcmDeviceIntegrationApplication", "request": "launch", "mainClass": "com.tcm.device.TcmDeviceIntegrationApplication", "projectName": "tcm-device-integration"}, {"type": "java", "name": "TcmStatisticsApplication", "request": "launch", "mainClass": "com.tcm.statistics.TcmStatisticsApplication", "projectName": "tcm-statistics"}, {"type": "java", "name": "TcmAppointmentApplication", "request": "launch", "mainClass": "com.tcm.appointment.TcmAppointmentApplication", "projectName": "tcm-appointment"}, {"type": "java", "name": "TcmAdminApplication", "request": "launch", "mainClass": "com.tcm.admin.TcmAdminApplication", "projectName": "tcm-admin"}, {"type": "java", "name": "TcmPlatformGatewayApplication", "request": "launch", "mainClass": "com.tcm.gateway.TcmPlatformGatewayApplication", "projectName": "tcm-platform-gateway"}, {"type": "java", "name": "Spring Boot-TcmAuthApplication<tcm-auth>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.tcm.auth.TcmAuthApplication", "projectName": "tcm-auth", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": " -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=44135 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Dspring.jmx.enabled=true -Djava.rmi.server.hostname=localhost -Dspring.application.admin.enabled=true -Dspring.boot.project.name=tcm-auth"}, {"type": "java", "name": "Spring Boot-TcmGatewayApplication<tcm-gateway>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.tcm.gateway.TcmGatewayApplication", "projectName": "tcm-gateway", "args": "", "envFile": "${workspaceFolder}/.env"}]}