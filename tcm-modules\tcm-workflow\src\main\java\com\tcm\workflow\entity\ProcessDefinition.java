package com.tcm.workflow.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 流程定义实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("wf_process_definition")
public class ProcessDefinition extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 流程定义ID */
    @TableId(value = "process_definition_id", type = IdType.ASSIGN_ID)
    private String processDefinitionId;

    /** 流程定义KEY */
    private String processDefinitionKey;

    /** 流程定义名称 */
    private String processDefinitionName;

    /** 流程定义分类 */
    private String category;

    /** 流程定义版本 */
    private Integer version;

    /** 部署ID */
    private String deploymentId;

    /** 资源名称 */
    private String resourceName;

    /** 流程状态：1激活，2挂起 */
    private Integer status;

    /** 租户ID */
    private String tenantId;

    /** 描述 */
    private String description;

    /** 是否是主流程 */
    private Boolean mainProcess;

    /** 流程XML内容 */
    private String processXml;

    /** 流程部署时间 */
    private Date deployTime;
}