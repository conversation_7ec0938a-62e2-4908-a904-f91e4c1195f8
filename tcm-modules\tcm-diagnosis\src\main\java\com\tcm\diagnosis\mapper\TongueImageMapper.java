package com.tcm.diagnosis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.diagnosis.domain.TongueImage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 舌象图像Mapper接口
 */
@Mapper
public interface TongueImageMapper extends BaseMapper<TongueImage> {

    /**
     * 根据诊断ID查询舌象图像列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 舌象图像列表
     */
    List<TongueImage> selectTongueImageByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据患者ID查询舌象图像列表
     *
     * @param patientId 患者ID
     * @return 舌象图像列表
     */
    List<TongueImage> selectTongueImageByPatientId(@Param("patientId") Long patientId);
}