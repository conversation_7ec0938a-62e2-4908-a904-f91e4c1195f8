package com.tcm.inspection.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 检查记录实体类
 */
@Data
@Accessors(chain = true)
@TableName("inspection_record")
public class InspectionRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 检查记录ID
     */
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /**
     * 检查单号
     */
    private String inspectionNo;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    @TableField(exist = false)
    private String patientName;

    /**
     * 检查项目ID
     */
    private Long itemId;

    /**
     * 检查项目名称
     */
    @TableField(exist = false)
    private String itemName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    @TableField(exist = false)
    private String doctorName;

    /**
     * 执行科室ID
     */
    private Long deptId;

    /**
     * 执行科室名称
     */
    @TableField(exist = false)
    private String deptName;

    /**
     * 检查申请时间
     */
    private Date applyTime;

    /**
     * 检查执行时间
     */
    private Date executeTime;

    /**
     * 检查结果
     */
    private String result;

    /**
     * 检查结论
     */
    private String conclusion;

    /**
     * 检查图片/文件路径（多个用逗号分隔）
     */
    private String filePaths;

    /**
     * 检查状态（0:待执行 1:已执行 2:已出结果 3:已取消）
     */
    private Integer status;

    /**
     * 检查执行人ID
     */
    private Long executorId;

    /**
     * 检查执行人姓名
     */
    @TableField(exist = false)
    private String executorName;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0:未删除 1:已删除）
     */
    private Integer delFlag;
} 