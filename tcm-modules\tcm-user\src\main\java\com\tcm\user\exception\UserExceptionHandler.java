package com.tcm.user.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户模块全局异常处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class UserExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<Map<String, Object>> handleBusinessException(BusinessException e) {
        log.error("业务异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        result.put("message", "业务处理失败: " + e.getMessage());
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理用户数据异常
     */
    @ExceptionHandler(UserDataException.class)
    public ResponseEntity<Map<String, Object>> handleUserDataException(UserDataException e) {
        log.error("用户数据异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        result.put("message", "用户数据错误: " + e.getMessage());
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        result.put("message", "系统内部错误");
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
}
