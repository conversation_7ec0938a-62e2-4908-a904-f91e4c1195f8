package com.tcm.doctor.dto;

import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 医生数据传输对象
 */
@Data
public class DoctorDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 医生姓名
     */
    @NotBlank(message = "医生姓名不能为空")
    @Size(max = 50, message = "医生姓名长度不能超过50个字符")
    private String doctorName;
    
    /**
     * 医生编号
     */
    private String doctorCode;
    
    /**
     * 职称 (1-主任医师 2-副主任医师 3-主治医师 4-住院医师)
     */
    private Integer title;
    
    /**
     * 专长
     */
    @Size(max = 200, message = "专长长度不能超过200个字符")
    private String specialty;
    
    /**
     * 简介
     */
    @Size(max = 500, message = "简介长度不能超过500个字符")
    private String introduction;
    
    /**
     * 执业证书编号
     */
    @Size(max = 50, message = "执业证书编号长度不能超过50个字符")
    private String licenseNo;
    
    /**
     * 所属医院
     */
    @Size(max = 100, message = "医院名称长度不能超过100个字符")
    private String hospital;
    
    /**
     * 所属科室
     */
    @Size(max = 50, message = "科室名称长度不能超过50个字符")
    private String department;
    
    /**
     * 工作年限
     */
    private Integer workYears;
    
    /**
     * 头像
     */
    private String avatar;
    
    /**
     * 联系电话
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phone;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过50个字符")
    private String email;
    
    /**
     * 状态（0正常 1停用）
     */
    private Integer status;
} 