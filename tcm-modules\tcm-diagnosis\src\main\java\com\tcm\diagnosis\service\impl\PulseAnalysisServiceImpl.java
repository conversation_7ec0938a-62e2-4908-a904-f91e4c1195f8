package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.ServiceException;
import com.tcm.common.core.utils.DateUtils;
import com.tcm.common.core.utils.JsonUtils;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.diagnosis.domain.PulseAnalysis;
import com.tcm.diagnosis.domain.PulseData;
import com.tcm.diagnosis.mapper.PulseAnalysisMapper;
import com.tcm.diagnosis.mapper.PulseDataMapper;
import com.tcm.diagnosis.service.IPulseAnalysisService;
import com.tcm.diagnosis.vo.PulseAnalysisVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 脉象分析服务实现类
 */
@Service
public class PulseAnalysisServiceImpl extends ServiceImpl<PulseAnalysisMapper, PulseAnalysis>
        implements IPulseAnalysisService {
    
    private static final Logger log = LoggerFactory.getLogger(PulseAnalysisServiceImpl.class);

    @Autowired
    private PulseAnalysisMapper pulseAnalysisMapper;

    @Autowired
    private PulseDataMapper pulseDataMapper;

    /**
     * 分析脉象数据
     *
     * @param pulseId 脉诊数据ID
     * @return 分析结果ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long analyzePulseData(Long pulseId) {
        // 获取脉诊数据
        PulseData pulseData = pulseDataMapper.selectById(pulseId);
        if (pulseData == null) {
            throw new ServiceException("脉诊数据不存在");
        }

        // 检查是否已经分析过
        LambdaQueryWrapper<PulseAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PulseAnalysis::getPulseId, pulseId);
        PulseAnalysis existingAnalysis = getOne(queryWrapper, false);

        if (existingAnalysis != null) {
            return existingAnalysis.getAnalysisId();
        }

        // 进行脉象分析（实际项目中应调用AI模型进行分析）
        String pulseWaveData = pulseData.getPulseWaveData();
        String pulsePosition = pulseData.getPulsePosition();
        String pulseDepth = pulseData.getPulseDepth();

        // 根据脉象位置、深度和波形数据进行分析
        Map<String, Object> analysisResult = analyzePulseWave(pulseWaveData, pulsePosition, pulseDepth);

        // 创建分析结果对象
        PulseAnalysis pulseAnalysis = new PulseAnalysis();
        pulseAnalysis.setPulseId(pulseId);
        pulseAnalysis.setPatientId(pulseData.getPatientId());
        pulseAnalysis.setDoctorId(pulseData.getDoctorId());
        pulseAnalysis.setPulseType((String) analysisResult.get("pulseType"));
        pulseAnalysis.setPulseStrength((String) analysisResult.get("pulseStrength"));
        pulseAnalysis.setPulseRhythm((String) analysisResult.get("pulseRhythm"));
        pulseAnalysis.setPulseWidth((String) analysisResult.get("pulseWidth"));
        pulseAnalysis.setAnalysisResult(JsonUtils.toJsonString(analysisResult));
        pulseAnalysis.setClinicalImplications((String) analysisResult.get("clinicalImplications"));
        pulseAnalysis.setAnalysisTime(DateUtils.getNowDate());
        pulseAnalysis.setModelVersion("v1.0.0");
        pulseAnalysis.setConfidence((Integer) analysisResult.get("confidence"));
        pulseAnalysis.setCreateBy(String.valueOf(pulseData.getDoctorId()));

        // 保存分析结果
        save(pulseAnalysis);

        // 更新脉诊数据状态
        pulseData.setStatus(1); // 已分析
        pulseDataMapper.updateById(pulseData);

        return pulseAnalysis.getAnalysisId();
    }

    /**
     * 获取脉象分析结果
     *
     * @param analysisId 分析ID
     * @return 脉象分析VO
     */
    @Override
    public PulseAnalysisVO getPulseAnalysis(Long analysisId) {
        PulseAnalysis pulseAnalysis = getById(analysisId);
        if (pulseAnalysis == null) {
            throw new ServiceException("脉象分析结果不存在");
        }

        return convertToVO(pulseAnalysis);
    }

    /**
     * 获取脉诊数据关联的分析结果
     *
     * @param pulseId 脉诊数据ID
     * @return 脉象分析VO
     */
    @Override
    public PulseAnalysisVO getPulseAnalysisByPulseId(Long pulseId) {
        LambdaQueryWrapper<PulseAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PulseAnalysis::getPulseId, pulseId);
        PulseAnalysis pulseAnalysis = getOne(queryWrapper, false);

        if (pulseAnalysis == null) {
            throw new ServiceException("脉象分析结果不存在");
        }

        return convertToVO(pulseAnalysis);
    }

    /**
     * 获取患者最近的脉象分析
     *
     * @param patientId 患者ID
     * @param limit     限制数量
     * @return 脉象分析VO列表
     */
    @Override
    public List<PulseAnalysisVO> getRecentPulseAnalysis(Long patientId, Integer limit) {
        LambdaQueryWrapper<PulseAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PulseAnalysis::getPatientId, patientId)
                .orderByDesc(PulseAnalysis::getAnalysisTime)
                .last("limit " + limit);
        List<PulseAnalysis> analysisList = list(queryWrapper);

        return convertToVOList(analysisList);
    }

    /**
     * 获取诊断相关的脉象分析列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 脉象分析VO列表
     */
    @Override
    public List<PulseAnalysisVO> getPulseAnalysisListByDiagnosisId(Long diagnosisId) {
        List<PulseAnalysis> analysisList = pulseAnalysisMapper.selectPulseAnalysisByDiagnosisId(diagnosisId);
        return convertToVOList(analysisList);
    }

    /**
     * 根据脉诊数据ID获取脉象分析结果
     *
     * @param dataId 脉诊数据ID
     * @return 脉象分析结果视图对象
     */
    @Override
    public PulseAnalysisVO getPulseAnalysisByDataId(Long dataId) {
        LambdaQueryWrapper<PulseAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PulseAnalysis::getPulseId, dataId);
        PulseAnalysis pulseAnalysis = getOne(queryWrapper, false);

        if (pulseAnalysis == null) {
            throw new ServiceException("脉象分析结果不存在");
        }

        return convertToVO(pulseAnalysis);
    }

    /**
     * 获取患者的脉象分析结果列表
     *
     * @param patientId 患者ID
     * @return 脉象分析结果视图对象列表
     */
    @Override
    public List<PulseAnalysisVO> getPulseAnalysisListByPatientId(Long patientId) {
        LambdaQueryWrapper<PulseAnalysis> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PulseAnalysis::getPatientId, patientId)
                .orderByDesc(PulseAnalysis::getAnalysisTime);
        List<PulseAnalysis> analysisList = list(queryWrapper);

        return convertToVOList(analysisList);
    }

    /**
     * 删除脉象分析
     *
     * @param analysisId 分析ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePulseAnalysis(Long analysisId) {
        PulseAnalysis pulseAnalysis = getById(analysisId);
        if (pulseAnalysis == null) {
            throw new ServiceException("脉象分析结果不存在");
        }

        // 更新对应的脉诊数据状态
        Long pulseId = pulseAnalysis.getPulseId();
        PulseData pulseData = pulseDataMapper.selectById(pulseId);
        if (pulseData != null) {
            pulseData.setStatus(0); // 未分析
            pulseDataMapper.updateById(pulseData);
        }

        return removeById(analysisId);
    }

    /**
     * 分析脉波数据
     * 注：实际项目中，这里应该调用AI模型进行分析
     *
     * @param pulseWaveData 脉波数据
     * @param pulsePosition 脉位
     * @param pulseDepth    脉深
     * @return 分析结果
     */
    private Map<String, Object> analyzePulseWave(String pulseWaveData, String pulsePosition, String pulseDepth) {
        Map<String, Object> result = new HashMap<>();

        // 模拟分析过程，实际项目中应调用AI模型
        // 脉位分析
        String positionAnalysis = analyzePosition(pulsePosition);

        // 脉深分析
        String depthAnalysis = analyzeDepth(pulseDepth);

        // 根据波形数据分析脉象类型
        String pulseType = determinePulseType(pulseWaveData, pulsePosition, pulseDepth);

        // 脉象强度
        String pulseStrength = determinePulseStrength(pulseWaveData);

        // 脉律
        String pulseRhythm = determinePulseRhythm(pulseWaveData);

        // 脉宽
        String pulseWidth = determinePulseWidth(pulseWaveData);

        // 临床意义
        String clinicalImplications = analyzeClinicalMeaning(pulseType, pulseStrength, pulseRhythm, pulseWidth);

        // 填充结果
        result.put("pulseType", pulseType);
        result.put("pulseStrength", pulseStrength);
        result.put("pulseRhythm", pulseRhythm);
        result.put("pulseWidth", pulseWidth);
        result.put("positionAnalysis", positionAnalysis);
        result.put("depthAnalysis", depthAnalysis);
        result.put("clinicalImplications", clinicalImplications);
        result.put("confidence", 85); // 模拟置信度

        return result;
    }

    /**
     * 分析脉位
     *
     * @param pulsePosition 脉位
     * @return 分析结果
     */
    private String analyzePosition(String pulsePosition) {
        if (StringUtils.isEmpty(pulsePosition)) {
            return "未知脉位";
        }

        // 脉位分析
        String result;
        switch (pulsePosition) {
            case "寸":
                result = "寸脉主心肺，反映上焦气血状态";
                break;
            case "关":
                result = "关脉主脾胃，反映中焦气血状态";
                break;
            case "尺":
                result = "尺脉主肾，反映下焦气血状态";
                break;
            default:
                result = "未知脉位，需进一步确认";
        }

        return result;
    }

    /**
     * 分析脉深
     *
     * @param pulseDepth 脉深
     * @return 分析结果
     */
    private String analyzeDepth(String pulseDepth) {
        if (StringUtils.isEmpty(pulseDepth)) {
            return "未知脉深";
        }

        // 脉深分析
        String result;
        switch (pulseDepth) {
            case "浮":
                result = "浮脉多为表证，如外感风寒等";
                break;
            case "沉":
                result = "沉脉多为里证，如内伤、寒凝血瘀等";
                break;
            case "中":
                result = "中脉多为平脉，如正常生理状态或半表半里证";
                break;
            default:
                result = "未知脉深，需进一步确认";
        }

        return result;
    }

    /**
     * 确定脉象类型
     *
     * @param pulseWaveData 脉波数据
     * @param pulsePosition 脉位
     * @param pulseDepth    脉深
     * @return 脉象类型
     */
    private String determinePulseType(String pulseWaveData, String pulsePosition, String pulseDepth) {
        // 实际项目中应分析波形数据特征，这里模拟返回结果
        String[] pulseTypes = { "浮脉", "沉脉", "迟脉", "数脉", "虚脉", "实脉", "滑脉", "涩脉", "弦脉", "紧脉" };
        int index = Math.abs(pulseWaveData.hashCode() % pulseTypes.length);
        return pulseTypes[index];
    }

    /**
     * 确定脉象强度
     *
     * @param pulseWaveData 脉波数据
     * @return 脉象强度
     */
    private String determinePulseStrength(String pulseWaveData) {
        // 实际项目中应分析波形数据振幅，这里模拟返回结果
        String[] strengthLevels = { "微弱", "适中", "强劲" };
        int index = Math.abs(pulseWaveData.hashCode() % strengthLevels.length);
        return strengthLevels[index];
    }

    /**
     * 确定脉律
     *
     * @param pulseWaveData 脉波数据
     * @return 脉律
     */
    private String determinePulseRhythm(String pulseWaveData) {
        // 实际项目中应分析波形数据周期性，这里模拟返回结果
        String[] rhythmTypes = { "规律", "不规律", "间代", "结代" };
        int index = Math.abs(pulseWaveData.hashCode() % rhythmTypes.length);
        return rhythmTypes[index];
    }

    /**
     * 确定脉宽
     *
     * @param pulseWaveData 脉波数据
     * @return 脉宽
     */
    private String determinePulseWidth(String pulseWaveData) {
        // 实际项目中应分析波形数据宽度，这里模拟返回结果
        String[] widthTypes = { "细", "中等", "宽" };
        int index = Math.abs(pulseWaveData.hashCode() % widthTypes.length);
        return widthTypes[index];
    }

    /**
     * 分析临床意义
     *
     * @param pulseType     脉象类型
     * @param pulseStrength 脉象强度
     * @param pulseRhythm   脉律
     * @param pulseWidth    脉宽
     * @return 临床意义
     */
    private String analyzeClinicalMeaning(String pulseType, String pulseStrength, String pulseRhythm,
            String pulseWidth) {
        // 根据各脉象特征组合分析临床意义
        StringBuilder sb = new StringBuilder();

        // 脉象类型意义
        switch (pulseType) {
            case "浮脉":
                sb.append("浮脉主表证，多见于外感风寒，表邪未解；");
                break;
            case "沉脉":
                sb.append("沉脉主里证，多见于内伤、阴寒内盛或气血不足；");
                break;
            case "迟脉":
                sb.append("迟脉主寒证，多见于阳气虚弱、寒邪内盛；");
                break;
            case "数脉":
                sb.append("数脉主热证，多见于外感热病、内伤热证；");
                break;
            case "虚脉":
                sb.append("虚脉主虚证，多见于气血亏虚；");
                break;
            case "实脉":
                sb.append("实脉主实证，多见于邪气实盛；");
                break;
            case "滑脉":
                sb.append("滑脉主痰湿、食积、气血有余，女子见滑脉可能示孕；");
                break;
            case "涩脉":
                sb.append("涩脉主血虚、血瘀或津液不足；");
                break;
            case "弦脉":
                sb.append("弦脉主肝胆疾病，多见于情志不舒、肝气郁结；");
                break;
            case "紧脉":
                sb.append("紧脉主寒证或痛症，多见于外感风寒或内有寒凝气滞；");
                break;
            default:
                sb.append("脉象需进一步辨析；");
        }

        // 补充其他特征意义
        if ("不规律".equals(pulseRhythm) || "间代".equals(pulseRhythm) || "结代".equals(pulseRhythm)) {
            sb.append("脉律不整，需注意心脏功能状况；");
        }

        if ("微弱".equals(pulseStrength)) {
            sb.append("脉力偏弱，提示气血不足；");
        } else if ("强劲".equals(pulseStrength)) {
            sb.append("脉力偏强，提示正气充盛或邪气旺盛；");
        }

        return sb.toString();
    }

    /**
     * 将实体转换为视图对象
     *
     * @param pulseAnalysis 脉象分析实体
     * @return 脉象分析视图对象
     */
    private PulseAnalysisVO convertToVO(PulseAnalysis pulseAnalysis) {
        if (pulseAnalysis == null) {
            return null;
        }

        PulseAnalysisVO vo = new PulseAnalysisVO();
        vo.setAnalysisId(pulseAnalysis.getAnalysisId());
        vo.setPulseId(pulseAnalysis.getPulseId());
        vo.setPatientId(pulseAnalysis.getPatientId());
        vo.setDoctorId(pulseAnalysis.getDoctorId());
        vo.setPulseType(pulseAnalysis.getPulseType());
        vo.setPulseStrength(pulseAnalysis.getPulseStrength());
        vo.setPulseRhythm(pulseAnalysis.getPulseRhythm());
        vo.setPulseWidth(pulseAnalysis.getPulseWidth());
        vo.setAnalysisResult(pulseAnalysis.getAnalysisResult());
        vo.setClinicalImplications(pulseAnalysis.getClinicalImplications());
        vo.setAnalysisTime(pulseAnalysis.getAnalysisTime());
        vo.setModelVersion(pulseAnalysis.getModelVersion());
        vo.setConfidence(pulseAnalysis.getConfidence());
        vo.setCreateTime(pulseAnalysis.getCreateTime());

        return vo;
    }

    /**
     * 将实体列表转换为视图对象列表
     *
     * @param pulseAnalysisList 脉象分析实体列表
     * @return 脉象分析视图对象列表
     */
    private List<PulseAnalysisVO> convertToVOList(List<PulseAnalysis> pulseAnalysisList) {
        if (pulseAnalysisList == null) {
            return List.of();
        }

        return pulseAnalysisList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
    
    /**
     * 批量分析脉诊数据
     *
     * @param dataIds 脉诊数据ID列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean analyzePulseDataBatch(List<Long> dataIds) {
        if (dataIds == null || dataIds.isEmpty()) {
            throw new ServiceException("脉诊数据ID列表不能为空");
        }
        
        for (Long dataId : dataIds) {
            try {
                analyzePulseData(dataId);
            } catch (Exception e) {
                log.error("分析脉诊数据失败, dataId={}, error={}", dataId, e.getMessage());
                // 继续处理下一条数据，不影响批量处理
            }
        }
        
        return true;
    }
    
    /**
     * 导出脉象分析结果报告
     *
     * @param analysisId 分析结果ID
     * @return 报告文件路径
     */
    @Override
    public String exportPulseAnalysisReport(Long analysisId) {
        PulseAnalysis pulseAnalysis = getById(analysisId);
        if (pulseAnalysis == null) {
            throw new ServiceException("脉象分析结果不存在");
        }
        
        // 获取相关的脉诊数据
        PulseData pulseData = pulseDataMapper.selectById(pulseAnalysis.getPulseId());
        if (pulseData == null) {
            throw new ServiceException("脉诊数据不存在");
        }
        
        try {
            // 生成报告文件名
            String fileName = "pulse_analysis_" + analysisId + "_" + System.currentTimeMillis() + ".pdf";
            String reportPath = System.getProperty("user.dir") + File.separator + "reports" + File.separator + fileName;
            
            // 确保报告目录存在
            File reportDir = new File(System.getProperty("user.dir") + File.separator + "reports");
            if (!reportDir.exists()) {
                reportDir.mkdirs();
            }
            
            // 实际项目中应调用PDF生成库生成报告
            // 这里仅作示例，不实际生成PDF
            // 可以使用iText、Apache PDFBox等库生成PDF报告
            log.info("导出脉象分析报告: {}", reportPath);
            
            // 返回报告路径
            return reportPath;
        } catch (Exception e) {
            log.error("导出脉象分析报告失败: {}", e.getMessage());
            throw new ServiceException("导出脉象分析报告失败: " + e.getMessage());
        }
    }
}