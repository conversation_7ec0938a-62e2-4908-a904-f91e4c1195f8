package com.tcm.platform.admin.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.platform.admin.dto.UserDTO;
import com.tcm.platform.admin.entity.SysRole;
import com.tcm.platform.admin.entity.SysUser;
import com.tcm.platform.admin.entity.SysUserRole;
import com.tcm.platform.admin.mapper.SysMenuMapper;
import com.tcm.platform.admin.mapper.SysRoleMapper;
import com.tcm.platform.admin.mapper.SysUserMapper;
import com.tcm.platform.admin.mapper.SysUserRoleMapper;
import com.tcm.platform.admin.service.SysUserService;
import com.tcm.platform.admin.config.AdminConfig;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 系统用户服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysRoleMapper roleMapper;

    @Autowired
    private SysMenuMapper menuMapper;

    @Autowired
    private SysUserRoleMapper userRoleMapper;

    @Autowired
    private AdminConfig adminConfig;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    /**
     * 分页查询用户列表
     */
    @Override
    public IPage<SysUser> selectUserPage(Integer pageNum, Integer pageSize, SysUser user) {
        Page<SysUser> page = new Page<>(pageNum, pageSize);
        return userMapper.selectUserPage(page, user);
    }

    /**
     * 根据用户ID查询用户详情
     */
    @Override
    public UserDTO selectUserById(Long userId) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            return null;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);

        // 查询用户角色
        List<SysRole> roles = roleMapper.selectRolesByUserId(userId);
        if (!CollectionUtils.isEmpty(roles)) {
            List<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toList());
            List<String> roleNames = roles.stream().map(SysRole::getRoleName).collect(Collectors.toList());
            userDTO.setRoleIds(roleIds);
            userDTO.setRoleNames(roleNames);
        }

        return userDTO;
    }

    /**
     * 根据用户名查询用户
     */
    @Override
    public UserDTO selectUserByUsername(String username) {
        SysUser user = userMapper.selectByUsername(username);
        if (user == null) {
            return null;
        }

        UserDTO userDTO = new UserDTO();
        BeanUtils.copyProperties(user, userDTO);

        // 查询用户角色
        List<SysRole> roles = roleMapper.selectRolesByUserId(user.getId());
        if (!CollectionUtils.isEmpty(roles)) {
            List<Long> roleIds = roles.stream().map(SysRole::getId).collect(Collectors.toList());
            List<String> roleNames = roles.stream().map(SysRole::getRoleName).collect(Collectors.toList());
            userDTO.setRoleIds(roleIds);
            userDTO.setRoleNames(roleNames);
        }

        return userDTO;
    }

    /**
     * 检查用户名是否唯一
     */
    @Override
    public boolean checkUsernameUnique(String username) {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username);
        return userMapper.selectCount(wrapper) > 0;
    }

    /**
     * 新增用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertUser(UserDTO userDTO) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userDTO, user);

        // 设置用户密码
        String password = userDTO.getPassword();
        if (password == null || password.isEmpty()) {
            password = adminConfig.getDefaultPassword();
        }
        user.setPassword(passwordEncoder.encode(password));

        // 设置创建时间等字段
        LocalDateTime now = LocalDateTime.now();
        user.setCreateTime(now);
        user.setUpdateTime(now);
        user.setDelFlag(0);
        user.setStatus(1); // 默认启用

        // 插入用户
        int rows = userMapper.insert(user);
        if (rows <= 0) {
            return false;
        }

        // 插入用户角色关系
        insertUserRoles(user.getId(), userDTO.getRoleIds());

        return true;
    }

    /**
     * 修改用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(UserDTO userDTO) {
        SysUser user = new SysUser();
        BeanUtils.copyProperties(userDTO, user);
        user.setUpdateTime(LocalDateTime.now());

        // 更新用户信息
        int rows = userMapper.updateById(user);
        if (rows <= 0) {
            return false;
        }

        // 更新用户角色关系
        // 先删除原有关系
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, user.getId());
        userRoleMapper.delete(wrapper);

        // 再插入新的关系
        insertUserRoles(user.getId(), userDTO.getRoleIds());

        return true;
    }

    /**
     * 删除用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUserById(Long userId) {
        int rows = userMapper.deleteById(userId);
        if (rows <= 0) {
            return false;
        }

        // 删除用户角色关系
        LambdaQueryWrapper<SysUserRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRole::getUserId, userId);
        userRoleMapper.delete(wrapper);

        return true;
    }

    /**
     * 重置用户密码
     */
    @Override
    public boolean resetPassword(Long userId, String password) {
        String encodedPassword = passwordEncoder.encode(password);
        return userMapper.updatePassword(userId, encodedPassword) > 0;
    }

    /**
     * 修改用户状态
     */
    @Override
    public boolean updateUserStatus(Long userId, Integer status) {
        return userMapper.updateStatus(userId, status) > 0;
    }

    /**
     * 获取用户权限
     */
    @Override
    public List<String> getUserPermissions(Long userId) {
        return menuMapper.selectMenuPermsByUserId(userId);
    }

    /**
     * 插入用户角色关系
     */
    private void insertUserRoles(Long userId, List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }

        List<SysUserRole> userRoles = new ArrayList<>();
        for (Long roleId : roleIds) {
            SysUserRole userRole = new SysUserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRole.setCreateTime(LocalDateTime.now());
            userRoles.add(userRole);
        }

        userRoleMapper.batchInsert(userRoles);
    }
}