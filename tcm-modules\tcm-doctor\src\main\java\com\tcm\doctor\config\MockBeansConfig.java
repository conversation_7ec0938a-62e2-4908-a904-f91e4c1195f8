package com.tcm.doctor.config;

import com.tcm.doctor.mapper.DoctorAppointmentMapper;
import com.tcm.doctor.mapper.DoctorScheduleMapper;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.lang.reflect.Proxy;

/**
 * Redis模拟Bean配置类
 * 提供Redis相关组件的模拟实现，以便应用可以在没有Redis服务器的情况下启动
 * 只有在配置属性tcm.mock.redis=true时才会激活
 */
@Configuration
@ConditionalOnProperty(name = "tcm.mock.redis", havingValue = "true", matchIfMissing = false)
public class MockBeansConfig {

    /**
     * 创建任意接口的模拟实现
     * @param interfaceClass 需要模拟的接口类
     * @return 接口的模拟实现
     */
    @SuppressWarnings("unchecked")
    private <T> T createMockProxy(Class<T> interfaceClass) {
        return (T) Proxy.newProxyInstance(
                this.getClass().getClassLoader(),
                new Class<?>[]{interfaceClass},
                (proxy, method, args) -> {
                    Class<?> returnType = method.getReturnType();
                    if (returnType.equals(Void.TYPE)) {
                        return null;
                    } else if (returnType.isPrimitive()) {
                        if (returnType.equals(Boolean.TYPE)) {
                            return false;
                        } else if (returnType.equals(Integer.TYPE)) {
                            return 0;
                        } else if (returnType.equals(Long.TYPE)) {
                            return 0L;
                        } else if (returnType.equals(Double.TYPE)) {
                            return 0.0;
                        } else if (returnType.equals(Float.TYPE)) {
                            return 0.0f;
                        } else {
                            return 0;
                        }
                    } else if (returnType.equals(String.class)) {
                        return "";
                    } else {
                        return null;
                    }
                });
    }

    /**
     * 模拟Redis连接工厂
     * 当缺少RedisConnectionFactory时，提供一个模拟实现
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(RedisConnectionFactory.class)
    public RedisConnectionFactory mockRedisConnectionFactory() {
        return createMockProxy(RedisConnectionFactory.class);
    }
    
    /**
     * 模拟RedisTemplate
     * 提供一个使用模拟连接工厂的RedisTemplate
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<Object, Object> redisTemplate() {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(mockRedisConnectionFactory());
        return template;
    }
    
    /**
     * 模拟StringRedisTemplate
     * 提供一个使用模拟连接工厂的StringRedisTemplate
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(StringRedisTemplate.class)
    public StringRedisTemplate stringRedisTemplate() {
        StringRedisTemplate template = new StringRedisTemplate();
        template.setConnectionFactory(mockRedisConnectionFactory());
        return template;
    }
    
    /**
     * 模拟RedisConnection
     * 当需要直接使用RedisConnection时，提供一个模拟实现
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(RedisConnection.class)
    public RedisConnection redisConnection() {
        return createMockProxy(RedisConnection.class);
    }
    
    /**
     * 模拟DoctorAppointmentMapper
     * 提供一个模拟实现，避免应用启动时因缺少数据库连接而失败
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(DoctorAppointmentMapper.class)
    public DoctorAppointmentMapper mockDoctorAppointmentMapper() {
        return createMockProxy(DoctorAppointmentMapper.class);
    }
    
    /**
     * 模拟DoctorScheduleMapper
     * 提供一个模拟实现，为MockDoctorScheduleServiceImpl提供支持
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(DoctorScheduleMapper.class)
    public DoctorScheduleMapper mockDoctorScheduleMapper() {
        return createMockProxy(DoctorScheduleMapper.class);
    }
    
    /**
     * 模拟IDoctorAppointmentService
     * 提供一个模拟实现，解决Bean依赖注入问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(com.tcm.doctor.service.IDoctorAppointmentService.class)
    public com.tcm.doctor.service.IDoctorAppointmentService mockDoctorAppointmentService() {
        return createMockProxy(com.tcm.doctor.service.IDoctorAppointmentService.class);
    }
    
    /**
     * 模拟IOnlineConsultationService
     * 提供一个模拟实现，解决在线问诊控制器依赖注入问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(com.tcm.doctor.service.IOnlineConsultationService.class)
    public com.tcm.doctor.service.IOnlineConsultationService mockOnlineConsultationService() {
        return createMockProxy(com.tcm.doctor.service.IOnlineConsultationService.class);
    }
    
    /**
     * 模拟IDoctorService
     * 提供一个模拟实现，解决医生控制器依赖注入问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(com.tcm.doctor.service.IDoctorService.class)
    public com.tcm.doctor.service.IDoctorService mockDoctorService() {
        return createMockProxy(com.tcm.doctor.service.IDoctorService.class);
    }
    
    /**
     * 模拟IDoctorScheduleService
     * 提供一个模拟实现，解决医生排班相关依赖注入问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(com.tcm.doctor.service.IDoctorScheduleService.class)
    public com.tcm.doctor.service.IDoctorScheduleService mockDoctorScheduleService() {
        return createMockProxy(com.tcm.doctor.service.IDoctorScheduleService.class);
    }
    
    /**
     * 模拟IConsultationMessageService
     * 提供一个模拟实现，解决问诊消息相关依赖注入问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(com.tcm.doctor.service.IConsultationMessageService.class)
    public com.tcm.doctor.service.IConsultationMessageService mockConsultationMessageService() {
        return createMockProxy(com.tcm.doctor.service.IConsultationMessageService.class);
    }
    
    /**
     * 模拟IDoctorEvaluationService
     * 提供一个模拟实现，解决医生评价相关依赖注入问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(com.tcm.doctor.service.IDoctorEvaluationService.class)
    public com.tcm.doctor.service.IDoctorEvaluationService mockDoctorEvaluationService() {
        return createMockProxy(com.tcm.doctor.service.IDoctorEvaluationService.class);
    }
    
    /**
     * 模拟RedissonClient
     * 提供一个模拟实现，解决Redis连接问题
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient mockRedissonClient() {
        return createMockProxy(RedissonClient.class);
    }
}
