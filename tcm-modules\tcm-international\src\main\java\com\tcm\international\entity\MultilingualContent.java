package com.tcm.international.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 多语言内容实体类
 */
@Data
@TableName("tcm_multilingual_content")
public class MultilingualContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容键
     */
    private String contentKey;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * 语言代码
     */
    private String locale;

    /**
     * 翻译内容
     */
    private String content;

    /**
     * 状态（1正常 0禁用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
