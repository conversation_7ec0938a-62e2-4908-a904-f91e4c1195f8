package com.tcm.evaluation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 评估服务启动程序
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(exclude = {
        org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration.class,
        org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration.class,
        org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class })
@ComponentScan(basePackages = {"com.tcm.evaluation", "com.tcm.common"})
public class TcmEvaluationApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmEvaluationApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性
    static {
        // 禁用Log4j2以解决日志冲突
        System.setProperty("log4j2.disable", "true");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-evaluation");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_EVALUATION_PORT") == null) {
            System.setProperty("server.port", "9301");
        }

        // 配置数据源类型，明确指定HikariCP
        System.setProperty("spring.datasource.type", "com.zaxxer.hikari.HikariDataSource");
    }

    public static void main(String[] args) {
        try {
            System.out.println("评估服务正在启动...");
            LOGGER.info("Starting evaluation service module...");

            // 设置日志级别
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");
            System.setProperty("spring.main.banner-mode", "off");

            // 设置端口
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.evaluation.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_EVALUATION_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "9301";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            // 启动应用程序
            ConfigurableApplicationContext context = SpringApplication.run(TcmEvaluationApplication.class, args);

            // 获取实际使用的端口
            String serverPort = determineActualServerPort(fixedPort);

            // 确保端口被设置到系统属性中
            System.setProperty("server.port", serverPort);

            LOGGER.info("=====================================================");
            LOGGER.info("           Evaluation Service Started!              ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);

            // 获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            LOGGER.info("=====================================================");
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 确定实际使用的服务器端口
     * 
     * @param configuredPort 配置的端口
     * @return 实际使用的端口
     */
    private static String determineActualServerPort(String configuredPort) {
        // 如果PortListener已经捕获到了实际端口，则使用它
        if (actualServerPort > 0) {
            return String.valueOf(actualServerPort);
        }
        return configuredPort;
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web服务器已初始化，使用端口: {}", actualServerPort);
        }
    }
} 