package com.tcm.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * JWT配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "auth.jwt")
public class JwtConfig {

    /**
     * JWT密钥
     */
    private String secretKey;

    /**
     * 访问令牌过期时间（分钟）
     */
    private Long accessTokenExpiration;

    /**
     * 刷新令牌过期时间（天）
     */
    private Long refreshTokenExpiration;
}