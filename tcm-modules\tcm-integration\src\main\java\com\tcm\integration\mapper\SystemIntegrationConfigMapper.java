package com.tcm.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.integration.entity.SystemIntegrationConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统集成配置Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface SystemIntegrationConfigMapper extends BaseMapper<SystemIntegrationConfig> {

    /**
     * 根据系统编码查询配置
     *
     * @param systemCode 系统编码
     * @return 系统配置信息
     */
    SystemIntegrationConfig selectBySystemCode(@Param("systemCode") String systemCode);
}