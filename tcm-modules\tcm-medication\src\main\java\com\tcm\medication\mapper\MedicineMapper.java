package com.tcm.medication.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.medication.entity.Medicine;
import org.apache.ibatis.annotations.Param;

/**
 * 药品Mapper接口
 *
 * <AUTHOR>
 */
public interface MedicineMapper extends BaseMapper<Medicine> {

    /**
     * 分页查询药品列表
     *
     * @param page         分页参数
     * @param medicineName 药品名称
     * @param medicineCode 药品编码
     * @param medicineType 药品类型
     * @param categoryId   分类ID
     * @return 分页结果
     */
    IPage<Medicine> selectMedicinePage(Page<Medicine> page, @Param("medicineName") String medicineName,
            @Param("medicineCode") String medicineCode, @Param("medicineType") Integer medicineType,
            @Param("categoryId") Long categoryId);

    /**
     * 根据药品名称查询药品
     *
     * @param medicineName 药品名称
     * @return 药品信息
     */
    Medicine selectByName(@Param("medicineName") String medicineName);

    /**
     * 根据药品编码查询药品
     *
     * @param medicineCode 药品编码
     * @return 药品信息
     */
    Medicine selectByCode(@Param("medicineCode") String medicineCode);

    /**
     * 更新药品库存
     *
     * @param id    药品ID
     * @param stock 库存数量
     * @return 影响行数
     */
    int updateStock(@Param("id") Long id, @Param("stock") Integer stock);
}