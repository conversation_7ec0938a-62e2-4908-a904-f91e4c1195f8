package com.tcm.integration.util;

import java.net.URL;
import java.net.URLClassLoader;

/**
 * 日志隔离类加载器
 * 用于在运行时隔离冲突的日志实现类
 */
public class IsolatingClassLoader extends URLClassLoader {
    
    private static final String[] ISOLATED_PREFIXES = {
        "org.slf4j.impl.",
        "org.apache.logging.slf4j.",
        "ch.qos.logback.",
        "org.apache.logging.log4j.slf4j"
    };
    
    public IsolatingClassLoader(ClassLoader parent) {
        super(new URL[0], parent);
    }
    
    @Override
    protected Class<?> loadClass(String name, boolean resolve) throws ClassNotFoundException {
        // 检查是否是需要隔离的类
        for (String prefix : ISOLATED_PREFIXES) {
            if (name.startsWith(prefix)) {
                // 对于冲突的日志实现类，让父类加载器处理，但在出现冲突时优先使用系统类加载器
                try {
                    return ClassLoader.getSystemClassLoader().loadClass(name);
                } catch (ClassNotFoundException e) {
                    // 如果系统类加载器找不到，则返回空实现
                    if (name.contains("StaticLoggerBinder")) {
                        // 返回一个不存在的类名，强制SLF4J使用NOP实现
                        throw new ClassNotFoundException("Logging implementation deliberately isolated");
                    }
                }
            }
        }
        
        // 对于其他类，正常加载
        return super.loadClass(name, resolve);
    }
}
