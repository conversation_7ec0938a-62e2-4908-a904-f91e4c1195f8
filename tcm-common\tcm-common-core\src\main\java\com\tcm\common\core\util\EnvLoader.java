package com.tcm.common.core.util;

import com.tcm.common.core.constant.ServiceConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 环境变量加载工具类 用于从属性文件加载环境变量配置
 */
public class EnvLoader {
    private static final Logger log = LoggerFactory.getLogger(EnvLoader.class);

    private static final String DEFAULT_ENV_FILE = "tcm-env.properties";
    private static final String ENV_PREFIX = "tcm-env-";
    private static final String ENV_SUFFIX = ".properties";
    private static final String ENV_PROFILE_KEY = "spring.profiles.active";
    private static final String ENV_PATH_KEY = "tcm.env.path";
    private static final String ENV_PROPERTY_KEY = "tcm.service.jdk.home";
    private static final String ENV_PROPERTY_WINDOWS_KEY = "tcm.service.jdk.windows.home";
    private static final String ENV_PROPERTY_LINUX_KEY = "tcm.service.jdk.linux.home";
    private static final String ENV_PROPERTY_MAC_KEY = "tcm.service.jdk.mac.home";

    private static Properties envProperties = new Properties();

    static {
        loadEnvProperties();
    }

    /**
     * 加载环境变量配置
     */
    private static void loadEnvProperties() {
        try {
            // 1. 尝试从系统属性指定的路径加载
            String envPath = System.getProperty(ENV_PATH_KEY);
            if (StringUtils.hasText(envPath)) {
                File envFile = new File(envPath);
                if (envFile.exists() && envFile.isFile()) {
                    try (InputStream is = new FileInputStream(envFile)) {
                        envProperties.load(is);
                        log.info("从指定路径加载环境配置成功: {}", envPath);
                        return;
                    } catch (IOException e) {
                        log.warn("从指定路径加载环境配置失败: {}", envPath, e);
                    }
                }
            }

            // 2. 尝试根据激活的profile加载对应的配置文件
            String activeProfile = System.getProperty(ENV_PROFILE_KEY);
            if (StringUtils.hasText(activeProfile)) {
                String profileEnvFile = ENV_PREFIX + activeProfile + ENV_SUFFIX;

                // 先尝试从文件系统加载
                Resource fsResource = new FileSystemResource(profileEnvFile);
                if (fsResource.exists()) {
                    try (InputStream is = fsResource.getInputStream()) {
                        envProperties.load(is);
                        log.info("从文件系统加载环境配置成功: {}", profileEnvFile);
                        return;
                    } catch (IOException e) {
                        log.warn("从文件系统加载环境配置失败: {}", profileEnvFile, e);
                    }
                }

                // 再尝试从类路径加载
                Resource cpResource = new ClassPathResource(profileEnvFile);
                if (cpResource.exists()) {
                    try (InputStream is = cpResource.getInputStream()) {
                        envProperties.load(is);
                        log.info("从类路径加载环境配置成功: {}", profileEnvFile);
                        return;
                    } catch (IOException e) {
                        log.warn("从类路径加载环境配置失败: {}", profileEnvFile, e);
                    }
                }
            }

            // 3. 加载默认配置文件
            // 先尝试从文件系统加载
            Resource fsResource = new FileSystemResource(DEFAULT_ENV_FILE);
            if (fsResource.exists()) {
                try (InputStream is = fsResource.getInputStream()) {
                    envProperties.load(is);
                    log.info("从文件系统加载默认环境配置成功: {}", DEFAULT_ENV_FILE);
                    return;
                } catch (IOException e) {
                    log.warn("从文件系统加载默认环境配置失败: {}", DEFAULT_ENV_FILE, e);
                }
            }

            // 再尝试从类路径加载
            Resource cpResource = new ClassPathResource(DEFAULT_ENV_FILE);
            if (cpResource.exists()) {
                try (InputStream is = cpResource.getInputStream()) {
                    envProperties.load(is);
                    log.info("从类路径加载默认环境配置成功: {}", DEFAULT_ENV_FILE);
                    return;
                } catch (IOException e) {
                    log.warn("从类路径加载默认环境配置失败: {}", DEFAULT_ENV_FILE, e);
                }
            }

            log.warn("未能加载任何环境配置文件");
        } catch (Exception e) {
            log.error("加载环境配置异常", e);
        }
    }

    /**
     * 获取配置属性值
     * 
     * @param key 属性键
     * @return 属性值，如果不存在则返回null
     */
    public static String getProperty(String key) {
        return envProperties.getProperty(key);
    }

    /**
     * 获取配置属性值，如果不存在则返回默认值
     * 
     * @param key          属性键
     * @param defaultValue 默认值
     * @return 属性值，如果不存在则返回默认值
     */
    public static String getProperty(String key, String defaultValue) {
        return envProperties.getProperty(key, defaultValue);
    }

    /**
     * 获取JDK路径 优先从环境变量中获取，其次从配置文件中获取
     * 
     * @return JDK路径
     */
    public static String getJdkHome() {
        String jdkHome = null;

        // 1. 从环境变量中获取
        jdkHome = System.getenv("TCM_JDK_HOME");
        if (StringUtils.hasText(jdkHome)) {
            log.debug("从环境变量中获取JDK路径: {}", jdkHome);
            return jdkHome;
        }

        // 2. 从系统属性中获取
        jdkHome = System.getProperty("tcm.service.jdk.home");
        if (StringUtils.hasText(jdkHome)) {
            log.debug("从系统属性中获取JDK路径: {}", jdkHome);
            return jdkHome;
        }

        // 3. 从配置文件中获取
        Properties props = loadProperties();
        if (props != null) {
            // 获取操作系统特定的JDK路径
            String osName = System.getProperty("os.name", "").toLowerCase();

            if (osName.contains("win")) {
                jdkHome = props.getProperty(ENV_PROPERTY_WINDOWS_KEY);
                if (StringUtils.hasText(jdkHome)) {
                    log.debug("从配置文件中获取Windows系统JDK路径: {}", jdkHome);
                    return jdkHome;
                }
            } else if (osName.contains("linux")) {
                jdkHome = props.getProperty(ENV_PROPERTY_LINUX_KEY);
                if (StringUtils.hasText(jdkHome)) {
                    log.debug("从配置文件中获取Linux系统JDK路径: {}", jdkHome);
                    return jdkHome;
                }
            } else if (osName.contains("mac")) {
                jdkHome = props.getProperty(ENV_PROPERTY_MAC_KEY);
                if (StringUtils.hasText(jdkHome)) {
                    log.debug("从配置文件中获取Mac系统JDK路径: {}", jdkHome);
                    return jdkHome;
                }
                // Mac系统如果没有特定配置，尝试使用Linux配置
                jdkHome = props.getProperty(ENV_PROPERTY_LINUX_KEY);
                if (StringUtils.hasText(jdkHome)) {
                    log.debug("Mac系统使用Linux系统JDK路径: {}", jdkHome);
                    return jdkHome;
                }
            }

            // 获取通用JDK路径
            jdkHome = props.getProperty(ENV_PROPERTY_KEY);
            if (StringUtils.hasText(jdkHome)) {
                log.debug("从配置文件中获取通用JDK路径: {}", jdkHome);
                return jdkHome;
            }
        }

        // 4. 从常量配置中获取
        String osName = System.getProperty("os.name", "").toLowerCase();

        if (osName.contains("win")) {
            jdkHome = ServiceConstants.Jdk.WINDOWS_HOME;
        } else if (osName.contains("linux")) {
            jdkHome = ServiceConstants.Jdk.LINUX_HOME;
        } else if (osName.contains("mac")) {
            jdkHome = ServiceConstants.Jdk.MAC_HOME;
        } else {
            jdkHome = ServiceConstants.Jdk.HOME;
        }

        if (StringUtils.hasText(jdkHome)) {
            log.debug("从常量配置中获取JDK路径: {}", jdkHome);
            return jdkHome;
        }

        return null;
    }

    /**
     * 获取所有配置属性
     * 
     * @return 所有配置属性
     */
    public static Properties getAllProperties() {
        return new Properties(envProperties);
    }

    /**
     * 加载属性文件
     * 
     * @return 属性对象
     */
    private static Properties loadProperties() {
        if (envProperties != null) {
            return envProperties;
        }

        try {
            Resource resource = new ClassPathResource(DEFAULT_ENV_FILE);
            if (resource.exists()) {
                Properties props = PropertiesLoaderUtils.loadProperties(resource);
                envProperties = props;
                return props;
            }
        } catch (IOException e) {
            log.warn("加载配置文件失败: {}", DEFAULT_ENV_FILE, e);
        }

        return null;
    }
}