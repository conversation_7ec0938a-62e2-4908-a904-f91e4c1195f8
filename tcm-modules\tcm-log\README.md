# TCM日志服务模块

该模块为中医药系统提供日志服务，包括系统日志、操作日志和登录日志的管理。

## 功能介绍

- 系统日志管理：记录系统级别的日志信息
- 操作日志管理：记录用户的操作行为
- 登录日志管理：记录用户的登录信息
- 日志查询和统计：提供日志查询和统计分析功能

## 技术架构

- Spring Boot：应用程序框架
- Spring Cloud：微服务框架
- MyBatis Plus：ORM框架
- MySQL：数据库
- Nacos：服务注册与配置中心

## 接口说明

### 系统日志接口

- GET `/log/sys/list` - 获取系统日志列表
- GET `/log/sys/{id}` - 获取系统日志详情
- POST `/log/sys` - 添加系统日志
- DELETE `/log/sys/{id}` - 删除系统日志
- DELETE `/log/sys/clean` - 清空系统日志

### 操作日志接口

- GET `/log/operation/list` - 获取操作日志列表
- GET `/log/operation/{id}` - 获取操作日志详情
- POST `/log/operation` - 添加操作日志
- DELETE `/log/operation/{id}` - 删除操作日志
- DELETE `/log/operation/clean` - 清空操作日志

### 登录日志接口

- GET `/log/login/list` - 获取登录日志列表
- GET `/log/login/{id}` - 获取登录日志详情
- POST `/log/login` - 添加登录日志
- DELETE `/log/login/{id}` - 删除登录日志
- DELETE `/log/login/clean` - 清空登录日志

## 启动说明

1. 确保Nacos服务已启动
2. 确保MySQL数据库已启动，并创建名为`tcm_log`的数据库
3. 执行数据库脚本：`src/main/resources/db/schema.sql`
4. 执行以下命令启动服务：

**Windows:**
```
start.bat
```

**Linux:**
```
chmod +x start.sh
./start.sh
```

## 配置说明

配置文件位于`src/main/resources/application.yml`，主要配置项包括：

- 服务端口：默认为8085
- 数据库连接：默认连接本地MySQL
- Redis配置：默认连接本地Redis
- 日志级别：默认com.tcm.log包为debug级别

## 开发指南

1. 日志切面使用：在需要记录日志的方法上添加`@Log`注解
2. 自定义日志内容：通过注解参数定制日志内容

```java
@Log(module = "用户管理", businessType = "用户查询", content = "查询用户信息")
@GetMapping("/user/{id}")
public AjaxResult getUser(@PathVariable Long id) {
    // 业务逻辑
}
``` 