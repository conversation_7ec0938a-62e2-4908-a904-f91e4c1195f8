<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.prescription.mapper.PrescriptionMapper">

    <resultMap id="PrescriptionResult" type="com.tcm.prescription.domain.Prescription">
        <id property="prescriptionId" column="prescription_id"/>
        <result property="prescriptionCode" column="prescription_code"/>
        <result property="diagnosisId" column="diagnosis_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="type" column="type"/>
        <result property="name" column="name"/>
        <result property="prescriptionDate" column="prescription_date"/>
        <result property="days" column="days"/>
        <result property="doses" column="doses"/>
        <result property="usageMethod" column="usage_method"/>
        <result property="frequency" column="frequency"/>
        <result property="totalPrice" column="total_price"/>
        <result property="decoctionMethod" column="decoction_method"/>
        <result property="advice" column="advice"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <resultMap id="PrescriptionVOResult" type="com.tcm.prescription.vo.PrescriptionVO">
        <id property="prescriptionId" column="prescription_id"/>
        <result property="prescriptionCode" column="prescription_code"/>
        <result property="diagnosisId" column="diagnosis_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="patientGender" column="patient_gender"/>
        <result property="patientAge" column="patient_age"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="doctorTitle" column="doctor_title"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="name" column="name"/>
        <result property="prescriptionDate" column="prescription_date"/>
        <result property="days" column="days"/>
        <result property="doses" column="doses"/>
        <result property="usageMethod" column="usage_method"/>
        <result property="frequency" column="frequency"/>
        <result property="totalPrice" column="total_price"/>
        <result property="decoctionMethod" column="decoction_method"/>
        <result property="advice" column="advice"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <sql id="selectPrescriptionVo">
        SELECT p.prescription_id,
               p.prescription_code,
               p.diagnosis_id,
               p.patient_id,
               pa.patient_name,
               CASE pa.gender
                   WHEN 0 THEN '男'
                   WHEN 1 THEN '女'
                   ELSE '未知'
               END AS patient_gender,
               pa.age AS patient_age,
               p.doctor_id,
               d.doctor_name,
               CASE d.title
                   WHEN 1 THEN '主任医师'
                   WHEN 2 THEN '副主任医师'
                   WHEN 3 THEN '主治医师'
                   WHEN 4 THEN '住院医师'
                   ELSE '其他'
               END AS doctor_title,
               p.type,
               CASE p.type
                   WHEN 1 THEN '中药方剂'
                   WHEN 2 THEN '中成药'
                   WHEN 3 THEN '西药'
                   ELSE '其他'
               END AS type_name,
               p.name,
               p.prescription_date,
               p.days,
               p.doses,
               p.usage_method,
               p.frequency,
               p.total_price,
               p.decoction_method,
               p.advice,
               p.status,
               CASE p.status
                   WHEN 0 THEN '未审核'
                   WHEN 1 THEN '已审核'
                   WHEN 2 THEN '已发药'
                   WHEN 3 THEN '已取消'
                   ELSE '未知'
               END AS status_name,
               p.create_time,
               p.update_time,
               p.remark
        FROM tcm_prescription p
        LEFT JOIN tcm_patient pa ON p.patient_id = pa.patient_id
        LEFT JOIN tcm_doctor d ON p.doctor_id = d.doctor_id
    </sql>
    
    <select id="selectPrescriptionList" parameterType="com.tcm.prescription.dto.PrescriptionQuery" resultMap="PrescriptionVOResult">
        <include refid="selectPrescriptionVo"/>
        <where>
            p.del_flag = 0
            <if test="query.prescriptionCode != null and query.prescriptionCode != ''">
                AND p.prescription_code = #{query.prescriptionCode}
            </if>
            <if test="query.diagnosisId != null">
                AND p.diagnosis_id = #{query.diagnosisId}
            </if>
            <if test="query.patientId != null">
                AND p.patient_id = #{query.patientId}
            </if>
            <if test="query.patientName != null and query.patientName != ''">
                AND pa.patient_name like concat('%', #{query.patientName}, '%')
            </if>
            <if test="query.doctorId != null">
                AND p.doctor_id = #{query.doctorId}
            </if>
            <if test="query.doctorName != null and query.doctorName != ''">
                AND d.doctor_name like concat('%', #{query.doctorName}, '%')
            </if>
            <if test="query.type != null">
                AND p.type = #{query.type}
            </if>
            <if test="query.name != null and query.name != ''">
                AND p.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.beginDate != null">
                AND p.prescription_date >= #{query.beginDate}
            </if>
            <if test="query.endDate != null">
                AND p.prescription_date &lt;= #{query.endDate}
            </if>
            <if test="query.status != null">
                AND p.status = #{query.status}
            </if>
        </where>
        ORDER BY p.prescription_date DESC, p.create_time DESC
    </select>
    
    <select id="selectPrescriptionById" parameterType="Long" resultMap="PrescriptionVOResult">
        <include refid="selectPrescriptionVo"/>
        WHERE p.prescription_id = #{prescriptionId} AND p.del_flag = 0
    </select>
    
    <select id="selectPrescriptionByDiagnosisId" parameterType="Long" resultMap="PrescriptionVOResult">
        <include refid="selectPrescriptionVo"/>
        WHERE p.diagnosis_id = #{diagnosisId} AND p.del_flag = 0
        ORDER BY p.prescription_date DESC, p.create_time DESC
    </select>
    
    <select id="selectPrescriptionByPatientId" parameterType="Long" resultMap="PrescriptionVOResult">
        <include refid="selectPrescriptionVo"/>
        WHERE p.patient_id = #{patientId} AND p.del_flag = 0
        ORDER BY p.prescription_date DESC, p.create_time DESC
    </select>
    
    <select id="selectPrescriptionByDoctorId" parameterType="Long" resultMap="PrescriptionVOResult">
        <include refid="selectPrescriptionVo"/>
        WHERE p.doctor_id = #{doctorId} AND p.del_flag = 0
        ORDER BY p.prescription_date DESC, p.create_time DESC
    </select>
    
</mapper> 