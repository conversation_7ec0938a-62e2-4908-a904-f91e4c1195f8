package com.tcm.doctor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.doctor.domain.DoctorAppointment;
import com.tcm.doctor.dto.AppointmentQuery;
import com.tcm.doctor.dto.DoctorAppointmentDTO;
import com.tcm.doctor.vo.DoctorAppointmentVO;

import java.util.Date;
import java.util.List;

/**
 * 医生预约服务接口
 */
public interface IDoctorAppointmentService extends IService<DoctorAppointment> {
    
    /**
     * 分页查询预约列表
     *
     * @param query 查询条件
     * @return 预约列表
     */
    IPage<DoctorAppointmentVO> listAppointments(AppointmentQuery query);
    
    /**
     * 根据ID查询预约详情
     *
     * @param appointmentId 预约ID
     * @return 预约详情
     */
    DoctorAppointmentVO getAppointmentById(Long appointmentId);
    
    /**
     * 根据患者ID查询预约列表
     *
     * @param patientId 患者ID
     * @return 预约列表
     */
    List<DoctorAppointmentVO> getAppointmentsByPatientId(Long patientId);
    
    /**
     * 根据医生ID和日期查询预约列表
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 预约列表
     */
    List<DoctorAppointmentVO> getAppointmentsByDoctorIdAndDate(Long doctorId, Date date);
    
    /**
     * 新增预约
     *
     * @param appointmentDTO 预约信息
     * @return 结果
     */
    boolean addAppointment(DoctorAppointmentDTO appointmentDTO);
    
    /**
     * 更新预约状态
     *
     * @param appointmentId 预约ID
     * @param status        状态
     * @return 结果
     */
    boolean updateAppointmentStatus(Long appointmentId, Integer status);
    
    /**
     * 取消预约
     *
     * @param appointmentId 预约ID
     * @param cancelReason  取消原因
     * @return 结果
     */
    boolean cancelAppointment(Long appointmentId, String cancelReason);
    
    /**
     * 更新就诊信息
     *
     * @param appointmentId 预约ID
     * @param diagnosisId   诊断记录ID
     * @return 结果
     */
    boolean updateVisitInfo(Long appointmentId, Long diagnosisId);
    
    /**
     * 检查能否预约（检查排班是否有余位）
     *
     * @param scheduleId 排班ID
     * @return true可以预约，false不可预约
     */
    boolean checkAppointmentAvailable(Long scheduleId);
} 