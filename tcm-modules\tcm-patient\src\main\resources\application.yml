server:
  port: ${tcm.service.patient.port:${TCM_PATIENT_PORT:9201}}

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: tcm-patient
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
      - optional:classpath:/META-INF/tcm-common.yml
    # 启用配置导入检查，确保配置正确加载
    import-check:
      enabled: true
  # 数据库配置
  datasource:
    # 明确指定使用HikariCP，避免Druid依赖问题
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/tcm_patient?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    # 连接池配置
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      connection-timeout: 30000
      max-lifetime: 900000
      auto-commit: true
      connection-test-query: SELECT 1
  # Redis配置
  data:
    redis:
      host: ${tcm.service.redis.host}
      port: ${tcm.service.redis.port}
      password: ${tcm.service.redis.password}
      database: ${tcm.service.redis.database:0}
  
  # 配置中心
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      # Nacos客户端配置 - 使用统一配置中的属性
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        enabled: true
        # 禁用监控相关功能
        metadata:
          "[preserved.heart.beat.interval]": 10000
          "[preserved.heart.beat.timeout]": 30000
          "[preserved.ip.delete.timeout]": 30000
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false   # 禁用配置导入检查
        # Nacos配置
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: application-common.yml
            refresh: true
        # 设置访问超时时间
        timeout: 10000
    sentinel:
      transport:
        dashboard: ${tcm.service.sentinel.host}:${tcm.service.sentinel.port}
      eager: true
      datasource:
        ds1:
          nacos:
            server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            data-type: json
            rule-type: flow
            username: ${tcm.service.nacos.username}
            password: ${tcm.service.nacos.password}
  
  # Redisson配置
  redis:
    redisson:
      config:
        singleServerConfig:
          address: "redis://${tcm.service.redis.host}:${tcm.service.redis.port}"
          password: ${tcm.service.redis.password}
          database: ${tcm.service.redis.database:0}
          connectionMinimumIdleSize: 5
          connectionPoolSize: 20
          connectTimeout: 3000
          idleConnectionTimeout: 10000
        threads: 16
        nettyThreads: 32
        transportMode: "NIO"
  
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# 日志配置
logging:
  level:
    com.tcm.patient: debug
    org.springframework: warn
    com.alibaba.nacos: ERROR
    com.alibaba.cloud.nacos: ERROR

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.patient.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

# Feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

