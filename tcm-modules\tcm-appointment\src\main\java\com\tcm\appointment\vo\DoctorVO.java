package com.tcm.appointment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 医生信息视图对象
 */
@Data
public class DoctorVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生头像
     */
    private String avatar;
    
    /**
     * 性别（0-女 1-男）
     */
    private Integer gender;
    
    /**
     * 职称
     */
    private String title;
    
    /**
     * 专长
     */
    private String specialty;
    
    /**
     * 简介
     */
    private String introduction;
    
    /**
     * 所属科室ID
     */
    private Long departmentId;
    
    /**
     * 所属科室名称
     */
    private String departmentName;
    
    /**
     * 挂号费用
     */
    private Double registrationFee;
    
    /**
     * 接诊状态（0-停诊 1-接诊）
     */
    private Integer status;
    
    /**
     * 评分
     */
    private Double rating;
    
    /**
     * 累计接诊人次
     */
    private Integer visitCount;
    
    /**
     * 累计好评数
     */
    private Integer goodReviewCount;
} 