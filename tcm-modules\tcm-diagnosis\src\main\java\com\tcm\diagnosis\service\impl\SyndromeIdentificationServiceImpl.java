package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.ServiceException;
import com.tcm.common.core.utils.DateUtils;
import com.tcm.common.core.utils.JsonUtils;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.diagnosis.domain.SyndromeIdentification;
import com.tcm.diagnosis.domain.DiagnosisRecord;
import com.tcm.diagnosis.domain.PulseAnalysis;
import com.tcm.diagnosis.domain.TongueAnalysis;
import com.tcm.diagnosis.dto.SyndromeIdentificationDTO;
import com.tcm.diagnosis.mapper.SyndromeIdentificationMapper;
import com.tcm.diagnosis.mapper.DiagnosisRecordMapper;
import com.tcm.diagnosis.mapper.PulseAnalysisMapper;
import com.tcm.diagnosis.mapper.TongueAnalysisMapper;
import com.tcm.diagnosis.service.ISyndromeIdentificationService;
import com.tcm.diagnosis.vo.SyndromeIdentificationVO;
import com.tcm.diagnosis.vo.SimilarCaseVO;
import lombok.extern.slf4j.Slf4j;
import java.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 辨证服务实现类
 */
@Slf4j
@Service
public class SyndromeIdentificationServiceImpl extends ServiceImpl<SyndromeIdentificationMapper, SyndromeIdentification>
        implements ISyndromeIdentificationService {

    @Autowired
    private SyndromeIdentificationMapper syndromeIdentificationMapper;

    @Autowired
    private DiagnosisRecordMapper diagnosisRecordMapper;

    @Autowired
    private PulseAnalysisMapper pulseAnalysisMapper;

    @Autowired
    private TongueAnalysisMapper tongueAnalysisMapper;

    /**
     * 进行辨证
     *
     * @param syndromeDTO 辨证数据
     * @return 辨证结果ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long identifySyndrome(SyndromeIdentificationDTO syndromeDTO) {
        if (syndromeDTO == null) {
            throw new ServiceException("辨证数据不能为空");
        }

        Long diagnosisId = syndromeDTO.getDiagnosisId();

        // 检查是否已有辨证结果
        LambdaQueryWrapper<SyndromeIdentification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SyndromeIdentification::getDiagnosisId, diagnosisId);
        SyndromeIdentification existingSyndrome = getOne(queryWrapper, false);

        if (existingSyndrome != null) {
            return existingSyndrome.getSyndromeId();
        }

        // 获取诊断记录信息
        DiagnosisRecord diagnosisRecord = diagnosisRecordMapper.selectById(diagnosisId);
        if (diagnosisRecord == null) {
            throw new ServiceException("诊断记录不存在");
        }

        // 获取舌诊和脉诊分析结果
        List<TongueAnalysis> tongueAnalysisList = tongueAnalysisMapper.selectTongueAnalysisByDiagnosisId(diagnosisId);
        List<PulseAnalysis> pulseAnalysisList = pulseAnalysisMapper.selectPulseAnalysisByDiagnosisId(diagnosisId);

        // 结合四诊信息进行辨证
        // 实际项目中应结合症状、体征、舌脉信息综合分析
        // 将Map转换为JSON字符串
        String symptomsJson = JsonUtils.toJsonString(syndromeDTO.getSymptoms());
        String signsJson = JsonUtils.toJsonString(syndromeDTO.getSigns());
        
        Map<String, Object> syndromeResult = performSyndromeIdentification(
                symptomsJson,
                signsJson,
                tongueAnalysisList,
                pulseAnalysisList);

        // 创建辨证结果
        SyndromeIdentification syndromeIdentification = new SyndromeIdentification();
        syndromeIdentification.setDiagnosisId(diagnosisId);
        syndromeIdentification.setPatientId(diagnosisRecord.getPatientId());
        syndromeIdentification.setDoctorId(diagnosisRecord.getDoctorId());
        syndromeIdentification.setSymptoms(JsonUtils.toJsonString(syndromeDTO.getSymptoms()));
        syndromeIdentification.setSigns(JsonUtils.toJsonString(syndromeDTO.getSigns()));
        syndromeIdentification.setDiseaseType((String) syndromeResult.get("diseaseType"));
        syndromeIdentification.setSyndromeType((String) syndromeResult.get("syndromeType"));
        syndromeIdentification.setPathogenesis((String) syndromeResult.get("pathogenesis"));
        syndromeIdentification.setTreatmentPrinciple((String) syndromeResult.get("treatmentPrinciple"));
        syndromeIdentification.setAnalysisResult(JsonUtils.toJsonString(syndromeResult));
        syndromeIdentification.setConfidence((Integer) syndromeResult.get("confidence"));
        syndromeIdentification.setStatus(0); // 未确认
        syndromeIdentification.setIdentificationTime(DateUtils.getNowDate());
        syndromeIdentification.setCreateBy(String.valueOf(diagnosisRecord.getDoctorId()));

        // 保存辨证结果
        save(syndromeIdentification);

        return syndromeIdentification.getSyndromeId();
    }

    /**
     * 获取辨证结果
     *
     * @param syndromeId 辨证结果ID
     * @return 辨证结果视图对象
     */
    @Override
    public SyndromeIdentificationVO getSyndromeIdentification(Long syndromeId) {
        SyndromeIdentification syndromeIdentification = getById(syndromeId);
        if (syndromeIdentification == null) {
            throw new ServiceException("辨证结果不存在");
        }

        return convertToVO(syndromeIdentification);
    }

    /**
     * 根据诊断ID获取辨证结果
     *
     * @param diagnosisId 诊断记录ID
     * @return 辨证结果视图对象
     */
    @Override
    public SyndromeIdentificationVO getSyndromeByDiagnosisId(Long diagnosisId) {
        LambdaQueryWrapper<SyndromeIdentification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SyndromeIdentification::getDiagnosisId, diagnosisId);
        SyndromeIdentification syndromeIdentification = getOne(queryWrapper, false);

        if (syndromeIdentification == null) {
            throw new ServiceException("该诊断尚未进行辨证");
        }

        return convertToVO(syndromeIdentification);
    }

    /**
     * 获取患者的辨证结果列表
     *
     * @param patientId 患者ID
     * @return 辨证结果视图对象列表
     */
    @Override
    public List<SyndromeIdentificationVO> getSyndromeListByPatientId(Long patientId) {
        LambdaQueryWrapper<SyndromeIdentification> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SyndromeIdentification::getPatientId, patientId)
                .orderByDesc(SyndromeIdentification::getIdentificationTime);
        List<SyndromeIdentification> syndromeList = list(queryWrapper);

        return convertToVOList(syndromeList);
    }

    /**
     * 医生确认辨证结果
     *
     * @param syndromeId 辨证结果ID
     * @param doctorId   医生ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmSyndrome(Long syndromeId, Long doctorId) {
        SyndromeIdentification syndromeIdentification = getById(syndromeId);
        if (syndromeIdentification == null) {
            throw new ServiceException("辨证结果不存在");
        }

        syndromeIdentification.setStatus(1); // 已确认
        syndromeIdentification.setConfirmTime(DateUtils.getNowDate());
        syndromeIdentification.setConfirmBy(String.valueOf(doctorId));
        syndromeIdentification.setUpdateBy(String.valueOf(doctorId));
        syndromeIdentification.setUpdateTime(DateUtils.getNowDate());

        return updateById(syndromeIdentification);
    }

    /**
     * 修改辨证结果
     *
     * @param syndromeDTO 辨证数据
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSyndrome(SyndromeIdentificationDTO syndromeDTO) {
        if (syndromeDTO == null || syndromeDTO.getSyndromeId() == null) {
            throw new ServiceException("辨证数据不完整");
        }

        Long syndromeId = syndromeDTO.getSyndromeId();
        SyndromeIdentification syndromeIdentification = getById(syndromeId);

        if (syndromeIdentification == null) {
            throw new ServiceException("辨证结果不存在");
        }

        // 更新辨证结果
        syndromeIdentification.setSymptoms(JsonUtils.toJsonString(syndromeDTO.getSymptoms()));
        syndromeIdentification.setSigns(JsonUtils.toJsonString(syndromeDTO.getSigns()));
        syndromeIdentification.setDiseaseType(syndromeDTO.getDiseaseType());
        syndromeIdentification.setSyndromeType(syndromeDTO.getSyndromeType());
        syndromeIdentification.setPathogenesis(syndromeDTO.getPathogenesis());
        syndromeIdentification.setTreatmentPrinciple(syndromeDTO.getTreatmentPrinciple());
        syndromeIdentification.setStatus(0); // 重置为未确认状态
        syndromeIdentification.setUpdateBy(String.valueOf(syndromeDTO.getDoctorId()));
        syndromeIdentification.setUpdateTime(DateUtils.getNowDate());

        // 更新分析结果JSON
        Map<String, Object> analysisResult = JsonUtils.parseMap(syndromeIdentification.getAnalysisResult());
        analysisResult.put("diseaseType", syndromeDTO.getDiseaseType());
        analysisResult.put("syndromeType", syndromeDTO.getSyndromeType());
        analysisResult.put("pathogenesis", syndromeDTO.getPathogenesis());
        analysisResult.put("treatmentPrinciple", syndromeDTO.getTreatmentPrinciple());
        analysisResult.put("manuallyEdited", true);

        syndromeIdentification.setAnalysisResult(JsonUtils.toJsonString(analysisResult));

        return updateById(syndromeIdentification);
    }

    /**
     * 删除辨证结果
     *
     * @param syndromeId 辨证结果ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSyndrome(Long syndromeId) {
        SyndromeIdentification syndromeIdentification = getById(syndromeId);
        if (syndromeIdentification == null) {
            throw new ServiceException("辨证结果不存在");
        }

        return removeById(syndromeId);
    }

    /**
     * 获取相似病例
     *
     * @param syndromeId 辨证结果ID
     * @param limit      限制数量
     * @return 相似病例视图对象列表
     */
    @Override
    public List<SimilarCaseVO> getSimilarCases(Long syndromeId, Integer limit) {
        SyndromeIdentification syndromeIdentification = getById(syndromeId);
        if (syndromeIdentification == null) {
            throw new ServiceException("辨证结果不存在");
        }

        // TODO: 实现相似病例检索逻辑
        // 实际项目中应使用向量数据库存储病例特征向量，通过余弦相似度等算法查找相似病例
        // 这里简化为模拟返回结果

        List<SimilarCaseVO> similarCases = new ArrayList<>();
        for (int i = 0; i < limit; i++) {
            SimilarCaseVO caseVO = new SimilarCaseVO();

            similarCases.add(caseVO);
        }

        return similarCases;
    }

    /**
     * 导出辨证报告
     *
     * @param syndromeId 辨证结果ID
     * @return 报告文件路径
     */
    @Override
    public String exportSyndromeReport(Long syndromeId) {
        SyndromeIdentification syndromeIdentification = getById(syndromeId);
        if (syndromeIdentification == null) {
            throw new ServiceException("辨证结果不存在");
        }

        // TODO: 实现报告生成逻辑
        // 1. 生成PDF报告文件，包含辨证结果、治法原则等信息

        // 2. 返回报告文件的下载路径
        return "reports/syndrome/" + DateUtils.datePath() + "/" + syndromeId + ".pdf";
    }

    /**
     * 根据四诊信息进行辨证
     *
     * @param symptoms   症状信息
     * @param signs      体征信息
     * @param tongueList 舌象分析列表
     * @param pulseList  脉象分析列表
     * @return 辨证结果
     */
    private Map<String, Object> performSyndromeIdentification(
            String symptoms, String signs, List<TongueAnalysis> tongueList, List<PulseAnalysis> pulseList) {
        Map<String, Object> result = new HashMap<>();

        // 实际项目中应使用中医辨证模型进行分析
        // 这里简化为根据症状关键词进行模拟分析

        String diseaseType = identifyDiseaseType(symptoms, signs);
        String syndromeType = identifySyndromeType(symptoms, signs, tongueList, pulseList);
        String pathogenesis = analyzePathogenesis(syndromeType);
        String treatmentPrinciple = determineTreatmentPrinciple(syndromeType);

        result.put("diseaseType", diseaseType);
        result.put("syndromeType", syndromeType);
        result.put("pathogenesis", pathogenesis);
        result.put("treatmentPrinciple", treatmentPrinciple);
        result.put("confidence", 80); // 模拟置信度

        return result;
    }

    /**
     * 辨别病名
     */
    private String identifyDiseaseType(String symptoms, String signs) {
        // 简化的疾病辨别逻辑，实际应基于完整的症状库和疾病知识图谱

        if (symptoms.contains("头痛") && symptoms.contains("发热")) {
            return "感冒";
        } else if (symptoms.contains("胃痛") || symptoms.contains("腹痛")) {
            return "胃痛";
        } else if (symptoms.contains("咳嗽") || symptoms.contains("痰多")) {
            return "咳嗽";
        } else if (symptoms.contains("失眠") || symptoms.contains("多梦")) {
            return "失眠";
        } else if (symptoms.contains("腰痛") || symptoms.contains("关节痛")) {
            return "痹症";
        } else if (symptoms.contains("头晕") || symptoms.contains("眼花")) {
            return "眩晕";
        } else if (symptoms.contains("疲劳") || symptoms.contains("乏力")) {
            return "虚劳";
        } else {
            return "未明确疾病";
        }
    }

    /**
     * 辨别证型
     */
    private String identifySyndromeType(String symptoms, String signs,
            List<TongueAnalysis> tongueList, List<PulseAnalysis> pulseList) {
        // 综合四诊信息进行辨证

        // 1. 提取舌象信息
        String tongueColor = "";
        String tongueCoating = "";
        if (!tongueList.isEmpty()) {
            TongueAnalysis tongueAnalysis = tongueList.get(0);
            tongueColor = tongueAnalysis.getTongueColor();
            tongueCoating = tongueAnalysis.getCoatingColor();
        }

        // 2. 提取脉象信息
        String pulseType = "";
        if (!pulseList.isEmpty()) {
            PulseAnalysis pulseAnalysis = pulseList.get(0);
            pulseType = pulseAnalysis.getPulseType();
        }

        // 3. 基于症状、舌象、脉象综合辨证

        // 寒证特征
        boolean hasColdSigns = symptoms.contains("畏寒") ||
                symptoms.contains("喜暖") ||
                "白苔".equals(tongueCoating) ||
                "迟脉".equals(pulseType) ||
                "沉脉".equals(pulseType);

        // 热证特征
        boolean hasHeatSigns = symptoms.contains("发热") ||
                symptoms.contains("口渴") ||
                "红舌".equals(tongueColor) ||
                "黄苔".equals(tongueCoating) ||
                "数脉".equals(pulseType);

        // 虚证特征
        boolean hasDeficiencySigns = symptoms.contains("疲劳") ||
                symptoms.contains("气短") ||
                "淡白舌".equals(tongueColor) ||
                "淡苔".equals(tongueCoating) ||
                "虚脉".equals(pulseType);

        // 实证特征
        boolean hasExcessSigns = symptoms.contains("胀痛") ||
                symptoms.contains("痰多") ||
                "厚苔".equals(tongueCoating) ||
                "实脉".equals(pulseType);

        // 根据特征组合确定证型
        if (hasHeatSigns && hasExcessSigns) {
            return "实热证";
        } else if (hasHeatSigns && hasDeficiencySigns) {
            return "虚热证";
        } else if (hasColdSigns && hasExcessSigns) {
            return "实寒证";
        } else if (hasColdSigns && hasDeficiencySigns) {
            return "虚寒证";
        } else if (hasDeficiencySigns) {
            return "气血两虚证";
        } else if (hasExcessSigns) {
            return "痰湿阻滞证";
        } else {
            return "未明确证型";
        }
    }

    /**
     * 分析病机
     */
    private String analyzePathogenesis(String syndromeType) {
        // 根据证型分析病机
        switch (syndromeType) {
            case "实热证":
                return "邪热炽盛，内陷营血，热扰心神";
            case "虚热证":
                return "阴虚内热，虚火上炎";
            case "实寒证":
                return "寒邪束表，经络阻滞，气血运行不畅";
            case "虚寒证":
                return "阳气虚衰，温煦失职，气血运行不足";
            case "气血两虚证":
                return "气血生化不足，脏腑功能减退";
            case "痰湿阻滞证":
                return "脾失健运，水湿内停，聚而成痰，阻滞气机";
            default:
                return "病机不明";
        }
    }

    /**
     * 确定治疗原则
     */
    private String determineTreatmentPrinciple(String syndromeType) {
        // 根据证型确定治疗原则
        switch (syndromeType) {
            case "实热证":
                return "清热解毒，泻火解表";
            case "虚热证":
                return "滋阴降火，清热养阴";
            case "实寒证":
                return "温经散寒，行气活血";
            case "虚寒证":
                return "温阳补虚，回阳救逆";
            case "气血两虚证":
                return "补气养血，调和气血";
            case "痰湿阻滞证":
                return "健脾化湿，理气化痰";
            default:
                return "辨证论治";
        }
    }

    /**
     * 将实体转换为视图对象
     */
    private SyndromeIdentificationVO convertToVO(SyndromeIdentification syndromeIdentification) {
        if (syndromeIdentification == null) {
            return null;
        }

        SyndromeIdentificationVO vo = new SyndromeIdentificationVO();
        vo.setId(syndromeIdentification.getSyndromeId());
        vo.setDiagnosisId(syndromeIdentification.getDiagnosisId());
        vo.setPatientId(syndromeIdentification.getPatientId());
        vo.setDoctorId(syndromeIdentification.getDoctorId());
        vo.setSymptoms(syndromeIdentification.getSymptoms());
        vo.setSigns(syndromeIdentification.getSigns());
        vo.setDiseaseType(syndromeIdentification.getDiseaseType());
        vo.setSyndromeType(syndromeIdentification.getSyndromeType());
        vo.setPathogenesis(syndromeIdentification.getPathogenesis());
        vo.setTreatmentPrinciple(syndromeIdentification.getTreatmentPrinciple());
        vo.setAnalysisResult(syndromeIdentification.getAnalysisResult());
        vo.setConfidence(syndromeIdentification.getConfidence());
        vo.setStatus(syndromeIdentification.getStatus());
        vo.setIdentificationTime(syndromeIdentification.getIdentificationTime());
        vo.setConfirmTime(syndromeIdentification.getConfirmTime());
        vo.setConfirmBy(syndromeIdentification.getConfirmBy());
        vo.setCreateTime(syndromeIdentification.getCreateTime());

        return vo;
    }

    /**
     * 将实体列表转换为视图对象列表
     */
    private List<SyndromeIdentificationVO> convertToVOList(List<SyndromeIdentification> syndromeList) {
        if (syndromeList == null) {
            return List.of();
        }

        return syndromeList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
}