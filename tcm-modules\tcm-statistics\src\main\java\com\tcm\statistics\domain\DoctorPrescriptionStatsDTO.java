package com.tcm.statistics.domain;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 医生处方统计数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class DoctorPrescriptionStatsDTO {
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 统计时间段
     */
    private DateRangeDTO period;
    
    /**
     * 处方总数
     */
    private Long prescriptionCount;
    
    /**
     * 处方类型分布
     */
    private Map<String, Long> typeDistribution;
    
    /**
     * 常用药物TOP10
     */
    private List<MedicineUsageDTO> topMedicines;
    
    /**
     * 证型分布
     */
    private List<SyndromeDistributionDTO> syndromeDistribution;
    
    /**
     * 平均药味数
     */
    private Double avgMedicineCount;
    
    /**
     * 平均处方费用
     */
    private BigDecimal avgCost;
} 