package com.tcm.statistics.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 合理性统计数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RationalityStatDTO {
    
    /**
     * 问题数量
     */
    private Integer count;
    
    /**
     * 问题占比
     */
    private Double percentage;
    
    /**
     * 严重程度（1-5，5为最严重）
     */
    private Integer severity;
} 