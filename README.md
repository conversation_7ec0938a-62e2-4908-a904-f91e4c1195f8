# 中医智能诊疗系统 (TCM System)

## 项目介绍
中医智能诊疗系统是一个基于微服务架构的现代化中医诊疗平台，旨在结合传统中医理论与现代信息技术，为中医医生和患者提供全方位的诊疗服务。系统利用人工智能、大数据分析等技术，辅助中医医生进行诊断、处方开具，并为患者提供便捷的就医体验。

## 系统架构
系统采用微服务架构，基于Spring Cloud Alibaba技术栈构建，主要包含以下模块：

- **网关服务(tcm-gateway)**: 统一入口，负责请求路由、负载均衡、安全控制等
- **认证服务(tcm-auth)**: 提供统一的认证授权服务
- **公共模块(tcm-common)**: 包含核心、Redis、安全等公共组件
- **业务模块(tcm-modules)**:
  - **医生服务(tcm-doctor)**: 医生信息管理、排班管理等
  - **患者服务(tcm-patient)**: 患者信息管理、预约挂号等
  - **诊断服务(tcm-diagnosis)**: 四诊数据采集、辨证分型等
  - **处方服务(tcm-prescription)**: 处方管理、药方推荐等
  - **知识库服务(tcm-knowledge)**: 中医知识库管理、智能检索等
  - **药物管理(tcm-medication)**: 中药管理、库存管理、药材信息管理等
  - **系统集成(tcm-integration)**: 与外部系统集成、数据同步等
  - **国际化支持(tcm-i18n)**: 多语言支持、国际化配置等

## 技术选型

### 后端技术
- **基础框架**: Spring Boot 3.2.3, Spring Cloud 2022.0.4
- **微服务框架**: Spring Cloud Alibaba 2022.0.0.0
- **注册中心/配置中心**: Nacos 2.2.3
- **服务网关**: Spring Cloud Gateway
- **服务调用**: OpenFeign
- **负载均衡**: Spring Cloud LoadBalancer
- **熔断限流**: Sentinel 1.8.6
- **分布式事务**: Seata 1.7.0
- **消息队列**: RocketMQ 5.1.4
- **数据库**: MySQL 8.0.36
- **数据库连接池**: Druid 1.2.20
- **ORM框架**: MyBatis-Plus 3.5.4
- **缓存**: Redis, Redisson 3.24.3
- **搜索引擎**: Elasticsearch 8.12.1
- **图数据库**: Neo4j 5.13.0
- **向量数据库**: Milvus 2.3.0
- **对象存储**: MinIO 8.5.7
- **API文档**: Knife4j 4.3.0
- **工具库**: Hutool 5.8.25

### 前端技术
- **框架**: Vue 3 + TypeScript
- **组件库**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **可视化**: ECharts

## 环境要求
- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+
- Nacos 2.2.0+
- Node.js 16+

## 配置管理

系统采用分层配置管理模式，遵循**"统一管理，分散使用"**的原则：

1. **通用配置**: 所有服务共享的基础配置，集中在`tcm-common.yml`中管理
2. **模块配置**: 各模块特有的配置，在各自的`application.yml`中管理
3. **环境配置**: 不同环境(dev/test/prod)的差异配置，通过配置中心和环境变量管理

详细规范请参考[配置管理规范](docs/配置管理规范.md)文档。

### 关键配置项
- 数据库连接
- Redis连接
- 服务注册发现
- 安全认证
- 三方服务集成

## 快速开始
1. 克隆项目到本地
   ```bash
   git clone https://github.com/yourusername/tcm-platform.git
   cd tcm-platform
   ```

2. 编译项目
   ```bash
   mvn clean package -DskipTests
   ```

3. 启动服务
   - 先启动Nacos、Redis、MySQL等基础服务
   - 启动网关服务
   - 启动认证服务
   - 启动各业务模块服务

## 项目结构
```
tcm-platform
├── tcm-common                      # 公共模块
│   ├── tcm-common-core             # 核心模块
│   ├── tcm-common-redis            # Redis模块
│   └── tcm-common-security         # 安全模块
├── tcm-gateway                     # 网关服务
├── tcm-auth                        # 认证服务
└── tcm-modules                     # 业务模块
    ├── tcm-doctor                  # 医生服务
    ├── tcm-patient                 # 患者服务
    ├── tcm-diagnosis               # 诊断服务
    ├── tcm-prescription            # 处方服务
    ├── tcm-knowledge               # 知识库服务
    ├── tcm-medication              # 药物管理
    └── tcm-integration             # 系统集成
```

## 贡献指南
欢迎贡献代码，提交问题和功能需求。在提交代码前，请确保遵循项目的代码规范。

## 许可证
本项目采用 MIT 许可证，详情请参阅 LICENSE 文件。 