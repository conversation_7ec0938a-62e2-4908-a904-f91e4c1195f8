package com.tcm.doctor.service.impl;

import com.tcm.doctor.domain.Doctor;
import com.tcm.doctor.domain.DoctorAppointment;
import com.tcm.doctor.feign.OrderFeignClient;
import com.tcm.doctor.feign.PrescriptionFeignClient;
import com.tcm.doctor.mapper.DoctorAppointmentMapper;
import com.tcm.doctor.mapper.DoctorMapper;
import com.tcm.doctor.service.IDoctorEvaluationService;
import com.tcm.doctor.vo.DoctorWorkloadVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * 医生服务实现类测试
 */
public class DoctorServiceImplTest {

    @InjectMocks
    private DoctorServiceImpl doctorService;

    @Mock
    private DoctorMapper doctorMapper;

    @Mock
    private DoctorAppointmentMapper doctorAppointmentMapper;

    @Mock
    private IDoctorEvaluationService doctorEvaluationService;

    @Mock
    private PrescriptionFeignClient prescriptionFeignClient;

    @Mock
    private OrderFeignClient orderFeignClient;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetDoctorWorkloadStatistics() {
        // 准备测试数据
        Long doctorId = 1L;
        
        // 模拟医生信息
        Doctor doctor = new Doctor();
        doctor.setDoctorId(doctorId);
        doctor.setDoctorName("张医生");
        doctor.setDoctorCode("D001");
        doctor.setDepartment("中医内科");
        doctor.setTitle(1); // 主任医师
        
        // 模拟诊断数量查询结果
        when(doctorMapper.selectById(doctorId)).thenReturn(doctor);
        when(doctorAppointmentMapper.selectCount(any())).thenReturn(25L);
        
        // 模拟患者列表查询
        List<DoctorAppointment> appointments = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            DoctorAppointment appointment = new DoctorAppointment();
            appointment.setPatientId((long) (i % 10)); // 10个患者，有些重复
            appointments.add(appointment);
        }
        when(doctorAppointmentMapper.selectList(any())).thenReturn(appointments);
        
        // 模拟处方数量查询
        when(prescriptionFeignClient.getPrescriptionCount(eq(doctorId), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(30L);
                
        // 模拟处方金额查询
        when(orderFeignClient.getTotalAmount(eq(doctorId), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(new BigDecimal("5000.00"));
                
        // 模拟复诊率查询
        when(orderFeignClient.getRevisitRate(eq(doctorId), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(new BigDecimal("0.35"));
                
        // 模拟评分查询
        when(doctorEvaluationService.getAverageDoctorRating(eq(doctorId), any(LocalDate.class), any(LocalDate.class)))
                .thenReturn(new BigDecimal("4.5"));
        
        // 调用测试方法
        DoctorWorkloadVO result = doctorService.getDoctorWorkloadStatistics(doctorId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(doctorId, result.getDoctorId());
        assertEquals("张医生", result.getDoctorName());
        assertEquals("D001", result.getDoctorCode());
        assertEquals("中医内科", result.getDepartment());
        assertEquals("主任医师", result.getTitleName());
        assertEquals(25, result.getDiagnosisCount());
        assertEquals(10, result.getPatientCount());
        assertEquals(30, result.getPrescriptionCount());
        assertEquals(new BigDecimal("5000.00"), result.getTotalAmount());
        assertEquals(new BigDecimal("0.35"), result.getRevisitRate());
        assertEquals(new BigDecimal("4.5"), result.getAvgRating());
        
        // 计算每个处方的平均金额：5000 / 30 = 166.67
        assertEquals(0, new BigDecimal("166.67").compareTo(result.getAvgPrescriptionAmount()), 0.01);
    }
    
    @Test
    public void testGetDoctorWorkloadStatisticsWithDateRange() {
        // 准备测试数据
        Long doctorId = 1L;
        LocalDate startDate = LocalDate.of(2025, 1, 1);
        LocalDate endDate = LocalDate.of(2025, 1, 31);
        
        // 模拟医生信息
        Doctor doctor = new Doctor();
        doctor.setDoctorId(doctorId);
        doctor.setDoctorName("李医生");
        doctor.setDoctorCode("D002");
        doctor.setDepartment("针灸科");
        doctor.setTitle(2); // 副主任医师
        
        // 模拟诊断数量查询结果
        when(doctorMapper.selectById(doctorId)).thenReturn(doctor);
        when(doctorAppointmentMapper.selectCount(any())).thenReturn(15L);
        
        // 模拟患者列表查询
        List<DoctorAppointment> appointments = new ArrayList<>();
        for (int i = 0; i < 12; i++) {
            DoctorAppointment appointment = new DoctorAppointment();
            appointment.setPatientId((long) (i % 8)); // 8个患者，有些重复
            appointments.add(appointment);
        }
        when(doctorAppointmentMapper.selectList(any())).thenReturn(appointments);
        
        // 模拟处方数量查询
        when(prescriptionFeignClient.getPrescriptionCount(eq(doctorId), eq(startDate), eq(endDate)))
                .thenReturn(18L);
                
        // 模拟处方金额查询
        when(orderFeignClient.getTotalAmount(eq(doctorId), eq(startDate), eq(endDate)))
                .thenReturn(new BigDecimal("3600.00"));
                
        // 模拟复诊率查询
        when(orderFeignClient.getRevisitRate(eq(doctorId), eq(startDate), eq(endDate)))
                .thenReturn(new BigDecimal("0.28"));
                
        // 模拟评分查询
        when(doctorEvaluationService.getAverageDoctorRating(eq(doctorId), eq(startDate), eq(endDate)))
                .thenReturn(new BigDecimal("4.2"));
        
        // 调用测试方法
        DoctorWorkloadVO result = doctorService.getDoctorWorkloadStatistics(doctorId, startDate, endDate);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(doctorId, result.getDoctorId());
        assertEquals("李医生", result.getDoctorName());
        assertEquals("D002", result.getDoctorCode());
        assertEquals("针灸科", result.getDepartment());
        assertEquals("副主任医师", result.getTitleName());
        assertEquals(15, result.getDiagnosisCount());
        assertEquals(8, result.getPatientCount());
        assertEquals(18, result.getPrescriptionCount());
        assertEquals(new BigDecimal("3600.00"), result.getTotalAmount());
        assertEquals(new BigDecimal("0.28"), result.getRevisitRate());
        assertEquals(new BigDecimal("4.2"), result.getAvgRating());
        assertEquals(startDate, result.getStartDate());
        assertEquals(endDate, result.getEndDate());
        
        // 计算每个处方的平均金额：3600 / 18 = 200
        assertEquals(0, new BigDecimal("200.00").compareTo(result.getAvgPrescriptionAmount()), 0.01);
    }
}
