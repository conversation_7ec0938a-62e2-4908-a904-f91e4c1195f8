package com.tcm.pharmacy.exception;

/**
 * 药房数据异常
 * 
 * <AUTHOR>
 */
public class PharmacyDataException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public PharmacyDataException() {
        super();
    }

    public PharmacyDataException(String message) {
        super(message);
    }

    public PharmacyDataException(String message, Throwable cause) {
        super(message, cause);
    }

    public PharmacyDataException(Throwable cause) {
        super(cause);
    }
}
