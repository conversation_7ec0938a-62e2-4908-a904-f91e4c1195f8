package com.tcm.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.core.web.page.PageDomain;
import com.tcm.inspection.domain.InspectionRecord;
import com.tcm.inspection.service.InspectionRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 检查记录控制器
 */
@Tag(name = "检查记录管理", description = "检查记录管理相关接口")
@RestController
@RequestMapping("/inspection/record")
public class InspectionRecordController extends BaseController {

    @Autowired
    private InspectionRecordService inspectionRecordService;

    /**
     * 获取检查记录列表
     */
    @Operation(summary = "获取检查记录列表")
    @GetMapping("/list")
    public R<List<InspectionRecord>> list(InspectionRecord inspectionRecord) {
        List<InspectionRecord> list = inspectionRecordService.selectInspectionRecordList(inspectionRecord);
        return R.ok(list);
    }

    /**
     * 分页查询检查记录列表
     */
    @Operation(summary = "分页查询检查记录列表")
    @GetMapping("/page")
    public R<IPage<InspectionRecord>> page(InspectionRecord inspectionRecord, PageDomain pageDomain) {
        Page<InspectionRecord> page = new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        IPage<InspectionRecord> pageData = inspectionRecordService.selectInspectionRecordPage(page, inspectionRecord);
        return R.ok(pageData);
    }

    /**
     * 获取检查记录详情
     */
    @Operation(summary = "获取检查记录详情")
    @GetMapping("/{recordId}")
    public R<InspectionRecord> getInfo(@Parameter(description = "检查记录ID") @PathVariable("recordId") Long recordId) {
        return R.ok(inspectionRecordService.selectInspectionRecordById(recordId));
    }

    /**
     * 根据检查单号获取检查记录
     */
    @Operation(summary = "根据检查单号获取检查记录")
    @GetMapping("/no/{inspectionNo}")
    public R<InspectionRecord> getByInspectionNo(@Parameter(description = "检查单号") @PathVariable("inspectionNo") String inspectionNo) {
        return R.ok(inspectionRecordService.selectInspectionRecordByInspectionNo(inspectionNo));
    }

    /**
     * 根据患者ID查询检查记录列表
     */
    @Operation(summary = "根据患者ID查询检查记录列表")
    @GetMapping("/patient/{patientId}")
    public R<List<InspectionRecord>> getRecordsByPatientId(@Parameter(description = "患者ID") @PathVariable("patientId") Long patientId) {
        List<InspectionRecord> records = inspectionRecordService.selectInspectionRecordByPatientId(patientId);
        return R.ok(records);
    }

    /**
     * 新增检查记录
     */
    @Operation(summary = "新增检查记录")
    @PostMapping
    public R<InspectionRecord> add(@Valid @RequestBody InspectionRecord inspectionRecord) {
        inspectionRecordService.insertInspectionRecord(inspectionRecord);
        return R.ok(inspectionRecord);
    }

    /**
     * 修改检查记录
     */
    @Operation(summary = "修改检查记录")
    @PutMapping
    public R<Void> edit(@Valid @RequestBody InspectionRecord inspectionRecord) {
        return toAjax(inspectionRecordService.updateInspectionRecord(inspectionRecord));
    }

    /**
     * 删除检查记录
     */
    @Operation(summary = "删除检查记录")
    @DeleteMapping("/{recordId}")
    public R<Void> remove(@Parameter(description = "检查记录ID") @PathVariable("recordId") Long recordId) {
        return toAjax(inspectionRecordService.deleteInspectionRecordById(recordId));
    }

    /**
     * 批量删除检查记录
     */
    @Operation(summary = "批量删除检查记录")
    @DeleteMapping("/batch/{recordIds}")
    public R<Void> batchRemove(@Parameter(description = "检查记录ID数组，多个以逗号分隔") @PathVariable("recordIds") Long[] recordIds) {
        return toAjax(inspectionRecordService.deleteInspectionRecordByIds(recordIds));
    }

    /**
     * 更新检查结果
     */
    @Operation(summary = "更新检查结果")
    @PutMapping("/result")
    public R<Void> updateResult(@RequestBody InspectionRecord inspectionRecord) {
        return toAjax(inspectionRecordService.updateInspectionResult(inspectionRecord));
    }

    /**
     * 取消检查记录
     */
    @Operation(summary = "取消检查记录")
    @PutMapping("/cancel/{recordId}")
    public R<Void> cancelRecord(@Parameter(description = "检查记录ID") @PathVariable("recordId") Long recordId, @RequestParam(value = "reason", required = false) String reason) {
        return toAjax(inspectionRecordService.cancelInspectionRecord(recordId, reason));
    }
} 