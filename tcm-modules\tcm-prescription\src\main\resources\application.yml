server:
  port: ${tcm.service.prescription.port:${TCM_PRESCRIPTION_PORT:9304}}
  servlet:
    context-path: /prescription

spring:
  application:
    name: tcm-prescription
  # 引入公共配置
  profiles:
    include: common
  config:
    import: 
      - optional:classpath:/META-INF/tcm-common.yml
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/tcm_prescription?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
        wall:
          enabled: true
        slf4j:
          enabled: true
  # Redis配置
  data:
    redis:
      host: ${tcm.service.redis.host}
      port: ${tcm.service.redis.port}
      password: ${tcm.service.redis.password}
      database: ${tcm.service.redis.database}
  
  # 服务注册与发现配置
  cloud:
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        file-extension: yml
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        import-check:
          enabled: false   # 禁用配置导入检查
    
    # Sentinel配置
    sentinel:
      transport:
        dashboard: ${tcm.service.sentinel.host}:${tcm.service.sentinel.port}
      eager: true
      datasource:
        ds1:
          nacos:
            server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
            namespace: ${tcm.service.nacos.namespace:}
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            data-type: json
            rule-type: flow

# 日志配置
logging:
  level:
    com.tcm.prescription: debug
    org.springframework: warn

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.prescription.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000

# Seata分布式事务配置
seata:
  enabled: true
  application-id: ${spring.application.name}
  tx-service-group: ${spring.application.name}-group
  config-type: file
  registry-type: file
  enable-auto-data-source-proxy: true
  service:
    vgroup-mapping:
      tcm-prescription-group: default
    grouplist:
      default: ${tcm.service.seata.host}:${tcm.service.seata.port}
  client:
    rm:
      report-success-enable: false

