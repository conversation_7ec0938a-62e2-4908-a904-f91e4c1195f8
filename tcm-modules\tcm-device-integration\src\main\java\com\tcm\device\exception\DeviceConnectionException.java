package com.tcm.device.exception;

/**
 * 设备连接异常
 * 
 * <AUTHOR>
 */
public class DeviceConnectionException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public DeviceConnectionException() {
        super();
    }

    public DeviceConnectionException(String message) {
        super(message);
    }

    public DeviceConnectionException(String message, Throwable cause) {
        super(message, cause);
    }

    public DeviceConnectionException(Throwable cause) {
        super(cause);
    }
}
