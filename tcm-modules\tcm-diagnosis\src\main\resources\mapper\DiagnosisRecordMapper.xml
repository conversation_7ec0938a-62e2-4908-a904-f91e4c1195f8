<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.diagnosis.mapper.DiagnosisRecordMapper">

    <resultMap id="DiagnosisRecordResult" type="com.tcm.diagnosis.domain.DiagnosisRecord">
        <id property="diagnosisId" column="diagnosis_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="diagnosisCode" column="diagnosis_code"/>
        <result property="diagnosisDate" column="diagnosis_date"/>
        <result property="chiefComplaint" column="chief_complaint"/>
        <result property="presentIllness" column="present_illness"/>
        <result property="pastHistory" column="past_history"/>
        <result property="personalHistory" column="personal_history"/>
        <result property="familyHistory" column="family_history"/>
        <result property="inspection" column="inspection"/>
        <result property="auscultation" column="auscultation"/>
        <result property="interrogation" column="interrogation"/>
        <result property="palpation" column="palpation"/>
        <result property="tongueCondition" column="tongue_condition"/>
        <result property="pulseCondition" column="pulse_condition"/>
        <result property="tcmDiagnosis" column="tcm_diagnosis"/>
        <result property="westernDiagnosis" column="western_diagnosis"/>
        <result property="syndromeType" column="syndrome_type"/>
        <result property="treatmentPrinciple" column="treatment_principle"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    
    <resultMap id="DiagnosisRecordVOResult" type="com.tcm.diagnosis.vo.DiagnosisRecordVO">
        <id property="diagnosisId" column="diagnosis_id"/>
        <result property="patientId" column="patient_id"/>
        <result property="patientName" column="patient_name"/>
        <result property="doctorId" column="doctor_id"/>
        <result property="doctorName" column="doctor_name"/>
        <result property="diagnosisCode" column="diagnosis_code"/>
        <result property="diagnosisDate" column="diagnosis_date"/>
        <result property="chiefComplaint" column="chief_complaint"/>
        <result property="presentIllness" column="present_illness"/>
        <result property="pastHistory" column="past_history"/>
        <result property="personalHistory" column="personal_history"/>
        <result property="familyHistory" column="family_history"/>
        <result property="inspection" column="inspection"/>
        <result property="auscultation" column="auscultation"/>
        <result property="interrogation" column="interrogation"/>
        <result property="palpation" column="palpation"/>
        <result property="tongueCondition" column="tongue_condition"/>
        <result property="pulseCondition" column="pulse_condition"/>
        <result property="tcmDiagnosis" column="tcm_diagnosis"/>
        <result property="westernDiagnosis" column="western_diagnosis"/>
        <result property="syndromeType" column="syndrome_type"/>
        <result property="treatmentPrinciple" column="treatment_principle"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    
    <sql id="selectDiagnosisRecordVo">
        SELECT d.diagnosis_id,
               d.patient_id,
               p.patient_name,
               d.doctor_id,
               dr.doctor_name,
               d.diagnosis_code,
               d.diagnosis_date,
               d.chief_complaint,
               d.present_illness,
               d.past_history,
               d.personal_history,
               d.family_history,
               d.inspection,
               d.auscultation,
               d.interrogation,
               d.palpation,
               d.tongue_condition,
               d.pulse_condition,
               d.tcm_diagnosis,
               d.western_diagnosis,
               d.syndrome_type,
               d.treatment_principle,
               d.status,
               CASE d.status
                   WHEN 0 THEN '草稿'
                   WHEN 1 THEN '已完成'
                   WHEN 2 THEN '已取消'
                   ELSE '未知'
               END AS status_name,
               d.create_time,
               d.update_time,
               d.remark
        FROM tcm_diagnosis_record d
        LEFT JOIN tcm_patient p ON d.patient_id = p.patient_id
        LEFT JOIN tcm_doctor dr ON d.doctor_id = dr.doctor_id
    </sql>
    
    <select id="selectDiagnosisRecordList" parameterType="com.tcm.diagnosis.dto.DiagnosisQuery" resultMap="DiagnosisRecordVOResult">
        <include refid="selectDiagnosisRecordVo"/>
        <where>
            d.del_flag = 0
            <if test="query.patientId != null">
                AND d.patient_id = #{query.patientId}
            </if>
            <if test="query.patientName != null and query.patientName != ''">
                AND p.patient_name like concat('%', #{query.patientName}, '%')
            </if>
            <if test="query.doctorId != null">
                AND d.doctor_id = #{query.doctorId}
            </if>
            <if test="query.doctorName != null and query.doctorName != ''">
                AND dr.doctor_name like concat('%', #{query.doctorName}, '%')
            </if>
            <if test="query.diagnosisCode != null and query.diagnosisCode != ''">
                AND d.diagnosis_code = #{query.diagnosisCode}
            </if>
            <if test="query.beginDate != null">
                AND d.diagnosis_date >= #{query.beginDate}
            </if>
            <if test="query.endDate != null">
                AND d.diagnosis_date &lt;= #{query.endDate}
            </if>
            <if test="query.tcmDiagnosis != null and query.tcmDiagnosis != ''">
                AND d.tcm_diagnosis like concat('%', #{query.tcmDiagnosis}, '%')
            </if>
            <if test="query.westernDiagnosis != null and query.westernDiagnosis != ''">
                AND d.western_diagnosis like concat('%', #{query.westernDiagnosis}, '%')
            </if>
            <if test="query.syndromeType != null and query.syndromeType != ''">
                AND d.syndrome_type like concat('%', #{query.syndromeType}, '%')
            </if>
            <if test="query.status != null">
                AND d.status = #{query.status}
            </if>
        </where>
        ORDER BY d.diagnosis_date DESC, d.create_time DESC
    </select>
    
    <select id="selectDiagnosisRecordById" parameterType="Long" resultMap="DiagnosisRecordVOResult">
        <include refid="selectDiagnosisRecordVo"/>
        WHERE d.diagnosis_id = #{diagnosisId} AND d.del_flag = 0
    </select>
    
    <select id="selectDiagnosisRecordByPatientId" parameterType="Long" resultMap="DiagnosisRecordVOResult">
        <include refid="selectDiagnosisRecordVo"/>
        WHERE d.patient_id = #{patientId} AND d.del_flag = 0
        ORDER BY d.diagnosis_date DESC, d.create_time DESC
    </select>
    
    <select id="selectDiagnosisRecordByDoctorId" parameterType="Long" resultMap="DiagnosisRecordVOResult">
        <include refid="selectDiagnosisRecordVo"/>
        WHERE d.doctor_id = #{doctorId} AND d.del_flag = 0
        ORDER BY d.diagnosis_date DESC, d.create_time DESC
    </select>
    
</mapper> 