package com.tcm.prescription.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 处方实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_prescription")
public class Prescription extends BaseEntity {
    
    /**
     * 处方ID
     */
    @TableId
    private Long prescriptionId;
    
    /**
     * 处方编号
     */
    private String prescriptionCode;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 处方类型（1：中药方剂，2：中成药，3：西药）
     */
    private Integer type;
    
    /**
     * 处方名称
     */
    private String name;
    
    /**
     * 处方日期
     */
    private Date prescriptionDate;
    
    /**
     * 用药天数
     */
    private Integer days;
    
    /**
     * 剂数
     */
    private Integer doses;
    
    /**
     * 用药方法
     */
    private String usageMethod;
    
    /**
     * 用药频次
     */
    private String frequency;
    
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    
    /**
     * 煎药方式
     */
    private String decoctionMethod;
    
    /**
     * 医嘱
     */
    private String advice;
    
    /**
     * 状态（0：未审核，1：已审核，2：已发药，3：已取消）
     */
    private Integer status;
} 