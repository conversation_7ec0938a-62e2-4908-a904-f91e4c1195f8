package com.tcm.consultation.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 远程会诊记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_consultation_record")
public class ConsultationRecord extends BaseEntity {

    /**
     * 会诊记录ID
     */
    @TableId
    private Long consultationId;

    /**
     * 会诊编号
     */
    private String consultationCode;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 主诊医生ID
     */
    private Long primaryDoctorId;

    /**
     * 会诊主题
     */
    private String title;

    /**
     * 会诊类型（1：病例分析会诊，2：实时远程会诊，3：MDT多学科会诊）
     */
    private Integer type;

    /**
     * 会诊预约时间
     */
    private Date appointmentTime;

    /**
     * 实际开始时间
     */
    private Date startTime;

    /**
     * 实际结束时间
     */
    private Date endTime;

    /**
     * 会诊时长（分钟）
     */
    private Integer duration;

    /**
     * 会诊状态（0：待确认，1：已确认，2：进行中，3：已完成，4：已取消）
     */
    private Integer status;

    /**
     * 会诊内容摘要
     */
    private String summary;

    /**
     * 诊断ID
     */
    private Long diagnosisId;

    /**
     * 会议室ID
     */
    private String roomId;

    /**
     * 会议密码
     */
    private String password;

    /**
     * 录制视频URL
     */
    private String videoUrl;
}