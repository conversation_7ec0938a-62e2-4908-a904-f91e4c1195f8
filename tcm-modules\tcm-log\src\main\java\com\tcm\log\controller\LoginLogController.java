package com.tcm.log.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.log.domain.LoginLog;
import com.tcm.log.service.ILoginLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 登录日志控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log/login")
public class LoginLogController {
    
    @Autowired
    private ILoginLogService loginLogService;
    
    /**
     * 获取登录日志列表
     */
    @GetMapping("/list")
    public Map<String, Object> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "ipAddress", required = false) String ipAddress,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "loginType", required = false) Integer loginType) {
        
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        if (username != null && !username.isEmpty()) {
            wrapper.like(LoginLog::getUsername, username);
        }
        if (ipAddress != null && !ipAddress.isEmpty()) {
            wrapper.eq(LoginLog::getIpAddress, ipAddress);
        }
        if (status != null) {
            wrapper.eq(LoginLog::getStatus, status);
        }
        if (loginType != null) {
            wrapper.eq(LoginLog::getLoginType, loginType);
        }
        wrapper.orderByDesc(LoginLog::getLoginTime);
        
        Page<LoginLog> page = new Page<>(pageNum, pageSize);
        Page<LoginLog> result = loginLogService.page(page, wrapper);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "查询成功");
        response.put("data", result.getRecords());
        response.put("total", result.getTotal());
        response.put("pageNum", result.getCurrent());
        response.put("pageSize", result.getSize());
        
        return response;
    }
    
    /**
     * 获取登录日志详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getInfo(@PathVariable Long id) {
        LoginLog loginLog = loginLogService.getById(id);
        Map<String, Object> response = new HashMap<>();
        
        if (loginLog != null) {
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", loginLog);
        } else {
            response.put("code", 404);
            response.put("msg", "日志不存在");
        }
        
        return response;
    }
    
    /**
     * 获取用户最新登录日志
     */
    @GetMapping("/user/{username}")
    public Map<String, Object> getUserLatestLogin(@PathVariable String username) {
        LoginLog loginLog = loginLogService.getLatestLoginLog(username);
        Map<String, Object> response = new HashMap<>();
        
        if (loginLog != null) {
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", loginLog);
        } else {
            response.put("code", 404);
            response.put("msg", "未找到登录记录");
        }
        
        return response;
    }
    
    /**
     * 记录登录信息
     */
    @PostMapping
    public Map<String, Object> add(@RequestBody LoginLog loginLog) {
        boolean result = loginLogService.saveLoginLog(loginLog);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "保存成功");
        } else {
            response.put("code", 500);
            response.put("msg", "保存失败");
        }
        
        return response;
    }
    
    /**
     * 记录登出信息
     */
    @PutMapping("/logout/{sessionId}")
    public Map<String, Object> logout(@PathVariable String sessionId) {
        boolean result = loginLogService.saveLogoutLog(sessionId);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "登出记录保存成功");
        } else {
            response.put("code", 500);
            response.put("msg", "登出记录保存失败");
        }
        
        return response;
    }
    
    /**
     * 删除登录日志
     */
    @DeleteMapping("/{ids}")
    public Map<String, Object> remove(@PathVariable Long[] ids) {
        boolean result = loginLogService.deleteLogs(ids);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "删除成功");
        } else {
            response.put("code", 500);
            response.put("msg", "删除失败");
        }
        
        return response;
    }
    
    /**
     * 清空登录日志
     */
    @DeleteMapping("/clear")
    public Map<String, Object> clear() {
        boolean result = loginLogService.clearLogs();
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "清空成功");
        } else {
            response.put("code", 500);
            response.put("msg", "清空失败");
        }
        
        return response;
    }
} 