package com.tcm.diagnosis.service;

import com.tcm.diagnosis.domain.TongueAnalysis;
import com.tcm.diagnosis.vo.TongueAnalysisVO;

import java.util.List;

/**
 * 舌象分析服务接口
 */
public interface ITongueAnalysisService {

    /**
     * 分析舌象图像
     *
     * @param imageId 图像ID
     * @return 分析结果ID
     */
    Long analyzeTongueImage(Long imageId);

    /**
     * 批量分析舌象图像
     *
     * @param imageIds 图像ID列表
     * @return 是否成功
     */
    boolean analyzeTongueImageBatch(List<Long> imageIds);

    /**
     * 获取舌象分析结果
     *
     * @param analysisId 分析结果ID
     * @return 舌象分析结果视图对象
     */
    TongueAnalysisVO getTongueAnalysis(Long analysisId);

    /**
     * 根据图像ID获取舌象分析结果
     *
     * @param imageId 图像ID
     * @return 舌象分析结果视图对象
     */
    TongueAnalysisVO getTongueAnalysisByImageId(Long imageId);

    /**
     * 获取诊断记录关联的舌象分析结果列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 舌象分析结果视图对象列表
     */
    List<TongueAnalysisVO> getTongueAnalysisListByDiagnosisId(Long diagnosisId);

    /**
     * 获取患者的舌象分析结果列表
     *
     * @param patientId 患者ID
     * @return 舌象分析结果视图对象列表
     */
    List<TongueAnalysisVO> getTongueAnalysisListByPatientId(Long patientId);

    /**
     * 删除舌象分析结果
     *
     * @param analysisId 分析结果ID
     * @return 是否成功
     */
    boolean deleteTongueAnalysis(Long analysisId);

    /**
     * 导出舌象分析结果报告
     *
     * @param analysisId 分析结果ID
     * @return 报告文件路径
     */
    String exportTongueAnalysisReport(Long analysisId);
}