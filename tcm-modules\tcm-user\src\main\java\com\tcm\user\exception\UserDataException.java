package com.tcm.user.exception;

/**
 * 用户数据异常
 * 
 * <AUTHOR>
 */
public class UserDataException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public UserDataException() {
        super();
    }

    public UserDataException(String message) {
        super(message);
    }

    public UserDataException(String message, Throwable cause) {
        super(message, cause);
    }

    public UserDataException(Throwable cause) {
        super(cause);
    }
}
