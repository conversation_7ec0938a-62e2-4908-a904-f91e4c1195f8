server:
  port: ${tcm.service.platform-gateway.port:${TCM_PLATFORM_GATEWAY_PORT:9001}}

spring:
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
  application:
    name: tcm-platform-gateway
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    password: ${tcm.service.redis.password}
    database: ${tcm.service.redis.database}
  liquibase:
    enabled: false
  thymeleaf:
    enabled: false
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.webflux.WebFluxAutoConfiguration,org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        enabled: true
        metadata:
          "[preserved.heart.beat.interval]": 10000
          "[preserved.heart.beat.timeout]": 30000
          "[preserved.ip.delete.timeout]": 30000
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: application-common.yml
            refresh: true
        timeout: 10000
    gateway:
      discovery:
        locator:
          enabled: true
      routes:
        # 平台认证服务
        - id: tcm-platform-auth
          uri: lb://tcm-platform-auth
          predicates:
            - Path=/auth/**
          filters:
            - StripPrefix=0
        # 平台管理服务
        - id: tcm-platform-admin
          uri: lb://tcm-platform-admin
          predicates:
            - Path=/admin/**
          filters:
            - StripPrefix=0
        # 临床系统服务
        - id: tcm-clinical-system
          uri: lb://tcm-clinical-system
          predicates:
            - Path=/clinical/**
          filters:
            - StripPrefix=0
        # 集成服务
        - id: tcm-integration
          uri: lb://tcm-integration
          predicates:
            - Path=/integration/**
          filters:
            - StripPrefix=0
    sentinel:
      transport:
        dashboard: ${tcm.service.sentinel.host}:${tcm.service.sentinel.port}
      eager: true
      datasource:
        ds1:
          nacos:
            server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
            namespace: ${tcm.service.nacos.namespace:}
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            data-type: json
            rule-type: flow
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.tcm.platform.gateway.controller

# 禁用Spring Boot Actuator
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: false

# 日志配置
logging:
  level:
    "[com.tcm]": debug
    "[com.tcm.gateway]": info
    "[org.springframework.web]": info
    "[org.springframework.security]": info
    "[org.springframework.cloud.gateway]": info
    "[org.springframework.http.server.reactive]": info
    "[org.springframework.web.reactive]": info
    "[reactor.ipc.netty]": info
    "[com.alibaba.nacos]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc]": ERROR
    "[com.alibaba.nacos.shaded.io.perfmark]": ERROR
    "[com.alibaba.nacos.common.remote.client]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc.netty]": ERROR
    "[com.alibaba.cloud.nacos]": ERROR
    "[com.alibaba.nacos.client.naming]": ERROR
    "[org.springframework.cloud]": debug
    "[org.springframework.boot.autoconfigure]": warn
    org.springframework.boot: warn
    org.springframework: warn

# TCM系统配置
tcm:
  service:
    rocketMq:
      nameServerAddress: ${tcm.service.rocketMq.host}:${tcm.service.rocketMq.port}
      producerGroup: ${spring.application.name}-producer-group
      enable-msg-trace: false
      customized-trace-topic: false
      retry-times-when-send-failed: 0
      retry-times-when-send-async-failed: 0
      check-connection: false
    rocket-mq:
      host: ${tcm.service.rocketMq.host}
      port: ${tcm.service.rocketMq.port}
  gateway:
    ignore-urls:
      - /auth/login
      - /auth/captcha
      - /auth/register
      - /*/v3/api-docs
      - /*/swagger-ui.html
      - /favicon.ico
    blacklist-ips:
      - *************
      - ***********