package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 医生工作量统计视图对象
 */
@Data
public class DoctorWorkloadVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生编号
     */
    private String doctorCode;
    
    /**
     * 职称名称
     */
    private String titleName;
    
    /**
     * 所属科室
     */
    private String department;
    
    /**
     * 诊断数量
     */
    private Integer diagnosisCount;
    
    /**
     * 处方数量
     */
    private Integer prescriptionCount;
    
    /**
     * 诊断患者数量
     */
    private Integer patientCount;
    
    /**
     * 总收费金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 平均诊断时长（分钟）
     */
    private Integer avgDiagnosisDuration;
    
    /**
     * 复诊率（百分比）
     */
    private BigDecimal revisitRate;
    
    /**
     * 统计开始日期
     */
    private LocalDate startDate;
    
    /**
     * 统计结束日期
     */
    private LocalDate endDate;
    
    /**
     * 平均评分（1-5）
     */
    private BigDecimal avgRating;
    
    /**
     * 平均处方金额
     */
    private BigDecimal avgPrescriptionAmount;
} 