package com.tcm.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.log.domain.OperationLog;
import com.tcm.log.mapper.OperationLogMapper;
import com.tcm.log.service.IOperationLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

/**
 * 操作日志服务实现
 * 
 * <AUTHOR>
 */
@Service
public class OperationLogServiceImpl extends ServiceImpl<OperationLogMapper, OperationLog> implements IOperationLogService {

    /**
     * 保存操作日志
     * 
     * @param operationLog 操作日志信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLog(OperationLog operationLog) {
        return save(operationLog);
    }

    /**
     * 清空操作日志
     * 
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearLogs() {
        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        return remove(wrapper);
    }

    /**
     * 删除操作日志
     * 
     * @param ids 需要删除的日志ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLogs(Long[] ids) {
        return removeByIds(Arrays.asList(ids));
    }

    /**
     * 查询指定业务类型和业务ID的操作日志
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 操作日志列表
     */
    @Override
    public OperationLog getByBusinessTypeAndId(String businessType, String businessId) {
        LambdaQueryWrapper<OperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OperationLog::getBusinessType, businessType)
                .eq(OperationLog::getBusinessId, businessId)
                .orderByDesc(OperationLog::getOperateTime)
                .last("LIMIT 1");
        return getOne(wrapper);
    }
} 