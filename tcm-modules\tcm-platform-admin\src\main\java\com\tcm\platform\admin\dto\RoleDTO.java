package com.tcm.platform.admin.dto;

import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;

/**
 * 角色数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class RoleDTO {

    /**
     * 角色ID
     */
    private Long id;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, max = 30, message = "角色名称长度必须在2-30个字符之间")
    private String roleName;

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(min = 2, max = 50, message = "角色编码长度必须在2-50个字符之间")
    private String roleCode;

    /**
     * 角色排序
     */
    private Integer sort;

    /**
     * 状态（0：禁用 1：启用）
     */
    private Integer status;

    /**
     * 数据权限范围
     */
    private Integer dataScope;

    /**
     * 菜单ID列表
     */
    private List<Long> menuIds;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}