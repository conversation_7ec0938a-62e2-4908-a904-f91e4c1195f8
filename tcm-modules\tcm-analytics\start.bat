@echo off
setlocal

REM 设置Java环境变量
set JAVA_HOME=%JAVA_HOME%
set PATH=%JAVA_HOME%\bin;%PATH%

REM 设置应用程序临时目录
set TEMP_DIR=%C:\Users\<USER>\AppData\Local\Temp%

REM 设置JVM参数
set JAVA_OPTS=-Xms512m -Xmx1024m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=512m

REM 设置日志配置
set LOGGING_OPTS=-Dlogging.config=classpath:logback-spring.xml -Dlog4j2.disable=true

REM 设置服务端口
set SERVER_PORT=9102
set JAVA_OPTS=%JAVA_OPTS% -Dserver.port=%SERVER_PORT%

REM 设置应用名称
set APP_NAME=tcm-analytics
set JAVA_OPTS=%JAVA_OPTS% -Dspring.application.name=%APP_NAME%

REM 设置Nacos配置
set NACOS_ADDR=localhost:8848
set JAVA_OPTS=%JAVA_OPTS% -Dspring.cloud.nacos.discovery.server-addr=%NACOS_ADDR% -Dspring.cloud.nacos.config.server-addr=%NACOS_ADDR%

REM 设置数据库连接
set JAVA_OPTS=%JAVA_OPTS% -Dspring.datasource.url=**************************************************************************************************************************************************** -Dspring.datasource.username=root -Dspring.datasource.password=123456

REM 设置Elasticsearch连接
set JAVA_OPTS=%JAVA_OPTS% -Delasticsearch.host=localhost -Delasticsearch.port=9200

REM 启动应用程序
echo 正在启动 %APP_NAME% 服务...
java %JAVA_OPTS% %LOGGING_OPTS% -jar target/tcm-analytics-1.0.0.jar

endlocal 