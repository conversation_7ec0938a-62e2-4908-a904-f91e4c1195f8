package com.tcm.prescription.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 处方药品数据传输对象
 */
@Data
public class PrescriptionMedicineDTO {
    
    /**
     * 处方药品ID（更新时需要）
     */
    private Long prescriptionMedicineId;
    
    /**
     * 药品ID
     */
    @NotNull(message = "药品ID不能为空")
    private Long medicineId;
    
    /**
     * 药品名称
     */
    @NotBlank(message = "药品名称不能为空")
    private String medicineName;
    
    /**
     * 药品类型（1：中药材，2：中成药，3：西药）
     */
    @NotNull(message = "药品类型不能为空")
    private Integer medicineType;
    
    /**
     * 药品规格
     */
    private String specification;
    
    /**
     * 剂量
     */
    @NotNull(message = "剂量不能为空")
    @Positive(message = "剂量必须大于0")
    private BigDecimal dosage;
    
    /**
     * 剂量单位
     */
    @NotBlank(message = "剂量单位不能为空")
    private String dosageUnit;
    
    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    @Positive(message = "单价必须大于0")
    private BigDecimal unitPrice;
    
    /**
     * 小计金额
     */
    private BigDecimal amount;
    
    /**
     * 用法
     */
    private String usage;
    
    /**
     * 特殊要求（如：先煎、后下等）
     */
    private String specialRequirement;
} 