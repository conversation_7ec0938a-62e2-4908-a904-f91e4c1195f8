package com.tcm.order.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.order.entity.Order;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 订单服务接口
 *
 * <AUTHOR> Team
 */
public interface IOrderService extends IService<Order> {

    /**
     * 创建订单
     *
     * @param order 订单信息
     * @return 创建的订单ID
     */
    Long createOrder(Order order);
    
    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @param status  订单状态
     * @return 是否成功
     */
    boolean updateOrderStatus(Long orderId, Integer status);
    
    /**
     * 支付订单
     *
     * @param orderId       订单ID
     * @param paymentMethod 支付方式
     * @param transactionNo 交易号
     * @return 是否成功
     */
    boolean payOrder(Long orderId, Integer paymentMethod, String transactionNo);
    
    /**
     * 取消订单
     *
     * @param orderId      订单ID
     * @param cancelReason 取消原因
     * @return 是否成功
     */
    boolean cancelOrder(Long orderId, String cancelReason);
    
    /**
     * 退款订单
     *
     * @param orderId      订单ID
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 是否成功
     */
    boolean refundOrder(Long orderId, String refundAmount, String refundReason);
    
    /**
     * 分页查询订单列表
     *
     * @param page      分页参数
     * @param orderType 订单类型
     * @param status    订单状态
     * @param keyword   关键字（订单号/患者姓名/医生姓名）
     * @return 订单分页列表
     */
    IPage<Order> getOrderPage(Page<Order> page, Integer orderType, Integer status, String keyword);
    
    /**
     * 查询订单统计数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计数据
     */
    List<Map<String, Object>> getOrderStatistics(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 查询患者订单列表
     *
     * @param patientId 患者ID
     * @param status    订单状态
     * @return 订单列表
     */
    List<Order> getPatientOrders(Long patientId, Integer status);
    
    /**
     * 查询医生订单列表
     *
     * @param doctorId 医生ID
     * @param status   订单状态
     * @return 订单列表
     */
    List<Order> getDoctorOrders(Long doctorId, Integer status);
    
    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    Order getOrderDetail(Long orderId);
    
    /**
     * 配送订单
     *
     * @param orderId        订单ID
     * @param expressCompany 物流公司
     * @param expressNo      物流单号
     * @return 是否成功
     */
    boolean deliveryOrder(Long orderId, String expressCompany, String expressNo);
    
    /**
     * 确认收货
     *
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean receiveOrder(Long orderId);
}
