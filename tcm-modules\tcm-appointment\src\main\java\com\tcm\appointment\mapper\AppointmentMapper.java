package com.tcm.appointment.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.appointment.domain.Appointment;
import com.tcm.appointment.vo.AppointmentStatisticsVO;
import com.tcm.appointment.vo.TimeSlotVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预约挂号数据访问接口
 */
@Mapper
public interface AppointmentMapper extends BaseMapper<Appointment> {
    
    /**
     * 获取医生工作时间段
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 工作时间段列表
     */
    List<TimeSlotVO> getDoctorWorkTimeSlots(@Param("doctorId") Long doctorId, @Param("date") Date date);
    
    /**
     * 获取已预约的时间段
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 已预约时间段列表
     */
    List<String> getBookedTimeSlots(@Param("doctorId") Long doctorId, @Param("date") Date date);
    
    /**
     * 检查时间段是否可用
     *
     * @param doctorId  医生ID
     * @param date      日期
     * @param timeSlot  时间段
     * @return 是否已被预约
     */
    int checkTimeSlotAvailable(@Param("doctorId") Long doctorId, @Param("date") Date date, @Param("timeSlot") String timeSlot);
    
    /**
     * 获取预约统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 预约统计
     */
    AppointmentStatisticsVO getAppointmentStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 获取按日期统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期统计数据列表
     */
    List<Map<String, Object>> getDailyStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 获取按科室统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 科室统计数据列表
     */
    List<Map<String, Object>> getDepartmentStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 获取按医生统计数据
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 医生统计数据列表
     */
    List<Map<String, Object>> getDoctorStatistics(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 获取最后插入的ID
     *
     * @return 最后插入的ID
     */
    Long getLastInsertId();
} 