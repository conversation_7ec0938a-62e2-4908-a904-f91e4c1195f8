package com.tcm.prescription.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.BusinessException;
import com.tcm.prescription.domain.PrescriptionMedicine;
import com.tcm.prescription.dto.PrescriptionMedicineDTO;
import com.tcm.prescription.mapper.PrescriptionMedicineMapper;
import com.tcm.prescription.service.PrescriptionMedicineService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 处方药品服务实现类
 */
@Service
public class PrescriptionMedicineServiceImpl extends ServiceImpl<PrescriptionMedicineMapper, PrescriptionMedicine> implements PrescriptionMedicineService {

    @Override
    @Transactional
    public boolean saveBatch(Long prescriptionId, List<PrescriptionMedicineDTO> medicines) {
        if (prescriptionId == null || medicines == null || medicines.isEmpty()) {
            return false;
        }
        
        List<PrescriptionMedicine> medicineList = new ArrayList<>();
        for (int i = 0; i < medicines.size(); i++) {
            PrescriptionMedicineDTO dto = medicines.get(i);
            PrescriptionMedicine medicine = new PrescriptionMedicine();
            BeanUtils.copyProperties(dto, medicine);
            medicine.setPrescriptionId(prescriptionId);
            medicine.setSortOrder(i + 1); // 设置排序号
            medicineList.add(medicine);
        }
        
        return super.saveBatch(medicineList);
    }

    @Override
    public List<PrescriptionMedicine> getByPrescriptionId(Long prescriptionId) {
        LambdaQueryWrapper<PrescriptionMedicine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PrescriptionMedicine::getPrescriptionId, prescriptionId);
        queryWrapper.orderByAsc(PrescriptionMedicine::getSortOrder);
        
        return list(queryWrapper);
    }

    @Override
    public boolean deleteByPrescriptionId(Long prescriptionId) {
        LambdaQueryWrapper<PrescriptionMedicine> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PrescriptionMedicine::getPrescriptionId, prescriptionId);
        
        return remove(queryWrapper);
    }

    @Override
    @Transactional
    public boolean updateBatch(Long prescriptionId, List<PrescriptionMedicineDTO> medicines) {
        // 先删除原有药品
        deleteByPrescriptionId(prescriptionId);
        
        // 再保存新的药品
        return saveBatch(prescriptionId, medicines);
    }
} 