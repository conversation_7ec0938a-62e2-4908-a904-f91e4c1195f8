package com.tcm.prescription.vo;

import com.tcm.prescription.domain.PrescriptionMedicine;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 处方视图对象
 */
@Data
public class PrescriptionVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 处方ID
     */
    private Long prescriptionId;
    
    /**
     * 处方编号
     */
    private String prescriptionCode;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 诊断名称
     */
    private String diagnosisName;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生职称
     */
    private String doctorTitle;
    
    /**
     * 处方类型（1：中药方剂，2：中成药，3：西药）
     */
    private Integer type;
    
    /**
     * 处方类型名称
     */
    private String typeName;
    
    /**
     * 处方名称
     */
    private String name;
    
    /**
     * 处方日期
     */
    private Date prescriptionDate;
    
    /**
     * 用药天数
     */
    private Integer days;
    
    /**
     * 剂数
     */
    private Integer doses;
    
    /**
     * 用药方法
     */
    private String usageMethod;
    
    /**
     * 用药频次
     */
    private String frequency;
    
    /**
     * 总价
     */
    private BigDecimal totalPrice;
    
    /**
     * 煎药方式
     */
    private String decoctionMethod;
    
    /**
     * 医嘱
     */
    private String advice;
    
    /**
     * 状态（0：未审核，1：已审核，2：已发药，3：已取消）
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 处方药品列表
     */
    private List<PrescriptionMedicine> medicines;
    
    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 0: return "未审核";
            case 1: return "已审核";
            case 2: return "已发药";
            case 3: return "已取消";
            default: return "未知状态";
        }
    }
    
    /**
     * 获取处方类型名称
     */
    public String getTypeName() {
        if (type == null) {
            return "";
        }
        switch (type) {
            case 1: return "中药方剂";
            case 2: return "中成药";
            case 3: return "西药";
            default: return "未知类型";
        }
    }
} 