package com.tcm.doctor.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 问诊消息数据传输对象
 */
@Data
public class ConsultationMessageDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息ID
     */
    private Long messageId;
    
    /**
     * 问诊ID
     */
    @NotNull(message = "问诊ID不能为空")
    private Long consultationId;
    
    /**
     * 发送者ID
     */
    @NotNull(message = "发送者ID不能为空")
    private Long senderId;
    
    /**
     * 发送者类型（1-医生 2-患者 3-系统）
     */
    @NotNull(message = "发送者类型不能为空")
    private Integer senderType;
    
    /**
     * 接收者ID
     */
    @NotNull(message = "接收者ID不能为空")
    private Long receiverId;
    
    /**
     * 消息类型（1-文本 2-图片 3-语音 4-视频 5-处方 6-系统通知）
     */
    @NotNull(message = "消息类型不能为空")
    private Integer messageType;
    
    /**
     * 消息内容
     */
    @Size(max = 1000, message = "消息内容长度不能超过1000个字符")
    private String content;
    
    /**
     * 媒体URL（如图片、语音、视频的地址）
     */
    private String mediaUrl;
    
    /**
     * 媒体时长（语音或视频的时长，单位：秒）
     */
    private Integer mediaDuration;
} 