package com.tcm.evaluation.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.evaluation.domain.EvaluationForm;

/**
 * 评估表单Mapper接口
 */
@Mapper
public interface EvaluationFormMapper extends BaseMapper<EvaluationForm> {
    
    /**
     * 根据评估类型查询表单，并分页
     * 
     * @param page 分页参数
     * @param evaluationType 评估类型
     * @return 分页结果
     */
    IPage<EvaluationForm> selectByEvaluationType(Page<EvaluationForm> page, @Param("evaluationType") String evaluationType);
    
    /**
     * 根据表单类型查询表单，并分页
     * 
     * @param page 分页参数
     * @param formType 表单类型
     * @return 分页结果
     */
    IPage<EvaluationForm> selectByFormType(Page<EvaluationForm> page, @Param("formType") String formType);
    
    /**
     * 查询已启用的表单，并分页
     * 
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<EvaluationForm> selectEnabledForms(Page<EvaluationForm> page);
} 