package com.tcm.analytics;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 数据分析服务启动类
 */
@SpringBootApplication(exclude = {
        org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class })
@EnableDiscoveryClient
@ComponentScan(basePackages = { "com.tcm.analytics", "com.tcm.common" })
@MapperScan("com.tcm.analytics.mapper")
public class TcmAnalyticsApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmAnalyticsApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // ===== SLF4J日志冲突解决 =====
        // 1. 确保使用logback作为唯一的SLF4J实现
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "WARN");
        System.setProperty("org.slf4j.simpleLogger.showThreadName", "false");
        System.setProperty("org.slf4j.simpleLogger.showLogName", "false");
        System.setProperty("org.slf4j.simpleLogger.showShortLogName", "true");

        // 2. 彻底禁用Log4j
        System.setProperty("log4j2.disable.jmx", "true");
        System.setProperty("log4j.configuration", "log4j-disabled.properties");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");

        // 3. 强制所有其他日志框架重定向到SLF4J
        System.setProperty("java.util.logging.manager", "org.slf4j.bridge.SLF4JBridgeHandler");
        System.setProperty("org.jboss.logging.provider", "slf4j");
        System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SLF4JLogFactory");

        // ===== 类加载器配置 =====
        System.setProperty("spring.classloader.overrideStandardClassLoader", "false");
        System.setProperty("spring.boot.register-shutdown-hook", "false");

        // ===== PerfMark相关冲突解决 =====
        // 彻底禁用PerfMark实现，避免类加载问题
        System.setProperty("io.perfmark.PerfMark.impl", "io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.impl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.startEnabled", "false");

        // 设置一个假的类名，防止通过Class.forName()加载原始类
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.impl.SecretPerfMarkImpl$PerfMarkImpl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");

        // 禁用gRPC通信，强制使用HTTP
        System.setProperty("nacos.client.naming.grpc.enabled", "false");
        System.setProperty("nacos.client.config.grpc.enabled", "false");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-analytics");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_ANALYTICS_PORT") == null) {
            System.setProperty("server.port", "${tcm.service.analytics.port:9102}");
        }
    }

    public static void main(String[] args) {
        try {
            // 启用调试模式，输出更多日志信息
            System.setProperty("logging.level.com.tcm", "DEBUG");
            System.setProperty("logging.level.org.springframework", "DEBUG");
            System.setProperty("logging.level.org.mybatis", "DEBUG");
            System.setProperty("logging.level.org.apache.ibatis", "DEBUG");

            // 打印当前工作目录
            LOGGER.info("当前工作目录: {}", System.getProperty("user.dir"));

            // 显示所有的系统属性，用于调试
            printSystemProperties();

            // 配置日志和类加载问题
            configureLogging();

            // 配置应用设置
            configureApplicationSettings();

            // 配置服务端口
            String fixedPort = configureServerPort();

            // 检查Elasticsearch是否可用
            boolean elasticsearchEnabled = Boolean
                    .parseBoolean(System.getProperty("spring.elasticsearch.enabled", "false"));

            if (elasticsearchEnabled) {
                LOGGER.info("Elasticsearch功能已启用，将尝试连接到Elasticsearch服务器");
            } else {
                LOGGER.warn("Elasticsearch功能已禁用，应用将在没有Elasticsearch功能的情况下启动");
                LOGGER.warn("如需启用Elasticsearch功能，请设置spring.elasticsearch.enabled=true并确保Elasticsearch服务可用");
            }

            // 启动应用程序
            ConfigurableApplicationContext context = SpringApplication.run(TcmAnalyticsApplication.class, args);

            // 运行诊断工具
            try {
                Class<?> diagnosticHelperClass = Class.forName("com.tcm.analytics.util.DiagnosticHelper");
                java.lang.reflect.Method diagnoseMethod = diagnosticHelperClass.getMethod("diagnoseEnvironment",
                        ConfigurableApplicationContext.class);
                diagnoseMethod.invoke(null, context);
            } catch (Exception e) {
                LOGGER.error("无法运行诊断工具", e);
            }

            // 处理和显示启动信息
            displayStartupInfo(context, fixedPort);
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打印所有系统属性
     */
    private static void printSystemProperties() {
        LOGGER.info("========== 系统属性 ==========");
        System.getProperties().forEach((key, value) -> {
            LOGGER.info("{} = {}", key, value);
        });
        LOGGER.info("========== 系统属性结束 ==========");
    }

    /**
     * 配置日志相关设置
     */
    private static void configureLogging() {
        // 1.1 清理并禁用所有可能的Log4j相关配置
        System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
        System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
        System.setProperty("log4j2.statusLogger.level", "OFF"); // 禁用Log4j2状态日志

        // 1.2 防止SLF4J日志绑定冲突
        try {
            // 尝试卸载可能已经加载的SLF4J绑定
            Class.forName("org.slf4j.LoggerFactory").getDeclaredMethod("reset").invoke(null);
        } catch (Exception e) {
            // 忽略任何反射错误，继续执行
        }

        // 1.3 配置日志级别和其他设置
        System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
        System.setProperty("logging.level.root", "INFO"); // 设置根日志级别
        System.setProperty("logging.level.com.tcm", "DEBUG"); // 设置TCM模块日志级别
        System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

        // 1.4 配置SLF4J桥接器
        try {
            // 尝试初始化SLF4J桥接器，将JUL日志重定向到SLF4J
            Class.forName("org.slf4j.bridge.SLF4JBridgeHandler").getDeclaredMethod("removeHandlersForRootLogger")
                    .invoke(null);
            Class.forName("org.slf4j.bridge.SLF4JBridgeHandler").getDeclaredMethod("install").invoke(null);
        } catch (Exception e) {
            // 忽略任何反射错误，继续执行
        }
    }

    /**
     * 配置应用程序设置
     */
    private static void configureApplicationSettings() {
        // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
        System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
        System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

        // 3. Nacos配置 - 彻底解决Nacos连接问题
        // 禁用Nacos配置导入检查，以避免启动错误
        System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

        // 设置类加载器
        System.setProperty("java.system.class.loader", "org.springframework.boot.loader.LaunchedURLClassLoader");
        System.setProperty("spring.devtools.restart.enabled", "false");

        // 设置统一配置文件
        System.setProperty("spring.config.import", "optional:classpath:/config/tcm-common.yml");

        // 解决Java反射和安全限制问题
        System.setProperty("java.net.preferIPv4Stack", "true");
        System.setProperty("io.netty.tryReflectionSetAccessible", "true");

        // 强制禁用所有netty unsafe访问
        System.setProperty("io.netty.noUnsafe", "true");

        // 禁用Spring Boot Actuator相关功能
        System.setProperty("management.endpoints.enabled-by-default", "false");
        System.setProperty("management.endpoint.health.enabled", "false");
        System.setProperty("management.endpoint.info.enabled", "false");
        System.setProperty("management.endpoints.web.exposure.include", "");
        System.setProperty("management.endpoints.jmx.exposure.include", "");
        System.setProperty("spring.jmx.enabled", "false");

        // 启用MyBatis映射器扫描的详细日志
        System.setProperty("logging.level.org.mybatis.spring.mapper", "DEBUG");

        // 显式设置mybatis-plus的配置
        System.setProperty("mybatis-plus.mapper-locations", "classpath*:/mapper/**/*.xml");
        System.setProperty("mybatis-plus.type-aliases-package", "com.tcm.analytics.entity");
        System.setProperty("mybatis-plus.global-config.db-config.id-type", "AUTO");
    }

    /**
     * 配置服务器端口
     * 
     * @return 配置的端口
     */
    private static String configureServerPort() {
        // 优先设置固定端口，确保Spring Boot使用这个端口启动
        String fixedPort = System.getProperty("server.port");
        if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
            fixedPort = System.getProperty("tcm.service.analytics.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getenv("TCM_ANALYTICS_PORT");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = "${tcm.service.analytics.port:9102}";
                }
            }
            System.setProperty("server.port", fixedPort);
        }

        // 打印将要使用的端口
        LOGGER.info("启动服务器使用的端口: {}", System.getProperty("server.port"));

        return fixedPort;
    }

    /**
     * 显示启动信息
     * 
     * @param context        Spring应用上下文
     * @param configuredPort 配置的端口
     */
    private static void displayStartupInfo(ConfigurableApplicationContext context, String configuredPort) {
        // 获取实际使用的端口（包括随机分配的端口）
        String serverPort = determineActualServerPort(context, configuredPort);

        // 确保端口被设置到系统属性中（使之可被其他组件获取）
        System.setProperty("server.port", serverPort);

        LOGGER.info("=====================================================");
        LOGGER.info("         TCM Analytics Service 已启动!             ");
        LOGGER.info("=====================================================");
        LOGGER.info("应用名称: {}", context.getEnvironment().getProperty("spring.application.name"));
        LOGGER.info("应用端口: {}", serverPort);

        // 安全地获取激活的 profile
        String[] activeProfiles = context.getEnvironment().getActiveProfiles();
        String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
        LOGGER.info("激活的配置文件: {}", activeProfile);

        printLogo();

        System.out.println("  启动成功: http://" + (System.getProperty("server.address", "localhost")) + ":" + serverPort
                + context.getEnvironment().getProperty("server.servlet.context-path", ""));
        System.out.println("=====================================================================");
    }

    /**
     * 确定实际使用的服务器端口
     * 
     * @param context        Spring应用上下文
     * @param configuredPort 配置的端口
     * @return 实际使用的端口
     */
    private static String determineActualServerPort(ConfigurableApplicationContext context, String configuredPort) {
        // 如果PortListener已经捕获到了实际端口，则使用它
        if (actualServerPort > 0) {
            return String.valueOf(actualServerPort);
        }

        // 尝试从环境中获取端口
        String serverPort = context.getEnvironment().getProperty("server.port");

        // 如果环境中没有端口配置，则使用配置的端口
        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
            return configuredPort;
        }

        return serverPort;
    }

    /**
     * 打印应用Logo
     */
    private static void printLogo() {
        System.out.println("  _____   _____ __  __              _           _       _   _         ");
        System.out.println(" |_   _| / ____|  \\/  |     /\\     | |         | |     | | (_)        ");
        System.out.println("   | |  | |    | \\  / |    /  \\    | |__   __ _| |_   _| |_ _  ___ ___ ");
        System.out.println("   | |  | |    | |\\/| |   / /\\ \\   | '_ \\ / _` | | | | | __| |/ __/ __|");
        System.out.println("  _| |_ | |____| |  | |  / ____ \\  | | | | (_| | | |_| | |_| | (_| (__ ");
        System.out.println(" |_____| \\_____|_|  |_| /_/    \\_\\ |_| |_|\\__,_|_|\\__, |\\__|_|\\___\\___|");
        System.out.println("                                                   __/ |              ");
        System.out.println("                                                  |___/               ");
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web服务器已初始化，使用端口: {}", actualServerPort);
        }
    }
}