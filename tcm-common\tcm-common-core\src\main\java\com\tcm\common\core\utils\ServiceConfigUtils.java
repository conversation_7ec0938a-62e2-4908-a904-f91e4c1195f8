package com.tcm.common.core.utils;

import com.tcm.common.core.config.TcmServiceConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 服务配置工具类
 */
@Component
public class ServiceConfigUtils {

    private static ServiceConfigUtils instance;

    @Autowired
    private TcmServiceConfig serviceConfig;

    /**
     * 初始化静态实例
     */
    @PostConstruct
    public void init() {
        instance = this;
        instance.serviceConfig = this.serviceConfig;
    }

    /**
     * 获取MySQL配置
     */
    public static TcmServiceConfig.Mysql getMysqlConfig() {
        return instance.serviceConfig.getMysql();
    }

    /**
     * 获取Redis配置
     */
    public static TcmServiceConfig.Redis getRedisConfig() {
        return instance.serviceConfig.getRedis();
    }

    /**
     * 获取Elasticsearch配置
     */
    public static TcmServiceConfig.Elasticsearch getElasticsearchConfig() {
        return instance.serviceConfig.getElasticsearch();
    }

    /**
     * 获取Neo4j配置
     */
    public static TcmServiceConfig.Neo4j getNeo4jConfig() {
        return instance.serviceConfig.getNeo4j();
    }

    /**
     * 获取Milvus配置
     */
    public static TcmServiceConfig.Milvus getMilvusConfig() {
        return instance.serviceConfig.getMilvus();
    }

    /**
     * 获取MinIO配置
     */
    public static TcmServiceConfig.Minio getMinioConfig() {
        return instance.serviceConfig.getMinio();
    }

    /**
     * 获取Sentinel配置
     */
    public static TcmServiceConfig.Sentinel getSentinelConfig() {
        return instance.serviceConfig.getSentinel();
    }

    /**
     * 获取ELK配置
     */
    public static TcmServiceConfig.Elk getElkConfig() {
        return instance.serviceConfig.getElk();
    }

    /**
     * 获取Seata配置
     */
    public static TcmServiceConfig.Seata getSeataConfig() {
        return instance.serviceConfig.getSeata();
    }

    /**
     * 获取Nacos配置
     */
    public static TcmServiceConfig.Nacos getNacosConfig() {
        return instance.serviceConfig.getNacos();
    }

    /**
     * 获取RocketMQ配置
     */
    public static TcmServiceConfig.RocketMq getRocketMqConfig() {
        return instance.serviceConfig.getRocketMq();
    }
}