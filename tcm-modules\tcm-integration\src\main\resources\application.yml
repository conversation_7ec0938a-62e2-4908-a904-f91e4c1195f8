server:
  port: ${tcm.service.integration.port:${TCM_INTEGRATION_PORT:9302}}
  servlet:
    context-path: /api

spring:
  application:
    name: tcm-integration
  # 引入公共配置
  profiles:
    include: common
    
  # 允许Bean覆盖，解决RedisTemplate等Bean定义冲突问题
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
    
  # 数据库配置 - 使用统一配置
  datasource:
    # 使用统一配置中的参数
    driver-class-name: ${tcm.service.db.driver-class-name}
    url: ${tcm.service.db.url}
    username: ${tcm.service.db.username}
    password: ${tcm.service.db.password}
    # 连接池参数
    druid:
      # 高可用性配置
      keep-alive: true
      # 缓解连接失败问题
      test-on-borrow: true
  
  # 数据库初始化配置
  sql:
    init:
      continue-on-error: true
      
  # 暂时禁用Liquibase自动配置
  liquibase:
    enabled: false
      
  # Redis配置 - 使用统一配置
  redis:
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    database: ${tcm.service.redis.database:0}
    password: ${tcm.service.redis.password:}
    timeout: ${tcm.service.redis.timeout:10000}
    lettuce:
      pool:
        max-active: ${tcm.service.redis.pool.max-active:8}
        max-wait: ${tcm.service.redis.pool.max-wait:-1}
        max-idle: ${tcm.service.redis.pool.max-idle:8}
        min-idle: ${tcm.service.redis.pool.min-idle:0}
      
  # Nacos配置
  cloud:
    nacos:
      discovery:
        # 服务注册配置
        server-addr: ${tcm.service.nacos.server-addr}
        namespace: ${tcm.service.nacos.namespace}
        group: ${tcm.service.nacos.group}
        # 防止注册失败导致应用启动失败
        fail-fast: false
        # 开启资源保护
        ephemeral: true
      config:
        # 配置中心配置
        server-addr: ${tcm.service.nacos.server-addr}
        namespace: ${tcm.service.nacos.namespace}
        group: ${tcm.service.nacos.group}
        file-extension: yaml
        # 支持共享配置
        shared-configs:
          - data-id: tcm-common.yaml
            group: ${tcm.service.nacos.group}
            refresh: true
  
  # 消息队列 - 使用统一配置
  rabbitmq:
    host: ${tcm.service.rabbitmq.host}
    port: ${tcm.service.rabbitmq.port}
    username: ${tcm.service.rabbitmq.username}
    password: ${tcm.service.rabbitmq.password}
    virtual-host: ${tcm.service.rabbitmq.virtual-host:/}

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.integration.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Feign配置
feign:
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000
  compression:
    request:
      enabled: true
    response:
      enabled: true

# 日志配置
logging:
  level:
    com.tcm.integration: debug
    org.springframework: warn

# 集成配置
integration:
  # 默认超时时间设置（毫秒）
  timeout:
    connect: 5000
    read: 10000
  # 日志保留天数
  log:
    retention-days: 30 