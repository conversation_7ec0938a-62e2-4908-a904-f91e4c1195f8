package com.tcm.doctor;

import java.io.IOException;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.stereotype.Component;

import com.tcm.doctor.constant.ServiceConstants;

/**
 * 医生服务启动程序 采用统一的日志和配置策略，确保应用稳定启动
 */
@EnableDiscoveryClient
@EnableFeignClients
@SpringBootApplication(exclude = {
        // 其他可能导致启动问题的自动配置
        org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration.class,
        org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration.class,

        // 排除RocketMQ自动配置，避免缺少配置导致启动失败
        org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class })
@ComponentScan(excludeFilters = {
        // 排除控制器
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
                com.tcm.doctor.controller.ConsultationMessageController.class })
// 注意：不再排除Mapper和Service实现类，而是使用MockBeansConfig提供模拟实现
})
public class TcmDoctorApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmDoctorApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 禁用Log4j2以解决日志冲突
        System.setProperty("log4j2.disable", "true");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 设置应用基础信息
        System.setProperty("spring.application.name", ServiceConstants.SERVICE_NAME);

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_DOCTOR_PORT") == null) {
            System.setProperty("server.port", "9300");
        }

        // 配置数据源类型，明确指定HikariCP
        System.setProperty("spring.datasource.type", "com.zaxxer.hikari.HikariDataSource");
    }

    public static void main(String[] args) {
        try {
            System.out.println("医生服务正在启动...");
            LOGGER.info("Starting doctor service module...");

            // 加载统一配置
            Properties commonProperties = loadCommonProperties();

            // ==================== 日志配置 ====================
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");
            System.setProperty("spring.main.banner-mode", "off");

            // ==================== Nacos配置 ====================
            // 优先设置固定端口，确保Spring Boot使用这个端口启动
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.doctor.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_DOCTOR_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "9300";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            // 置立Nacos的所有服务器信息，包括默认地址和gRPC地址
            String nacosServerAddr = ServiceConstants.NACOS_SERVER_ADDR;
            System.setProperty("nacos.server.addr", nacosServerAddr);

            // 设置gRPC地址，使用统一配置的服务器
            String nacosHost = nacosServerAddr.split(":")[0];
            System.setProperty("nacos.client.grpc.server.host", nacosHost);
            System.setProperty("nacos.client.grpc.server.port", "9848");

            // 配置gRPC相关参数
            System.setProperty("nacos.client.naming.grpc.enabled", "true");
            System.setProperty("nacos.client.config.grpc.enabled", "true");
            System.setProperty("nacos.client.contextPath", "/");
            System.setProperty("nacos.client.grpc.tls.enable", "false");
            System.setProperty("grpc.client.perfmark-enabled", "false");

            // Nacos认证信息 - 直接设置认证信息，确保优先级高于配置文件
            System.setProperty("spring.cloud.nacos.discovery.username", ServiceConstants.NACOS_USERNAME);
            System.setProperty("spring.cloud.nacos.discovery.password", ServiceConstants.NACOS_PASSWORD);
            System.setProperty("spring.cloud.nacos.config.username", ServiceConstants.NACOS_USERNAME);
            System.setProperty("spring.cloud.nacos.config.password", ServiceConstants.NACOS_PASSWORD);

            // 设置Nacos地址信息，确保使用统一配置
            System.setProperty("spring.cloud.nacos.discovery.server-addr", ServiceConstants.NACOS_SERVER_ADDR);
            System.setProperty("spring.cloud.nacos.config.server-addr", ServiceConstants.NACOS_SERVER_ADDR);
            System.setProperty("spring.cloud.nacos.config.namespace", ServiceConstants.NACOS_NAMESPACE);
            System.setProperty("spring.cloud.nacos.discovery.namespace", ServiceConstants.NACOS_NAMESPACE);

            // ==================== 服务配置占位符 ====================
            // 将必要的服务地址设置为系统属性，供配置文件中的占位符使用
            System.setProperty("tcm.service.nacos.url", ServiceConstants.NACOS_SERVER_ADDR);
            System.setProperty("tcm.service.nacos.username", ServiceConstants.NACOS_USERNAME);
            System.setProperty("tcm.service.nacos.password", ServiceConstants.NACOS_PASSWORD);

            // MySQL配置
            System.setProperty("tcm.service.mysql.host", ServiceConstants.MYSQL_HOST);
            System.setProperty("tcm.service.mysql.port", ServiceConstants.MYSQL_PORT);
            System.setProperty("tcm.service.mysql.username", ServiceConstants.MYSQL_USERNAME);
            System.setProperty("tcm.service.mysql.password", ServiceConstants.MYSQL_PASSWORD);
            System.setProperty("tcm.service.mysql.database", ServiceConstants.MYSQL_DATABASE);
            System.setProperty("tcm.service.mysql.url", ServiceConstants.MYSQL_URL);

            // Redis配置
            System.setProperty("tcm.service.redis.host", ServiceConstants.REDIS_HOST);
            System.setProperty("tcm.service.redis.port", ServiceConstants.REDIS_PORT);
            System.setProperty("tcm.service.redis.password", ServiceConstants.REDIS_PASSWORD);
            System.setProperty("tcm.service.redis.database", ServiceConstants.REDIS_DATABASE);

            // 启用Redis模拟模式
            System.setProperty("tcm.mock.redis", "true");

            // Redisson配置，禁用自动配置以避免连接错误
            System.setProperty("spring.autoconfigure.exclude", "org.redisson.spring.starter.RedissonAutoConfiguration,"
                    + System.getProperty("spring.autoconfigure.exclude", ""));

            // RocketMQ配置
            System.setProperty("tcm.service.rocketMq.nameServerAddress", ServiceConstants.ROCKETMQ_NAME_SERVER);

            // Sentinel配置
            System.setProperty("tcm.service.sentinel.dashboardUrl", "localhost:8858");

            // 禁用自动配置
            System.setProperty("spring.liquibase.enabled", "false");

            ConfigurableApplicationContext context = SpringApplication.run(TcmDoctorApplication.class, args);

            // 获取实际使用的端口（包括随机分配的端口）
            String serverPort;

            // 如果PortListener已经捕获到了实际端口，则使用它
            if (actualServerPort > 0) {
                serverPort = String.valueOf(actualServerPort);
            } else {
                // 尝试从环境中获取端口
                serverPort = context.getEnvironment().getProperty("server.port");

                // 如果环境中没有端口配置，则尝试从系统属性和环境变量获取
                if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                    serverPort = System.getProperty("tcm.service.doctor.port");

                    // 如果系统属性中没有端口配置，则尝试从环境变量获取
                    if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                        serverPort = System.getenv("TCM_DOCTOR_PORT");

                        // 如果环境变量中没有端口配置，则使用默认端口
                        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                            serverPort = "9300";
                        }
                    }
                }
            }

            // 确保端口被设置到系统属性中（使之可被其他组件获取）
            System.setProperty("server.port", serverPort);

            LOGGER.info("=====================================================");
            LOGGER.info("            Doctor Service Module Started!           ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            System.out.println("  _____   _____ __  __   _____             _             ");
            System.out.println(" |_   _| / ____|  \\/  | |  __ \\           | |            ");
            System.out.println("   | |  | |    | \\  / | | |  | | ___   ___| |_ ___  _ __ ");
            System.out.println("   | |  | |    | |\\/| | | |  | |/ _ \\ / __| __/ _ \\| '__|");
            System.out.println("  _| |_ | |____| |  | | | |__| | (_) | (__| || (_) | |   ");
            System.out.println(" |_____| \\_____|_|  |_| |_____/ \\___/ \\___|\\__\\___/|_|   ");
            System.out.println("                                                          ");

            System.out.println("  Started successfully: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort);
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("Startup failed: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 加载通用配置属性
     */
    private static Properties loadCommonProperties() {
        try {
            // 尝试加载application-common.yml对应的属性
            ClassPathResource resource = new ClassPathResource("application-common.yml");
            if (resource.exists()) {
                Properties props = new Properties();
                // 注意：这里简化处理，实际上yml文件需要特殊处理才能正确加载
                // 这里只是示例，实际项目中可能需要使用专门的YML解析器
                try {
                    return PropertiesLoaderUtils.loadProperties(resource);
                } catch (IOException e) {
                    System.out.println("无法加载application-common.yml，使用默认配置: " + e.getMessage());
                }
                return props;
            }
        } catch (Exception e) {
            System.out.println("加载配置时出错: " + e.getMessage());
        }
        return new Properties();
    }

    /**
     * 从属性中获取JDK主目录
     */
    private static String getJdkHomeFromProperties(Properties props) {
        // 首先尝试从系统属性获取
        String jdkHome = System.getProperty("tcm.service.jdk.home");
        if (jdkHome != null && !jdkHome.isEmpty()) {
            return jdkHome;
        }

        // 其次从环境变量获取
        jdkHome = System.getenv("TCM_JDK_HOME");
        if (jdkHome != null && !jdkHome.isEmpty()) {
            return jdkHome;
        }

        // 获取当前操作系统信息
        String osName = System.getProperty("os.name").toLowerCase();

        // 从属性文件获取特定操作系统的配置
        if (props != null) {
            if (osName.contains("win")) {
                jdkHome = props.getProperty("tcm.service.jdk.windows.home");
                if (jdkHome != null && !jdkHome.isEmpty()) {
                    return jdkHome;
                }
            } else if (osName.contains("linux") || osName.contains("unix")) {
                jdkHome = props.getProperty("tcm.service.jdk.linux.home");
                if (jdkHome != null && !jdkHome.isEmpty()) {
                    return jdkHome;
                }
            }

            // 从通用配置获取
            jdkHome = props.getProperty("tcm.service.jdk.home");
            if (jdkHome != null && !jdkHome.isEmpty()) {
                return jdkHome;
            }
        }

        // 最后使用通用常量配置中的特定操作系统路径
        if (osName.contains("win")) {
            return com.tcm.common.core.constant.ServiceConstants.Jdk.WINDOWS_HOME;
        } else if (osName.contains("linux") || osName.contains("unix")) {
            return com.tcm.common.core.constant.ServiceConstants.Jdk.LINUX_HOME;
        }

        // 默认使用通用HOME
        return com.tcm.common.core.constant.ServiceConstants.Jdk.HOME;
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web server initialized with port: {}", actualServerPort);
        }
    }
}