registry {
  # file 、nacos 、eureka、redis、zk、consul、etcd3、sofa
  type = "file"

  nacos {
    application = "seata-server"
    serverAddr = "${tcm.service.nacos.host}:${tcm.service.nacos.port}"
    group = "SEATA_GROUP"
    namespace = "${tcm.service.nacos.namespace:}"
    cluster = "default"
    username = "${tcm.service.nacos.username}"
    password = "${tcm.service.nacos.password}"
  }
  file {
    name = "file.conf"
  }
}

config {
  # file、nacos 、apollo、zk、consul、etcd3
  type = "file"

  nacos {
    serverAddr = "${tcm.service.nacos.host}:${tcm.service.nacos.port}"
    namespace = "${tcm.service.nacos.namespace:}"
    group = "SEATA_GROUP"
    username = "${tcm.service.nacos.username}"
    password = "${tcm.service.nacos.password}"
    dataId = "seataServer.properties"
  }
  file {
    name = "file.conf"
  }
} 