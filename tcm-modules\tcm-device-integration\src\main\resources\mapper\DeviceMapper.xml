<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.device.mapper.DeviceMapper">

    <!-- 基本结果集映射 -->
    <resultMap id="BaseResultMap" type="com.tcm.device.domain.Device">
        <id column="id" property="id"/>
        <result column="device_name" property="deviceName"/>
        <result column="device_code" property="deviceCode"/>
        <result column="device_type" property="deviceType"/>
        <result column="model" property="model"/>
        <result column="manufacturer" property="manufacturer"/>
        <result column="status" property="status"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="port" property="port"/>
        <result column="protocol" property="protocol"/>
        <result column="location" property="location"/>
        <result column="description" property="description"/>
        <result column="register_time" property="registerTime"/>
        <result column="last_online_time" property="lastOnlineTime"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
        <result column="deleted" property="deleted"/>
    </resultMap>

    <!-- 基本列 -->
    <sql id="Base_Column_List">
        id, device_name, device_code, device_type, model, manufacturer, status, ip_address, 
        port, protocol, location, description, register_time, last_online_time, 
        create_by, create_time, update_by, update_time, remark, deleted
    </sql>
    
</mapper> 