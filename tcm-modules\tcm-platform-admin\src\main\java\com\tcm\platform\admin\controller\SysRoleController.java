package com.tcm.platform.admin.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.platform.admin.dto.Result;
import com.tcm.platform.admin.dto.RoleDTO;
import com.tcm.platform.admin.entity.SysRole;
import com.tcm.platform.admin.service.SysRoleService;
import com.tcm.platform.admin.service.SysMenuService;

import java.util.List;
import java.util.Map;

/**
 * 系统角色控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/role")
public class SysRoleController extends BaseController {

    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysMenuService menuService;

    /**
     * 获取角色列表
     */
    @GetMapping("/list")
    public Result<IPage<SysRole>> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "roleName", required = false) String roleName,
            @RequestParam(value = "roleCode", required = false) String roleCode,
            @RequestParam(value = "status", required = false) Integer status) {

        SysRole role = new SysRole();
        role.setRoleName(roleName);
        role.setRoleCode(roleCode);
        role.setStatus(status);

        IPage<SysRole> page = roleService.selectRolePage(pageNum, pageSize, role);
        return Result.success(page);
    }

    /**
     * 获取角色选择框列表
     */
    @GetMapping("/options")
    public Result<List<SysRole>> getOptions() {
        List<SysRole> roles = roleService.selectRoleOptions();
        return Result.success(roles);
    }

    /**
     * 根据角色ID获取详细信息
     */
    @GetMapping("/{roleId}")
    public Result<RoleDTO> getInfo(@PathVariable Long roleId) {
        SysRole role = roleService.selectRoleById(roleId);
        if (role == null) {
            return Result.error("角色不存在");
        }

        // 转换为DTO
        RoleDTO roleDTO = new RoleDTO();
        BeanUtils.copyProperties(role, roleDTO);

        // 获取角色菜单列表
        List<Long> menuIds = menuService.selectMenuIdsByRoleId(roleId);
        roleDTO.setMenuIds(menuIds);

        return Result.success(roleDTO);
    }

    /**
     * 获取角色的菜单ID列表
     */
    @GetMapping("/menu-ids/{roleId}")
    public Result<List<Long>> getRoleMenuIds(@PathVariable Long roleId) {
        List<Long> menuIds = menuService.selectMenuIdsByRoleId(roleId);
        return Result.success(menuIds);
    }

    /**
     * 新增角色
     */
    @PostMapping
    public Result<Void> add(@Validated @RequestBody RoleDTO roleDTO) {
        if (roleService.checkRoleNameUnique(roleDTO.getRoleName(), null)) {
            return Result.error("新增角色'" + roleDTO.getRoleName() + "'失败，角色名称已存在");
        }
        if (roleService.checkRoleCodeUnique(roleDTO.getRoleCode(), null)) {
            return Result.error("新增角色'" + roleDTO.getRoleCode() + "'失败，角色编码已存在");
        }

        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO, role);

        boolean success = roleService.insertRole(role, roleDTO.getMenuIds());
        return success ? Result.success() : Result.error("新增角色失败");
    }

    /**
     * 修改角色
     */
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody RoleDTO roleDTO) {
        if (roleDTO.getId() == null) {
            return Result.error("角色ID不能为空");
        }

        if (roleService.checkRoleNameUnique(roleDTO.getRoleName(), roleDTO.getId())) {
            return Result.error("修改角色'" + roleDTO.getRoleName() + "'失败，角色名称已存在");
        }
        if (roleService.checkRoleCodeUnique(roleDTO.getRoleCode(), roleDTO.getId())) {
            return Result.error("修改角色'" + roleDTO.getRoleCode() + "'失败，角色编码已存在");
        }

        SysRole role = new SysRole();
        BeanUtils.copyProperties(roleDTO, role);

        boolean success = roleService.updateRole(role, roleDTO.getMenuIds());
        return success ? Result.success() : Result.error("修改角色失败");
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{roleId}")
    public Result<Void> remove(@PathVariable Long roleId) {
        try {
            boolean success = roleService.deleteRoleById(roleId);
            return success ? Result.success() : Result.error("删除角色失败");
        } catch (RuntimeException e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 修改角色状态
     */
    @PutMapping("/change-status")
    public Result<Void> changeStatus(@RequestBody Map<String, Object> params) {
        Long roleId = Long.valueOf(params.get("roleId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());

        boolean success = roleService.updateRoleStatus(roleId, status);
        return success ? Result.success() : Result.error("修改角色状态失败");
    }
}