package com.tcm.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.admin.dto.DeptDTO;
import com.tcm.admin.entity.Dept;
import com.tcm.admin.vo.DeptVO;
import com.tcm.admin.vo.TreeSelectVO;

import java.util.List;

/**
 * 部门服务接口
 */
public interface DeptService extends IService<Dept> {

    /**
     * 查询部门列表
     *
     * @param deptDTO 查询条件
     * @return 部门列表
     */
    List<DeptVO> selectDeptList(DeptDTO deptDTO);

    /**
     * 构建前端所需要的部门树结构
     *
     * @param depts 部门列表
     * @return 部门树
     */
    List<DeptVO> buildDeptTree(List<DeptVO> depts);

    /**
     * 构建前端所需要部门下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    List<TreeSelectVO> buildDeptTreeSelect(List<DeptVO> depts);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    DeptVO getDeptById(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param deptDTO 部门信息
     * @return 结果
     */
    boolean checkDeptNameUnique(DeptDTO deptDTO);

    /**
     * 新增部门信息
     *
     * @param deptDTO 部门信息
     * @return 结果
     */
    boolean insertDept(DeptDTO deptDTO);

    /**
     * 修改部门信息
     *
     * @param deptDTO 部门信息
     * @return 结果
     */
    boolean updateDept(DeptDTO deptDTO);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    boolean deleteDeptById(Long deptId);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    List<Long> selectDeptListByRoleId(Long roleId);
}