package com.tcm.device.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.tcm.device.domain.DeviceData;
import com.tcm.device.mapper.DeviceDataMapper;
import com.tcm.device.service.IDeviceDataService;

/**
 * 设备数据服务实现类
 * 
 * <AUTHOR>
 */
@Service
public class DeviceDataServiceImpl extends ServiceImpl<DeviceDataMapper, DeviceData> implements IDeviceDataService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveDeviceData(Long deviceId, String dataType, String dataValue, String unit) {
        DeviceData deviceData = new DeviceData();
        deviceData.setDeviceId(deviceId);
        deviceData.setDataType(dataType);
        deviceData.setDataValue(dataValue);
        deviceData.setUnit(unit);
        deviceData.setCollectTime(new Date());
        deviceData.setStatus(0); // 正常状态
        deviceData.setCreateTime(new Date());

        return this.save(deviceData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveDeviceData(Long deviceId, Map<String, String> dataMap, Map<String, String> unitMap) {
        if (dataMap == null || dataMap.isEmpty()) {
            return false;
        }

        List<DeviceData> dataList = new ArrayList<>();
        Date now = new Date();

        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            String dataType = entry.getKey();
            String dataValue = entry.getValue();

            DeviceData deviceData = new DeviceData();
            deviceData.setDeviceId(deviceId);
            deviceData.setDataType(dataType);
            deviceData.setDataValue(dataValue);
            deviceData.setUnit(unitMap != null ? unitMap.get(dataType) : null);
            deviceData.setCollectTime(now);
            deviceData.setStatus(0); // 正常状态
            deviceData.setCreateTime(now);

            dataList.add(deviceData);
        }

        return this.saveBatch(dataList);
    }

    @Override
    public IPage<DeviceData> selectDeviceDataPage(IPage<DeviceData> page, Long deviceId, String dataType,
            Date startTime, Date endTime) {
        LambdaQueryWrapper<DeviceData> queryWrapper = new LambdaQueryWrapper<>();

        // 设备ID条件
        if (deviceId != null) {
            queryWrapper.eq(DeviceData::getDeviceId, deviceId);
        }

        // 数据类型条件
        if (!StringUtils.isEmpty(dataType)) {
            queryWrapper.eq(DeviceData::getDataType, dataType);
        }

        // 时间范围条件
        if (startTime != null) {
            queryWrapper.ge(DeviceData::getCollectTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(DeviceData::getCollectTime, endTime);
        }

        // 按采集时间降序
        queryWrapper.orderByDesc(DeviceData::getCollectTime);

        return this.page(page, queryWrapper);
    }

    @Override
    public List<DeviceData> getLatestDeviceData(Long deviceId) {
        // 查询当前设备所有的数据类型
        LambdaQueryWrapper<DeviceData> typeQuery = new LambdaQueryWrapper<>();
        typeQuery.eq(DeviceData::getDeviceId, deviceId)
                .select(DeviceData::getDataType)
                .groupBy(DeviceData::getDataType);

        List<DeviceData> typeList = this.list(typeQuery);

        // 查询每种数据类型的最新数据
        List<DeviceData> latestDataList = new ArrayList<>();
        for (DeviceData typeData : typeList) {
            DeviceData latestData = getLatestDeviceData(deviceId, typeData.getDataType());
            if (latestData != null) {
                latestDataList.add(latestData);
            }
        }

        return latestDataList;
    }

    @Override
    public DeviceData getLatestDeviceData(Long deviceId, String dataType) {
        LambdaQueryWrapper<DeviceData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceData::getDeviceId, deviceId)
                .eq(DeviceData::getDataType, dataType)
                .orderByDesc(DeviceData::getCollectTime)
                .last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    @Override
    public List<DeviceData> getDeviceDataByTimeRange(Long deviceId, String dataType, Date startTime, Date endTime) {
        LambdaQueryWrapper<DeviceData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DeviceData::getDeviceId, deviceId);

        // 数据类型条件
        if (!StringUtils.isEmpty(dataType)) {
            queryWrapper.eq(DeviceData::getDataType, dataType);
        }

        // 时间范围条件
        if (startTime != null) {
            queryWrapper.ge(DeviceData::getCollectTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(DeviceData::getCollectTime, endTime);
        }

        // 按采集时间升序
        queryWrapper.orderByAsc(DeviceData::getCollectTime);

        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeviceDataBefore(Date time) {
        if (time == null) {
            return 0;
        }

        LambdaQueryWrapper<DeviceData> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(DeviceData::getCollectTime, time);

        return this.baseMapper.delete(queryWrapper);
    }
}