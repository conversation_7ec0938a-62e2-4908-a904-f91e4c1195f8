# TCM 用户模块 (tcm-user)

## 功能概述

TCM用户模块负责系统用户的管理，包括用户信息的增删改查、用户状态管理、角色分配等功能。

## 主要功能

- 用户管理：创建、修改、删除用户，查询用户列表和详情
- 角色管理：创建、修改、删除角色，查询角色列表和详情
- 用户角色分配：为用户分配和移除角色
- 密码管理：重置用户密码，密码加密存储

## 技术栈

- Spring Boot
- Spring Security
- MyBatis Plus
- Nacos（服务注册与配置中心）
- Druid（数据库连接池）
- Swagger（API文档）
- Logback（日志）

## 配置说明

主要配置文件：

- `bootstrap.yml`: 启动配置，包含Nacos配置中心设置
- `application.yml`: 应用配置，包含数据库、Redis、服务端口等配置
- `logback-spring.xml`: 日志配置

## API接口

### 用户管理

- `GET /user/list`: 获取用户列表（分页）
- `GET /user/{id}`: 获取用户详情
- `POST /user`: 创建用户
- `PUT /user/{id}`: 更新用户
- `DELETE /user/{id}`: 删除用户
- `PUT /user/{id}/status/{status}`: 更新用户状态
- `PUT /user/{id}/password/reset`: 重置用户密码
- `GET /user/{userId}/roles`: 获取用户的角色
- `POST /user/{userId}/roles`: 分配角色给用户

### 角色管理

- `GET /role/list`: 获取角色列表（分页）
- `GET /role/{id}`: 获取角色详情
- `POST /role`: 创建角色
- `PUT /role`: 更新角色
- `DELETE /role/{id}`: 删除角色
- `GET /role/user/{userId}`: 获取用户的角色列表

## 数据库表结构

### 用户表 (sys_user)

| 字段名      | 类型         | 说明                     |
|------------|--------------|-------------------------|
| id         | bigint       | 用户ID，主键             |
| username   | varchar(50)  | 用户名                   |
| password   | varchar(100) | 密码                     |
| real_name  | varchar(50)  | 真实姓名                 |
| phone      | varchar(11)  | 手机号                   |
| email      | varchar(50)  | 邮箱                     |
| gender     | tinyint      | 性别（0男 1女 2未知）     |
| avatar     | varchar(100) | 头像地址                 |
| dept_id    | bigint       | 部门ID                   |
| status     | tinyint      | 账号状态（0正常 1停用）   |
| del_flag   | tinyint      | 删除标志（0存在 1删除）   |
| login_ip   | varchar(50)  | 最后登录IP               |
| login_time | datetime     | 最后登录时间             |
| create_by  | varchar(50)  | 创建者                   |
| create_time| datetime     | 创建时间                 |
| update_by  | varchar(50)  | 更新者                   |
| update_time| datetime     | 更新时间                 |
| remark     | varchar(500) | 备注                     |

### 角色表 (sys_role)

| 字段名      | 类型         | 说明                     |
|------------|--------------|-------------------------|
| id         | bigint       | 角色ID，主键             |
| role_name  | varchar(30)  | 角色名称                 |
| role_key   | varchar(100) | 角色权限字符串           |
| role_sort  | int          | 显示顺序                 |
| data_scope | tinyint      | 数据范围                 |
| status     | tinyint      | 角色状态（0正常 1停用）   |
| del_flag   | tinyint      | 删除标志（0存在 1删除）   |
| create_by  | varchar(50)  | 创建者                   |
| create_time| datetime     | 创建时间                 |
| update_by  | varchar(50)  | 更新者                   |
| update_time| datetime     | 更新时间                 |
| remark     | varchar(500) | 备注                     |

### 用户角色关联表 (sys_user_role)

| 字段名  | 类型    | 说明       |
|--------|---------|-----------|
| id     | bigint  | 主键ID     |
| user_id| bigint  | 用户ID     |
| role_id| bigint  | 角色ID     |

## 使用方法

1. 确保配置文件中的数据库、Redis等配置正确
2. 运行 `start.bat` 脚本启动服务
3. 访问 `http://localhost:9313/swagger-ui.html` 查看API文档

## 依赖服务

- Nacos服务注册中心
- MySQL数据库
- Redis缓存服务 