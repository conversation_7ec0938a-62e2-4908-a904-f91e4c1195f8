package com.tcm.platform.admin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.mybatis.spring.annotation.MapperScan;

/**
 * 平台管理服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan("com.tcm.platform.admin.mapper")
public class TcmPlatformAdminApplication {

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // 1. 处理日志冲突问题
        // 禁用Log4j2相关配置
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.configurationFile", "log4j2-empty.xml");

        // 禁用日志多绑定警告
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "warn");
        System.setProperty("org.slf4j.logger.org.slf4j", "error");
        System.setProperty("org.slf4j.loggerFactory.multipleBindingCheck", "ignore");

        // 强制使用Logback
        System.setProperty("logging.config", "classpath:logback-spring.xml");
        System.setProperty("java.util.logging.manager", "org.apache.logging.log4j.jul.LogManager");

        // 2. 设置统一配置文件
        System.setProperty("spring.profiles.include", "common");

        // 3. 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-platform-admin");
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 使用统一的Logback配置
            System.setProperty("logging.level.root", "WARN");
            System.setProperty("logging.level.com.tcm", "INFO");

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false");
            System.setProperty("spring.main.allow-bean-definition-overriding", "true");

            // 3. Nacos配置 - 禁用配置导入检查
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

            // 4. 禁用gRPC相关功能，解决Java 9+环境中的类加载问题
            System.setProperty("nacos.client.naming.grpc.enabled", "false");
            System.setProperty("nacos.client.config.grpc.enabled", "false");
            System.setProperty("com.alibaba.nacos.config.remote.type", "http");
            System.setProperty("com.alibaba.nacos.naming.remote.type", "http");

            // 5. 配置Sentinel
            System.setProperty("spring.cloud.sentinel.enabled", "true");

            // 启动应用并获取上下文
            ConfigurableApplicationContext context = SpringApplication.run(TcmPlatformAdminApplication.class, args);

            // 获取环境对象，用于解析配置
            Environment env = context.getEnvironment();
            String port = env.getProperty("server.port");
            String appName = env.getProperty("spring.application.name");

            // 成功运行提示
            System.out.println("\n=====================================================\n" + "【" + appName.toUpperCase()
                    + "】平台管理服务已成功启动！\n运行端口: " + port + "\n服务健康状态: 正常运行" + "\nJDK路径: " + System.getProperty("java.home")
                    + "\n=====================================================\n");
        } catch (Exception e) {
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}