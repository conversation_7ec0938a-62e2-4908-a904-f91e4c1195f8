package com.tcm.inventory.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.inventory.entity.InventoryItem;

/**
 * 库存项目服务接口
 */
public interface IInventoryItemService extends IService<InventoryItem> {
    
    /**
     * 根据药品编码查询库存项目
     * 
     * @param itemCode 药品编码
     * @return 库存项目
     */
    InventoryItem getByItemCode(String itemCode);
    
    /**
     * 更新库存数量
     * 
     * @param itemId 库存项目ID
     * @param quantity 变更数量（正数增加，负数减少）
     * @return 是否更新成功
     */
    boolean updateQuantity(Long itemId, int quantity);
} 