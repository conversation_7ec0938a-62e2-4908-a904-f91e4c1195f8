package com.tcm.diagnosis.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 诊断建议实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_diagnosis_suggestion")
public class DiagnosisSuggestion extends BaseEntity {

    /**
     * 建议ID
     */
    @TableId
    private Long suggestionId;

    /**
     * 诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 建议标题
     */
    private String suggestionTitle;
    
    /**
     * 建议内容
     */
    private String suggestion;
    
    /**
     * 建议内容（用于兼容性）
     */
    private String suggestionContent;

    /**
     * 建议类型（诊断建议/治疗建议/用药建议/注意事项等）
     */
    private String suggestionType;

    /**
     * 建议依据
     */
    private String basis;

    /**
     * 关联知识条目（JSON格式）
     */
    private String relatedKnowledge;

    /**
     * 参考来源
     */
    private String references;

    /**
     * 生成方法（AI/知识库/医生）
     */
    private String generationMethod;

    /**
     * 生成时间
     */
    private Date generationTime;

    /**
     * 置信度（0-100）
     */
    private Integer confidence;

    /**
     * 是否医生采纳（0：未采纳，1：已采纳）
     */
    private Integer doctorAdopted;
    
    /**
     * 状态（0：待处理，1：已采纳，2：已拒绝）
     */
    private Integer status;
    
    /**
     * 是否修改（0：未修改，1：已修改）
     */
    private Integer isModified;
    
    /**
     * 采纳时间
     */
    private Date adoptTime;
    
    /**
     * 采纳人
     */
    private String adoptBy;
    
    /**
     * 拒绝时间
     */
    private Date rejectTime;
    
    /**
     * 拒绝人
     */
    private String rejectBy;
    
    /**
     * 拒绝原因
     */
    private String rejectReason;
}