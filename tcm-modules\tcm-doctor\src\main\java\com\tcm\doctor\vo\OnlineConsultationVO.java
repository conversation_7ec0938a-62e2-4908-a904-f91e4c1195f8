package com.tcm.doctor.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 在线问诊视图对象
 */
@Data
public class OnlineConsultationVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 问诊ID
     */
    private Long consultationId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生职称
     */
    private String titleName;
    
    /**
     * 医生头像
     */
    private String doctorAvatar;
    
    /**
     * 医生科室
     */
    private String department;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 患者头像
     */
    private String patientAvatar;
    
    /**
     * 问诊标题
     */
    private String title;
    
    /**
     * 问诊类型（1-图文咨询 2-语音咨询 3-视频咨询）
     */
    private Integer type;
    
    /**
     * 问诊类型名称
     */
    private String typeName;
    
    /**
     * 主诉
     */
    private String chiefComplaint;
    
    /**
     * 病情描述
     */
    private String illnessDesc;
    
    /**
     * 患病时长
     */
    private String illnessDuration;
    
    /**
     * 既往病史
     */
    private String medicalHistory;
    
    /**
     * 药物过敏史
     */
    private String allergicHistory;
    
    /**
     * 问诊图片（多个以逗号分隔）
     */
    private String images;
    
    /**
     * 问诊图片列表
     */
    private List<String> imageList;
    
    /**
     * 问诊状态（0-待接诊 1-进行中 2-已完成 3-已取消 4-已超时）
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 发起时间
     */
    private Date createTime;
    
    /**
     * 接诊时间
     */
    private Date acceptTime;
    
    /**
     * 完成时间
     */
    private Date finishTime;
    
    /**
     * 医生建议
     */
    private String doctorAdvice;
    
    /**
     * 诊断结果
     */
    private String diagnosis;
    
    /**
     * 关联处方ID
     */
    private Long prescriptionId;
    
    /**
     * 评分（1-5分）
     */
    private Integer score;
    
    /**
     * 评价内容
     */
    private String evaluation;
    
    /**
     * 订单编号
     */
    private String orderNo;
    
    /**
     * 支付状态（0-未支付 1-已支付 2-已退款）
     */
    private Integer payStatus;
    
    /**
     * 支付状态名称
     */
    private String payStatusName;
    
    /**
     * 支付时间
     */
    private Date payTime;
    
    /**
     * 支付金额
     */
    private Double payAmount;
    
    /**
     * 未读消息数
     */
    private Integer unreadCount;
}