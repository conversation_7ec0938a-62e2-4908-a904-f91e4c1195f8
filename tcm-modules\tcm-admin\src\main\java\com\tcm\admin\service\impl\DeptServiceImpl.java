package com.tcm.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.admin.dto.DeptDTO;
import com.tcm.admin.entity.Dept;
import com.tcm.admin.mapper.DeptMapper;
import com.tcm.admin.service.DeptService;
import com.tcm.admin.vo.DeptVO;
import com.tcm.admin.vo.TreeSelectVO;
import com.tcm.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {

    /**
     * 查询部门列表
     *
     * @param deptDTO 查询条件
     * @return 部门列表
     */
    @Override
    public List<DeptVO> selectDeptList(DeptDTO deptDTO) {
        Dept dept = new Dept();
        if (deptDTO != null) {
            BeanUtils.copyProperties(deptDTO, dept);
        }

        List<Dept> depts = baseMapper.selectDeptList(dept);
        return depts.stream()
                .map(this::convertToVO)
                .sorted(Comparator.comparing(DeptVO::getOrderNum))
                .collect(Collectors.toList());
    }

    /**
     * 构建前端所需要的部门树结构
     *
     * @param depts 部门列表
     * @return 部门树
     */
    @Override
    public List<DeptVO> buildDeptTree(List<DeptVO> depts) {
        List<DeptVO> returnList = new ArrayList<>();
        List<Long> tempList = depts.stream().map(DeptVO::getId).collect(Collectors.toList());

        for (DeptVO dept : depts) {
            // 如果是顶级节点，遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionBuild(depts, dept);
                returnList.add(dept);
            }
        }

        if (returnList.isEmpty()) {
            returnList = depts;
        }

        return returnList;
    }

    /**
     * 递归构建部门树
     */
    private void recursionBuild(List<DeptVO> depts, DeptVO parent) {
        // 得到子节点列表
        List<DeptVO> children = getChildList(depts, parent);
        parent.setChildren(children);

        // 递归获取子节点的子节点
        for (DeptVO child : children) {
            if (hasChild(depts, child)) {
                recursionBuild(depts, child);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<DeptVO> getChildList(List<DeptVO> depts, DeptVO parent) {
        return depts.stream()
                .filter(dept -> parent.getId().equals(dept.getParentId()))
                .sorted(Comparator.comparing(DeptVO::getOrderNum))
                .collect(Collectors.toList());
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<DeptVO> depts, DeptVO parent) {
        return depts.stream().anyMatch(dept -> parent.getId().equals(dept.getParentId()));
    }

    /**
     * 构建前端所需要部门下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelectVO> buildDeptTreeSelect(List<DeptVO> depts) {
        List<DeptVO> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelectVO::new).collect(Collectors.toList());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public DeptVO getDeptById(Long deptId) {
        Dept dept = getById(deptId);
        if (dept == null) {
            throw new BusinessException("部门不存在");
        }

        DeptVO deptVO = convertToVO(dept);

        // 获取父部门名称
        Dept parentDept = getById(dept.getParentId());
        if (parentDept != null) {
            deptVO.setParentName(parentDept.getDeptName());
        }

        return deptVO;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param deptDTO 部门信息
     * @return 结果
     */
    @Override
    public boolean checkDeptNameUnique(DeptDTO deptDTO) {
        Long deptId = deptDTO.getId() == null ? -1L : deptDTO.getId();

        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Dept::getDeptName, deptDTO.getDeptName())
                .eq(Dept::getParentId, deptDTO.getParentId());

        Dept dept = getOne(queryWrapper);
        return dept == null || dept.getId().equals(deptId);
    }

    /**
     * 新增部门信息
     *
     * @param deptDTO 部门信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDept(DeptDTO deptDTO) {
        Dept dept = new Dept();
        BeanUtils.copyProperties(deptDTO, dept);

        // 设置默认值
        if (dept.getStatus() == null) {
            dept.setStatus(0); // 默认正常状态
        }
        dept.setCreateTime(LocalDateTime.now());

        // 如果父部门不为正常状态，则不允许新增子部门
        if (deptDTO.getParentId() != null && deptDTO.getParentId() > 0) {
            Dept parentDept = getById(deptDTO.getParentId());
            if (parentDept == null) {
                throw new BusinessException("父部门不存在");
            }

            if (parentDept.getStatus() != null && parentDept.getStatus() != 0) {
                throw new BusinessException("部门停用，不允许新增");
            }
        }

        boolean result = save(dept);

        // 如果父部门为空，则设置为顶级部门
        if (deptDTO.getParentId() == null) {
            dept.setParentId(0L);
            updateById(dept);
        }

        return result;
    }

    /**
     * 修改部门信息
     *
     * @param deptDTO 部门信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDept(DeptDTO deptDTO) {
        Dept oldDept = getById(deptDTO.getId());
        if (oldDept == null) {
            throw new BusinessException("部门不存在");
        }

        // 如果当前部门的父部门不是原来的父部门，则需要判断该父部门是否存在
        if (deptDTO.getParentId() != null && !oldDept.getParentId().equals(deptDTO.getParentId())) {
            Dept parentDept = getById(deptDTO.getParentId());
            if (parentDept == null) {
                throw new BusinessException("父部门不存在");
            }

            if (parentDept.getStatus() != null && parentDept.getStatus() != 0) {
                throw new BusinessException("部门停用，不允许修改");
            }
        }

        Dept dept = new Dept();
        BeanUtils.copyProperties(deptDTO, dept);
        dept.setUpdateTime(LocalDateTime.now());

        // 如果当前部门状态变更为停用，则所有子部门同步停用
        if (dept.getStatus() != null && dept.getStatus() != 0) {
            updateDeptChildren(dept);
        }

        return updateById(dept);
    }

    /**
     * 修改该部门的子部门状态
     */
    private void updateDeptChildren(Dept dept) {
        baseMapper.updateDeptStatus(dept);
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDeptById(Long deptId) {
        // 判断是否存在子部门
        if (baseMapper.hasChildByDeptId(deptId) > 0) {
            throw new BusinessException("存在下级部门，不允许删除");
        }

        // 判断是否存在用户
        if (baseMapper.checkDeptExistUser(deptId) > 0) {
            throw new BusinessException("部门存在用户，不允许删除");
        }

        return removeById(deptId);
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        return baseMapper.selectDeptListByRoleId(roleId);
    }

    /**
     * 将实体转换为VO
     */
    private DeptVO convertToVO(Dept dept) {
        if (dept == null) {
            return null;
        }

        DeptVO deptVO = new DeptVO();
        BeanUtils.copyProperties(dept, deptVO);

        // 设置状态描述
        if (dept.getStatus() != null) {
            deptVO.setStatusDesc(dept.getStatus() == 0 ? "正常" : "停用");
        }

        return deptVO;
    }
}