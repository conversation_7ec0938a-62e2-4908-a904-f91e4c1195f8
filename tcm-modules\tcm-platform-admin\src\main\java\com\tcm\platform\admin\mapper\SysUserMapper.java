package com.tcm.platform.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.platform.admin.entity.SysUser;
import org.apache.ibatis.annotations.Param;

/**
 * 系统用户Mapper接口
 * 
 * <AUTHOR>
 */
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 分页查询用户列表
     * 
     * @param page 分页参数
     * @param user 查询参数
     * @return 用户列表
     */
    IPage<SysUser> selectUserPage(Page<SysUser> page, @Param("user") SysUser user);

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    SysUser selectByUsername(@Param("username") String username);

    /**
     * 更新用户状态
     * 
     * @param userId 用户ID
     * @param status 用户状态
     * @return 影响的行数
     */
    int updateStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新用户密码
     * 
     * @param userId   用户ID
     * @param password 新密码
     * @return 影响的行数
     */
    int updatePassword(@Param("userId") Long userId, @Param("password") String password);
}