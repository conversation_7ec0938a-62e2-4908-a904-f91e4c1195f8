package com.tcm.common.core.utils.uuid;

import java.util.UUID;

/**
 * UUID工具类
 */
public class UuidUtils {
    
    /**
     * 获取随机UUID
     *
     * @return 随机UUID
     */
    public static String randomUUID() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 获取随机UUID，并使用指定的分隔符
     *
     * @param separator 分隔符
     * @return 随机UUID
     */
    public static String randomUUID(String separator) {
        String uuid = UUID.randomUUID().toString();
        return uuid.replace("-", separator);
    }
    
    /**
     * 获取随机UUID，不含分隔符
     *
     * @return 随机UUID
     */
    public static String simpleUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 获取指定长度的随机UUID
     *
     * @param length UUID长度
     * @return 随机UUID
     */
    public static String randomUUID(int length) {
        String uuid = simpleUUID();
        if (length >= uuid.length()) {
            return uuid;
        }
        return uuid.substring(0, length);
    }
    
    /**
     * 验证字符串是否为有效的UUID
     *
     * @param str 字符串
     * @return 是否为有效的UUID
     */
    public static boolean isValidUUID(String str) {
        if (str == null) {
            return false;
        }
        try {
            UUID.fromString(str);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
