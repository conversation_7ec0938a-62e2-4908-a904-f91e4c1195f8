package com.tcm.security.filter;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tcm.security.utils.JwtUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * JWT认证过滤器
 */
@Slf4j
@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private static final String USER_REDIS_KEY = "auth:user:";
    private static final String TOKEN_PREFIX = "Bearer ";

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        try {
            // 获取请求头中的令牌
            String token = getTokenFromRequest(request);

            // 如果令牌为空或无效，则放行，由后续的过滤器处理
            if (!StringUtils.hasText(token) || jwtUtils.isTokenExpired(token)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);

            // 从Redis获取用户权限信息
            Map<String, Object> userMap = getUserFromRedis(userId);
            if (userMap == null) {
                filterChain.doFilter(request, response);
                return;
            }

            // 获取用户权限集合
            Set<String> permissions = getPermissions(userMap);

            // 创建用户认证信息
            List<SimpleGrantedAuthority> authorities = permissions.stream().map(SimpleGrantedAuthority::new)
                    .collect(Collectors.toList());

            // 设置认证信息到安全上下文
            String username = jwtUtils.getUsernameFromToken(token);
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username,
                    null, authorities);

            SecurityContextHolder.getContext().setAuthentication(authenticationToken);

            // 放行请求
            filterChain.doFilter(request, response);
        } catch (Exception e) {
            log.error("JWT认证失败", e);
            // 认证失败，返回错误信息
            handleAuthenticationFailure(response, e.getMessage());
        }
    }

    /**
     * 从请求头中获取令牌
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String authorization = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(authorization) && authorization.startsWith(TOKEN_PREFIX)) {
            return authorization.substring(TOKEN_PREFIX.length());
        }
        return null;
    }

    /**
     * 从Redis获取用户信息（模拟实现）
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getUserFromRedis(Long userId) {
        Object userObj = redisTemplate.opsForValue().get(USER_REDIS_KEY + userId);
        if (userObj == null) {
            return null;
        }

        // 在实际项目中，这里应该是从Redis中获取的LoginUser对象
        // 这里为了演示，我们简单地将其转换为Map
        try {
            return objectMapper.convertValue(userObj, Map.class);
        } catch (Exception e) {
            log.error("转换用户信息失败", e);
            return null;
        }
    }

    /**
     * 获取用户权限集合
     */
    @SuppressWarnings("unchecked")
    private Set<String> getPermissions(Map<String, Object> userMap) {
        Object permissionsObj = userMap.get("permissions");
        if (permissionsObj instanceof Set) {
            return (Set<String>) permissionsObj;
        }
        return Collections.emptySet();
    }

    /**
     * 处理认证失败情况
     */
    private void handleAuthenticationFailure(HttpServletResponse response, String errorMessage) throws IOException {
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        Map<String, Object> result = Map.of("code", 401, "message", errorMessage != null ? errorMessage : "未授权访问");

        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}