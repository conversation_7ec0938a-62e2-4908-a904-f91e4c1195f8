package com.tcm.appointment.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 预约挂号视图对象
 */
@Data
public class AppointmentVO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 预约ID
     */
    private Long appointmentId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 患者性别
     */
    private String patientGender;
    
    /**
     * 患者年龄
     */
    private Integer patientAge;
    
    /**
     * 患者手机号
     */
    private String patientPhone;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 医生职称
     */
    private String doctorTitle;
    
    /**
     * 医生头像
     */
    private String doctorAvatar;
    
    /**
     * 预约日期
     */
    private Date appointmentDate;
    
    /**
     * 时间段
     */
    private String timeSlot;
    
    /**
     * 预约类型（1-初诊 2-复诊）
     */
    private Integer type;
    
    /**
     * 预约类型名称
     */
    private String typeName;
    
    /**
     * 主要症状
     */
    private String symptom;
    
    /**
     * 就诊科室
     */
    private String department;
    
    /**
     * 预约状态（0-待支付 1-已预约 2-已到诊 3-已取消 4-已爽约）
     */
    private Integer status;
    
    /**
     * 预约状态名称
     */
    private String statusName;
    
    /**
     * 费用
     */
    private Double fee;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 备注
     */
    private String notes;
    
    /**
     * 支付状态（0-未支付 1-已支付 2-已退款）
     */
    private Integer payStatus;
    
    /**
     * 支付状态名称
     */
    private String payStatusName;
    
    /**
     * 支付时间
     */
    private Date payTime;
    
    /**
     * 到诊时间
     */
    private Date arriveTime;
    
    /**
     * 排序号
     */
    private Integer orderNum;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 