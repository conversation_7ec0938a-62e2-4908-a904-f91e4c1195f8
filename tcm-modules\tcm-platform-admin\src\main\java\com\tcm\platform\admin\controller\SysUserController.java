package com.tcm.platform.admin.controller;

import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.platform.admin.dto.Result;
import com.tcm.platform.admin.dto.UserDTO;
import com.tcm.platform.admin.entity.SysUser;
import com.tcm.platform.admin.service.SysUserService;
import java.util.Map;
import java.util.HashMap;

/**
 * 系统用户控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user")
public class SysUserController extends BaseController {

    @Autowired
    private SysUserService userService;

    /**
     * 获取用户列表
     */
    @GetMapping("/list")
    public Result<IPage<SysUser>> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "deptId", required = false) Long deptId) {

        SysUser user = new SysUser();
        user.setUsername(username);
        user.setStatus(status);
        user.setDeptId(deptId);

        IPage<SysUser> page = userService.selectUserPage(pageNum, pageSize, user);
        return Result.success(page);
    }

    /**
     * 根据用户ID获取详细信息
     */
    @GetMapping("/{userId}")
    public Result<UserDTO> getInfo(@PathVariable Long userId) {
        UserDTO userDTO = userService.selectUserById(userId);
        if (userDTO == null) {
            return Result.error("用户不存在");
        }
        return Result.success(userDTO);
    }

    /**
     * 新增用户
     */
    @PostMapping
    public Result<Void> add(@Validated @RequestBody UserDTO user) {
        if (userService.checkUsernameUnique(user.getUsername())) {
            return Result.error("新增用户'" + user.getUsername() + "'失败，用户名已存在");
        }

        boolean success = userService.insertUser(user);
        return success ? Result.success() : Result.error("新增用户失败");
    }

    /**
     * 修改用户
     */
    @PutMapping
    public Result<Void> edit(@Validated @RequestBody UserDTO user) {
        // 检查是否允许修改用户
        if (user.getId() == null) {
            return Result.error("用户ID不能为空");
        }

        boolean success = userService.updateUser(user);
        return success ? Result.success() : Result.error("修改用户失败");
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{userId}")
    public Result<Void> remove(@PathVariable Long userId) {
        if (userId == null) {
            return Result.error("用户ID不能为空");
        }

        boolean success = userService.deleteUserById(userId);
        return success ? Result.success() : Result.error("删除用户失败");
    }

    /**
     * 重置密码
     */
    @PutMapping("/resetPwd")
    public Result<Void> resetPwd(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        String password = params.get("password").toString();

        boolean success = userService.resetPassword(userId, password);
        return success ? Result.success() : Result.error("重置密码失败");
    }

    /**
     * 修改用户状态
     */
    @PutMapping("/changeStatus")
    public Result<Void> changeStatus(@RequestBody Map<String, Object> params) {
        Long userId = Long.valueOf(params.get("userId").toString());
        Integer status = Integer.valueOf(params.get("status").toString());

        boolean success = userService.updateUserStatus(userId, status);
        return success ? Result.success() : Result.error("修改用户状态失败");
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getUserInfo() {
        String username = getCurrentUsername();
        if (username == null) {
            return Result.error("获取用户信息失败");
        }

        UserDTO userInfo = userService.selectUserByUsername(username);
        if (userInfo == null) {
            return Result.error("用户不存在");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("user", userInfo);
        result.put("roles", userInfo.getRoleIds());
        result.put("permissions", userService.getUserPermissions(userInfo.getId()));

        return Result.success(result);
    }
}