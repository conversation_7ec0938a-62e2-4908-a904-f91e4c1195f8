package com.tcm.international.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import com.tcm.international.dto.MultilingualContentDTO;
import com.tcm.international.entity.MultilingualContent;
import com.tcm.international.mapper.MultilingualContentMapper;
import com.tcm.international.service.InternationalService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 国际化服务实现
 */
@Service
public class InternationalServiceImpl implements InternationalService {

    @Autowired
    private MultilingualContentMapper multilingualContentMapper;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "tcm:international:";
    private static final long CACHE_TIMEOUT = 24; // 缓存超时时间（小时）

    @Override
    @Transactional
    public boolean addContent(MultilingualContentDTO contentDTO) {
        MultilingualContent content = new MultilingualContent();
        BeanUtils.copyProperties(contentDTO, content);

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        content.setCreateTime(now);
        content.setUpdateTime(now);

        int result = multilingualContentMapper.insert(content);

        // 添加成功后刷新缓存
        if (result > 0) {
            refreshTranslationCache(contentDTO.getContentType(), contentDTO.getLocale());
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean batchAddContent(List<MultilingualContentDTO> contentDTOList) {
        if (contentDTOList == null || contentDTOList.isEmpty()) {
            return false;
        }

        // 批量转换并插入
        LocalDateTime now = LocalDateTime.now();
        List<MultilingualContent> contentList = contentDTOList.stream().map(dto -> {
            MultilingualContent content = new MultilingualContent();
            BeanUtils.copyProperties(dto, content);
            content.setCreateTime(now);
            content.setUpdateTime(now);
            return content;
        }).collect(Collectors.toList());

        // 批量插入
        for (MultilingualContent content : contentList) {
            multilingualContentMapper.insert(content);
        }

        // 刷新相关缓存
        Map<String, String> typeLocaleMap = new HashMap<>();
        contentDTOList.forEach(dto -> typeLocaleMap.put(dto.getContentType() + ":" + dto.getLocale(), ""));

        typeLocaleMap.forEach((key, value) -> {
            String[] parts = key.split(":");
            refreshTranslationCache(parts[0], parts[1]);
        });

        return true;
    }

    @Override
    @Transactional
    public boolean updateContent(MultilingualContentDTO contentDTO) {
        MultilingualContent existingContent = multilingualContentMapper.selectById(contentDTO.getId());
        if (existingContent == null) {
            return false;
        }

        // 保存旧的类型和语言代码，用于刷新缓存
        String oldContentType = existingContent.getContentType();
        String oldLocale = existingContent.getLocale();

        // 更新内容
        MultilingualContent content = new MultilingualContent();
        BeanUtils.copyProperties(contentDTO, content);
        content.setUpdateTime(LocalDateTime.now());

        int result = multilingualContentMapper.updateById(content);

        // 更新成功后刷新缓存
        if (result > 0) {
            // 如果类型或语言码变更，需要刷新两个缓存
            refreshTranslationCache(oldContentType, oldLocale);
            if (!oldContentType.equals(contentDTO.getContentType())
                    || !oldLocale.equals(contentDTO.getLocale())) {
                refreshTranslationCache(contentDTO.getContentType(), contentDTO.getLocale());
            }
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteContent(Long id) {
        MultilingualContent content = multilingualContentMapper.selectById(id);
        if (content == null) {
            return false;
        }

        int result = multilingualContentMapper.deleteById(id);

        // 删除成功后刷新缓存
        if (result > 0) {
            refreshTranslationCache(content.getContentType(), content.getLocale());
        }

        return result > 0;
    }

    @Override
    public MultilingualContent getContent(Long id) {
        return multilingualContentMapper.selectById(id);
    }

    @Override
    public IPage<MultilingualContent> pageContent(String contentType, String locale,
            String contentKey, long current, long size) {
        LambdaQueryWrapper<MultilingualContent> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.isNotEmpty(contentType)) {
            wrapper.eq(MultilingualContent::getContentType, contentType);
        }

        if (StringUtils.isNotEmpty(locale)) {
            wrapper.eq(MultilingualContent::getLocale, locale);
        }

        if (StringUtils.isNotEmpty(contentKey)) {
            wrapper.like(MultilingualContent::getContentKey, contentKey);
        }

        wrapper.orderByDesc(MultilingualContent::getUpdateTime);

        return multilingualContentMapper.selectPage(new Page<>(current, size), wrapper);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, String> getTranslations(String contentType, String locale) {
        String cacheKey = getCacheKey(contentType, locale);
        Map<String, String> cachedMap = (Map<String, String>) redisTemplate.opsForValue().get(cacheKey);

        if (cachedMap != null) {
            return cachedMap;
        }

        // 缓存未命中，从数据库加载
        List<Map<String, String>> mapList = multilingualContentMapper.getContentMapByTypeAndLocale(contentType, locale);
        Map<String, String> result = new HashMap<>();

        if (mapList != null && !mapList.isEmpty()) {
            for (Map<String, String> map : mapList) {
                String key = map.get("content_key");
                String value = map.get("content");
                if (key != null && value != null) {
                    result.put(key, value);
                }
            }
        }

        // 更新缓存
        redisTemplate.opsForValue().set(cacheKey, result, CACHE_TIMEOUT, TimeUnit.HOURS);

        return result;
    }

    @Override
    public List<MultilingualContent> getTranslationsByKey(String contentKey, String contentType) {
        return multilingualContentMapper.getTranslationsByKey(contentKey, contentType);
    }

    @Override
    public boolean refreshTranslationCache(String contentType, String locale) {
        String cacheKey = getCacheKey(contentType, locale);
        redisTemplate.delete(cacheKey);

        // 重新加载缓存
        List<Map<String, String>> mapList = multilingualContentMapper.getContentMapByTypeAndLocale(contentType, locale);
        Map<String, String> result = new HashMap<>();

        if (mapList != null && !mapList.isEmpty()) {
            for (Map<String, String> map : mapList) {
                String key = map.get("content_key");
                String value = map.get("content");
                if (key != null && value != null) {
                    result.put(key, value);
                }
            }
        }

        redisTemplate.opsForValue().set(cacheKey, result, CACHE_TIMEOUT, TimeUnit.HOURS);

        return true;
    }

    /**
     * 生成缓存键名
     */
    private String getCacheKey(String contentType, String locale) {
        return CACHE_KEY_PREFIX + contentType + ":" + locale;
    }
}
