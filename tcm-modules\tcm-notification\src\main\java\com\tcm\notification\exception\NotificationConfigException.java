package com.tcm.notification.exception;

/**
 * 通知配置异常
 * 
 * <AUTHOR>
 */
public class NotificationConfigException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    public NotificationConfigException() {
        super();
    }

    public NotificationConfigException(String message) {
        super(message);
    }

    public NotificationConfigException(String message, Throwable cause) {
        super(message, cause);
    }

    public NotificationConfigException(Throwable cause) {
        super(cause);
    }
}
