package com.tcm.doctor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.doctor.domain.DoctorSchedule;
import com.tcm.doctor.vo.DoctorScheduleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 医生排班Mapper接口
 */
@Mapper
public interface DoctorScheduleMapper extends BaseMapper<DoctorSchedule> {
    
    /**
     * 查询医生排班列表
     *
     * @param page      分页参数
     * @param doctorId  医生ID
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 医生排班列表
     */
    IPage<DoctorScheduleVO> selectDoctorScheduleList(Page<DoctorSchedule> page,
                                                   @Param("doctorId") Long doctorId,
                                                   @Param("beginDate") Date beginDate,
                                                   @Param("endDate") Date endDate);
    
    /**
     * 根据ID查询医生排班详情
     *
     * @param scheduleId 排班ID
     * @return 医生排班详情
     */
    DoctorScheduleVO selectDoctorScheduleById(@Param("scheduleId") Long scheduleId);
    
    /**
     * 根据医生ID和日期查询排班信息
     *
     * @param doctorId     医生ID
     * @param scheduleDate 排班日期
     * @return 医生排班信息
     */
    List<DoctorScheduleVO> selectDoctorScheduleByDoctorIdAndDate(@Param("doctorId") Long doctorId,
                                                               @Param("scheduleDate") Date scheduleDate);
    
    /**
     * 根据科室和日期查询排班信息
     *
     * @param department   科室
     * @param scheduleDate 排班日期
     * @return 医生排班信息
     */
    List<DoctorScheduleVO> selectDoctorScheduleByDepartmentAndDate(@Param("department") String department,
                                                                 @Param("scheduleDate") Date scheduleDate);
    
    /**
     * 更新预约人数
     *
     * @param scheduleId 排班ID
     * @param count      增加数量
     * @return 结果
     */
    int updateAppointmentCount(@Param("scheduleId") Long scheduleId, @Param("count") int count);
} 