service.vgroupMapping.tcm-prescription-group=default
service.default.grouplist=${tcm.service.seata.host}:${tcm.service.seata.port}
service.enableDegrade=false
service.disableGlobalTransaction=false

client.rm.asyncCommitBufferLimit=10000
client.rm.report.retry.count=5
client.rm.tableMetaCheckEnable=false
client.rm.tableMetaCheckerInterval=60000
client.rm.reportSuccessEnable=false
client.rm.sagaBranchRegisterEnable=false
client.rm.sagaJsonParser=fastjson
client.rm.sagaRetryPersistModeUpdate=false
client.rm.sagaCompensatePersistModeUpdate=false
client.rm.tccActionInterceptorOrder=-2147482648

client.tm.commitRetryCount=5
client.tm.rollbackRetryCount=5
client.tm.defaultGlobalTransactionTimeout=60000
client.tm.degradeCheck=false
client.tm.degradeCheckPeriod=2000
client.tm.degradeCheckAllowTimes=10

client.undo.dataValidation=true
client.undo.logSerialization=jackson
client.undo.logTable=undo_log
client.undo.onlyCareUpdateColumns=true

client.loadBalance.type=RandomLoadBalance
client.loadBalance.virtualNodes=10 