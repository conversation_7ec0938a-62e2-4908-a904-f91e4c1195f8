package com.tcm.security.config.properties;

import java.util.ArrayList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 安全配置属性
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "security")
public class SecurityProperties {

    /**
     * 免认证URL列表
     */
    private List<String> ignoreUrls = new ArrayList<>();

    /**
     * 令牌超时时间（分钟）
     */
    private int tokenExpireMinutes = 60;

    /**
     * 刷新令牌超时时间（分钟）
     */
    private int refreshTokenExpireMinutes = 1440;

    /**
     * JWT密钥
     */
    private String jwtSecret = "tcm-security-secret-key-should-be-changed-in-production";

    public List<String> getIgnoreUrls() {
        return ignoreUrls;
    }

    public void setIgnoreUrls(List<String> ignoreUrls) {
        this.ignoreUrls = ignoreUrls;
    }

    public int getTokenExpireMinutes() {
        return tokenExpireMinutes;
    }

    public void setTokenExpireMinutes(int tokenExpireMinutes) {
        this.tokenExpireMinutes = tokenExpireMinutes;
    }

    public int getRefreshTokenExpireMinutes() {
        return refreshTokenExpireMinutes;
    }

    public void setRefreshTokenExpireMinutes(int refreshTokenExpireMinutes) {
        this.refreshTokenExpireMinutes = refreshTokenExpireMinutes;
    }

    public String getJwtSecret() {
        return jwtSecret;
    }

    public void setJwtSecret(String jwtSecret) {
        this.jwtSecret = jwtSecret;
    }
}