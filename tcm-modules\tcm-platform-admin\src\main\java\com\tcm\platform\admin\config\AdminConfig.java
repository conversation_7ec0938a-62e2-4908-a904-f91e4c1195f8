package com.tcm.platform.admin.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import lombok.Data;
import java.util.List;

/**
 * 平台管理模块配置
 * 
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "admin")
public class AdminConfig {

    /**
     * 权限配置
     */
    private Auth auth = new Auth();

    /**
     * 默认密码
     */
    private String defaultPassword = "tcm123456";

    /**
     * 密码策略
     */
    private PasswordPolicy passwordPolicy = new PasswordPolicy();

    /**
     * 会话超时(分钟)
     */
    private Integer sessionTimeout = 30;

    /**
     * 权限配置类
     */
    @Data
    public static class Auth {
        /**
         * 免登录接口
         */
        private List<String> excludes;
    }

    /**
     * 密码策略
     */
    @Data
    public static class PasswordPolicy {
        /**
         * 最小长度
         */
        private Integer minLength = 8;

        /**
         * 最大长度
         */
        private Integer maxLength = 20;

        /**
         * 是否需要大写字母
         */
        private Boolean needUppercase = true;

        /**
         * 是否需要小写字母
         */
        private Boolean needLowercase = true;

        /**
         * 是否需要数字
         */
        private Boolean needNumber = true;

        /**
         * 是否需要特殊字符
         */
        private Boolean needSpecialChar = true;
    }
}