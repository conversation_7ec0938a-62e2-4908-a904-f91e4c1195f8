package com.tcm.appointment.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.appointment.domain.Appointment;
import com.tcm.appointment.dto.AppointmentDTO;
import com.tcm.appointment.dto.AppointmentQuery;
import com.tcm.appointment.vo.AppointmentStatisticsVO;
import com.tcm.appointment.vo.AppointmentVO;
import com.tcm.appointment.vo.TimeSlotVO;

import java.util.Date;
import java.util.List;

/**
 * 预约挂号服务接口
 */
public interface IAppointmentService extends IService<Appointment> {
    
    /**
     * 查询预约列表
     *
     * @param query 查询条件
     * @return 预约列表
     */
    List<AppointmentVO> listAppointments(AppointmentQuery query);
    
    /**
     * 根据ID查询预约详情
     *
     * @param appointmentId 预约ID
     * @return 预约详情
     */
    AppointmentVO getAppointmentById(Long appointmentId);
    
    /**
     * 创建预约
     *
     * @param appointmentDTO 预约信息
     * @return 预约ID
     */
    Long createAppointment(AppointmentDTO appointmentDTO);
    
    /**
     * 取消预约
     *
     * @param appointmentId 预约ID
     * @param reason        取消原因
     * @return 结果
     */
    boolean cancelAppointment(Long appointmentId, String reason);
    
    /**
     * 更新预约状态
     *
     * @param appointmentId 预约ID
     * @param status        状态
     * @return 结果
     */
    boolean updateAppointmentStatus(Long appointmentId, Integer status);
    
    /**
     * 获取可用时间段
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 可用时间段列表
     */
    List<TimeSlotVO> getAvailableTimeSlots(Long doctorId, Date date);
    
    /**
     * 患者到诊
     *
     * @param appointmentId 预约ID
     * @return 结果
     */
    boolean patientArrive(Long appointmentId);
    
    /**
     * 获取预约统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 预约统计
     */
    AppointmentStatisticsVO getAppointmentStatistics(Date startDate, Date endDate);
} 