-- ----------------------------
-- Table structure for tcm_schedule
-- ----------------------------
DROP TABLE IF EXISTS `tcm_schedule`;
CREATE TABLE `tcm_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '排班ID',
  `doctor_id` bigint(20) NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) DEFAULT NULL COMMENT '医生名称',
  `dept_id` bigint(20) NOT NULL COMMENT '科室ID',
  `dept_name` varchar(50) DEFAULT NULL COMMENT '科室名称',
  `shift_type` tinyint(4) DEFAULT '1' COMMENT '班次类型（1上午 2下午 3晚上）',
  `schedule_date` datetime NOT NULL COMMENT '排班日期',
  `max_appointment` int(11) DEFAULT '0' COMMENT '最大可预约数',
  `appointment_count` int(11) DEFAULT '0' COMMENT '已预约数',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态（0正常 1停诊）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_dept_id` (`dept_id`),
  KEY `idx_schedule_date` (`schedule_date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='排班信息表';

-- ----------------------------
-- Table structure for sys_log_operation
-- ----------------------------
DROP TABLE IF EXISTS `sys_log_operation`;
CREATE TABLE `sys_log_operation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `module` varchar(50) DEFAULT NULL COMMENT '操作模块',
  `operate_type` tinyint(4) DEFAULT '0' COMMENT '操作类型（0其它 1新增 2修改 3删除 4授权 5导出 6导入 7强退 8生成代码 9清空数据）',
  `description` varchar(255) DEFAULT NULL COMMENT '操作描述',
  `method` varchar(100) DEFAULT NULL COMMENT '请求方法',
  `request_url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方式',
  `request_params` longtext COMMENT '请求参数',
  `response_result` longtext COMMENT '返回结果',
  `execution_time` bigint(20) DEFAULT '0' COMMENT '执行时长（毫秒）',
  `ip` varchar(64) DEFAULT NULL COMMENT 'IP地址',
  `location` varchar(255) DEFAULT NULL COMMENT '操作地点',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
  `status` tinyint(4) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT NULL COMMENT '错误消息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='系统操作日志表'; 