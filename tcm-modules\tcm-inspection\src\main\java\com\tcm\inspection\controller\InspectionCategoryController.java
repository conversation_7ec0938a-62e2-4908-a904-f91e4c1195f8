package com.tcm.inspection.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.common.core.domain.R;
import com.tcm.common.core.web.controller.BaseController;
import com.tcm.common.core.web.page.PageDomain;
import com.tcm.inspection.domain.InspectionCategory;
import com.tcm.inspection.service.InspectionCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 检查分类控制器
 */
@Tag(name = "检查分类管理", description = "检查分类管理相关接口")
@RestController
@RequestMapping("/inspection/category")
public class InspectionCategoryController extends BaseController {

    @Autowired
    private InspectionCategoryService inspectionCategoryService;

    /**
     * 获取检查分类列表
     */
    @Operation(summary = "获取检查分类列表")
    @GetMapping("/list")
    public R<List<InspectionCategory>> list(InspectionCategory inspectionCategory) {
        List<InspectionCategory> list = inspectionCategoryService.selectInspectionCategoryList(inspectionCategory);
        return R.ok(list);
    }

    /**
     * 分页查询检查分类列表
     */
    @Operation(summary = "分页查询检查分类列表")
    @GetMapping("/page")
    public R<IPage<InspectionCategory>> page(InspectionCategory inspectionCategory, PageDomain pageDomain) {
        Page<InspectionCategory> page = new Page<>(pageDomain.getPageNum(), pageDomain.getPageSize());
        IPage<InspectionCategory> pageData = inspectionCategoryService.selectInspectionCategoryPage(page, inspectionCategory);
        return R.ok(pageData);
    }

    /**
     * 获取检查分类详情
     */
    @Operation(summary = "获取检查分类详情")
    @GetMapping("/{categoryId}")
    public R<InspectionCategory> getInfo(@Parameter(description = "检查分类ID") @PathVariable("categoryId") Long categoryId) {
        return R.ok(inspectionCategoryService.selectInspectionCategoryById(categoryId));
    }

    /**
     * 获取检查分类树结构
     */
    @Operation(summary = "获取检查分类树结构")
    @GetMapping("/tree")
    public R<List<InspectionCategory>> tree() {
        List<InspectionCategory> tree = inspectionCategoryService.buildCategoryTree();
        return R.ok(tree);
    }

    /**
     * 新增检查分类
     */
    @Operation(summary = "新增检查分类")
    @PostMapping
    public R<Void> add(@Valid @RequestBody InspectionCategory inspectionCategory) {
        // 校验分类名称和编码是否唯一
        if (inspectionCategoryService.checkCategoryNameUnique(inspectionCategory)) {
            return R.fail("新增检查分类失败，分类名称已存在");
        }
        if (inspectionCategoryService.checkCategoryCodeUnique(inspectionCategory)) {
            return R.fail("新增检查分类失败，分类编码已存在");
        }
        
        return toAjax(inspectionCategoryService.insertInspectionCategory(inspectionCategory));
    }

    /**
     * 修改检查分类
     */
    @Operation(summary = "修改检查分类")
    @PutMapping
    public R<Void> edit(@Valid @RequestBody InspectionCategory inspectionCategory) {
        // 校验分类名称和编码是否唯一
        if (inspectionCategoryService.checkCategoryNameUnique(inspectionCategory)) {
            return R.fail("修改检查分类失败，分类名称已存在");
        }
        if (inspectionCategoryService.checkCategoryCodeUnique(inspectionCategory)) {
            return R.fail("修改检查分类失败，分类编码已存在");
        }
        
        return toAjax(inspectionCategoryService.updateInspectionCategory(inspectionCategory));
    }

    /**
     * 删除检查分类
     */
    @Operation(summary = "删除检查分类")
    @DeleteMapping("/{categoryId}")
    public R<Void> remove(@Parameter(description = "检查分类ID") @PathVariable("categoryId") Long categoryId) {
        // 检查是否有子分类
        if (inspectionCategoryService.hasChildCategory(categoryId)) {
            return R.fail("存在子分类，不允许删除");
        }
        // 检查是否有关联的检查项目
        if (inspectionCategoryService.hasAssociatedItems(categoryId)) {
            return R.fail("该分类下存在检查项目，不允许删除");
        }
        
        return toAjax(inspectionCategoryService.deleteInspectionCategoryById(categoryId));
    }

    /**
     * 批量删除检查分类
     */
    @Operation(summary = "批量删除检查分类")
    @DeleteMapping("/batch/{categoryIds}")
    public R<Void> batchRemove(@Parameter(description = "检查分类ID数组，多个以逗号分隔") @PathVariable("categoryIds") Long[] categoryIds) {
        // 检查是否有子分类或关联的检查项目
        for (Long categoryId : categoryIds) {
            if (inspectionCategoryService.hasChildCategory(categoryId)) {
                return R.fail("存在子分类，不允许删除");
            }
            if (inspectionCategoryService.hasAssociatedItems(categoryId)) {
                return R.fail("该分类下存在检查项目，不允许删除");
            }
        }
        
        return toAjax(inspectionCategoryService.deleteInspectionCategoryByIds(categoryIds));
    }
} 