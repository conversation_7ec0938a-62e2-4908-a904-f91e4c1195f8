package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 问诊消息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_consultation_message")
public class ConsultationMessage extends BaseEntity {
    
    /**
     * 消息ID
     */
    @TableId
    private Long messageId;
    
    /**
     * 问诊ID
     */
    private Long consultationId;
    
    /**
     * 发送者ID
     */
    private Long senderId;
    
    /**
     * 发送者类型（1-医生 2-患者 3-系统）
     */
    private Integer senderType;
    
    /**
     * 接收者ID
     */
    private Long receiverId;
    
    /**
     * 消息类型（1-文本 2-图片 3-语音 4-视频 5-处方 6-系统通知）
     */
    private Integer messageType;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 媒体URL（如图片、语音、视频的地址）
     */
    private String mediaUrl;
    
    /**
     * 媒体时长（语音或视频的时长，单位：秒）
     */
    private Integer mediaDuration;
    
    /**
     * 是否已读（0-未读 1-已读）
     */
    private Integer readStatus;
    
    /**
     * 是否撤回（0-否 1-是）
     */
    private Integer recalled;
} 