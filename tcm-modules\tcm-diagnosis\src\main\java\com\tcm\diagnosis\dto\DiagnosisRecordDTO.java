package com.tcm.diagnosis.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 诊断记录数据传输对象
 */
@Data
public class DiagnosisRecordDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 诊断记录ID
     */
    private Long diagnosisId;
    
    /**
     * 患者ID
     */
    @NotNull(message = "患者ID不能为空")
    private Long patientId;
    
    /**
     * 医生ID
     */
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;
    
    /**
     * 诊断编号
     */
    private String diagnosisCode;
    
    /**
     * 诊断日期
     */
    private Date diagnosisDate;
    
    /**
     * 主诉
     */
    @Size(max = 500, message = "主诉长度不能超过500个字符")
    private String chiefComplaint;
    
    /**
     * 现病史
     */
    @Size(max = 1000, message = "现病史长度不能超过1000个字符")
    private String presentIllness;
    
    /**
     * 既往史
     */
    @Size(max = 500, message = "既往史长度不能超过500个字符")
    private String pastHistory;
    
    /**
     * 个人史
     */
    @Size(max = 500, message = "个人史长度不能超过500个字符")
    private String personalHistory;
    
    /**
     * 家族史
     */
    @Size(max = 500, message = "家族史长度不能超过500个字符")
    private String familyHistory;
    
    /**
     * 望诊结果
     */
    @Size(max = 500, message = "望诊结果长度不能超过500个字符")
    private String inspection;
    
    /**
     * 闻诊结果
     */
    @Size(max = 500, message = "闻诊结果长度不能超过500个字符")
    private String auscultation;
    
    /**
     * 问诊结果
     */
    @Size(max = 500, message = "问诊结果长度不能超过500个字符")
    private String interrogation;
    
    /**
     * 切诊结果
     */
    @Size(max = 500, message = "切诊结果长度不能超过500个字符")
    private String palpation;
    
    /**
     * 舌象
     */
    @Size(max = 200, message = "舌象长度不能超过200个字符")
    private String tongueCondition;
    
    /**
     * 脉象
     */
    @Size(max = 200, message = "脉象长度不能超过200个字符")
    private String pulseCondition;
    
    /**
     * 中医诊断
     */
    @Size(max = 500, message = "中医诊断长度不能超过500个字符")
    private String tcmDiagnosis;
    
    /**
     * 西医诊断
     */
    @Size(max = 500, message = "西医诊断长度不能超过500个字符")
    private String westernDiagnosis;
    
    /**
     * 辨证分型
     */
    @Size(max = 200, message = "辨证分型长度不能超过200个字符")
    private String syndromeType;
    
    /**
     * 治疗原则
     */
    @Size(max = 500, message = "治疗原则长度不能超过500个字符")
    private String treatmentPrinciple;
    
    /**
     * 状态（0：草稿，1：已完成，2：已取消）
     */
    private Integer status;
} 