package com.tcm.prescription.service;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 处方统计服务接口
 */
public interface IPrescriptionStatService {

    /**
     * 获取医生处方数量
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处方数量
     */
    Long getPrescriptionCount(Long doctorId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取医生处方总金额
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 处方总金额
     */
    BigDecimal getPrescriptionAmount(Long doctorId, LocalDate startDate, LocalDate endDate);
}
