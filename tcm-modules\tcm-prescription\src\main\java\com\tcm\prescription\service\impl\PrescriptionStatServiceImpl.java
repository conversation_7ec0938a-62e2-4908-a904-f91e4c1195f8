package com.tcm.prescription.service.impl;

import com.tcm.prescription.service.IPrescriptionStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 处方统计服务实现类
 */
@Service
public class PrescriptionStatServiceImpl implements IPrescriptionStatService {

    @Autowired
    private com.tcm.prescription.mapper.PrescriptionMapper prescriptionMapper;

    @Override
    public Long getPrescriptionCount(Long doctorId, LocalDate startDate, LocalDate endDate) {
        // 将LocalDate转换为Date以适配可能存在的旧代码
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        Map<String, Object> params = new HashMap<>();
        params.put("doctorId", doctorId);
        params.put("startDate", startDateTime);
        params.put("endDate", endDateTime);
        
        try {
            return prescriptionMapper.countPrescriptionsByDoctor(params);
        } catch (Exception e) {
            // 记录异常并返回默认值
            return 0L;
        }
    }

    @Override
    public BigDecimal getPrescriptionAmount(Long doctorId, LocalDate startDate, LocalDate endDate) {
        // 将LocalDate转换为Date以适配可能存在的旧代码
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        Map<String, Object> params = new HashMap<>();
        params.put("doctorId", doctorId);
        params.put("startDate", startDateTime);
        params.put("endDate", endDateTime);
        
        try {
            return prescriptionMapper.sumPrescriptionAmountByDoctor(params);
        } catch (Exception e) {
            // 记录异常并返回默认值
            return BigDecimal.ZERO;
        }
    }
}
