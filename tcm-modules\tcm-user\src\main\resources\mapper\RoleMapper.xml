<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.user.mapper.RoleMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.user.entity.Role">
        <id column="id" property="id" />
        <result column="role_name" property="roleName" />
        <result column="role_key" property="roleKey" />
        <result column="role_sort" property="roleSort" />
        <result column="data_scope" property="dataScope" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>
</mapper> 