package com.tcm.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色视图对象
 */
@Data
@Schema(description = "角色视图对象")
public class RoleVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 角色ID
     */
    @Schema(description = "角色ID")
    private Long roleId;

    /**
     * 角色名称
     */
    @Schema(description = "角色名称")
    private String roleName;

    /**
     * 角色编码
     */
    @Schema(description = "角色编码")
    private String roleCode;

    /**
     * 角色排序
     */
    @Schema(description = "角色排序")
    private Integer roleSort;

    /**
     * 数据范围（1：全部数据权限 2：自定义数据权限 3：本部门数据权限 4：本部门及以下数据权限）
     */
    @Schema(description = "数据范围")
    private Integer dataScope;

    /**
     * 数据范围描述
     */
    private String dataScopeDesc;

    /**
     * 角色状态（0正常 1停用）
     */
    @Schema(description = "状态（0正常 1停用）")
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 菜单权限列表
     */
    private Long[] menuIds;

    /**
     * 部门数据权限列表
     */
    private Long[] deptIds;
}