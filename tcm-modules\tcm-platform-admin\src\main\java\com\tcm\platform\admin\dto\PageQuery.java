package com.tcm.platform.admin.dto;

import lombok.Data;

/**
 * 分页查询参数
 *
 * <AUTHOR>
 */
@Data
public class PageQuery {

    /**
     * 页码，从1开始
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    private String orderByColumn;

    /**
     * 排序方向 asc/desc
     */
    private String orderByDirection;

    /**
     * 获取页码（MyBatis-Plus分页从0开始）
     */
    public Integer getPage() {
        return pageNum <= 0 ? 0 : pageNum - 1;
    }
}