-- ----------------------------
-- 1. 时间段表
-- ----------------------------
DROP TABLE IF EXISTS `time_slot`;
CREATE TABLE `time_slot` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `value` varchar(20) NOT NULL COMMENT '时间段值（如08:00-08:30）',
  `label` varchar(50) NOT NULL COMMENT '时间段标签（如上午 08:00-08:30）',
  `type` tinyint(1) NOT NULL COMMENT '时间段类型（1-上午 2-下午 3-晚上）',
  `order_num` int(5) NOT NULL COMMENT '排序号',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态（0-正常 1-停用）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_value` (`value`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='时间段表';

-- ----------------------------
-- 2. 医生排班表
-- ----------------------------
DROP TABLE IF EXISTS `doctor_schedule`;
CREATE TABLE `doctor_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `doctor_id` bigint(20) NOT NULL COMMENT '医生ID',
  `department_id` bigint(20) NOT NULL COMMENT '科室ID',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `time_slot_id` bigint(20) NOT NULL COMMENT '时间段ID',
  `max_appointments` int(5) DEFAULT '1' COMMENT '最大预约人数',
  `appointment_limit` int(5) DEFAULT NULL COMMENT '剩余可预约数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态（0-停诊 1-正常 2-满号）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_doctor_date_time` (`doctor_id`,`schedule_date`,`time_slot_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_schedule_date` (`schedule_date`),
  KEY `idx_department_id` (`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='医生排班表';

-- ----------------------------
-- 3. 预约挂号表
-- ----------------------------
DROP TABLE IF EXISTS `appointment`;
CREATE TABLE `appointment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `doctor_id` bigint(20) NOT NULL COMMENT '医生ID',
  `department_id` bigint(20) NOT NULL COMMENT '科室ID',
  `appointment_date` date NOT NULL COMMENT '预约日期',
  `time_slot` varchar(20) NOT NULL COMMENT '时间段（如08:00-08:30）',
  `type` tinyint(1) DEFAULT '1' COMMENT '预约类型（1-初诊 2-复诊）',
  `status` tinyint(1) DEFAULT '0' COMMENT '预约状态（0-待支付 1-已预约 2-已到诊 3-已取消 4-已爽约）',
  `symptom` varchar(500) DEFAULT NULL COMMENT '症状描述',
  `fee` decimal(10,2) DEFAULT '0.00' COMMENT '挂号费用',
  `pay_status` tinyint(1) DEFAULT '0' COMMENT '支付状态（0-未支付 1-已支付 2-已退款）',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `arrive_time` datetime DEFAULT NULL COMMENT '到诊时间',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `order_num` int(5) DEFAULT NULL COMMENT '排队序号',
  `notes` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_appointment_date` (`appointment_date`),
  KEY `idx_status` (`status`),
  KEY `idx_department_id` (`department_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='预约挂号表';

-- ----------------------------
-- 4. 黑名单表（用于记录多次爽约的患者）
-- ----------------------------
DROP TABLE IF EXISTS `appointment_blacklist`;
CREATE TABLE `appointment_blacklist` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `patient_id` bigint(20) NOT NULL COMMENT '患者ID',
  `no_show_count` int(5) DEFAULT '0' COMMENT '爽约次数',
  `blacklist_reason` varchar(200) DEFAULT NULL COMMENT '加入黑名单原因',
  `blacklist_time` datetime DEFAULT NULL COMMENT '加入黑名单时间',
  `expire_time` datetime DEFAULT NULL COMMENT '解除黑名单时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态（0-已解除 1-生效中）',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_patient_id` (`patient_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='预约黑名单表'; 