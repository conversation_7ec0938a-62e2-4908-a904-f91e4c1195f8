<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.platform.admin.mapper.SysMenuMapper">

    <!-- 菜单结果映射 -->
    <resultMap id="MenuResultMap" type="com.tcm.platform.admin.entity.SysMenu">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="menuName" column="menu_name"/>
        <result property="menuType" column="menu_type"/>
        <result property="icon" column="icon"/>
        <result property="sort" column="sort"/>
        <result property="path" column="path"/>
        <result property="component" column="component"/>
        <result property="permission" column="permission"/>
        <result property="isCache" column="is_cache"/>
        <result property="visible" column="visible"/>
        <result property="status" column="status"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <!-- 查询系统菜单列表 -->
    <select id="selectMenuList" parameterType="com.tcm.platform.admin.entity.SysMenu" resultMap="MenuResultMap">
        select id, parent_id, menu_name, menu_type, icon, sort, path, component, permission,
        is_cache, visible, status, remark, create_by, create_time, update_by, update_time, del_flag
        from sys_menu
        where del_flag = 0
        <if test="menuName != null and menuName != ''">
            AND menu_name like concat('%', #{menuName}, '%')
        </if>
        <if test="menuType != null and menuType != ''">
            AND menu_type = #{menuType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        order by parent_id, sort
    </select>

    <!-- 根据用户ID查询权限 -->
    <select id="selectMenuPermsByUserId" parameterType="Long" resultType="String">
        select distinct m.permission
        from sys_menu m
        inner join sys_role_menu rm on m.id = rm.menu_id
        inner join sys_user_role ur on rm.role_id = ur.role_id
        where ur.user_id = #{userId} and m.menu_type = 'F' and m.status = 1 and m.del_flag = 0
    </select>

    <!-- 根据用户ID查询菜单树 -->
    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="MenuResultMap">
        select distinct m.id, m.parent_id, m.menu_name, m.menu_type, m.icon, m.sort, m.path, 
        m.component, m.permission, m.is_cache, m.visible, m.status
        from sys_menu m
        inner join sys_role_menu rm on m.id = rm.menu_id
        inner join sys_user_role ur on rm.role_id = ur.role_id
        where ur.user_id = #{userId} and m.menu_type in ('M', 'C') and m.status = 1 and m.del_flag = 0
        order by m.parent_id, m.sort
    </select>

    <!-- 根据角色ID查询菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" parameterType="Long" resultType="Long">
        select m.id
        from sys_menu m
        inner join sys_role_menu rm on m.id = rm.menu_id
        where rm.role_id = #{roleId} and m.status = 1 and m.del_flag = 0
    </select>

    <!-- 查询菜单是否存在子菜单 -->
    <select id="hasChildByMenuId" parameterType="Long" resultType="Integer">
        select count(1) from sys_menu where parent_id = #{menuId} and del_flag = 0
    </select>
</mapper> 