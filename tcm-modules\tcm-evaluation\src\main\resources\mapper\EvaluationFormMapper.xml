<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.evaluation.mapper.EvaluationFormMapper">
    
    <resultMap id="evaluationFormResultMap" type="com.tcm.evaluation.domain.EvaluationForm">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="type" column="type"/>
        <result property="evaluationType" column="evaluation_type"/>
        <result property="description" column="description"/>
        <result property="formStructure" column="form_structure" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="formTemplate" column="form_template" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="scoringRules" column="scoring_rules" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result property="enabled" column="enabled"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="version" column="version"/>
    </resultMap>
    
    <sql id="selectEvaluationFormVo">
        select id, name, type, evaluation_type, description, form_structure, form_template, 
        scoring_rules, enabled, create_by, create_time, update_by, update_time, del_flag, version
        from eval_form
    </sql>
    
    <select id="selectByEvaluationType" resultMap="evaluationFormResultMap">
        <include refid="selectEvaluationFormVo"/>
        where del_flag = 0 
        and evaluation_type = #{evaluationType}
        order by id desc
    </select>
    
    <select id="selectByFormType" resultMap="evaluationFormResultMap">
        <include refid="selectEvaluationFormVo"/>
        where del_flag = 0 
        and type = #{formType}
        order by id desc
    </select>
    
    <select id="selectEnabledForms" resultMap="evaluationFormResultMap">
        <include refid="selectEvaluationFormVo"/>
        where del_flag = 0 
        and enabled = 1
        order by id desc
    </select>
    
</mapper> 