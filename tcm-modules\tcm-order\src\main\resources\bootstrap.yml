spring:
  application:
    name: tcm-order
  profiles:
    active: dev
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      # 注册中心配置
      discovery:
        enabled: true
        # 这里添加必要的配置，确保在系统属性未设置时仍能正常注册
        server-addr: ${spring.cloud.nacos.config.server-addr}
        namespace: ${spring.cloud.nacos.config.namespace:public}
        group: DEFAULT_GROUP
        username: ${spring.cloud.nacos.config.username}
        password: ${spring.cloud.nacos.config.password}
        # 确保服务可被发现
        ephemeral: true
        weight: 1
      config:
        file-extension: yml
        shared-configs:
          - data-id: tcm-common.yml
            group: DEFAULT_GROUP
            refresh: true
