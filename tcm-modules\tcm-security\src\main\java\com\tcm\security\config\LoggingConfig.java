package com.tcm.security.config;

import javax.annotation.PostConstruct;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.ConfigurableEnvironment;

import lombok.extern.slf4j.Slf4j;

/**
 * 日志配置类，用于解决日志冲突问题
 */
@Configuration
@Slf4j
public class LoggingConfig implements EnvironmentPostProcessor {

    /**
     * 在环境准备阶段初始化日志配置
     */
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // 禁用冲突的日志实现
        System.setProperty("log4j2.disable", "true");
        System.setProperty("log4j.disable", "true");
        System.setProperty("log4j2.disableJmx", "true");

        // 强制使用logback
        System.setProperty("org.springframework.boot.logging.LoggingSystem",
                "org.springframework.boot.logging.logback.LogbackLoggingSystem");
    }

    /**
     * Spring容器初始化后检查日志配置
     */
    @PostConstruct
    public void init() {
        // 检查是否存在冲突的日志实现
        try {
            Class.forName("org.apache.logging.log4j.LoggingException");
            log.warn("检测到 log4j-slf4j-impl，可能与 logback 冲突");

            // 尝试禁用
            System.setProperty("log4j2.disable", "true");
            System.setProperty("log4j.disable", "true");
        } catch (ClassNotFoundException e) {
            // 没有冲突的日志实现，很好
            log.info("未检测到日志冲突");
        }

        try {
            Class.forName("org.apache.logging.slf4j.Log4jLoggerFactory");
            log.warn("检测到 Log4jLoggerFactory，可能与 logback 冲突");
        } catch (ClassNotFoundException e) {
            // 这是我们想要的
            log.info("使用的是 Logback，没有 Log4j 实现");
        }

        // 再次确认使用 Logback
        System.setProperty("org.springframework.boot.logging.LoggingSystem",
                "org.springframework.boot.logging.logback.LogbackLoggingSystem");

        log.info("日志系统初始化完成，使用 Logback");
    }
}