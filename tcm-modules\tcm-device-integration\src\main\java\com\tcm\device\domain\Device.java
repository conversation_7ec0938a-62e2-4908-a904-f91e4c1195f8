package com.tcm.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("device")
@EqualsAndHashCode(callSuper = false)
public class Device implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 厂商
     */
    private String manufacturer;

    /**
     * 状态（0离线 1在线 2维护中）
     */
    private Integer status;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 端口号
     */
    private Integer port;

    /**
     * 通信协议
     */
    private String protocol;

    /**
     * 设备位置
     */
    private String location;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 最后在线时间
     */
    private Date lastOnlineTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private Integer deleted;
}