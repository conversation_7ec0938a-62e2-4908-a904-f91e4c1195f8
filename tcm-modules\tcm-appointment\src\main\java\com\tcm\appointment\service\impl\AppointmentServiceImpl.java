package com.tcm.appointment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.appointment.domain.Appointment;
import com.tcm.appointment.dto.AppointmentDTO;
import com.tcm.appointment.dto.AppointmentQuery;
import com.tcm.appointment.mapper.AppointmentMapper;
import com.tcm.appointment.service.IAppointmentService;
import com.tcm.appointment.vo.AppointmentStatisticsVO;
import com.tcm.appointment.vo.AppointmentVO;
import com.tcm.appointment.vo.TimeSlotVO;
import com.tcm.common.core.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约挂号服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppointmentServiceImpl extends ServiceImpl<AppointmentMapper, Appointment> implements IAppointmentService {

    private final AppointmentMapper appointmentMapper;
    
    @Value("${appointment.allow-multiple-per-time-slot:false}")
    private boolean allowMultiplePerTimeSlot;
    
    @Value("${appointment.advance-days:14}")
    private int appointmentAdvanceDays;

    /**
     * 查询预约列表
     *
     * @param query 查询条件
     * @return 预约列表
     */
    @Override
    public List<AppointmentVO> listAppointments(AppointmentQuery query) {
        LambdaQueryWrapper<Appointment> wrapper = new LambdaQueryWrapper<>();
        
        // 根据查询条件构建查询
        if (query != null) {
            // 按患者ID查询
            if (query.getPatientId() != null) {
                wrapper.eq(Appointment::getPatientId, query.getPatientId());
            }
            
            // 按医生ID查询
            if (query.getDoctorId() != null) {
                wrapper.eq(Appointment::getDoctorId, query.getDoctorId());
            }
            
            // 按预约状态查询
            if (query.getStatus() != null) {
                wrapper.eq(Appointment::getStatus, query.getStatus());
            }
            
            // 按预约日期查询
            if (query.getStartDate() != null && query.getEndDate() != null) {
                wrapper.between(Appointment::getAppointmentDate, query.getStartDate(), query.getEndDate());
            } else if (query.getStartDate() != null) {
                wrapper.ge(Appointment::getAppointmentDate, query.getStartDate());
            } else if (query.getEndDate() != null) {
                wrapper.le(Appointment::getAppointmentDate, query.getEndDate());
            }
        }
        
        // 默认按预约日期降序排序
        wrapper.orderByDesc(Appointment::getAppointmentDate);
        
        List<Appointment> list = list(wrapper);
        return list.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 根据ID查询预约详情
     *
     * @param appointmentId 预约ID
     * @return 预约详情
     */
    @Override
    public AppointmentVO getAppointmentById(Long appointmentId) {
        Appointment appointment = getById(appointmentId);
        if (appointment == null) {
            throw new BusinessException("预约不存在");
        }
        return convertToVO(appointment);
    }

    /**
     * 创建预约
     *
     * @param appointmentDTO 预约信息
     * @return 预约ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createAppointment(AppointmentDTO appointmentDTO) {
        // 检查时间段是否可用
        if (!allowMultiplePerTimeSlot) {
            checkTimeSlotAvailable(appointmentDTO.getDoctorId(), appointmentDTO.getAppointmentDate(), appointmentDTO.getTimeSlot());
        }
        
        // 转换为实体
        Appointment appointment = new Appointment();
        BeanUtils.copyProperties(appointmentDTO, appointment);
        
        // 设置默认状态
        appointment.setStatus(0); // 待支付
        appointment.setPayStatus(0); // 未支付
        appointment.setCreateTime(new Date());
        
        // 保存到数据库
        save(appointment);
        
        return appointment.getId();
    }

    /**
     * 取消预约
     *
     * @param appointmentId 预约ID
     * @param reason        取消原因
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelAppointment(Long appointmentId, String reason) {
        Appointment appointment = getById(appointmentId);
        if (appointment == null) {
            throw new BusinessException("预约不存在");
        }
        
        // 判断是否可取消
        if (appointment.getStatus() == 2 || appointment.getStatus() == 3 || appointment.getStatus() == 4) {
            throw new BusinessException("当前状态不可取消");
        }
        
        // 更新预约状态
        Appointment updateObj = new Appointment();
        updateObj.setId(appointmentId);
        updateObj.setStatus(3); // 已取消
        updateObj.setCancelReason(reason);
        updateObj.setUpdateTime(new Date());
        
        return updateById(updateObj);
    }

    /**
     * 更新预约状态
     *
     * @param appointmentId 预约ID
     * @param status        状态
     * @return 结果
     */
    @Override
    public boolean updateAppointmentStatus(Long appointmentId, Integer status) {
        Appointment appointment = getById(appointmentId);
        if (appointment == null) {
            throw new BusinessException("预约不存在");
        }
        
        Appointment updateObj = new Appointment();
        updateObj.setId(appointmentId);
        updateObj.setStatus(status);
        updateObj.setUpdateTime(new Date());
        
        return updateById(updateObj);
    }

    /**
     * 获取可用时间段
     *
     * @param doctorId 医生ID
     * @param date     日期
     * @return 可用时间段列表
     */
    @Override
    public List<TimeSlotVO> getAvailableTimeSlots(Long doctorId, Date date) {
        // 获取医生当天的工作时间
        List<TimeSlotVO> workTimeSlots = appointmentMapper.getDoctorWorkTimeSlots(doctorId, date);
        
        if (allowMultiplePerTimeSlot) {
            return workTimeSlots;
        }
        
        // 获取已预约的时间段
        List<String> bookedTimeSlots = appointmentMapper.getBookedTimeSlots(doctorId, date);
        
        // 过滤出可用时间段
        return workTimeSlots.stream()
                .filter(slot -> !bookedTimeSlots.contains(slot.getValue()))
                .collect(Collectors.toList());
    }

    /**
     * 患者到诊
     *
     * @param appointmentId 预约ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean patientArrive(Long appointmentId) {
        Appointment appointment = getById(appointmentId);
        if (appointment == null) {
            throw new BusinessException("预约不存在");
        }
        
        if (appointment.getStatus() != 1) {
            throw new BusinessException("预约状态不正确");
        }
        
        Appointment updateObj = new Appointment();
        updateObj.setId(appointmentId);
        updateObj.setStatus(2); // 已到诊
        updateObj.setArriveTime(new Date());
        updateObj.setUpdateTime(new Date());
        
        return updateById(updateObj);
    }

    /**
     * 获取预约统计
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 预约统计
     */
    @Override
    public AppointmentStatisticsVO getAppointmentStatistics(Date startDate, Date endDate) {
        AppointmentStatisticsVO statistics = appointmentMapper.getAppointmentStatistics(startDate, endDate);
        
        // 填充各维度的详细统计数据
        if (statistics != null) {
            // 获取日期维度统计
            statistics.setDailyStatistics(appointmentMapper.getDailyStatistics(startDate, endDate));
            
            // 获取科室维度统计
            statistics.setDepartmentStatistics(appointmentMapper.getDepartmentStatistics(startDate, endDate));
            
            // 获取医生维度统计
            statistics.setDoctorStatistics(appointmentMapper.getDoctorStatistics(startDate, endDate));
        }
        
        return statistics;
    }
    
    /**
     * 转换为VO对象
     */
    private AppointmentVO convertToVO(Appointment appointment) {
        if (appointment == null) {
            return null;
        }
        
        AppointmentVO vo = new AppointmentVO();
        BeanUtils.copyProperties(appointment, vo);
        
        // 设置预约ID
        vo.setAppointmentId(appointment.getId());
        
        // 设置状态描述
        if (appointment.getStatus() != null) {
            switch (appointment.getStatus()) {
                case 0:
                    vo.setStatusName("待支付");
                    break;
                case 1:
                    vo.setStatusName("已预约");
                    break;
                case 2:
                    vo.setStatusName("已到诊");
                    break;
                case 3:
                    vo.setStatusName("已取消");
                    break;
                case 4:
                    vo.setStatusName("已爽约");
                    break;
                default:
                    vo.setStatusName("未知");
            }
        }
        
        // 设置预约类型描述
        if (appointment.getType() != null) {
            vo.setTypeName(appointment.getType() == 1 ? "初诊" : "复诊");
        }
        
        // 设置支付状态描述
        if (appointment.getPayStatus() != null) {
            switch (appointment.getPayStatus()) {
                case 0:
                    vo.setPayStatusName("未支付");
                    break;
                case 1:
                    vo.setPayStatusName("已支付");
                    break;
                case 2:
                    vo.setPayStatusName("已退款");
                    break;
                default:
                    vo.setPayStatusName("未知");
            }
        }
        
        return vo;
    }
    
    /**
     * 检查时间段是否可用
     */
    private void checkTimeSlotAvailable(Long doctorId, Date date, String timeSlot) {
        int count = appointmentMapper.checkTimeSlotAvailable(doctorId, date, timeSlot);
        if (count > 0) {
            throw new BusinessException("该时间段已被预约");
        }
    }
} 