package com.tcm.notification.exception;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 通知模块全局异常处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class NotificationExceptionHandler {

    /**
     * 处理通知发送异常
     */
    @ExceptionHandler(NotificationSendException.class)
    public ResponseEntity<Map<String, Object>> handleNotificationSendException(NotificationSendException e) {
        log.error("通知发送异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        result.put("message", "通知发送失败: " + e.getMessage());
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }

    /**
     * 处理通知配置异常
     */
    @ExceptionHandler(NotificationConfigException.class)
    public ResponseEntity<Map<String, Object>> handleNotificationConfigException(NotificationConfigException e) {
        log.error("通知配置异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 400);
        result.put("message", "通知配置错误: " + e.getMessage());
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(result);
    }

    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleException(Exception e) {
        log.error("系统异常: {}", e.getMessage(), e);
        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        result.put("message", "系统内部错误");
        result.put("data", null);
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
    }
}
