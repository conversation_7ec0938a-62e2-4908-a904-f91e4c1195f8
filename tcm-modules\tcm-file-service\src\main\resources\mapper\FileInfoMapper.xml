<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.file.mapper.FileInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.file.domain.FileInfo">
        <id column="file_id" property="fileId" />
        <result column="file_name" property="fileName" />
        <result column="original_name" property="originalName" />
        <result column="extension" property="extension" />
        <result column="file_size" property="fileSize" />
        <result column="file_path" property="filePath" />
        <result column="file_url" property="fileUrl" />
        <result column="storage_type" property="storageType" />
        <result column="file_type" property="fileType" />
        <result column="mime_type" property="mimeType" />
        <result column="md5" property="md5" />
        <result column="status" property="status" />
        <result column="module" property="module" />
        <result column="business_id" property="businessId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        file_id, file_name, original_name, extension, file_size, file_path, file_url, 
        storage_type, file_type, mime_type, md5, status, module, business_id, 
        create_by, create_time, update_by, update_time, remark, del_flag
    </sql>

    <!-- 根据业务ID查询文件列表 -->
    <select id="selectByBusinessId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM file_info
        WHERE business_id = #{businessId}
        AND module = #{module}
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据MD5值查询文件信息 -->
    <select id="selectByMd5" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM file_info
        WHERE md5 = #{md5}
        AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 批量更新文件状态 -->
    <update id="updateStatusBatch">
        UPDATE file_info
        SET status = #{status},
        update_time = NOW()
        WHERE file_id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </update>

    <!-- 根据业务ID删除文件 -->
    <update id="deleteByBusinessId">
        UPDATE file_info
        SET del_flag = 1,
        update_time = NOW()
        WHERE business_id = #{businessId}
        AND module = #{module}
    </update>

</mapper> 