package com.tcm.admin.controller;

import com.tcm.admin.dto.MenuDTO;
import com.tcm.admin.service.MenuService;
import com.tcm.admin.vo.MenuVO;
import com.tcm.admin.vo.TreeSelectVO;
import com.tcm.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 菜单管理控制器
 */
@Tag(name = "菜单管理接口")
@RestController
@RequestMapping("/menu")
@RequiredArgsConstructor
public class MenuController {

    private final MenuService menuService;

    /**
     * 获取菜单列表
     */
    @Operation(summary = "获取菜单列表")
    @GetMapping("/list")
    public Result<List<MenuVO>> list(MenuDTO menuDTO) {
        List<MenuVO> menus = menuService.selectMenuList(menuDTO);
        return Result.success(menus);
    }

    /**
     * 根据菜单ID获取详细信息
     */
    @Operation(summary = "根据菜单ID获取详细信息")
    @GetMapping("/{menuId}")
    public Result<MenuVO> getInfo(@Parameter(description = "菜单ID") @PathVariable Long menuId) {
        MenuVO menu = menuService.getMenuById(menuId);
        return Result.success(menu);
    }

    /**
     * 获取菜单下拉树列表
     */
    @Operation(summary = "获取菜单下拉树列表")
    @GetMapping("/treeselect")
    public Result<List<TreeSelectVO>> treeselect(MenuDTO menuDTO) {
        List<MenuVO> menus = menuService.selectMenuList(menuDTO);
        return Result.success(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @Operation(summary = "加载对应角色菜单列表树")
    @GetMapping("/roleMenuTreeselect/{roleId}")
    public Result<Object> roleMenuTreeselect(@Parameter(description = "角色ID") @PathVariable Long roleId) {
        List<MenuVO> menus = menuService.selectMenuList(new MenuDTO());
        return Result.success(List.of(
                "checkedKeys", menuService.selectMenuListByRoleId(roleId),
                "menus", menuService.buildMenuTreeSelect(menus)));
    }

    /**
     * 新增菜单
     */
    @Operation(summary = "新增菜单")
    @PostMapping
    public Result<Boolean> add(@Validated @RequestBody MenuDTO menuDTO) {
        if (!menuService.checkMenuNameUnique(menuDTO)) {
            return Result.error("新增菜单'" + menuDTO.getMenuName() + "'失败，菜单名称已存在");
        }

        return Result.success(menuService.createMenu(menuDTO));
    }

    /**
     * 修改菜单
     */
    @Operation(summary = "修改菜单")
    @PutMapping
    public Result<Boolean> edit(@Validated @RequestBody MenuDTO menuDTO) {
        if (!menuService.checkMenuNameUnique(menuDTO)) {
            return Result.error("修改菜单'" + menuDTO.getMenuName() + "'失败，菜单名称已存在");
        }

        if (menuDTO.getParentId() != null && menuDTO.getParentId().equals(menuDTO.getId())) {
            return Result.error("修改菜单'" + menuDTO.getMenuName() + "'失败，上级菜单不能选择自己");
        }

        return Result.success(menuService.updateMenu(menuDTO));
    }

    /**
     * 删除菜单
     */
    @Operation(summary = "删除菜单")
    @DeleteMapping("/{menuId}")
    public Result<Boolean> remove(@Parameter(description = "菜单ID") @PathVariable Long menuId) {
        return Result.success(menuService.deleteMenu(menuId));
    }

    /**
     * 获取路由信息
     */
    @Operation(summary = "获取路由信息")
    @GetMapping("/getRouters")
    public Result<List<MenuVO>> getRouters() {
        // 获取当前用户ID，这里为演示
        Long userId = 1L;
        List<MenuVO> menus = menuService.selectMenuListByUserId(userId);
        return Result.success(menuService.buildMenuTree(menus));
    }

    /**
     * 获取菜单权限
     */
    @Operation(summary = "获取菜单权限")
    @GetMapping("/getPermissions")
    public Result<Set<String>> getPermissions() {
        // 获取当前用户ID，这里为演示
        Long userId = 1L;
        Set<String> perms = menuService.selectMenuPermsByUserId(userId);
        return Result.success(perms);
    }
}