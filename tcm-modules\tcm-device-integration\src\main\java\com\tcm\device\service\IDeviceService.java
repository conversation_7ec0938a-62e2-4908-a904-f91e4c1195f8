package com.tcm.device.service;

import com.tcm.device.domain.Device;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.device.dto.DeviceDTO;
import com.tcm.device.vo.DeviceVO;
import java.util.List;

/**
 * 设备服务接口
 * 
 * <AUTHOR>
 */
public interface IDeviceService extends IService<Device> {

    /**
     * 分页查询设备列表
     * 
     * @param page   分页参数
     * @param device 查询条件
     * @return 设备分页列表
     */
    IPage<DeviceVO> selectDevicePage(IPage<Device> page, DeviceDTO device);

    /**
     * 获取设备详情
     * 
     * @param id 设备ID
     * @return 设备详情
     */
    DeviceVO getDeviceDetail(Long id);

    /**
     * 添加设备
     * 
     * @param device 设备信息
     * @return 结果
     */
    int addDevice(DeviceDTO device);

    /**
     * 修改设备
     * 
     * @param device 设备信息
     * @return 结果
     */
    int updateDevice(DeviceDTO device);

    /**
     * 删除设备
     * 
     * @param ids 设备ID数组
     * @return 结果
     */
    int deleteDeviceByIds(Long[] ids);

    /**
     * 启用设备连接
     * 
     * @param id 设备ID
     * @return 结果
     */
    boolean connectDevice(Long id);

    /**
     * 断开设备连接
     * 
     * @param id 设备ID
     * @return 结果
     */
    boolean disconnectDevice(Long id);

    /**
     * 获取所有在线设备
     * 
     * @return 在线设备列表
     */
    List<DeviceVO> getOnlineDevices();

    /**
     * 根据类型获取设备列表
     * 
     * @param deviceType 设备类型
     * @return 设备列表
     */
    List<DeviceVO> getDevicesByType(String deviceType);
}