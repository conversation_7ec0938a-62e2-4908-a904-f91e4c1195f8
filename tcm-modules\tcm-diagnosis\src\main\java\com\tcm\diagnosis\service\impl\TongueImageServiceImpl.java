package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.ServiceException;
import com.tcm.common.core.utils.DateUtils;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.common.core.utils.file.FileTypeUtils;
import com.tcm.common.core.utils.file.ImageUtils;
import com.tcm.common.core.utils.uuid.IdUtils;
import com.tcm.diagnosis.domain.TongueImage;
import com.tcm.diagnosis.dto.TongueImageCaptureDTO;
import com.tcm.diagnosis.mapper.TongueImageMapper;
import com.tcm.diagnosis.service.ITongueImageService;
import com.tcm.diagnosis.vo.TongueImageVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 舌象图像服务实现类
 */
@Service
public class TongueImageServiceImpl extends ServiceImpl<TongueImageMapper, TongueImage> implements ITongueImageService {

    @Value("${tcm.upload.path:/tcm/upload}")
    private String uploadPath;

    @Value("${tcm.upload.domain:http://localhost:8080}")
    private String uploadDomain;

    @Autowired
    private TongueImageMapper tongueImageMapper;

    /**
     * 上传并保存舌象图像
     *
     * @param tongueImageDTO 舌象图像数据
     * @return 图像ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long uploadTongueImage(TongueImageCaptureDTO tongueImageDTO) {
        // 解析Base64图像数据
        String base64Image = tongueImageDTO.getImageData();
        if (StringUtils.isEmpty(base64Image)) {
            throw new ServiceException("图像数据不能为空");
        }

        // 去除Base64前缀
        String imageData = base64Image;
        if (base64Image.contains(",")) {
            imageData = base64Image.substring(base64Image.indexOf(",") + 1);
        }

        try {
            // 解码Base64数据
            byte[] imageBytes = Base64.getDecoder().decode(imageData);

            // 获取图像类型
            String imageFormat = FileTypeUtils.getImageFileType(new ByteArrayInputStream(imageBytes));
            if (StringUtils.isEmpty(imageFormat)) {
                imageFormat = "jpg";
            }

            // 生成图像路径和文件名
            String filename = IdUtils.fastUUID() + "." + imageFormat;
            String relativePath = "tongue/" + DateUtils.datePath() + "/" + filename;
            String fullPath = uploadPath + "/" + relativePath;

            // 保存原始图像
            ImageUtils.saveImage(imageBytes, fullPath);

            // 生成缩略图
            byte[] thumbnailBytes = ImageUtils.createThumbnail(imageBytes, 200, 150);
            String thumbnailFilename = "thumb_" + filename;
            String thumbnailRelativePath = "tongue/" + DateUtils.datePath() + "/" + thumbnailFilename;
            String thumbnailFullPath = uploadPath + "/" + thumbnailRelativePath;
            ImageUtils.saveImage(thumbnailBytes, thumbnailFullPath);

            // 创建舌象图像记录
            TongueImage tongueImage = new TongueImage();
            tongueImage.setPatientId(tongueImageDTO.getPatientId());
            tongueImage.setDoctorId(tongueImageDTO.getDoctorId());
            tongueImage.setDiagnosisId(tongueImageDTO.getDiagnosisId());
            tongueImage.setImagePath(relativePath);
            tongueImage.setThumbnailPath(thumbnailRelativePath);
            tongueImage.setCaptureTime(DateUtils.getNowDate());
            tongueImage.setImageSize((long) imageBytes.length / 1024); // 以KB为单位
            tongueImage.setImageFormat(imageFormat);
            Map<String, Integer> resolution = ImageUtils.getImageResolution(imageBytes);
            String resolutionStr = resolution.get("width") + "x" + resolution.get("height");
            tongueImage.setResolution(resolutionStr);
            tongueImage.setDeviceInfo(tongueImageDTO.getDeviceInfo());
            tongueImage.setStatus(0); // 未分析
            tongueImage.setCreateBy(String.valueOf(tongueImageDTO.getDoctorId()));

            // 保存到数据库
            save(tongueImage);

            return tongueImage.getImageId();
        } catch (IOException e) {
            throw new ServiceException("图像处理失败: " + e.getMessage());
        }
    }

    /**
     * 获取舌象图像详情
     *
     * @param imageId 图像ID
     * @return 舌象图像视图对象
     */
    @Override
    public TongueImageVO getTongueImage(Long imageId) {
        TongueImage tongueImage = getById(imageId);
        if (tongueImage == null) {
            throw new ServiceException("图像不存在");
        }
        return convertToVO(tongueImage);
    }

    /**
     * 获取诊断记录关联的舌象图像列表
     *
     * @param diagnosisId 诊断记录ID
     * @return 舌象图像视图对象列表
     */
    @Override
    public List<TongueImageVO> getTongueImageListByDiagnosisId(Long diagnosisId) {
        List<TongueImage> imageList = tongueImageMapper.selectTongueImageByDiagnosisId(diagnosisId);
        return convertToVOList(imageList);
    }

    /**
     * 获取患者的舌象图像列表
     *
     * @param patientId 患者ID
     * @return 舌象图像视图对象列表
     */
    @Override
    public List<TongueImageVO> getTongueImageListByPatientId(Long patientId) {
        List<TongueImage> imageList = tongueImageMapper.selectTongueImageByPatientId(patientId);
        return convertToVOList(imageList);
    }

    /**
     * 删除舌象图像
     *
     * @param imageId 图像ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTongueImage(Long imageId) {
        TongueImage tongueImage = getById(imageId);
        if (tongueImage == null) {
            throw new ServiceException("图像不存在");
        }

        // 物理删除图像文件
        // 删除图像文件
        String imagePath = uploadPath + "/" + tongueImage.getImagePath();
        String thumbnailPath = uploadPath + "/" + tongueImage.getThumbnailPath();
        boolean deleteResult1 = ImageUtils.deleteImage(imagePath);
        boolean deleteResult2 = ImageUtils.deleteImage(thumbnailPath);
        
        if (!deleteResult1 || !deleteResult2) {
            log.warn("删除图像文件可能未完全成功，但不影响数据库记录删除");
        }

        // 删除数据库记录
        return removeById(imageId);
    }

    /**
     * 获取最近的舌象图像列表
     *
     * @param patientId 患者ID
     * @param limit     限制数量
     * @return 舌象图像视图对象列表
     */
    @Override
    public List<TongueImageVO> getRecentTongueImages(Long patientId, Integer limit) {
        LambdaQueryWrapper<TongueImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TongueImage::getPatientId, patientId)
                .orderByDesc(TongueImage::getCaptureTime)
                .last("limit " + limit);
        List<TongueImage> imageList = list(queryWrapper);
        return convertToVOList(imageList);
    }

    /**
     * 将实体转换为视图对象
     *
     * @param tongueImage 舌象图像实体
     * @return 舌象图像视图对象
     */
    private TongueImageVO convertToVO(TongueImage tongueImage) {
        if (tongueImage == null) {
            return null;
        }

        TongueImageVO vo = new TongueImageVO();
        vo.setId(tongueImage.getImageId());
        vo.setPatientId(tongueImage.getPatientId());
        vo.setDoctorId(tongueImage.getDoctorId());
        vo.setDiagnosisId(tongueImage.getDiagnosisId());
        vo.setImageUrl(uploadDomain + "/" + tongueImage.getImagePath());
        vo.setThumbnail(uploadDomain + "/" + tongueImage.getThumbnailPath());
        vo.setCaptureTime(tongueImage.getCaptureTime());
        vo.setImageSize(tongueImage.getImageSize());
        vo.setImageFormat(tongueImage.getImageFormat());
        vo.setResolution(tongueImage.getResolution());
        vo.setDeviceInfo(tongueImage.getDeviceInfo());
        vo.setStatus(tongueImage.getStatus());

        // TODO: 通过远程调用获取患者和医生姓名
        // vo.setPatientName(remotePatientService.getPatientNameById(tongueImage.getPatientId()));
        // vo.setDoctorName(remoteDoctorService.getDoctorNameById(tongueImage.getDoctorId()));

        return vo;
    }

    /**
     * 将实体列表转换为视图对象列表
     *
     * @param tongueImageList 舌象图像实体列表
     * @return 舌象图像视图对象列表
     */
    private List<TongueImageVO> convertToVOList(List<TongueImage> tongueImageList) {
        if (tongueImageList == null || tongueImageList.isEmpty()) {
            return new ArrayList<>();
        }

        return tongueImageList.stream()
                .filter(Objects::nonNull)
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }
}