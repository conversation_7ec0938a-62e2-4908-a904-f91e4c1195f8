package com.tcm.workflow.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.flowable.engine.RepositoryService;
import org.flowable.engine.repository.Deployment;
import org.flowable.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.workflow.mapper.ProcessDefinitionMapper;
import com.tcm.workflow.service.IProcessDefinitionService;

/**
 * 流程定义 服务层实现
 * 
 * <AUTHOR>
 */
@Service
public class ProcessDefinitionServiceImpl
        extends ServiceImpl<ProcessDefinitionMapper, com.tcm.workflow.entity.ProcessDefinition>
        implements IProcessDefinitionService {

    @Autowired
    private ProcessDefinitionMapper processDefinitionMapper;

    @Autowired
    private RepositoryService repositoryService;

    /**
     * 查询流程定义列表
     * 
     * @param processDefinition 流程定义信息
     * @return 流程定义集合
     */
    @Override
    public List<com.tcm.workflow.entity.ProcessDefinition> selectProcessDefinitionList(
            com.tcm.workflow.entity.ProcessDefinition processDefinition) {
        return processDefinitionMapper.selectList(new LambdaQueryWrapper<com.tcm.workflow.entity.ProcessDefinition>()
                .like(StringUtils.isNotEmpty(processDefinition.getProcessDefinitionName()),
                        com.tcm.workflow.entity.ProcessDefinition::getProcessDefinitionName,
                        processDefinition.getProcessDefinitionName())
                .eq(StringUtils.isNotEmpty(processDefinition.getProcessDefinitionKey()),
                        com.tcm.workflow.entity.ProcessDefinition::getProcessDefinitionKey,
                        processDefinition.getProcessDefinitionKey())
                .eq(processDefinition.getStatus() != null, com.tcm.workflow.entity.ProcessDefinition::getStatus,
                        processDefinition.getStatus())
                .eq(StringUtils.isNotEmpty(processDefinition.getCategory()),
                        com.tcm.workflow.entity.ProcessDefinition::getCategory, processDefinition.getCategory())
                .orderByDesc(com.tcm.workflow.entity.ProcessDefinition::getCreateTime));
    }

    /**
     * 查询流程定义详细信息
     * 
     * @param processDefinitionId 流程定义ID
     * @return 流程定义信息
     */
    @Override
    public com.tcm.workflow.entity.ProcessDefinition selectProcessDefinitionById(String processDefinitionId) {
        return processDefinitionMapper.selectById(processDefinitionId);
    }

    /**
     * 部署流程定义
     * 
     * @param processDefinition 流程定义信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deployProcessDefinition(com.tcm.workflow.entity.ProcessDefinition processDefinition) {
        // 部署流程定义
        Deployment deployment = repositoryService.createDeployment().name(processDefinition.getProcessDefinitionName())
                .key(processDefinition.getProcessDefinitionKey()).category(processDefinition.getCategory())
                .tenantId(processDefinition.getTenantId())
                .addString(processDefinition.getResourceName(), processDefinition.getProcessXml()).deploy();

        // 获取部署后的流程定义
        ProcessDefinition deployedDefinition = repositoryService.createProcessDefinitionQuery()
                .deploymentId(deployment.getId()).singleResult();

        // 填充流程定义信息
        processDefinition.setProcessDefinitionId(deployedDefinition.getId());
        processDefinition.setDeploymentId(deployment.getId());
        processDefinition.setVersion(deployedDefinition.getVersion());
        processDefinition.setDeployTime(new Date());
        processDefinition.setStatus(1); // 默认激活状态

        // 保存流程定义信息
        processDefinitionMapper.insert(processDefinition);

        return deployment.getId();
    }

    /**
     * 修改流程定义
     * 
     * @param processDefinition 流程定义信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProcessDefinition(com.tcm.workflow.entity.ProcessDefinition processDefinition) {
        return processDefinitionMapper.updateById(processDefinition);
    }

    /**
     * 批量删除流程定义
     * 
     * @param processDefinitionIds 需要删除的流程定义ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProcessDefinitionByIds(String[] processDefinitionIds) {
        // 获取流程定义列表
        List<com.tcm.workflow.entity.ProcessDefinition> processDefinitions = processDefinitionMapper
                .selectBatchIds(Arrays.asList(processDefinitionIds));

        // 删除部署和流程定义
        for (com.tcm.workflow.entity.ProcessDefinition processDefinition : processDefinitions) {
            // 删除流程部署（级联删除流程定义、流程实例等）
            repositoryService.deleteDeployment(processDefinition.getDeploymentId(), true);
        }

        // 删除自定义表中的流程定义记录
        return processDefinitionMapper.deleteBatchIds(Arrays.asList(processDefinitionIds));
    }

    /**
     * 删除流程定义信息
     * 
     * @param processDefinitionId 流程定义ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteProcessDefinitionById(String processDefinitionId) {
        // 获取流程定义
        com.tcm.workflow.entity.ProcessDefinition processDefinition = processDefinitionMapper
                .selectById(processDefinitionId);

        // 删除流程部署（级联删除流程定义、流程实例等）
        repositoryService.deleteDeployment(processDefinition.getDeploymentId(), true);

        // 删除自定义表中的流程定义记录
        return processDefinitionMapper.deleteById(processDefinitionId);
    }

    /**
     * 激活或挂起流程定义
     * 
     * @param processDefinitionId 流程定义ID
     * @param status              流程状态：1激活，2挂起
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProcessDefinitionStatus(String processDefinitionId, Integer status) {
        // 获取流程定义
        com.tcm.workflow.entity.ProcessDefinition processDefinition = processDefinitionMapper
                .selectById(processDefinitionId);

        // 更新流程定义状态
        if (status == 1) {
            // 激活流程定义
            repositoryService.activateProcessDefinitionById(processDefinition.getProcessDefinitionId(), true, null);
        } else if (status == 2) {
            // 挂起流程定义
            repositoryService.suspendProcessDefinitionById(processDefinition.getProcessDefinitionId(), true, null);
        }

        // 更新自定义表中的流程定义状态
        processDefinition.setStatus(status);
        return processDefinitionMapper.updateById(processDefinition);
    }
}