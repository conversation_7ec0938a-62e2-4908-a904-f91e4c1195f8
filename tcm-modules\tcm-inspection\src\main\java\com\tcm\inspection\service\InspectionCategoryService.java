package com.tcm.inspection.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.inspection.domain.InspectionCategory;

import java.util.List;

/**
 * 检查分类服务接口
 */
public interface InspectionCategoryService extends IService<InspectionCategory> {

    /**
     * 查询检查分类列表
     *
     * @param inspectionCategory 检查分类信息
     * @return 检查分类集合
     */
    List<InspectionCategory> selectInspectionCategoryList(InspectionCategory inspectionCategory);

    /**
     * 分页查询检查分类列表
     *
     * @param page               分页信息
     * @param inspectionCategory 检查分类信息
     * @return 检查分类分页信息
     */
    IPage<InspectionCategory> selectInspectionCategoryPage(Page<InspectionCategory> page, InspectionCategory inspectionCategory);

    /**
     * 根据ID查询检查分类
     *
     * @param categoryId 检查分类ID
     * @return 检查分类信息
     */
    InspectionCategory selectInspectionCategoryById(Long categoryId);

    /**
     * 构建检查分类树结构
     *
     * @return 检查分类树列表
     */
    List<InspectionCategory> buildCategoryTree();

    /**
     * 新增检查分类
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    boolean insertInspectionCategory(InspectionCategory inspectionCategory);

    /**
     * 修改检查分类
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    boolean updateInspectionCategory(InspectionCategory inspectionCategory);

    /**
     * 删除检查分类
     *
     * @param categoryId 检查分类ID
     * @return 结果
     */
    boolean deleteInspectionCategoryById(Long categoryId);

    /**
     * 批量删除检查分类
     *
     * @param categoryIds 需要删除的检查分类ID数组
     * @return 结果
     */
    boolean deleteInspectionCategoryByIds(Long[] categoryIds);

    /**
     * 检查分类名称是否存在
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    boolean checkCategoryNameUnique(InspectionCategory inspectionCategory);

    /**
     * 检查分类编码是否存在
     *
     * @param inspectionCategory 检查分类信息
     * @return 结果
     */
    boolean checkCategoryCodeUnique(InspectionCategory inspectionCategory);

    /**
     * 检查是否有子分类
     *
     * @param categoryId 检查分类ID
     * @return 结果
     */
    boolean hasChildCategory(Long categoryId);

    /**
     * 检查是否有关联的检查项目
     *
     * @param categoryId 检查分类ID
     * @return 结果
     */
    boolean hasAssociatedItems(Long categoryId);
} 