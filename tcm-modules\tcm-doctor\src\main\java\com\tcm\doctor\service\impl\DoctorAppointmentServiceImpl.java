package com.tcm.doctor.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.utils.SecurityUtils;
import com.tcm.doctor.domain.DoctorAppointment;
import com.tcm.doctor.domain.DoctorSchedule;
import com.tcm.doctor.dto.AppointmentQuery;
import com.tcm.doctor.dto.DoctorAppointmentDTO;
import com.tcm.doctor.mapper.DoctorAppointmentMapper;
import com.tcm.doctor.service.IDoctorAppointmentService;
import com.tcm.doctor.service.IDoctorScheduleService;
import com.tcm.doctor.vo.DoctorAppointmentVO;
import com.tcm.doctor.vo.DoctorScheduleVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 医生预约服务实现类
 */
@Service
public class DoctorAppointmentServiceImpl extends ServiceImpl<DoctorAppointmentMapper, DoctorAppointment> implements IDoctorAppointmentService {
    
    @Autowired
    private IDoctorScheduleService doctorScheduleService;
    
    @Override
    public IPage<DoctorAppointmentVO> listAppointments(AppointmentQuery query) {
        Page<DoctorAppointment> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectAppointmentList(page, query);
    }
    
    @Override
    public DoctorAppointmentVO getAppointmentById(Long appointmentId) {
        return baseMapper.selectAppointmentById(appointmentId);
    }
    
    @Override
    public List<DoctorAppointmentVO> getAppointmentsByPatientId(Long patientId) {
        return baseMapper.selectAppointmentByPatientId(patientId);
    }
    
    @Override
    public List<DoctorAppointmentVO> getAppointmentsByDoctorIdAndDate(Long doctorId, Date date) {
        return baseMapper.selectAppointmentByDoctorIdAndDate(doctorId, date);
    }
    
    @Override
    @Transactional
    public boolean addAppointment(DoctorAppointmentDTO appointmentDTO) {
        // 检查排班是否有剩余名额
        if (!checkAppointmentAvailable(appointmentDTO.getScheduleId())) {
            throw new RuntimeException("该时段预约已满，请选择其他时段");
        }
        
        // 获取排班信息
        DoctorScheduleVO scheduleVO = doctorScheduleService.getDoctorScheduleById(appointmentDTO.getScheduleId());
        if (scheduleVO == null) {
            throw new RuntimeException("排班信息不存在");
        }
        
        // 检查排班状态
        if (scheduleVO.getStatus() != 0) {
            throw new RuntimeException("该时段已停诊，请选择其他时段");
        }
        
        // 获取下一个预约号序
        Integer sequenceNumber = baseMapper.selectNextSequenceNumber(appointmentDTO.getScheduleId());
        if (sequenceNumber == null) {
            sequenceNumber = 1;
        }
        
        // 创建预约记录
        DoctorAppointment appointment = new DoctorAppointment();
        BeanUtils.copyProperties(appointmentDTO, appointment);
        appointment.setSequenceNumber(sequenceNumber);
        appointment.setStatus(0); // 待就诊状态
        
        // 保存预约
        boolean result = save(appointment);
        
        if (result) {
            // 更新排班的已预约人数
            doctorScheduleService.updateAppointmentCount(appointmentDTO.getScheduleId(), 1);
        }
        
        return result;
    }
    
    @Override
    public boolean updateAppointmentStatus(Long appointmentId, Integer status) {
        return baseMapper.updateAppointmentStatus(appointmentId, status, SecurityUtils.getUsername()) > 0;
    }
    
    @Override
    @Transactional
    public boolean cancelAppointment(Long appointmentId, String cancelReason) {
        // 获取预约信息
        DoctorAppointment appointment = getById(appointmentId);
        if (appointment == null) {
            throw new RuntimeException("预约信息不存在");
        }
        
        // 检查是否可以取消
        if (appointment.getStatus() != 0) {
            throw new RuntimeException("当前状态不可取消");
        }
        
        // 取消预约
        boolean result = baseMapper.cancelAppointment(
                appointmentId,
                cancelReason,
                new Date(),
                SecurityUtils.getUsername()) > 0;
        
        if (result) {
            // 更新排班的已预约人数
            doctorScheduleService.updateAppointmentCount(appointment.getScheduleId(), -1);
        }
        
        return result;
    }
    
    @Override
    public boolean updateVisitInfo(Long appointmentId, Long diagnosisId) {
        return baseMapper.updateVisitInfo(
                appointmentId,
                diagnosisId,
                new Date(),
                SecurityUtils.getUsername()) > 0;
    }
    
    @Override
    public boolean checkAppointmentAvailable(Long scheduleId) {
        // 获取排班信息
        DoctorScheduleVO scheduleVO = doctorScheduleService.getDoctorScheduleById(scheduleId);
        if (scheduleVO == null) {
            return false;
        }
        
        // 检查排班状态
        if (scheduleVO.getStatus() != 0) {
            return false;
        }
        
        // 检查是否还有余位
        return scheduleVO.getAppointmentCount() < scheduleVO.getLimitCount();
    }
} 