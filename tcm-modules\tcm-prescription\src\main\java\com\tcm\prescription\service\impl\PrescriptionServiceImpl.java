package com.tcm.prescription.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.exception.BusinessException;
import com.tcm.prescription.domain.Prescription;
import com.tcm.prescription.domain.PrescriptionMedicine;
import com.tcm.prescription.dto.PrescriptionDTO;
import com.tcm.prescription.dto.PrescriptionMedicineDTO;
import com.tcm.prescription.mapper.PrescriptionMapper;
import com.tcm.prescription.service.PrescriptionMedicineService;
import com.tcm.prescription.service.PrescriptionService;
import com.tcm.prescription.vo.PrescriptionVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 处方服务实现类
 */
@Service
public class PrescriptionServiceImpl extends ServiceImpl<PrescriptionMapper, Prescription> implements PrescriptionService {

    @Autowired
    private PrescriptionMedicineService prescriptionMedicineService;

    @Override
    public IPage<PrescriptionVO> selectPrescriptionPage(int page, int size, Prescription prescription) {
        Page<Prescription> pageParam = new Page<>(page, size);
        LambdaQueryWrapper<Prescription> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (prescription != null) {
            if (StringUtils.hasText(prescription.getPrescriptionCode())) {
                queryWrapper.like(Prescription::getPrescriptionCode, prescription.getPrescriptionCode());
            }
            if (prescription.getPatientId() != null) {
                queryWrapper.eq(Prescription::getPatientId, prescription.getPatientId());
            }
            if (prescription.getDoctorId() != null) {
                queryWrapper.eq(Prescription::getDoctorId, prescription.getDoctorId());
            }
            if (prescription.getType() != null) {
                queryWrapper.eq(Prescription::getType, prescription.getType());
            }
            if (prescription.getStatus() != null) {
                queryWrapper.eq(Prescription::getStatus, prescription.getStatus());
            }
            if (StringUtils.hasText(prescription.getName())) {
                queryWrapper.like(Prescription::getName, prescription.getName());
            }
        }
        
        // 排序
        queryWrapper.orderByDesc(Prescription::getCreateTime);
        
        // 执行查询
        IPage<Prescription> result = baseMapper.selectPage(pageParam, queryWrapper);
        
        // 转换为VO对象
        IPage<PrescriptionVO> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        List<PrescriptionVO> voList = result.getRecords().stream().map(p -> {
            PrescriptionVO vo = new PrescriptionVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
        
        voPage.setRecords(voList);
        return voPage;
    }

    @Override
    public PrescriptionVO getPrescriptionById(Long prescriptionId) {
        Prescription prescription = getById(prescriptionId);
        if (prescription == null) {
            throw new BusinessException("处方不存在");
        }
        
        PrescriptionVO vo = new PrescriptionVO();
        BeanUtils.copyProperties(prescription, vo);
        
        // 获取处方药品列表
        List<PrescriptionMedicine> medicines = prescriptionMedicineService.getByPrescriptionId(prescriptionId);
        vo.setMedicines(medicines);
        
        return vo;
    }

    @Override
    @Transactional
    public Long createPrescription(PrescriptionDTO prescriptionDTO) {
        // 创建处方对象
        Prescription prescription = new Prescription();
        BeanUtils.copyProperties(prescriptionDTO, prescription);
        
        // 生成处方编号
        prescription.setPrescriptionCode("TCM" + UUID.randomUUID().toString().replace("-", "").substring(0, 16).toUpperCase());
        prescription.setPrescriptionDate(new Date());
        prescription.setStatus(0); // 设置状态为未审核
        
        // 保存处方
        boolean saved = save(prescription);
        if (!saved) {
            throw new BusinessException("保存处方失败");
        }
        
        // 保存处方药品
        if (prescriptionDTO.getMedicines() != null && !prescriptionDTO.getMedicines().isEmpty()) {
            prescriptionMedicineService.saveBatch(prescription.getPrescriptionId(), prescriptionDTO.getMedicines());
        }
        
        return prescription.getPrescriptionId();
    }

    @Override
    @Transactional
    public boolean updatePrescription(PrescriptionDTO prescriptionDTO) {
        // 检查处方是否存在
        Prescription prescription = getById(prescriptionDTO.getPrescriptionId());
        if (prescription == null) {
            throw new BusinessException("处方不存在");
        }
        
        // 只有未审核的处方才能修改
        if (prescription.getStatus() != 0) {
            throw new BusinessException("只能修改未审核的处方");
        }
        
        // 更新处方信息
        BeanUtils.copyProperties(prescriptionDTO, prescription);
        boolean updated = updateById(prescription);
        
        // 更新处方药品
        if (updated && prescriptionDTO.getMedicines() != null) {
            prescriptionMedicineService.updateBatch(prescription.getPrescriptionId(), prescriptionDTO.getMedicines());
        }
        
        return updated;
    }

    @Override
    @Transactional
    public boolean deletePrescription(Long prescriptionId) {
        // 检查处方是否存在
        Prescription prescription = getById(prescriptionId);
        if (prescription == null) {
            throw new BusinessException("处方不存在");
        }
        
        // 只有未审核或已取消的处方才能删除
        if (prescription.getStatus() != 0 && prescription.getStatus() != 3) {
            throw new BusinessException("只能删除未审核或已取消的处方");
        }
        
        // 删除处方药品
        prescriptionMedicineService.deleteByPrescriptionId(prescriptionId);
        
        // 删除处方
        return removeById(prescriptionId);
    }

    @Override
    public boolean auditPrescription(Long prescriptionId, Integer status, String remark) {
        // 检查处方是否存在
        Prescription prescription = getById(prescriptionId);
        if (prescription == null) {
            throw new BusinessException("处方不存在");
        }
        
        // 只有未审核的处方才能审核
        if (prescription.getStatus() != 0) {
            throw new BusinessException("只能审核未审核的处方");
        }
        
        // 更新处方状态
        prescription.setStatus(status);
        prescription.setRemark(remark);
        prescription.setUpdateTime(new Date());
        
        return updateById(prescription);
    }

    @Override
    public List<PrescriptionVO> getPrescriptionsByPatientId(Long patientId) {
        LambdaQueryWrapper<Prescription> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Prescription::getPatientId, patientId);
        queryWrapper.orderByDesc(Prescription::getCreateTime);
        
        List<Prescription> prescriptions = list(queryWrapper);
        
        return prescriptions.stream().map(p -> {
            PrescriptionVO vo = new PrescriptionVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PrescriptionVO> getPrescriptionsByDiagnosisId(Long diagnosisId) {
        LambdaQueryWrapper<Prescription> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Prescription::getDiagnosisId, diagnosisId);
        queryWrapper.orderByDesc(Prescription::getCreateTime);
        
        List<Prescription> prescriptions = list(queryWrapper);
        
        return prescriptions.stream().map(p -> {
            PrescriptionVO vo = new PrescriptionVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList());
    }
} 