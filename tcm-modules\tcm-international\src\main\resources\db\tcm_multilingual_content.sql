-- ----------------------------
-- 1. 创建多语言内容表
-- ----------------------------
DROP TABLE IF EXISTS `tcm_multilingual_content`;
CREATE TABLE `tcm_multilingual_content` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content_key` varchar(100) NOT NULL COMMENT '内容键',
  `content_type` varchar(50) NOT NULL COMMENT '内容类型',
  `locale` varchar(10) NOT NULL COMMENT '语言代码',
  `content` text NOT NULL COMMENT '翻译内容',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（1正常 0禁用）',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key_type_locale` (`content_key`,`content_type`,`locale`) COMMENT '键类型语言唯一索引',
  INDEX `idx_content_type_locale` (`content_type`,`locale`) COMMENT '类型语言索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='多语言内容表';

-- ----------------------------
-- 2. 插入初始化数据（系统消息）
-- ----------------------------
-- 中文
INSERT INTO `tcm_multilingual_content` (`content_key`, `content_type`, `locale`, `content`, `status`, `create_time`, `update_time`) VALUES
('common.success', 'system', 'zh-CN', '操作成功', 1, NOW(), NOW()),
('common.fail', 'system', 'zh-CN', '操作失败', 1, NOW(), NOW()),
('common.save.success', 'system', 'zh-CN', '保存成功', 1, NOW(), NOW()),
('common.save.fail', 'system', 'zh-CN', '保存失败', 1, NOW(), NOW()),
('common.delete.success', 'system', 'zh-CN', '删除成功', 1, NOW(), NOW()),
('common.delete.fail', 'system', 'zh-CN', '删除失败', 1, NOW(), NOW()),
('common.update.success', 'system', 'zh-CN', '更新成功', 1, NOW(), NOW()),
('common.update.fail', 'system', 'zh-CN', '更新失败', 1, NOW(), NOW());

-- 英文
INSERT INTO `tcm_multilingual_content` (`content_key`, `content_type`, `locale`, `content`, `status`, `create_time`, `update_time`) VALUES
('common.success', 'system', 'en-US', 'Operation successful', 1, NOW(), NOW()),
('common.fail', 'system', 'en-US', 'Operation failed', 1, NOW(), NOW()),
('common.save.success', 'system', 'en-US', 'Save successful', 1, NOW(), NOW()),
('common.save.fail', 'system', 'en-US', 'Save failed', 1, NOW(), NOW()),
('common.delete.success', 'system', 'en-US', 'Delete successful', 1, NOW(), NOW()),
('common.delete.fail', 'system', 'en-US', 'Delete failed', 1, NOW(), NOW()),
('common.update.success', 'system', 'en-US', 'Update successful', 1, NOW(), NOW()),
('common.update.fail', 'system', 'en-US', 'Update failed', 1, NOW(), NOW());

-- 日文
INSERT INTO `tcm_multilingual_content` (`content_key`, `content_type`, `locale`, `content`, `status`, `create_time`, `update_time`) VALUES
('common.success', 'system', 'ja-JP', '操作が成功しました', 1, NOW(), NOW()),
('common.fail', 'system', 'ja-JP', '操作が失敗しました', 1, NOW(), NOW()),
('common.save.success', 'system', 'ja-JP', '保存に成功しました', 1, NOW(), NOW()),
('common.save.fail', 'system', 'ja-JP', '保存に失敗しました', 1, NOW(), NOW()),
('common.delete.success', 'system', 'ja-JP', '削除に成功しました', 1, NOW(), NOW()),
('common.delete.fail', 'system', 'ja-JP', '削除に失敗しました', 1, NOW(), NOW()),
('common.update.success', 'system', 'ja-JP', '更新に成功しました', 1, NOW(), NOW()),
('common.update.fail', 'system', 'ja-JP', '更新に失敗しました', 1, NOW(), NOW());

-- 3. 诊断相关术语
-- 中文
INSERT INTO `tcm_multilingual_content` (`content_key`, `content_type`, `locale`, `content`, `status`, `create_time`, `update_time`) VALUES
('tcm.tongue.red', 'terminology', 'zh-CN', '舌红', 1, NOW(), NOW()),
('tcm.tongue.pale', 'terminology', 'zh-CN', '舌淡白', 1, NOW(), NOW()),
('tcm.tongue.purple', 'terminology', 'zh-CN', '舌紫', 1, NOW(), NOW()),
('tcm.pulse.floating', 'terminology', 'zh-CN', '脉浮', 1, NOW(), NOW()),
('tcm.pulse.sinking', 'terminology', 'zh-CN', '脉沉', 1, NOW(), NOW()),
('tcm.syndrome.yin.deficiency', 'terminology', 'zh-CN', '阴虚', 1, NOW(), NOW()),
('tcm.syndrome.yang.deficiency', 'terminology', 'zh-CN', '阳虚', 1, NOW(), NOW());

-- 英文
INSERT INTO `tcm_multilingual_content` (`content_key`, `content_type`, `locale`, `content`, `status`, `create_time`, `update_time`) VALUES
('tcm.tongue.red', 'terminology', 'en-US', 'Red tongue', 1, NOW(), NOW()),
('tcm.tongue.pale', 'terminology', 'en-US', 'Pale tongue', 1, NOW(), NOW()),
('tcm.tongue.purple', 'terminology', 'en-US', 'Purple tongue', 1, NOW(), NOW()),
('tcm.pulse.floating', 'terminology', 'en-US', 'Floating pulse', 1, NOW(), NOW()),
('tcm.pulse.sinking', 'terminology', 'en-US', 'Sinking pulse', 1, NOW(), NOW()),
('tcm.syndrome.yin.deficiency', 'terminology', 'en-US', 'Yin deficiency', 1, NOW(), NOW()),
('tcm.syndrome.yang.deficiency', 'terminology', 'en-US', 'Yang deficiency', 1, NOW(), NOW());

-- 日文
INSERT INTO `tcm_multilingual_content` (`content_key`, `content_type`, `locale`, `content`, `status`, `create_time`, `update_time`) VALUES
('tcm.tongue.red', 'terminology', 'ja-JP', '紅舌', 1, NOW(), NOW()),
('tcm.tongue.pale', 'terminology', 'ja-JP', '淡白舌', 1, NOW(), NOW()),
('tcm.tongue.purple', 'terminology', 'ja-JP', '紫舌', 1, NOW(), NOW()),
('tcm.pulse.floating', 'terminology', 'ja-JP', '浮脈', 1, NOW(), NOW()),
('tcm.pulse.sinking', 'terminology', 'ja-JP', '沈脈', 1, NOW(), NOW()),
('tcm.syndrome.yin.deficiency', 'terminology', 'ja-JP', '陰虚', 1, NOW(), NOW()),
('tcm.syndrome.yang.deficiency', 'terminology', 'ja-JP', '陽虚', 1, NOW(), NOW()); 