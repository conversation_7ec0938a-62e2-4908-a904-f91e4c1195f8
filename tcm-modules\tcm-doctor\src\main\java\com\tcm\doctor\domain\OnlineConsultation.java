package com.tcm.doctor.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tcm.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 在线问诊实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tcm_online_consultation")
public class OnlineConsultation extends BaseEntity {
    
    /**
     * 问诊ID
     */
    @TableId
    private Long consultationId;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 问诊标题
     */
    private String title;
    
    /**
     * 问诊类型（1-图文咨询 2-语音咨询 3-视频咨询）
     */
    private Integer type;
    
    /**
     * 主诉
     */
    private String chiefComplaint;
    
    /**
     * 病情描述
     */
    private String illnessDesc;
    
    /**
     * 患病时长
     */
    private String illnessDuration;
    
    /**
     * 既往病史
     */
    private String medicalHistory;
    
    /**
     * 药物过敏史
     */
    private String allergicHistory;
    
    /**
     * 问诊图片（多个以逗号分隔）
     */
    private String images;
    
    /**
     * 问诊状态（0-待接诊 1-进行中 2-已完成 3-已取消 4-已超时）
     */
    private Integer status;
    
    /**
     * 接诊时间
     */
    private Date acceptTime;
    
    /**
     * 完成时间
     */
    private Date finishTime;
    
    /**
     * 医生建议
     */
    private String doctorAdvice;
    
    /**
     * 诊断结果
     */
    private String diagnosis;
    
    /**
     * 关联处方ID
     */
    private Long prescriptionId;
    
    /**
     * 评分（1-5分）
     */
    private Integer score;
    
    /**
     * 评价内容
     */
    private String evaluation;
    
    /**
     * 订单编号
     */
    private String orderNo;
    
    /**
     * 支付状态（0-未支付 1-已支付 2-已退款）
     */
    private Integer payStatus;
    
    /**
     * 支付时间
     */
    private Date payTime;
    
    /**
     * 支付金额
     */
    private Double payAmount;
} 