package com.tcm.common.security.handler;

import java.io.IOException;
import java.io.Serializable;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.http.HttpStatus;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 认证失败处理类
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class AuthenticationEntryPointImpl implements AuthenticationEntryPoint, Serializable {

    private static final long serialVersionUID = 1L;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException authException) throws IOException {
        int code = HttpStatus.UNAUTHORIZED.value();
        String msg = "认证失败，无法访问系统资源";

        log.error("认证失败，请求URL: {}, 错误信息: {}", request.getRequestURI(), authException.getMessage());

        response.setStatus(code);
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");

        // 构建返回结果
        ErrorResponse error = new ErrorResponse(code, msg);

        response.getWriter().print(objectMapper.writeValueAsString(error));
    }

    /**
     * 错误响应结构
     */
    static class ErrorResponse {
        private int code;
        private String message;

        public ErrorResponse(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }
    }
}