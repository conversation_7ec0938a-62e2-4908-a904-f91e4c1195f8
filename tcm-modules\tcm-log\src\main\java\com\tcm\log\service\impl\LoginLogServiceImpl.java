package com.tcm.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.log.domain.LoginLog;
import com.tcm.log.mapper.LoginLogMapper;
import com.tcm.log.service.ILoginLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;

/**
 * 登录日志服务实现
 * 
 * <AUTHOR>
 */
@Service
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLog> implements ILoginLogService {

    /**
     * 记录登录信息
     * 
     * @param loginLog 登录日志信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLoginLog(LoginLog loginLog) {
        return save(loginLog);
    }

    /**
     * 记录登出信息
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveLogoutLog(String sessionId) {
        LambdaUpdateWrapper<LoginLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(LoginLog::getSessionId, sessionId)
                .set(LoginLog::getLogoutTime, new Date());
        return update(wrapper);
    }

    /**
     * 清空登录日志
     * 
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean clearLogs() {
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        return remove(wrapper);
    }

    /**
     * 删除登录日志
     * 
     * @param ids 需要删除的日志ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLogs(Long[] ids) {
        return removeByIds(Arrays.asList(ids));
    }

    /**
     * 查询指定用户的最新登录日志
     * 
     * @param username 用户名
     * @return 登录日志
     */
    @Override
    public LoginLog getLatestLoginLog(String username) {
        LambdaQueryWrapper<LoginLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LoginLog::getUsername, username)
                .orderByDesc(LoginLog::getLoginTime)
                .last("LIMIT 1");
        return getOne(wrapper);
    }
} 