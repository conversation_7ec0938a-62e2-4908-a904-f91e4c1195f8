package com.tcm.medication.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.medication.dto.MedicineDTO;
import com.tcm.medication.entity.Medicine;

/**
 * 药品服务接口
 *
 * <AUTHOR>
 */
public interface MedicineService extends IService<Medicine> {

    /**
     * 分页查询药品列表
     *
     * @param page         页码
     * @param pageSize     每页大小
     * @param medicineName 药品名称
     * @param medicineCode 药品编码
     * @param medicineType 药品类型
     * @param categoryId   分类ID
     * @return 分页结果
     */
    IPage<Medicine> page(int page, int pageSize, String medicineName, String medicineCode, Integer medicineType,
            Long categoryId);

    /**
     * 根据ID获取药品详情
     *
     * @param id 药品ID
     * @return 药品详情
     */
    MedicineDTO getDetail(Long id);

    /**
     * 新增药品
     *
     * @param dto 药品信息
     * @return 是否成功
     */
    boolean add(MedicineDTO dto);

    /**
     * 更新药品
     *
     * @param dto 药品信息
     * @return 是否成功
     */
    boolean update(MedicineDTO dto);

    /**
     * 删除药品
     *
     * @param id 药品ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 更新药品库存
     *
     * @param id         药品ID
     * @param stockDelta 库存变化量
     * @return 是否成功
     */
    boolean updateStock(Long id, int stockDelta);

    /**
     * 检查药品是否存在
     *
     * @param id 药品ID
     * @return 是否存在
     */
    boolean checkExist(Long id);

    /**
     * 启用药品
     *
     * @param id 药品ID
     * @return 是否成功
     */
    boolean enable(Long id);

    /**
     * 禁用药品
     *
     * @param id 药品ID
     * @return 是否成功
     */
    boolean disable(Long id);
}