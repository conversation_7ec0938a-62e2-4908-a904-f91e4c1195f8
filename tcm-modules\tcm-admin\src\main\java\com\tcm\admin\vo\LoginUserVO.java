package com.tcm.admin.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 登录用户视图对象
 */
@Data
@Schema(description = "登录用户视图对象")
public class LoginUserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 用户昵称
     */
    @Schema(description = "用户昵称")
    private String nickname;

    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String avatar;

    /**
     * 用户类型（1系统用户 2医生 3患者）
     */
    @Schema(description = "用户类型（1系统用户 2医生 3患者）")
    private Integer userType;

    /**
     * 角色列表
     */
    @Schema(description = "角色列表")
    private List<RoleVO> roles;

    /**
     * 权限集合
     */
    @Schema(description = "权限集合")
    private Set<String> permissions;

    /**
     * 部门ID
     */
    @Schema(description = "部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 是否为超级管理员
     */
    @Schema(description = "是否为超级管理员")
    private Boolean isAdmin;
}