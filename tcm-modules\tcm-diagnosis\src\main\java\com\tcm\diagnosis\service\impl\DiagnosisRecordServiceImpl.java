package com.tcm.diagnosis.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.diagnosis.domain.DiagnosisRecord;
import com.tcm.diagnosis.dto.DiagnosisQuery;
import com.tcm.diagnosis.dto.DiagnosisRecordDTO;
import com.tcm.diagnosis.mapper.DiagnosisRecordMapper;
import com.tcm.diagnosis.service.IDiagnosisRecordService;
import com.tcm.diagnosis.vo.DiagnosisRecordVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 诊断记录服务实现类
 */
@Service
public class DiagnosisRecordServiceImpl extends ServiceImpl<DiagnosisRecordMapper, DiagnosisRecord> implements IDiagnosisRecordService {
    
    @Override
    public IPage<DiagnosisRecordVO> listDiagnosisRecords(DiagnosisQuery query) {
        Page<DiagnosisRecord> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectDiagnosisRecordList(page, query);
    }
    
    @Override
    public DiagnosisRecordVO getDiagnosisRecordById(Long diagnosisId) {
        return baseMapper.selectDiagnosisRecordById(diagnosisId);
    }
    
    @Override
    public List<DiagnosisRecordVO> getDiagnosisRecordsByPatientId(Long patientId) {
        return baseMapper.selectDiagnosisRecordByPatientId(patientId);
    }
    
    @Override
    public List<DiagnosisRecordVO> getDiagnosisRecordsByDoctorId(Long doctorId) {
        return baseMapper.selectDiagnosisRecordByDoctorId(doctorId);
    }
    
    @Override
    public boolean addDiagnosisRecord(DiagnosisRecordDTO diagnosisRecordDTO) {
        DiagnosisRecord diagnosisRecord = new DiagnosisRecord();
        BeanUtils.copyProperties(diagnosisRecordDTO, diagnosisRecord);
        
        // 生成诊断编号
        if (StringUtils.isEmpty(diagnosisRecord.getDiagnosisCode())) {
            diagnosisRecord.setDiagnosisCode(generateDiagnosisCode());
        }
        
        // 设置诊断日期
        if (diagnosisRecord.getDiagnosisDate() == null) {
            diagnosisRecord.setDiagnosisDate(new Date());
        }
        
        // 设置默认状态为草稿
        if (diagnosisRecord.getStatus() == null) {
            diagnosisRecord.setStatus(0);
        }
        
        return save(diagnosisRecord);
    }
    
    @Override
    public boolean updateDiagnosisRecord(DiagnosisRecordDTO diagnosisRecordDTO) {
        DiagnosisRecord diagnosisRecord = getById(diagnosisRecordDTO.getDiagnosisId());
        if (diagnosisRecord == null) {
            return false;
        }
        
        BeanUtils.copyProperties(diagnosisRecordDTO, diagnosisRecord);
        return updateById(diagnosisRecord);
    }
    
    @Override
    public boolean deleteDiagnosisRecord(Long diagnosisId) {
        return removeById(diagnosisId);
    }
    
    @Override
    public boolean deleteDiagnosisRecords(Long[] diagnosisIds) {
        return removeByIds(Arrays.asList(diagnosisIds));
    }
    
    @Override
    public boolean updateDiagnosisRecordStatus(Long diagnosisId, Integer status) {
        LambdaUpdateWrapper<DiagnosisRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(DiagnosisRecord::getDiagnosisId, diagnosisId)
                .set(DiagnosisRecord::getStatus, status);
        return update(wrapper);
    }
    
    /**
     * 生成诊断编号
     * 规则：DG + 年月日 + 4位随机数
     */
    private String generateDiagnosisCode() {
        String prefix = "DG";
        String date = StringUtils.dateTimeNow("yyyyMMdd");
        String random = StringUtils.makeRandomStr(4);
        return prefix + date + random;
    }
} 