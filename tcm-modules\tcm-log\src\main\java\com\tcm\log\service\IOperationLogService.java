package com.tcm.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.log.domain.OperationLog;

/**
 * 操作日志服务接口
 * 
 * <AUTHOR>
 */
public interface IOperationLogService extends IService<OperationLog> {
    
    /**
     * 保存操作日志
     * 
     * @param operationLog 操作日志信息
     * @return 结果
     */
    boolean saveLog(OperationLog operationLog);
    
    /**
     * 清空操作日志
     * 
     * @return 结果
     */
    boolean clearLogs();
    
    /**
     * 删除操作日志
     * 
     * @param ids 需要删除的日志ID
     * @return 结果
     */
    boolean deleteLogs(Long[] ids);
    
    /**
     * 查询指定业务类型和业务ID的操作日志
     * 
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 操作日志列表
     */
    OperationLog getByBusinessTypeAndId(String businessType, String businessId);
} 