package com.tcm.common.core.exception;

/**
 * 基础异常
 */
public class BaseException extends RuntimeException {
    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误提示
     */
    private String message;

    /**
     * 错误明细，内部调试错误
     */
    private String detailMessage;

    /**
     * 构造函数
     *
     * @param message 错误提示
     */
    public BaseException(String message) {
        this.message = message;
    }

    /**
     * 构造函数
     *
     * @param message 错误提示
     * @param code    错误码
     */
    public BaseException(String message, Integer code) {
        this.message = message;
        this.code = code;
    }

    /**
     * 构造函数
     *
     * @param message       错误提示
     * @param detailMessage 错误明细
     */
    public BaseException(String message, String detailMessage) {
        this.message = message;
        this.detailMessage = detailMessage;
    }

    /**
     * 构造函数
     *
     * @param message       错误提示
     * @param code          错误码
     * @param detailMessage 错误明细
     */
    public BaseException(String message, Integer code, String detailMessage) {
        this.message = message;
        this.code = code;
        this.detailMessage = detailMessage;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    public String getDetailMessage() {
        return detailMessage;
    }
} 