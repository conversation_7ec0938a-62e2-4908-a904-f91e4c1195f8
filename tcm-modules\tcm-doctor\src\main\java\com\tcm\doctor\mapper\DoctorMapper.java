package com.tcm.doctor.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.doctor.domain.Doctor;
import com.tcm.doctor.dto.DoctorQuery;
import com.tcm.doctor.vo.DoctorVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医生Mapper接口
 */
@Mapper
public interface DoctorMapper extends BaseMapper<Doctor> {
    
    /**
     * 查询医生列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 医生列表
     */
    IPage<DoctorVO> selectDoctorList(Page<Doctor> page, @Param("query") DoctorQuery query);
    
    /**
     * 根据ID查询医生详情
     *
     * @param doctorId 医生ID
     * @return 医生详情
     */
    DoctorVO selectDoctorById(@Param("doctorId") Long doctorId);
    
    /**
     * 根据科室查询医生列表
     *
     * @param department 科室名称
     * @return 医生列表
     */
    List<DoctorVO> selectDoctorByDepartment(@Param("department") String department);
    
    /**
     * 根据专长查询医生列表
     *
     * @param specialty 专长
     * @return 医生列表
     */
    List<DoctorVO> selectDoctorBySpecialty(@Param("specialty") String specialty);
} 