package com.tcm.file.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * 文件存储配置类
 */
@Configuration
public class FileStorageConfig {

    @Value("${tcm.file.storage.base-path:/tcm-files}")
    private String baseStoragePath;

    @Value("${tcm.file.storage.temp-path:/tcm-files/temp}")
    private String tempStoragePath;

    @Value("${tcm.file.storage.minio.endpoint:#{null}}")
    private String minioEndpoint;

    @Value("${tcm.file.storage.minio.access-key:#{null}}")
    private String minioAccessKey;

    @Value("${tcm.file.storage.minio.secret-key:#{null}}")
    private String minioSecretKey;

    @Value("${tcm.file.storage.minio.bucket:tcm-files}")
    private String minioBucket;

    @Value("${tcm.file.storage.minio.enabled:false}")
    private boolean minioEnabled;

    /**
     * 初始化本地存储目录
     */
    @Bean
    public void initLocalStorageDirs() {
        // 创建基础存储目录
        File baseDir = new File(baseStoragePath);
        if (!baseDir.exists()) {
            baseDir.mkdirs();
        }

        // 创建临时文件目录
        File tempDir = new File(tempStoragePath);
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
    }

    /**
     * 配置MinIO客户端
     */
    @Bean
    public MinioClient minioClient() {
        if (!minioEnabled || minioEndpoint == null || minioAccessKey == null || minioSecretKey == null) {
            return null;
        }
        
        return MinioClient.builder()
                .endpoint(minioEndpoint)
                .credentials(minioAccessKey, minioSecretKey)
                .build();
    }

    public String getBaseStoragePath() {
        return baseStoragePath;
    }

    public String getTempStoragePath() {
        return tempStoragePath;
    }

    public String getMinioBucket() {
        return minioBucket;
    }

    public boolean isMinioEnabled() {
        return minioEnabled;
    }
} 