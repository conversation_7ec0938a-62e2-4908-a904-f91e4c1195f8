package com.tcm.international.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.international.dto.MultilingualContentDTO;
import com.tcm.international.entity.MultilingualContent;

import java.util.List;
import java.util.Map;

/**
 * 国际化服务接口
 */
public interface InternationalService {

    /**
     * 添加翻译内容
     *
     * @param contentDTO 内容DTO
     * @return 添加结果
     */
    boolean addContent(MultilingualContentDTO contentDTO);

    /**
     * 批量添加翻译内容
     *
     * @param contentDTOList 内容DTO列表
     * @return 添加结果
     */
    boolean batchAddContent(List<MultilingualContentDTO> contentDTOList);

    /**
     * 更新翻译内容
     *
     * @param contentDTO 内容DTO
     * @return 更新结果
     */
    boolean updateContent(MultilingualContentDTO contentDTO);

    /**
     * 删除翻译内容
     *
     * @param id 内容ID
     * @return 删除结果
     */
    boolean deleteContent(Long id);

    /**
     * 获取单个翻译内容
     *
     * @param id 内容ID
     * @return 翻译内容
     */
    MultilingualContent getContent(Long id);

    /**
     * 分页查询翻译内容
     *
     * @param contentType 内容类型
     * @param locale      语言代码
     * @param contentKey  内容键
     * @param current     当前页
     * @param size        每页大小
     * @return 分页结果
     */
    IPage<MultilingualContent> pageContent(String contentType, String locale,
            String contentKey, long current, long size);

    /**
     * 获取指定类型和语言的所有翻译
     *
     * @param contentType 内容类型
     * @param locale      语言代码
     * @return 翻译映射
     */
    Map<String, String> getTranslations(String contentType, String locale);

    /**
     * 获取指定键的所有语言翻译
     *
     * @param contentKey  内容键
     * @param contentType 内容类型
     * @return 所有翻译
     */
    List<MultilingualContent> getTranslationsByKey(String contentKey, String contentType);

    /**
     * 刷新翻译缓存
     *
     * @param contentType 内容类型
     * @param locale      语言代码
     * @return 刷新结果
     */
    boolean refreshTranslationCache(String contentType, String locale);
}
