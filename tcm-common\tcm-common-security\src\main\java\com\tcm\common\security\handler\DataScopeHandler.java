package com.tcm.common.security.handler;

import com.tcm.common.security.annotation.DataScope;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.*;

/**
 * 数据权限处理器
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class DataScopeHandler {

    /**
     * 全部数据权限
     */
    public static final int DATA_SCOPE_ALL = 1;

    /**
     * 自定义数据权限
     */
    public static final int DATA_SCOPE_CUSTOM = 2;

    /**
     * 部门数据权限
     */
    public static final int DATA_SCOPE_DEPT = 3;

    /**
     * 部门及以下数据权限
     */
    public static final int DATA_SCOPE_DEPT_AND_CHILD = 4;

    /**
     * 仅本人数据权限
     */
    public static final int DATA_SCOPE_SELF = 5;

    /**
     * 配置织入点
     */
    @Pointcut("@annotation(com.tcm.common.security.annotation.DataScope)")
    public void dataScopePointCut() {
    }

    /**
     * 前置通知
     */
    @Before("dataScopePointCut()")
    public void doBefore(JoinPoint point) {
        handleDataScope(point);
    }

    /**
     * 处理数据权限
     */
    protected void handleDataScope(JoinPoint point) {
        // 获得注解
        DataScope dataScope = getAnnotationLog(point);
        if (dataScope == null) {
            return;
        }

        // 获取当前用户的角色权限
        // 此处使用ThreadLocal存储的用户信息，实际项目中会从认证上下文中获取
        // LoginUser loginUser = SecurityUtils.getLoginUser();
        // if (loginUser == null) {
        //     return;
        // }

        // 模拟用户信息
        LoginUser loginUser = mockLoginUser();
        if (loginUser == null) {
            return;
        }

        // 获取数据权限表达式
        dataScopeFilter(point, loginUser, dataScope);
    }

    /**
     * 获取注解
     */
    private DataScope getAnnotationLog(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(DataScope.class);
    }

    /**
     * 数据范围过滤
     */
    public void dataScopeFilter(JoinPoint joinPoint, LoginUser loginUser, DataScope dataScope) {
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return;
        }

        // 生成SQL条件 - 实际项目中应对方法参数进行处理
        StringBuilder sqlString = new StringBuilder();

        // 如果是管理员，不过滤数据
        if (loginUser.isAdmin()) {
            return;
        }

        String deptAlias = dataScope.deptAlias();
        String userAlias = dataScope.userAlias();

        // 获取角色数据范围
        for (Role role : loginUser.getRoles()) {
            int dataScopeType = role.getDataScope();
            switch (dataScopeType) {
                case DATA_SCOPE_ALL:
                    // 全部数据权限
                    return;
                case DATA_SCOPE_CUSTOM:
                    // 自定义数据权限
                    sqlString.append(String.format(" OR %s.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = %d ) ", 
                            deptAlias, role.getRoleId()));
                    break;
                case DATA_SCOPE_DEPT:
                    // 本部门数据权限
                    sqlString.append(String.format(" OR %s.dept_id = %d ", deptAlias, loginUser.getDeptId()));
                    break;
                case DATA_SCOPE_DEPT_AND_CHILD:
                    // 本部门及以下数据权限
                    sqlString.append(String.format(" OR %s.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = %d OR find_in_set(%d, ancestors) )", 
                            deptAlias, loginUser.getDeptId(), loginUser.getDeptId()));
                    break;
                case DATA_SCOPE_SELF:
                    // 仅本人数据权限
                    if (dataScope.filterUser()) {
                        sqlString.append(String.format(" OR %s.user_id = %d", userAlias, loginUser.getUserId()));
                    } else {
                        // 数据权限为仅本人且过滤员工为空，则不查询任何数据
                        sqlString.append(" OR 1=0 ");
                    }
                    break;
                default:
                    break;
            }
        }

        // 将生成的条件SQL传递给查询参数对象
        if (sqlString.toString().length() > 0) {
            for (Object arg : args) {
                if (arg instanceof BaseEntity) {
                    BaseEntity baseEntity = (BaseEntity) arg;
                    baseEntity.setDataScope(" AND (" + sqlString.substring(4) + ")");
                }
            }
        }
    }

    /**
     * 模拟登录用户数据
     */
    private LoginUser mockLoginUser() {
        LoginUser loginUser = new LoginUser();
        loginUser.setUserId(1L);
        loginUser.setUsername("admin");
        loginUser.setDeptId(1L);
        loginUser.setAdmin(true);

        List<Role> roles = new ArrayList<>();
        Role role = new Role();
        role.setRoleId(1L);
        role.setRoleName("超级管理员");
        role.setDataScope(DATA_SCOPE_ALL);
        roles.add(role);

        loginUser.setRoles(roles);
        return loginUser;
    }

    /**
     * 模拟登录用户类
     */
    @Data
    public static class LoginUser {
        private Long userId;
        private String username;
        private Long deptId;
        private boolean isAdmin;
        private List<Role> roles;
    }

    /**
     * 模拟角色类
     */
    @Data
    public static class Role {
        private Long roleId;
        private String roleName;
        private int dataScope;
    }

    /**
     * 基础实体类，用于设置数据权限SQL条件
     */
    @Data
    public static class BaseEntity {
        /**
         * 数据权限SQL条件
         */
        private String dataScope;
    }
} 