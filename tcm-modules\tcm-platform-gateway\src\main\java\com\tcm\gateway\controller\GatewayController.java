package com.tcm.gateway.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.route.RouteDefinition;
import org.springframework.cloud.gateway.route.RouteDefinitionLocator;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * 网关控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/gateway")
public class GatewayController {

    @Autowired
    private RouteDefinitionLocator routeDefinitionLocator;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Mono<Map<String, Object>> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("message", "网关服务正常运行");
        result.put("timestamp", System.currentTimeMillis());
        return Mono.just(result);
    }

    /**
     * 获取所有路由信息
     */
    @GetMapping("/routes")
    public Flux<RouteDefinition> routes() {
        return routeDefinitionLocator.getRouteDefinitions();
    }
}