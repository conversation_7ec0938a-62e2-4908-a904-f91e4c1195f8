server:
  port: ${tcm.service.platform-auth.port:${TCM_PLATFORM_AUTH_PORT:9004}}
  servlet:
    context-path: /auth

spring:
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/tcm_auth?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    # Druid连接池配置
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
        wall:
          enabled: true
        slf4j:
          enabled: true
      
  # Redis配置
  data:
    redis:
      host: ${tcm.service.redis.host}
      port: ${tcm.service.redis.port}
      password: ${tcm.service.redis.password}
      database: ${tcm.service.redis.database}
  
  # 配置其他云服务相关属性
  cloud:
    compatibility-verifier:
      enabled: false
    # 配置Sentinel，移除Nacos配置（已在bootstrap.yml中定义）
    sentinel:
      transport:
        dashboard: ${tcm.service.sentinel.host}:${tcm.service.sentinel.port}
      eager: true
      datasource:
        ds1:
          nacos:
            server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
            namespace: ${tcm.service.nacos.namespace:}
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            data-type: json
            rule-type: flow
  
  # 禁用自动配置
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfiguration

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*Mapper.xml
  type-aliases-package: com.tcm.auth.domain
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.tcm.auth: debug
    org.springframework: warn

# 认证配置
auth:
  # JWT配置
  jwt:
    # 密钥
    secret-key: tcm-platform-auth-jwt-secret-key-123456
    # 访问令牌有效期（单位：分钟）
    access-token-expiration: 30
    # 刷新令牌有效期（单位：天）
    refresh-token-expiration: 7
  # 免认证的接口
  excludes:
    - /auth/login
    - /auth/captcha
    - /actuator/**
    - /doc.html
    - /favicon.ico 