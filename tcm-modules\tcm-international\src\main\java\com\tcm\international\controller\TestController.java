package com.tcm.international.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器，用于验证应用程序启动状态
 */
@RestController
@RequestMapping("/test")
public class TestController {

    /**
     * 简单的健康检查接口
     * @return 状态信息
     */
    @GetMapping("/health")
    public String health() {
        return "国际化服务启动成功！当前时间: " + System.currentTimeMillis();
    }
}
