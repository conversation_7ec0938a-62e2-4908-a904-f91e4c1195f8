server:
  port: ${tcm.service.schedule.port:${TCM_SCHEDULE_PORT:9312}}
  servlet:
    context-path: /schedule

spring:
  main:
    allow-bean-definition-overriding: true
    # 禁用不需要的自动配置
    banner-mode: off
  application:
    name: tcm-schedule
  profiles:
    active: dev
    include: common
  config:
    import: 
      - optional:classpath:/config/tcm-common.yml
  # Redis配置，使用统一配置中的属性
  data:
    redis:
      host: ${tcm.service.redis.host}
      port: ${tcm.service.redis.port}
      password: ${tcm.service.redis.password}
      database: ${tcm.service.redis.database}
  liquibase:
    enabled: false
  # 禁用其他不需要的自动配置  
  thymeleaf:
    enabled: false
  # 配置其他自动配置项
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.webflux.WebFluxAutoConfiguration,org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration
  # 配置中心
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      # Nacos客户端配置 - 使用统一配置中的属性
      discovery:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        enabled: true
        # 禁用监控相关功能
        metadata:
          "[preserved.heart.beat.interval]": 10000
          "[preserved.heart.beat.timeout]": 30000
          "[preserved.ip.delete.timeout]": 30000
      config:
        server-addr: ${tcm.service.nacos.host}:${tcm.service.nacos.port}
        context-path: /nacos
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username}
        password: ${tcm.service.nacos.password}
        file-extension: yml
        import-check:
          enabled: false   # 禁用配置导入检查
        # Nacos配置
        group: DEFAULT_GROUP
        shared-configs:
          - data-id: application-common.yml
            refresh: true
        # 设置访问超时时间
        timeout: 10000
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${tcm.service.mysql.host}:${tcm.service.mysql.port}/${tcm.service.mysql.database}?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=Asia/Shanghai
    username: ${tcm.service.mysql.username}
    password: ${tcm.service.mysql.password}
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      pool-name: ScheduleHikariCP
      max-lifetime: 2000000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.tcm.schedule.entity
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# Swagger配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  packages-to-scan: com.tcm.schedule.controller

# 禁用Spring Boot Actuator
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: false

# 日志配置
logging:
  level:
    "[com.tcm]": debug
    "[org.springframework.web]": info
    "[org.springframework.security]": info
    "[com.alibaba.nacos]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc]": ERROR
    "[com.alibaba.nacos.shaded.io.perfmark]": ERROR
    "[com.alibaba.nacos.common.remote.client]": ERROR
    "[com.alibaba.nacos.shaded.io.grpc.netty]": ERROR
    "[com.alibaba.cloud.nacos]": ERROR
    "[com.alibaba.nacos.client.naming]": ERROR
    "[org.springframework.cloud]": debug
    # 降低自动配置日志级别，减少信息性消息
    "[org.springframework.boot.autoconfigure]": warn
    org.springframework.boot: warn
    org.springframework: warn

# RocketMQ配置
tcm:
  service:
    # 使用统一配置中的RocketMQ属性
    rocketMq:
      nameServerAddress: ${tcm.service.rocketMq.host}:${tcm.service.rocketMq.port}
      producerGroup: ${spring.application.name}-producer-group
      # 添加错误处理配置
      enable-msg-trace: false
      customized-trace-topic: false
      retry-times-when-send-failed: 0
      retry-times-when-send-async-failed: 0
      # 启动时不检查服务器可用性
      check-connection: false
    # 兼容另一种命名方式
    rocket-mq:
      host: ${tcm.service.rocketMq.host}
      port: ${tcm.service.rocketMq.port}
