package com.tcm.doctor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tcm.common.core.utils.StringUtils;
import com.tcm.doctor.domain.Doctor;
import com.tcm.doctor.domain.DoctorAppointment;
import com.tcm.doctor.domain.DoctorSchedule;
import com.tcm.doctor.dto.DoctorDTO;
import com.tcm.doctor.dto.DoctorQuery;
import com.tcm.doctor.dto.DoctorScheduleDTO;
import com.tcm.doctor.feign.OrderFeignClient;
import com.tcm.doctor.feign.PrescriptionFeignClient;
import com.tcm.doctor.mapper.DoctorAppointmentMapper;
import com.tcm.doctor.mapper.DoctorMapper;
import com.tcm.doctor.service.IDoctorEvaluationService;
import com.tcm.doctor.service.IDoctorScheduleService;
import com.tcm.doctor.service.IDoctorService;
import com.tcm.doctor.vo.DoctorRecommendVO;
import com.tcm.doctor.vo.DoctorScheduleVO;
import com.tcm.doctor.vo.DoctorVO;
import com.tcm.doctor.vo.DoctorWorkloadVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 医生服务实现类
 */
@Service
public class DoctorServiceImpl extends ServiceImpl<DoctorMapper, Doctor> implements IDoctorService {
    
    private static final Logger log = LoggerFactory.getLogger(DoctorServiceImpl.class);
    
    @Autowired
    private IDoctorEvaluationService doctorEvaluationService;
    
    @Autowired
    private IDoctorScheduleService doctorScheduleService;
    
    @Autowired
    private DoctorAppointmentMapper doctorAppointmentMapper;
    
    @Autowired
    private PrescriptionFeignClient prescriptionFeignClient;
    
    @Autowired
    private OrderFeignClient orderFeignClient;
    
    @Override
    public IPage<DoctorVO> listDoctors(DoctorQuery query) {
        Page<Doctor> page = new Page<>(query.getPageNum(), query.getPageSize());
        return baseMapper.selectDoctorList(page, query);
    }
    
    @Override
    public DoctorVO getDoctorById(Long doctorId) {
        return baseMapper.selectDoctorById(doctorId);
    }
    
    @Override
    @Caching(evict = {
        @CacheEvict(value = "doctorWorkloadStats", key = "#doctorDTO.getDoctorId().toString() + '_default'"),
        @CacheEvict(value = "doctorWorkloadStats", allEntries = true)
    })
    public boolean updateDoctor(DoctorDTO doctorDTO) {
        log.info("更新医生信息，ID: {}", doctorDTO.getDoctorId());
        Doctor doctor = getById(doctorDTO.getDoctorId());
        if (doctor == null) {
            return false;
        }
        
        BeanUtils.copyProperties(doctorDTO, doctor);
        return updateById(doctor);
    }
    
    @Override
    public boolean addDoctor(DoctorDTO doctorDTO) {
        Doctor doctor = new Doctor();
        BeanUtils.copyProperties(doctorDTO, doctor);
        
        // 生成医生编号
        if (StringUtils.isEmpty(doctor.getDoctorCode())) {
            doctor.setDoctorCode(generateDoctorCode());
        }
        
        return save(doctor);
    }
    
    @Override
    public boolean deleteDoctor(Long doctorId) {
        return removeById(doctorId);
    }
    
    @Override
    public boolean deleteDoctors(Long[] doctorIds) {
        return removeByIds(Arrays.asList(doctorIds));
    }
    
    @Override
    public List<DoctorVO> getDoctorsByDepartment(String department) {
        return baseMapper.selectDoctorByDepartment(department);
    }
    
    @Override
    public List<DoctorVO> getDoctorsBySpecialty(String specialty) {
        return baseMapper.selectDoctorBySpecialty(specialty);
    }
    
    @Override
    public List<DoctorScheduleVO> getDoctorSchedule(Long doctorId) {
        // 使用排班服务获取医生排班信息
        // 这里使用当前日期获取排班信息
        return doctorScheduleService.getDoctorSchedulesByDoctorIdAndDate(doctorId, new Date());
    }
    
    @Override
    public boolean setDoctorSchedule(Long doctorId, List<DoctorScheduleVO> scheduleList) {
        // 转换VO为DTO并批量保存
        List<DoctorScheduleDTO> scheduleDTOs = new ArrayList<>();
        for (DoctorScheduleVO vo : scheduleList) {
            DoctorScheduleDTO dto = new DoctorScheduleDTO();
            BeanUtils.copyProperties(vo, dto);
            dto.setDoctorId(doctorId);
            scheduleDTOs.add(dto);
        }
        
        // 先删除原有排班，再添加新排班
        LambdaQueryWrapper<DoctorSchedule> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DoctorSchedule::getDoctorId, doctorId);
        doctorScheduleService.remove(queryWrapper);
        
        return doctorScheduleService.batchAddDoctorSchedules(scheduleDTOs);
    }
    
    @Override
    public boolean updateDoctorStatus(Long doctorId, Integer status) {
        LambdaUpdateWrapper<Doctor> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(Doctor::getDoctorId, doctorId)
                .set(Doctor::getStatus, status);
        return update(wrapper);
    }
    
    /**
     * 根据疾病症状推荐医生
     *
     * @param symptom    症状描述
     * @param department 科室（可选）
     * @param limit      推荐数量
     * @return 推荐医生列表
     */
    public List<DoctorRecommendVO> recommendDoctorsBySymptom(String symptom, String department, int limit) {
        // 根据症状关键词匹配专长
        LambdaQueryWrapper<Doctor> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Doctor::getSpecialty, symptom)
               .eq(Doctor::getStatus, 0);
        
        if (StringUtils.isNotEmpty(department)) {
            wrapper.eq(Doctor::getDepartment, department);
        }
        
        // 按职称和工作年限排序
        wrapper.orderByAsc(Doctor::getTitle)
               .orderByDesc(Doctor::getWorkYears);
        
        // 获取医生列表
        List<Doctor> doctors = list(wrapper);
        
        // 转换为推荐VO对象
        List<DoctorRecommendVO> recommendList = new ArrayList<>();
        for (Doctor doctor : doctors) {
            if (recommendList.size() >= limit) {
                break;
            }
            
            DoctorRecommendVO recommendVO = new DoctorRecommendVO();
            BeanUtils.copyProperties(doctor, recommendVO);
            
            // 设置职称名称
            switch (doctor.getTitle()) {
                case 1:
                    recommendVO.setTitleName("主任医师");
                    break;
                case 2:
                    recommendVO.setTitleName("副主任医师");
                    break;
                case 3:
                    recommendVO.setTitleName("主治医师");
                    break;
                case 4:
                    recommendVO.setTitleName("住院医师");
                    break;
                default:
                    recommendVO.setTitleName("其他");
            }
            
            // 获取评分和评价数量
            Double averageScore = doctorEvaluationService.calculateDoctorAverageScore(doctor.getDoctorId());
            Integer evaluationCount = doctorEvaluationService.countDoctorEvaluation(doctor.getDoctorId());
            
            recommendVO.setAverageScore(averageScore);
            recommendVO.setEvaluationCount(evaluationCount);
            
            // 设置推荐理由
            StringBuilder reason = new StringBuilder();
            reason.append(recommendVO.getTitleName())
                  .append("，从业")
                  .append(doctor.getWorkYears())
                  .append("年，");
            
            if (StringUtils.isNotEmpty(doctor.getSpecialty())) {
                reason.append("专长：")
                     .append(doctor.getSpecialty())
                     .append("，");
            }
            
            if (averageScore != null) {
                reason.append("患者评分：")
                     .append(String.format("%.1f", averageScore))
                     .append("分，");
            }
            
            if (reason.length() > 0) {
                reason.setLength(reason.length() - 1);
            }
            
            recommendVO.setRecommendReason(reason.toString());
            
            // 获取近期排班
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.add(Calendar.DAY_OF_MONTH, 7);
            Date endDate = calendar.getTime();
            
            List<DoctorScheduleVO> schedules = doctorScheduleService.getDoctorSchedulesByDoctorIdAndDate(doctor.getDoctorId(), now);
            if (!schedules.isEmpty()) {
                // 按日期正序排列，取最近的一个排班
                schedules.sort(Comparator.comparing(DoctorScheduleVO::getScheduleDate));
                recommendVO.setRecentSchedule(schedules.get(0));
            }
            
            recommendList.add(recommendVO);
        }
        
        return recommendList;
    }
    
    /**
     * 生成医生编号
     * 规则：DR + 年月日 + 4位随机数
     */
    private String generateDoctorCode() {
        String prefix = "DR";
        String date = StringUtils.dateTimeNow("yyyyMMdd");
        String random = StringUtils.makeRandomStr(4);
        return prefix + date + random;
    }
    
    @Override
    @Cacheable(value = "doctorWorkloadStats", key = "#doctorId.toString() + '_default'")
    public DoctorWorkloadVO getDoctorWorkloadStatistics(Long doctorId) {
        log.info("获取医生[{}]的默认工作量统计数据", doctorId);
        // 默认获取过去一个月的统计数据
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(1);
        
        return getDoctorWorkloadStatistics(doctorId, startDate, endDate);
    }
    
    @Override
    @Cacheable(value = "doctorWorkloadStats", key = "#doctorId.toString() + '_' + #startDate.toString() + '_' + #endDate.toString()")
    public DoctorWorkloadVO getDoctorWorkloadStatistics(Long doctorId, LocalDate startDate, LocalDate endDate) {
        log.info("获取医生[{}]的工作量统计数据, 时间范围: {} - {}", doctorId, startDate, endDate);
        // 获取医生基本信息
        Doctor doctor = getById(doctorId);
        if (doctor == null) {
            return null;
        }
        
        DoctorWorkloadVO workloadVO = new DoctorWorkloadVO();
        workloadVO.setDoctorId(doctorId);
        workloadVO.setDoctorName(doctor.getDoctorName());
        workloadVO.setDoctorCode(doctor.getDoctorCode());
        workloadVO.setDepartment(doctor.getDepartment());
        workloadVO.setStartDate(startDate);
        workloadVO.setEndDate(endDate);
        
        // 设置职称名称
        switch (doctor.getTitle()) {
            case 1:
                workloadVO.setTitleName("主任医师");
                break;
            case 2:
                workloadVO.setTitleName("副主任医师");
                break;
            case 3:
                workloadVO.setTitleName("主治医师");
                break;
            case 4:
                workloadVO.setTitleName("住院医师");
                break;
            default:
                workloadVO.setTitleName("其他");
        }
        
        // 将LocalDate转换为Date以适配旧代码
        Date startDateTime = Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(endDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());
        
        // 查询诊断数量 - 通过已完成的预约数量统计
        LambdaQueryWrapper<DoctorAppointment> diagnosisQuery = new LambdaQueryWrapper<>();
        diagnosisQuery.eq(DoctorAppointment::getDoctorId, doctorId)
                      .eq(DoctorAppointment::getStatus, 3) // 假设状态3表示已完成诊断
                      .ge(DoctorAppointment::getCreateTime, startDateTime)
                      .lt(DoctorAppointment::getCreateTime, endDateTime);
        
        Long diagnosisCount = doctorAppointmentMapper.selectCount(diagnosisQuery);
        workloadVO.setDiagnosisCount(diagnosisCount == null ? 0 : diagnosisCount.intValue());
        
        // 远程调用获取处方数量
        try {
            Long prescriptionCount = prescriptionFeignClient.getPrescriptionCount(doctorId, startDate, endDate);
            workloadVO.setPrescriptionCount(prescriptionCount.intValue());
        } catch (Exception e) {
            workloadVO.setPrescriptionCount(0);
        }
        
        // 统计患者数量 - 通过统计患者ID的唯一数量
        // 注意：这里假设DoctorAppointment有patientId字段
        try {
            Integer patientCount = doctorAppointmentMapper.selectList(diagnosisQuery)
                                                         .stream()
                                                         .map(DoctorAppointment::getPatientId)
                                                         .collect(Collectors.toSet())
                                                         .size();
            workloadVO.setPatientCount(patientCount);
        } catch (Exception e) {
            workloadVO.setPatientCount(0);
        }
        
        // 远程调用获取总收费金额
        try {
            BigDecimal totalAmount = orderFeignClient.getTotalAmount(doctorId, startDate, endDate);
            workloadVO.setTotalAmount(totalAmount);
        } catch (Exception e) {
            workloadVO.setTotalAmount(BigDecimal.ZERO);
        }
        
        // 远程调用获取复诊率
        try {
            BigDecimal revisitRate = orderFeignClient.getRevisitRate(doctorId, startDate, endDate);
            workloadVO.setRevisitRate(revisitRate);
        } catch (Exception e) {
            workloadVO.setRevisitRate(BigDecimal.ZERO);
        }
        
        // 计算平均诊断时长（假设有诊断开始和结束时间）
        try {
            workloadVO.setAvgDiagnosisDuration(30); // 这里假设平均30分钟，实际应该从数据库查询计算
        } catch (Exception e) {
            workloadVO.setAvgDiagnosisDuration(0);
        }
        
        // 获取医生评分
        try {
            BigDecimal avgRating = doctorEvaluationService.getAverageDoctorRating(doctorId, startDate, endDate);
            workloadVO.setAvgRating(avgRating);
        } catch (Exception e) {
            workloadVO.setAvgRating(BigDecimal.ZERO);
        }
        
        // 计算平均处方金额
        try {
            if (workloadVO.getPrescriptionCount() > 0 && workloadVO.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal avgAmount = workloadVO.getTotalAmount()
                        .divide(new BigDecimal(workloadVO.getPrescriptionCount()), 2, RoundingMode.HALF_UP);
                workloadVO.setAvgPrescriptionAmount(avgAmount);
            } else {
                workloadVO.setAvgPrescriptionAmount(BigDecimal.ZERO);
            }
        } catch (Exception e) {
            workloadVO.setAvgPrescriptionAmount(BigDecimal.ZERO);
        }
        
        return workloadVO;
    }
}