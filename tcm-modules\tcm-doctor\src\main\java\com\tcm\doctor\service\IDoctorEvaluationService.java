package com.tcm.doctor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.doctor.domain.DoctorEvaluation;
import com.tcm.doctor.dto.DoctorEvaluationDTO;
import com.tcm.doctor.vo.DoctorEvaluationVO;
import com.tcm.doctor.vo.DoctorWorkloadVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 医生评价服务接口
 */
public interface IDoctorEvaluationService extends IService<DoctorEvaluation> {
    
    /**
     * 分页查询医生评价列表
     *
     * @param doctorId  医生ID
     * @param pageNum   页码
     * @param pageSize  页大小
     * @return 医生评价列表
     */
    IPage<DoctorEvaluationVO> listDoctorEvaluations(Long doctorId, int pageNum, int pageSize);
    
    /**
     * 根据ID查询医生评价详情
     *
     * @param evaluationId 评价ID
     * @return 医生评价详情
     */
    DoctorEvaluationVO getDoctorEvaluationById(Long evaluationId);
    
    /**
     * 根据诊断ID查询医生评价
     *
     * @param diagnosisId 诊断ID
     * @return 医生评价
     */
    DoctorEvaluationVO getDoctorEvaluationByDiagnosisId(Long diagnosisId);
    
    /**
     * 计算医生评分
     *
     * @param doctorId 医生ID
     * @return 平均评分
     */
    Double calculateDoctorAverageScore(Long doctorId);
    
    /**
     * 获取医生平均评分（带时间范围）
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 平均评分
     */
    BigDecimal getAverageDoctorRating(Long doctorId, LocalDate startDate, LocalDate endDate);
    
    /**
     * 获取医生评价数量
     *
     * @param doctorId 医生ID
     * @return 评价数量
     */
    Integer countDoctorEvaluation(Long doctorId);
    
    /**
     * 新增医生评价
     *
     * @param evaluationDTO 评价信息
     * @return 结果
     */
    boolean addDoctorEvaluation(DoctorEvaluationDTO evaluationDTO);
    
    /**
     * 医生回复评价
     *
     * @param evaluationId 评价ID
     * @param reply        回复内容
     * @return 结果
     */
    boolean replyDoctorEvaluation(Long evaluationId, String reply);
    
    /**
     * 修改评价状态
     *
     * @param evaluationId 评价ID
     * @param status       状态（0-显示 1-隐藏）
     * @return 结果
     */
    boolean updateDoctorEvaluationStatus(Long evaluationId, Integer status);
    
    /**
     * 查询医生工作量统计
     *
     * @param beginDate 开始日期
     * @param endDate   结束日期
     * @return 医生工作量统计
     */
    List<DoctorWorkloadVO> getDoctorWorkloadList(Date beginDate, Date endDate);
} 