<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 引入公共日志配置 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <property name="LOG_HOME" value="logs/tcm-workflow"/>
    <property name="CONSOLE_LOG_PATTERN" 
              value="%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    <property name="FILE_LOG_PATTERN" 
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 系统日志文件 -->
    <appender name="SYSTEM" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/system.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/system-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 错误日志文件 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/error-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- Flowable日志文件 -->
    <appender name="FLOWABLE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/flowable.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/flowable-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>15</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 彻底关闭nacos日志输出 -->
    <logger name="com.alibaba.nacos" level="OFF"/>
    <logger name="com.alibaba.cloud.nacos" level="OFF"/>
    
    <!-- Flowable日志单独输出 -->
    <logger name="org.flowable" level="INFO" additivity="false">
        <appender-ref ref="FLOWABLE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 项目业务日志 -->
    <logger name="com.tcm.workflow" level="DEBUG" additivity="false">
        <appender-ref ref="SYSTEM"/>
        <appender-ref ref="ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 默认日志级别 -->
    <root level="WARN">
        <appender-ref ref="SYSTEM"/>
        <appender-ref ref="ERROR"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration> 