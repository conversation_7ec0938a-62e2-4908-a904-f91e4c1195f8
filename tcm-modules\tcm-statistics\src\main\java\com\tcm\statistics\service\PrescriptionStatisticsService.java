package com.tcm.statistics.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Service;

import com.tcm.statistics.domain.DateRangeDTO;
import com.tcm.statistics.domain.DoctorPrescriptionStatsDTO;
import com.tcm.statistics.domain.IssueDistributionDTO;
import com.tcm.statistics.domain.MedicineUsageDTO;
import com.tcm.statistics.domain.PrescriptionRationalityReportDTO;
import com.tcm.statistics.domain.RationalityStatDTO;
import com.tcm.statistics.domain.SyndromeDistributionDTO;

import lombok.extern.slf4j.Slf4j;

/**
 * 处方统计分析服务
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class PrescriptionStatisticsService {

    /**
     * 医生处方统计分析
     */
    public DoctorPrescriptionStatsDTO analyzeDoctorPrescriptions(Long doctorId, 
                                                               LocalDate startDate, 
                                                               LocalDate endDate) {
        log.info("开始分析医生处方统计数据: doctorId={}, startDate={}, endDate={}", doctorId, startDate, endDate);
        
        DoctorPrescriptionStatsDTO stats = new DoctorPrescriptionStatsDTO();
        stats.setDoctorId(doctorId);
        stats.setPeriod(new DateRangeDTO(startDate, endDate));
        
        // 模拟数据 - 实际项目中应该从数据库查询
        stats.setPrescriptionCount(120L);
        
        // 模拟常用药物TOP10
        List<MedicineUsageDTO> topMedicines = new ArrayList<>();
        topMedicines.add(new MedicineUsageDTO(1L, "黄芪", 78L, 65.0));
        topMedicines.add(new MedicineUsageDTO(2L, "党参", 65L, 54.2));
        topMedicines.add(new MedicineUsageDTO(3L, "白术", 60L, 50.0));
        topMedicines.add(new MedicineUsageDTO(4L, "茯苓", 55L, 45.8));
        topMedicines.add(new MedicineUsageDTO(5L, "甘草", 50L, 41.7));
        topMedicines.add(new MedicineUsageDTO(6L, "当归", 45L, 37.5));
        topMedicines.add(new MedicineUsageDTO(7L, "川芎", 40L, 33.3));
        topMedicines.add(new MedicineUsageDTO(8L, "白芍", 35L, 29.2));
        topMedicines.add(new MedicineUsageDTO(9L, "熟地黄", 30L, 25.0));
        topMedicines.add(new MedicineUsageDTO(10L, "陈皮", 25L, 20.8));
        stats.setTopMedicines(topMedicines);
        
        // 模拟证型分布统计
        List<SyndromeDistributionDTO> syndromeDistribution = new ArrayList<>();
        syndromeDistribution.add(new SyndromeDistributionDTO("气虚证", 35L, 29.2));
        syndromeDistribution.add(new SyndromeDistributionDTO("阴虚证", 25L, 20.8));
        syndromeDistribution.add(new SyndromeDistributionDTO("血瘀证", 20L, 16.7));
        syndromeDistribution.add(new SyndromeDistributionDTO("湿热证", 15L, 12.5));
        syndromeDistribution.add(new SyndromeDistributionDTO("寒证", 10L, 8.3));
        syndromeDistribution.add(new SyndromeDistributionDTO("其他", 15L, 12.5));
        stats.setSyndromeDistribution(syndromeDistribution);
        
        // 模拟处方类型分布
        Map<String, Long> typeDistribution = new HashMap<>();
        typeDistribution.put("汤剂", 80L);
        typeDistribution.put("丸剂", 20L);
        typeDistribution.put("散剂", 10L);
        typeDistribution.put("膏剂", 5L);
        typeDistribution.put("其他", 5L);
        stats.setTypeDistribution(typeDistribution);
        
        // 模拟处方平均药味数和平均费用
        stats.setAvgMedicineCount(12.5);
        stats.setAvgCost(new BigDecimal("120.50"));
        
        log.info("医生处方统计数据分析完成: doctorId={}", doctorId);
        return stats;
    }
    
    /**
     * 药物使用趋势分析
     * 
     * @param medicineName 药物名称
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 药物使用趋势数据
     */
    public List<MedicineUsageDTO> analyzeMedicineTrend(String medicineName, 
                                                     LocalDate startDate,
                                                     LocalDate endDate) {
        log.info("开始分析药物使用趋势: medicineName={}, startDate={}, endDate={}", medicineName, startDate, endDate);
        
        // 模拟数据 - 实际项目中应该从数据库查询
        List<MedicineUsageDTO> trend = new ArrayList<>();
        trend.add(new MedicineUsageDTO(null, "第一周", 15L, 12.5));
        trend.add(new MedicineUsageDTO(null, "第二周", 18L, 15.0));
        trend.add(new MedicineUsageDTO(null, "第三周", 22L, 18.3));
        trend.add(new MedicineUsageDTO(null, "第四周", 20L, 16.7));
        
        log.info("药物使用趋势分析完成: medicineName={}", medicineName);
        return trend;
    }
    
    /**
     * 处方合理性评估分析
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param departmentId 科室ID
     * @return 处方合理性报告
     */
    public PrescriptionRationalityReportDTO analyzeRationality(LocalDate startDate,
                                                             LocalDate endDate,
                                                             Long departmentId) {
        log.info("开始处方合理性评估分析: departmentId={}, startDate={}, endDate={}", departmentId, startDate, endDate);
        
        PrescriptionRationalityReportDTO report = new PrescriptionRationalityReportDTO();
        report.setPeriod(new DateRangeDTO(startDate, endDate));
        report.setDepartmentId(departmentId);
        
        // 模拟数据 - 实际项目中应该从数据库查询
        report.setSampleSize(200);
        
        // 模拟各类问题的发生率
        Map<String, RationalityStatDTO> issueStats = new HashMap<>();
        issueStats.put("药物配伍禁忌", new RationalityStatDTO(15, 7.5, 5));
        issueStats.put("用药剂量不合理", new RationalityStatDTO(25, 12.5, 4));
        issueStats.put("证型与用药不符", new RationalityStatDTO(18, 9.0, 4));
        issueStats.put("药物重复使用", new RationalityStatDTO(12, 6.0, 3));
        issueStats.put("其他问题", new RationalityStatDTO(10, 5.0, 2));
        report.setIssueStats(issueStats);
        
        // 模拟不合理处方比例
        report.setIrrationalRate(40.0);
        
        // 模拟问题类型分布
        List<IssueDistributionDTO> issueDistribution = new ArrayList<>();
        issueDistribution.add(new IssueDistributionDTO("药物配伍禁忌", "存在相互作用的药物组合", 15, 18.75));
        issueDistribution.add(new IssueDistributionDTO("用药剂量不合理", "药物剂量超出安全范围", 25, 31.25));
        issueDistribution.add(new IssueDistributionDTO("证型与用药不符", "所用药物与辨证结果不符", 18, 22.5));
        issueDistribution.add(new IssueDistributionDTO("药物重复使用", "处方中存在重复药物", 12, 15.0));
        issueDistribution.add(new IssueDistributionDTO("其他问题", "其他不合理用药情况", 10, 12.5));
        report.setIssueDistribution(issueDistribution);
        
        log.info("处方合理性评估分析完成: departmentId={}", departmentId);
        return report;
    }
} 