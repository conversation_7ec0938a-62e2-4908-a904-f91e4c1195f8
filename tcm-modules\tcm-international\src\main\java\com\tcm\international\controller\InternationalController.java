package com.tcm.international.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.common.core.domain.Result;
import com.tcm.common.core.domain.PageVO;
import com.tcm.international.dto.MultilingualContentDTO;
import com.tcm.international.entity.MultilingualContent;
import com.tcm.international.service.InternationalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 国际化控制器
 */
@RestController
@RequestMapping("/international")
public class InternationalController {

    @Autowired
    private InternationalService internationalService;

    /**
     * 获取指定类型和语言的所有翻译内容
     */
    @GetMapping("/translations")
    public Result<Map<String, String>> getTranslations(
            @RequestParam("contentType") String contentType,
            @RequestParam("locale") String locale) {
        Map<String, String> translations = internationalService.getTranslations(contentType, locale);
        return Result.success(translations);
    }

    /**
     * 获取指定键的所有翻译
     */
    @GetMapping("/translations/{contentKey}")
    public Result<List<MultilingualContent>> getTranslationsByKey(
            @PathVariable("contentKey") String contentKey,
            @RequestParam("contentType") String contentType) {
        List<MultilingualContent> translations = internationalService.getTranslationsByKey(contentKey, contentType);
        return Result.success(translations);
    }

    /**
     * 添加翻译内容
     */
    @PostMapping("/content")
    public Result<Boolean> addContent(@RequestBody @Validated MultilingualContentDTO contentDTO) {
        boolean result = internationalService.addContent(contentDTO);
        return Result.success(result);
    }

    /**
     * 批量添加翻译内容
     */
    @PostMapping("/content/batch")
    public Result<Boolean> batchAddContent(@RequestBody @Validated List<MultilingualContentDTO> contentDTOList) {
        boolean result = internationalService.batchAddContent(contentDTOList);
        return Result.success(result);
    }

    /**
     * 更新翻译内容
     */
    @PutMapping("/content")
    public Result<Boolean> updateContent(@RequestBody @Validated MultilingualContentDTO contentDTO) {
        boolean result = internationalService.updateContent(contentDTO);
        return Result.success(result);
    }

    /**
     * 删除翻译内容
     */
    @DeleteMapping("/content/{id}")
    public Result<Boolean> deleteContent(@PathVariable("id") Long id) {
        boolean result = internationalService.deleteContent(id);
        return Result.success(result);
    }

    /**
     * 获取单个翻译内容
     */
    @GetMapping("/content/{id}")
    public Result<MultilingualContent> getContent(@PathVariable("id") Long id) {
        MultilingualContent content = internationalService.getContent(id);
        return Result.success(content);
    }

    /**
     * 分页查询翻译内容
     */
    @GetMapping("/content/page")
    public Result<PageVO<MultilingualContent>> pageContent(
            @RequestParam(value = "contentType", required = false) String contentType,
            @RequestParam(value = "locale", required = false) String locale,
            @RequestParam(value = "contentKey", required = false) String contentKey,
            @RequestParam(value = "current", defaultValue = "1") long current,
            @RequestParam(value = "size", defaultValue = "10") long size) {
        IPage<MultilingualContent> page = internationalService.pageContent(contentType, locale, contentKey, current, size);
        return Result.success(new PageVO<MultilingualContent>(page.getRecords(), page.getTotal(), current, size));
    }

    /**
     * 刷新翻译缓存
     */
    @PostMapping("/cache/refresh")
    public Result<Boolean> refreshCache(
            @RequestParam("contentType") String contentType,
            @RequestParam("locale") String locale) {
        boolean result = internationalService.refreshTranslationCache(contentType, locale);
        return Result.success(result);
    }
}
