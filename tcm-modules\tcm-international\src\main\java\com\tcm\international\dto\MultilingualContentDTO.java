package com.tcm.international.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 多语言内容传输对象
 */
@Data
public class MultilingualContentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 内容键
     */
    @NotBlank(message = "内容键不能为空")
    @Size(max = 100, message = "内容键长度不能超过100")
    private String contentKey;

    /**
     * 内容类型
     */
    @NotBlank(message = "内容类型不能为空")
    @Size(max = 50, message = "内容类型长度不能超过50")
    private String contentType;

    /**
     * 语言代码
     */
    @NotBlank(message = "语言代码不能为空")
    @Pattern(regexp = "^[a-z]{2}(-[A-Z]{2})?$", message = "语言代码格式不正确，应为zh-CN、en-US等格式")
    private String locale;

    /**
     * 翻译内容
     */
    @NotBlank(message = "翻译内容不能为空")
    private String content;

    /**
     * 状态（1正常 0禁用）
     */
    private Integer status;
}
