package com.tcm.evaluation.constant;

/**
 * 评估服务常量定义
 */
public class EvaluationConstants {
    
    /**
     * 评估类型：中医体质评估
     */
    public static final String EVAL_TYPE_CONSTITUTION = "constitution";
    
    /**
     * 评估类型：症状评估
     */
    public static final String EVAL_TYPE_SYMPTOM = "symptom";
    
    /**
     * 评估类型：功能评估
     */
    public static final String EVAL_TYPE_FUNCTION = "function";
    
    /**
     * 评估类型：疗效评估
     */
    public static final String EVAL_TYPE_EFFICACY = "efficacy";
    
    /**
     * 评估状态：待评估
     */
    public static final String EVAL_STATUS_PENDING = "pending";
    
    /**
     * 评估状态：进行中
     */
    public static final String EVAL_STATUS_IN_PROGRESS = "in_progress";
    
    /**
     * 评估状态：已完成
     */
    public static final String EVAL_STATUS_COMPLETED = "completed";
    
    /**
     * 评估状态：已取消
     */
    public static final String EVAL_STATUS_CANCELLED = "cancelled";
    
    /**
     * 评估表单类型：标准问卷
     */
    public static final String FORM_TYPE_STANDARD = "standard";
    
    /**
     * 评估表单类型：自定义问卷
     */
    public static final String FORM_TYPE_CUSTOM = "custom";
    
    /**
     * 评估表单类型：临床量表
     */
    public static final String FORM_TYPE_CLINICAL = "clinical";
    
    private EvaluationConstants() {
        // 私有构造函数，防止实例化
    }
} 