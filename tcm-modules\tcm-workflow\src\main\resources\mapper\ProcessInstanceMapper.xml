<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.workflow.mapper.ProcessInstanceMapper">
    
    <resultMap type="com.tcm.workflow.entity.ProcessInstance" id="ProcessInstanceResult">
        <id property="processInstanceId" column="process_instance_id"/>
        <result property="processDefinitionId" column="process_definition_id"/>
        <result property="processDefinitionKey" column="process_definition_key"/>
        <result property="processDefinitionName" column="process_definition_name"/>
        <result property="businessKey" column="business_key"/>
        <result property="deploymentId" column="deployment_id"/>
        <result property="startUserId" column="start_user_id"/>
        <result property="startUserName" column="start_user_name"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="status" column="status"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="formData" column="form_data"/>
        <result property="currentTaskId" column="current_task_id"/>
        <result property="currentTaskName" column="current_task_name"/>
        <result property="currentAssignee" column="current_assignee"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    
</mapper> 