package com.tcm.common.core.service;

import com.tcm.common.core.config.JdkProperties;
import com.tcm.common.core.constant.ServiceConstants;
import com.tcm.common.core.util.EnvLoader;
import com.tcm.common.core.util.JdkPathValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * 通用服务配置类 负责集中处理所有模块通用的系统配置，如JDK路径、Nacos配置等
 */
public class CommonServiceConfig {
    private static final Logger log = LoggerFactory.getLogger(CommonServiceConfig.class);
    private static final String JDK_HOME_KEY = "java.home";
    private static boolean initialized = false;

    /**
     * 设置应用程序配置
     * 
     * @param appName 应用名称
     */
    public static void setupApplication(String appName) {
        if (initialized) {
            log.info("通用服务配置已初始化，跳过重复配置");
            return;
        }

        try {
            log.info("正在初始化通用服务配置...");

            // 设置应用名称
            System.setProperty("spring.application.name", appName);

            // 1. 设置JDK路径
            setupJdkPath();

            // 2. 设置通用Spring配置
            setupSpringConfig();

            // 3. 设置Nacos配置
            setupNacosConfig();

            // 4. 设置其他服务配置
            setupOtherConfigs();

            initialized = true;
            log.info("通用服务配置初始化完成");
        } catch (Exception e) {
            log.error("初始化通用服务配置时出错", e);
        }
    }

    /**
     * 设置JDK路径
     */
    private static void setupJdkPath() {
        String jdkHome = getJdkHome();

        // 获取当前JDK路径
        String currentJdkHome = System.getProperty(JDK_HOME_KEY);

        // 如果已经是配置的路径，则不再设置
        if (StringUtils.hasText(jdkHome) && StringUtils.hasText(currentJdkHome)) {
            // 标准化路径进行比较
            String normalizedJdkHome = normalizePath(jdkHome);
            String normalizedCurrentJdkHome = normalizePath(currentJdkHome);

            if (normalizedJdkHome.equals(normalizedCurrentJdkHome)) {
                log.info("当前JDK路径已经是配置的路径，无需更改: {}", currentJdkHome);
                return;
            }
        }

        if (StringUtils.hasText(jdkHome)) {
            // 验证JDK路径
            if (JdkPathValidator.isValidJdkPath(jdkHome)) {
                log.info("设置JDK路径: {} (原路径: {})", jdkHome, currentJdkHome);
                System.setProperty(JDK_HOME_KEY, jdkHome);
            } else {
                log.warn("配置的JDK路径无效: {}，将使用当前JDK: {}", jdkHome, currentJdkHome);
            }
        } else {
            log.warn("未配置JDK路径，将使用系统默认JDK: {}", currentJdkHome);
        }
    }

    /**
     * 设置Spring通用配置
     */
    private static void setupSpringConfig() {
        // 1. 禁用不需要的系统功能
        System.setProperty("spring.main.banner-mode", "off");
        System.setProperty("spring.liquibase.enabled", "false");
        System.setProperty("spring.main.allow-bean-definition-overriding", "true");

        // 2. 确保加载通用配置
        System.setProperty("spring.profiles.include", "common");

        // 3. 设置日志级别
        System.setProperty("logging.level.root", "WARN");
        System.setProperty("log4j2.disable", "true");
    }

    /**
     * 设置Nacos配置
     */
    private static void setupNacosConfig() {
        // 设置Nacos服务器地址
        System.setProperty("spring.cloud.nacos.discovery.server-addr", ServiceConstants.Nacos.SERVER_ADDR);
        System.setProperty("spring.cloud.nacos.config.server-addr", ServiceConstants.Nacos.SERVER_ADDR);

        // 设置Nacos认证信息
        System.setProperty("spring.cloud.nacos.discovery.username", ServiceConstants.Nacos.USERNAME);
        System.setProperty("spring.cloud.nacos.discovery.password", ServiceConstants.Nacos.PASSWORD);
        System.setProperty("spring.cloud.nacos.config.username", ServiceConstants.Nacos.USERNAME);
        System.setProperty("spring.cloud.nacos.config.password", ServiceConstants.Nacos.PASSWORD);

        // 设置命名空间
        System.setProperty("spring.cloud.nacos.discovery.namespace", "public");
        System.setProperty("spring.cloud.nacos.config.namespace", "public");

        // 设置服务注册与发现参数
        System.setProperty("spring.cloud.nacos.discovery.enabled", "true");
        System.setProperty("spring.cloud.nacos.discovery.register-enabled", "true");
        System.setProperty("spring.cloud.nacos.discovery.ephemeral", "true");
        System.setProperty("spring.cloud.nacos.discovery.weight", "1");
        System.setProperty("spring.cloud.nacos.discovery.group", "DEFAULT_GROUP");

        // 其他配置
        System.setProperty("spring.cloud.nacos.config.file-extension", "yml");
        System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

        // 设置gRPC相关参数
        String nacosHost = ServiceConstants.Nacos.HOST;
        System.setProperty("nacos.client.grpc.server.host", nacosHost);
        System.setProperty("nacos.client.grpc.server.port", "9848");
        System.setProperty("nacos.client.naming.grpc.enabled", "true");
        System.setProperty("nacos.client.config.grpc.enabled", "true");
    }

    /**
     * 设置其他服务配置
     */
    private static void setupOtherConfigs() {
        // 将必要的服务地址设置为系统属性，供配置文件中的占位符使用
        System.setProperty("tcm.service.nacos.url", ServiceConstants.Nacos.SERVER_ADDR);
        System.setProperty("tcm.service.nacos.username", ServiceConstants.Nacos.USERNAME);
        System.setProperty("tcm.service.nacos.password", ServiceConstants.Nacos.PASSWORD);

        // MySQL配置
        System.setProperty("TCM_MYSQL_HOST", ServiceConstants.Mysql.HOST);
        System.setProperty("TCM_MYSQL_PORT", String.valueOf(ServiceConstants.Mysql.PORT));
        System.setProperty("TCM_MYSQL_USERNAME", ServiceConstants.Mysql.USERNAME);
        System.setProperty("TCM_MYSQL_PASSWORD", ServiceConstants.Mysql.PASSWORD);
        System.setProperty("TCM_MYSQL_DATABASE", ServiceConstants.Mysql.DATABASE);

        // Redis配置
        System.setProperty("TCM_REDIS_HOST", ServiceConstants.Redis.HOST);
        System.setProperty("TCM_REDIS_PORT", String.valueOf(ServiceConstants.Redis.PORT));
        System.setProperty("TCM_REDIS_PASSWORD", ServiceConstants.Redis.PASSWORD);
        System.setProperty("TCM_REDIS_DATABASE", "0");

        // RocketMQ配置
        System.setProperty("tcm.service.rocketMq.nameServerAddress", ServiceConstants.RocketMq.NAME_SERVER_ADDR);

        // Nacos环境变量
        System.setProperty("TCM_NACOS_HOST", ServiceConstants.Nacos.HOST);
        System.setProperty("TCM_NACOS_PORT", String.valueOf(ServiceConstants.Nacos.PORT));
        System.setProperty("TCM_NACOS_USERNAME", ServiceConstants.Nacos.USERNAME);
        System.setProperty("TCM_NACOS_PASSWORD", ServiceConstants.Nacos.PASSWORD);
    }

    /**
     * 获取JDK路径 优先级： 1. 环境变量TCM_JDK_HOME 2. EnvLoader配置 3. ServiceConstants常量
     */
    private static String getJdkHome() {
        String jdkHome = null;

        // 1. 从环境变量获取
        jdkHome = System.getenv("TCM_JDK_HOME");
        if (StringUtils.hasText(jdkHome)) {
            log.info("使用环境变量TCM_JDK_HOME配置的JDK路径: {}", jdkHome);
            return jdkHome;
        }

        // 2. 从EnvLoader获取
        try {
            jdkHome = EnvLoader.getJdkHome();
            if (StringUtils.hasText(jdkHome)) {
                log.info("使用EnvLoader配置的JDK路径: {}", jdkHome);
                return jdkHome;
            }
        } catch (Exception e) {
            log.warn("从EnvLoader获取JDK路径失败", e);
        }

        // 3. 从ServiceConstants获取
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            jdkHome = ServiceConstants.Jdk.WINDOWS_HOME;
        } else if (osName.contains("linux") || osName.contains("unix")) {
            jdkHome = ServiceConstants.Jdk.LINUX_HOME;
        } else {
            jdkHome = ServiceConstants.Jdk.HOME;
        }

        if (StringUtils.hasText(jdkHome)) {
            log.info("使用ServiceConstants配置的JDK路径: {}", jdkHome);
            return jdkHome;
        }

        return null;
    }

    /**
     * 标准化路径，去除末尾的路径分隔符并统一为小写比较
     */
    private static String normalizePath(String path) {
        if (path == null) {
            return "";
        }

        // 去除末尾的路径分隔符
        String normalized = path;
        while (normalized.endsWith("/") || normalized.endsWith("\\")) {
            normalized = normalized.substring(0, normalized.length() - 1);
        }

        // 统一为小写（在Windows下不区分大小写）
        return normalized.toLowerCase();
    }
}