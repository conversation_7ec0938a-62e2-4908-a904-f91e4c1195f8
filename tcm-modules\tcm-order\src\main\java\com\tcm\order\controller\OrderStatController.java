package com.tcm.order.controller;

import com.tcm.common.core.domain.R;
import com.tcm.order.service.IOrderStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 订单统计控制器
 */
@RestController
@RequestMapping("/order/stat")
public class OrderStatController {

    @Autowired
    private IOrderStatService orderStatService;

    /**
     * 获取医生收费总金额
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 收费总金额
     */
    @GetMapping("/amount")
    public R<BigDecimal> getTotalAmount(
            @RequestParam("doctorId") Long doctorId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        BigDecimal amount = orderStatService.getTotalAmount(doctorId, startDate, endDate);
        return R.ok(amount);
    }

    /**
     * 获取医生复诊率
     *
     * @param doctorId  医生ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 复诊率（百分比）
     */
    @GetMapping("/revisit-rate")
    public R<BigDecimal> getRevisitRate(
            @RequestParam("doctorId") Long doctorId,
            @RequestParam("startDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        BigDecimal revisitRate = orderStatService.getRevisitRate(doctorId, startDate, endDate);
        return R.ok(revisitRate);
    }
}
