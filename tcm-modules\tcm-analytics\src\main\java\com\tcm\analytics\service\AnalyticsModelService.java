package com.tcm.analytics.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.analytics.entity.AnalyticsModel;

/**
 * 分析模型服务接口
 */
public interface AnalyticsModelService extends IService<AnalyticsModel> {

    /**
     * 创建分析模型
     * 
     * @param model 模型信息
     * @return 创建的模型
     */
    AnalyticsModel createModel(AnalyticsModel model);

    /**
     * 更新分析模型
     * 
     * @param model 模型信息
     * @return 是否成功
     */
    boolean updateModel(AnalyticsModel model);

    /**
     * 删除分析模型
     * 
     * @param id 模型ID
     * @return 是否成功
     */
    boolean deleteModel(Long id);

    /**
     * 获取模型详情
     * 
     * @param id 模型ID
     * @return 模型详情
     */
    AnalyticsModel getModelDetail(Long id);

    /**
     * 获取用户所有模型
     * 
     * @param userId 用户ID
     * @return 模型列表
     */
    List<AnalyticsModel> getUserModels(Long userId);

    /**
     * 使用模型进行预测
     * 
     * @param modelId   模型ID
     * @param inputData 输入数据
     * @return 预测结果
     */
    Map<String, Object> predict(Long modelId, Map<String, Object> inputData);

    /**
     * 评估模型性能
     * 
     * @param modelId 模型ID
     * @return 评估结果
     */
    Map<String, Object> evaluateModel(Long modelId);

    /**
     * 分页查询模型列表
     * 
     * @param page   分页参数
     * @param params 查询参数
     * @return 分页结果
     */
    Page<AnalyticsModel> listModelsByPage(Page<AnalyticsModel> page, Map<String, Object> params);

    /**
     * 根据类型获取模型列表
     * 
     * @param modelType 模型类型
     * @return 模型列表
     */
    List<AnalyticsModel> listModelsByType(Integer modelType);

    /**
     * 根据创建者获取模型列表
     * 
     * @param creator 创建者
     * @return 模型列表
     */
    List<AnalyticsModel> listModelsByCreator(String creator);
}