spring:
  application:
    name: tcm-evaluation
  profiles:
    active: dev
  cloud:
    nacos:
      config:
        server-addr: ${tcm.service.nacos.host:localhost}:${tcm.service.nacos.port:8848}
        file-extension: yml
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username:nacos}
        password: ${tcm.service.nacos.password:nacos}
        shared-configs:
          - data-id: tcm-common.yml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        server-addr: ${tcm.service.nacos.host:localhost}:${tcm.service.nacos.port:8848}
        namespace: ${tcm.service.nacos.namespace:}
        username: ${tcm.service.nacos.username:nacos}
        password: ${tcm.service.nacos.password:nacos}
        enabled: true 