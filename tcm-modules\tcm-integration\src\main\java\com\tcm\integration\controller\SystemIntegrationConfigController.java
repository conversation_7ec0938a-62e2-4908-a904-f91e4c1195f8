package com.tcm.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.integration.dto.Result;
import com.tcm.integration.dto.SystemIntegrationConfigDTO;
import com.tcm.integration.entity.SystemIntegrationConfig;
import com.tcm.integration.service.SystemIntegrationConfigService;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统集成配置控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/integration/config")
public class SystemIntegrationConfigController {

    @Autowired
    private SystemIntegrationConfigService systemIntegrationConfigService;

    /**
     * 分页查询系统配置
     */
    @GetMapping("/page")
    public Result<IPage<SystemIntegrationConfig>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String systemName,
            @RequestParam(required = false) String systemCode,
            @RequestParam(required = false) String systemType) {
        IPage<SystemIntegrationConfig> page = systemIntegrationConfigService.page(pageNum, pageSize, systemName,
                systemCode, systemType);
        return Result.success(page);
    }

    /**
     * 根据ID获取详情
     */
    @GetMapping("/{id}")
    public Result<SystemIntegrationConfig> getById(@PathVariable Long id) {
        SystemIntegrationConfig config = systemIntegrationConfigService.getById(id);
        if (config == null) {
            return Result.error("系统配置不存在");
        }
        return Result.success(config);
    }

    /**
     * 根据系统编码获取配置
     */
    @GetMapping("/code/{systemCode}")
    public Result<SystemIntegrationConfig> getBySystemCode(@PathVariable String systemCode) {
        SystemIntegrationConfig config = systemIntegrationConfigService.getBySystemCode(systemCode);
        if (config == null) {
            return Result.error("系统配置不存在");
        }
        return Result.success(config);
    }

    /**
     * 新增系统配置
     */
    @PostMapping
    public Result<Boolean> add(@RequestBody @Valid SystemIntegrationConfigDTO dto) {
        try {
            boolean result = systemIntegrationConfigService.add(dto);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 更新系统配置
     */
    @PutMapping
    public Result<Boolean> update(@RequestBody @Valid SystemIntegrationConfigDTO dto) {
        try {
            if (dto.getId() == null) {
                return Result.error("ID不能为空");
            }
            boolean result = systemIntegrationConfigService.update(dto);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 删除系统配置
     */
    @DeleteMapping("/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        try {
            boolean result = systemIntegrationConfigService.deleteById(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 启用系统配置
     */
    @PutMapping("/enable/{id}")
    public Result<Boolean> enable(@PathVariable Long id) {
        try {
            boolean result = systemIntegrationConfigService.enable(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 禁用系统配置
     */
    @PutMapping("/disable/{id}")
    public Result<Boolean> disable(@PathVariable Long id) {
        try {
            boolean result = systemIntegrationConfigService.disable(id);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 测试系统连接
     */
    @GetMapping("/test/{id}")
    public Result<Boolean> testConnection(@PathVariable Long id) {
        try {
            boolean result = systemIntegrationConfigService.testConnection(id);
            if (result) {
                return Result.success(true);
            } else {
                return Result.error("连接失败，请检查配置信息");
            }
        } catch (Exception e) {
            return Result.error("连接失败: " + e.getMessage());
        }
    }
}