package com.tcm.monitor;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
// import com.tcm.common.security.annotation.EnableCustomConfig;
// import com.tcm.common.security.annotation.EnableTcmFeignClients;
// import com.tcm.common.swagger.annotation.EnableCustomSwagger2;

/**
 * 监控服务启动类
 *
 * <AUTHOR>
 */
// @EnableCustomConfig
// @EnableCustomSwagger2
// @EnableTcmFeignClients
@SpringBootApplication(exclude = {
        org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.server.ManagementContextAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.web.servlet.ServletManagementContextAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthEndpointAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.health.HealthContributorAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration.class })
@EnableDiscoveryClient
@ComponentScan(basePackages = { "com.tcm.monitor", "com.tcm.common" })
@MapperScan("com.tcm.monitor.mapper")
public class TcmMonitorApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(TcmMonitorApplication.class);
    // 用于存储实际端口号
    private static int actualServerPort = 0;

    // 通过静态代码块预先设置关键系统属性，确保在任何类加载之前执行
    static {
        // ===== SLF4J日志冲突解决 =====
        // 1. 确保使用logback作为唯一的SLF4J实现
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "WARN");
        System.setProperty("org.slf4j.simpleLogger.showThreadName", "false");
        System.setProperty("org.slf4j.simpleLogger.showLogName", "false");
        System.setProperty("org.slf4j.simpleLogger.showShortLogName", "true");

        // 2. 彻底禁用Log4j
        System.setProperty("log4j2.disable.jmx", "true");
        System.setProperty("log4j.configuration", "log4j-disabled.properties");
        System.setProperty("org.apache.logging.log4j.simplelog.StatusLogger.level", "OFF");

        // 3. 强制所有其他日志框架重定向到SLF4J
        System.setProperty("java.util.logging.manager", "org.slf4j.bridge.SLF4JBridgeHandler");
        System.setProperty("org.jboss.logging.provider", "slf4j");
        System.setProperty("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SLF4JLogFactory");

        // ===== Spark和Spring Boot类加载冲突解决 =====
        // 1. 配置类加载器隔离，防止重复加载冲突类
        System.setProperty("spring.classloader.overrideStandardClassLoader", "false");
        System.setProperty("spring.boot.register-shutdown-hook", "false");

        // ===== PerfMark相关冲突解决 =====
        // 彻底禁用PerfMark实现，避免类加载问题
        System.setProperty("io.perfmark.PerfMark.impl", "io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.impl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.PerfMark.startEnabled", "false");

        // 设置一个假的类名，防止通过Class.forName()加载原始类
        System.setProperty("com.alibaba.nacos.shaded.io.perfmark.impl.SecretPerfMarkImpl$PerfMarkImpl",
                "com.alibaba.nacos.shaded.io.perfmark.impl.DisabledPerfMarkImpl");

        // 禁用gRPC通信，强制使用HTTP
        System.setProperty("nacos.client.naming.grpc.enabled", "false");
        System.setProperty("nacos.client.config.grpc.enabled", "false");

        // 设置应用基础信息
        System.setProperty("spring.application.name", "tcm-monitor");

        // 设置默认端口（如果未指定）
        if (System.getProperty("server.port") == null && System.getenv("TCM_MONITOR_PORT") == null) {
            System.setProperty("server.port", "${tcm.service.monitor.port:9310}");
        }
    }

    public static void main(String[] args) {
        try {
            // 1. 日志配置 - 彻底解决日志冲突问题

            // 1.1 清理并禁用所有可能的Log4j相关配置
            System.setProperty("log4j2.disable", "true"); // 强制禁用Log4j2
            System.setProperty("log4j.configurationFile", "log4j2-empty.xml"); // 使用空的Log4j2配置
            System.setProperty("log4j.skipJansi", "true"); // 跳过Jansi初始化
            System.setProperty("log4j2.statusLogger.level", "OFF"); // 禁用Log4j2状态日志

            // 1.2 防止SLF4J日志绑定冲突
            try {
                // 尝试卸载可能已经加载的SLF4J绑定
                Class.forName("org.slf4j.LoggerFactory").getDeclaredMethod("reset").invoke(null);
            } catch (Exception e) {
                // 忽略任何反射错误，继续执行
            }

            // 1.3 配置日志级别和其他设置
            System.setProperty("org.jboss.logging.provider", "slf4j"); // 强制JBoss使用SLF4J
            System.setProperty("logging.level.root", "WARN"); // 降低根日志级别以减少干扰
            System.setProperty("logging.level.com.tcm", "INFO"); // 设置TCM模块日志级别
            System.setProperty("spring.main.banner-mode", "off"); // 关闭Spring Boot的横幅

            // 1.4 配置SLF4J桥接器
            try {
                // 尝试初始化SLF4J桥接器，将JUL日志重定向到SLF4J
                Class.forName("org.slf4j.bridge.SLF4JBridgeHandler").getDeclaredMethod("removeHandlersForRootLogger")
                        .invoke(null);
                Class.forName("org.slf4j.bridge.SLF4JBridgeHandler").getDeclaredMethod("install").invoke(null);
            } catch (Exception e) {
                // 忽略任何反射错误，继续执行
            }

            // 2. 关闭自动配置 - 禁用不需要的自动配置以避免启动问题
            System.setProperty("spring.liquibase.enabled", "false"); // 避免依赖错误
            System.setProperty("spring.main.allow-bean-definition-overriding", "true"); // 允许bean定义覆盖

            // 3. Nacos配置 - 彻底解决Nacos连接问题
            // 禁用Nacos配置导入检查，以避免启动错误
            System.setProperty("spring.cloud.nacos.config.import-check.enabled", "false");

            // 优先设置固定端口，确保Spring Boot使用这个端口启动
            String fixedPort = System.getProperty("server.port");
            if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                fixedPort = System.getProperty("tcm.service.monitor.port");
                if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                    fixedPort = System.getenv("TCM_MONITOR_PORT");
                    if (fixedPort == null || fixedPort.isEmpty() || "0".equals(fixedPort)) {
                        fixedPort = "${tcm.service.monitor.port:9310}";
                    }
                }
                System.setProperty("server.port", fixedPort);
            }

            // 打印将要使用的端口
            LOGGER.info("Starting server with port: {}", System.getProperty("server.port"));

            // 设置统一配置文件
            System.setProperty("spring.config.import", "optional:classpath:/config/tcm-common.yml");

            // 解决Java反射和安全限制问题
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("io.netty.tryReflectionSetAccessible", "true");

            // 强制禁用所有netty unsafe访问
            System.setProperty("io.netty.noUnsafe", "true");

            // 禁用Spring Boot Actuator相关功能
            System.setProperty("management.endpoints.enabled-by-default", "false");
            System.setProperty("management.endpoint.health.enabled", "false");
            System.setProperty("management.endpoint.info.enabled", "false");
            System.setProperty("management.endpoints.web.exposure.include", "");
            System.setProperty("management.endpoints.jmx.exposure.include", "");
            System.setProperty("spring.jmx.enabled", "false");

            // 启动应用程序
            ConfigurableApplicationContext context = SpringApplication.run(TcmMonitorApplication.class, args);

            // 获取实际使用的端口（包括随机分配的端口）
            String serverPort;

            // 如果PortListener已经捕获到了实际端口，则使用它
            if (actualServerPort > 0) {
                serverPort = String.valueOf(actualServerPort);
            } else {
                // 尝试从环境中获取端口
                serverPort = context.getEnvironment().getProperty("server.port");

                // 如果环境中没有端口配置，则尝试从系统属性和环境变量获取
                if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                    serverPort = System.getProperty("tcm.service.monitor.port");

                    // 如果系统属性中没有端口配置，则尝试从环境变量获取
                    if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                        serverPort = System.getenv("TCM_MONITOR_PORT");

                        // 如果环境变量中没有端口配置，则使用默认端口
                        if (serverPort == null || serverPort.isEmpty() || "0".equals(serverPort)) {
                            serverPort = "${tcm.service.monitor.port:9310}";
                        }
                    }
                }
            }

            // 确保端口被设置到系统属性中（使之可被其他组件获取）
            System.setProperty("server.port", serverPort);

            LOGGER.info("=====================================================");
            LOGGER.info("           TCM Monitor Service Started!             ");
            LOGGER.info("=====================================================");
            LOGGER.info("Application Name: {}", context.getEnvironment().getProperty("spring.application.name"));
            LOGGER.info("Application Port: {}", serverPort);

            // 安全地获取激活的 profile
            String[] activeProfiles = context.getEnvironment().getActiveProfiles();
            String activeProfile = activeProfiles.length > 0 ? activeProfiles[0] : "default";
            LOGGER.info("Active Profile: {}", activeProfile);

            System.out.println("  _____   _____ __  __   __  __             _ _             ");
            System.out.println(" |_   _| / ____|  \\/  | |  \\/  |           (_) |            ");
            System.out.println("   | |  | |    | \\  / | | \\  / | ___  _ __  _| |_ ___  _ __ ");
            System.out.println("   | |  | |    | |\\/| | | |\\/| |/ _ \\| '_ \\| | __/ _ \\| '__|");
            System.out.println("  _| |_ | |____| |  | | | |  | | (_) | | | | | || (_) | |   ");
            System.out.println(" |_____| \\_____|_|  |_| |_|  |_|\\___/|_| |_|_|\\__\\___/|_|   ");
            System.out.println("                                                             ");

            System.out.println("  Started successfully: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort
                    + context.getEnvironment().getProperty("server.servlet.context-path", "/monitor"));
            System.out.println("  Swagger UI: http://" + (System.getProperty("server.address", "localhost"))
                    + ":" + serverPort + "/monitor/swagger-ui/index.html");
            System.out.println("=====================================================================");
        } catch (Exception e) {
            LOGGER.error("启动失败: {}", e.getMessage(), e);
            System.err.println("启动过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 端口监听器组件，用于捕获Web服务器初始化事件并获取实际端口
     */
    @Component
    public static class PortListener {

        @EventListener
        public void onApplicationEvent(ServletWebServerInitializedEvent event) {
            // 获取实际分配的端口
            ServletWebServerApplicationContext applicationContext = event.getApplicationContext();
            actualServerPort = applicationContext.getWebServer().getPort();
            LOGGER.info("Web server initialized with port: {}", actualServerPort);
        }
    }
} 