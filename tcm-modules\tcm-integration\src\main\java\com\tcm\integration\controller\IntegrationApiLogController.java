package com.tcm.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.tcm.integration.dto.Result;
import com.tcm.integration.entity.IntegrationApiLog;
import com.tcm.integration.service.IntegrationApiLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 系统集成API日志控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/integration/log")
public class IntegrationApiLogController {

    @Autowired
    private IntegrationApiLogService integrationApiLogService;

    /**
     * 分页查询API日志
     *
     * @param pageNum    页码
     * @param pageSize   每页大小
     * @param systemCode 系统编码
     * @param apiName    接口名称
     * @param status     状态
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 分页结果
     */
    @GetMapping("/page")
    public Result<IPage<IntegrationApiLog>> page(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String systemCode,
            @RequestParam(required = false) String apiName,
            @RequestParam(required = false) Integer status,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        IPage<IntegrationApiLog> page = integrationApiLogService.page(pageNum, pageSize, systemCode, apiName, status,
                startTime, endTime);
        return Result.success(page);
    }

    /**
     * 获取API统计数据
     *
     * @param systemCode 系统编码
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @return 统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getApiStatistics(
            @RequestParam(required = false) String systemCode,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {

        Map<String, Object> statistics = integrationApiLogService.getApiStatistics(systemCode, startTime, endTime);
        return Result.success(statistics);
    }

    /**
     * 清理过期日志
     *
     * @param days 保留天数
     * @return 清理数量
     */
    @DeleteMapping("/clean")
    public Result<Integer> cleanExpiredLogs(@RequestParam(defaultValue = "30") Integer days) {
        int count = integrationApiLogService.cleanExpiredLogs(days);
        return Result.success(count);
    }
}