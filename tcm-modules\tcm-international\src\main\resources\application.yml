server:
  port: ${tcm.service.international.port:${TCM_INTERNATIONAL_PORT:9303}}
  servlet:
    context-path: /international

spring:
  application:
    name: tcm-international
  # 引入公共配置
  profiles:
    include: common
    
  # 允许Bean覆盖，解决RedisTemplate等Bean定义冲突问题
  main:
    allow-bean-definition-overriding: true
    banner-mode: off
    
  # 数据库配置 - 使用application-common.yml中的配置
  datasource:
    # 只配置额外的连接池参数
    druid:
      # 高可用性配置
      keep-alive: true
      # 缓解连接失败问题
      test-on-borrow: true
  
  # 数据库初始化配置 - 替代已废弃的datasource.continue-on-error
  sql:
    init:
      continue-on-error: true
      
  # 暂时禁用Liquibase自动配置
  liquibase:
    enabled: false
      
  # Redis配置
  redis:
    # 从统一配置中获取
    host: ${tcm.service.redis.host}
    port: ${tcm.service.redis.port}
    database: ${tcm.service.redis.database:0}
    password: ${tcm.service.redis.password:}
    timeout: ${tcm.service.redis.timeout:10000}
    lettuce:
      pool:
        max-active: ${tcm.service.redis.pool.max-active:8}
        max-wait: ${tcm.service.redis.pool.max-wait:-1}
        max-idle: ${tcm.service.redis.pool.max-idle:8}
        min-idle: ${tcm.service.redis.pool.min-idle:0}
      
  # Nacos配置
  cloud:
    nacos:
      discovery:
        # 服务注册配置
        server-addr: ${tcm.service.nacos.server-addr}
        namespace: ${tcm.service.nacos.namespace}
        group: ${tcm.service.nacos.group}
        # 防止注册失败导致应用启动失败
        fail-fast: false
        # 开启资源保护
        ephemeral: true
      config:
        # 配置中心配置
        server-addr: ${tcm.service.nacos.server-addr}
        namespace: ${tcm.service.nacos.namespace}
        group: ${tcm.service.nacos.group}
        file-extension: yaml
        # 支持共享配置
        shared-configs:
          - data-id: tcm-common.yaml
            group: ${tcm.service.nacos.group}
            refresh: true

# MyBatis Plus配置会从common配置中继承

# Knife4j配置 - 不使用硬编码值
knife4j:
  enable: true
  setting:
    language: zh-CN
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 国际化服务API
    enable-host-text: ${server.servlet.context-path}
    enable-footer-custom: true
    footer-custom-content: 中医诊疗系统 - 国际化服务
  production: false

# 日志配置
logging:
  level:
    com.tcm: debug
    org.springframework: warn 

# RocketMQ配置 - 使用统一配置中的参数
rocketmq:
  name-server: ${tcm.service.rocketMq.name-server}
  producer:
    group: ${spring.application.name}-producer-group