package com.tcm.log.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.log.domain.SysLog;
import com.tcm.log.service.ISysLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统日志控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/log/sys")
public class SysLogController {
    
    @Autowired
    private ISysLogService sysLogService;
    
    /**
     * 获取系统日志列表
     */
    @GetMapping("/list")
    public Map<String, Object> list(
            @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
            @RequestParam(value = "module", required = false) String module,
            @RequestParam(value = "operationType", required = false) String operationType,
            @RequestParam(value = "username", required = false) String username,
            @RequestParam(value = "status", required = false) Integer status) {
        
        LambdaQueryWrapper<SysLog> wrapper = new LambdaQueryWrapper<>();
        if (module != null && !module.isEmpty()) {
            wrapper.like(SysLog::getModule, module);
        }
        if (operationType != null && !operationType.isEmpty()) {
            wrapper.eq(SysLog::getOperationType, operationType);
        }
        if (username != null && !username.isEmpty()) {
            wrapper.like(SysLog::getUsername, username);
        }
        if (status != null) {
            wrapper.eq(SysLog::getStatus, status);
        }
        wrapper.orderByDesc(SysLog::getOperateTime);
        
        Page<SysLog> page = new Page<>(pageNum, pageSize);
        Page<SysLog> result = sysLogService.page(page, wrapper);
        
        Map<String, Object> response = new HashMap<>();
        response.put("code", 200);
        response.put("msg", "查询成功");
        response.put("data", result.getRecords());
        response.put("total", result.getTotal());
        response.put("pageNum", result.getCurrent());
        response.put("pageSize", result.getSize());
        
        return response;
    }
    
    /**
     * 获取系统日志详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getInfo(@PathVariable Long id) {
        SysLog sysLog = sysLogService.getById(id);
        Map<String, Object> response = new HashMap<>();
        
        if (sysLog != null) {
            response.put("code", 200);
            response.put("msg", "查询成功");
            response.put("data", sysLog);
        } else {
            response.put("code", 404);
            response.put("msg", "日志不存在");
        }
        
        return response;
    }
    
    /**
     * 新增系统日志
     */
    @PostMapping
    public Map<String, Object> add(@RequestBody SysLog sysLog) {
        boolean result = sysLogService.saveLog(sysLog);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "保存成功");
        } else {
            response.put("code", 500);
            response.put("msg", "保存失败");
        }
        
        return response;
    }
    
    /**
     * 删除系统日志
     */
    @DeleteMapping("/{ids}")
    public Map<String, Object> remove(@PathVariable Long[] ids) {
        boolean result = sysLogService.deleteLogs(ids);
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "删除成功");
        } else {
            response.put("code", 500);
            response.put("msg", "删除失败");
        }
        
        return response;
    }
    
    /**
     * 清空系统日志
     */
    @DeleteMapping("/clear")
    public Map<String, Object> clear() {
        boolean result = sysLogService.clearLogs();
        Map<String, Object> response = new HashMap<>();
        
        if (result) {
            response.put("code", 200);
            response.put("msg", "清空成功");
        } else {
            response.put("code", 500);
            response.put("msg", "清空失败");
        }
        
        return response;
    }
} 