package com.tcm.medication.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.registry.NacosAutoServiceRegistration;
import com.alibaba.cloud.nacos.registry.NacosRegistration;
import com.alibaba.cloud.nacos.registry.NacosServiceRegistry;

/**
 * Nacos服务发现配置类 在应用启动完成后进行服务注册，避免因Nacos连接问题导致应用启动失败
 */
@Configuration
public class NacosDiscoveryConfig implements ApplicationRunner {
    private static final Logger LOGGER = LoggerFactory.getLogger(NacosDiscoveryConfig.class);

    @Autowired(required = false)
    private NacosServiceRegistry nacosServiceRegistry;

    @Autowired(required = false)
    private NacosRegistration nacosRegistration;

    @Autowired(required = false)
    private NacosDiscoveryProperties nacosDiscoveryProperties;

    @Autowired(required = false)
    private NacosAutoServiceRegistration nacosAutoServiceRegistration;

    @Override
    public void run(ApplicationArguments args) {
        if (nacosServiceRegistry != null && nacosRegistration != null) {
            LOGGER.info("Nacos服务发现配置: 延迟注册服务到Nacos");

            // 使用定制逻辑注册服务，处理异常
            try {
                if (nacosAutoServiceRegistration != null) {
                    nacosAutoServiceRegistration.start();
                    LOGGER.info("成功注册服务到Nacos: {}", nacosDiscoveryProperties.getService());
                } else {
                    LOGGER.warn("无法注册服务到Nacos，NacosAutoServiceRegistration不可用");
                }
            } catch (Exception e) {
                LOGGER.error("注册服务到Nacos时发生错误: {}", e.getMessage());
                LOGGER.info("应用将继续启动，但服务可能未在Nacos中注册");
            }
        } else {
            LOGGER.warn("Nacos服务注册组件不可用，跳过服务注册");
        }
    }
}