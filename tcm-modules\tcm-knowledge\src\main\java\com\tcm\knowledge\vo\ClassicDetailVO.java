package com.tcm.knowledge.vo;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 经典著作详情VO
 */
@Data
public class ClassicDetailVO {
    /**
     * 著作ID
     */
    private Long id;
    
    /**
     * 著作名称
     */
    private String title;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 朝代
     */
    private String dynasty;
    
    /**
     * 成书年代
     */
    private String year;
    
    /**
     * 内容简介
     */
    private String introduction;
    
    /**
     * 全文内容
     */
    private String content;
    
    /**
     * 章节列表
     */
    private List<ChapterVO> chapters;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 章节VO
     */
    @Data
    public static class ChapterVO {
        /**
         * 章节ID
         */
        private Long id;
        
        /**
         * 章节标题
         */
        private String title;
        
        /**
         * 章节内容
         */
        private String content;
        
        /**
         * 排序
         */
        private Integer sort;
    }
}
