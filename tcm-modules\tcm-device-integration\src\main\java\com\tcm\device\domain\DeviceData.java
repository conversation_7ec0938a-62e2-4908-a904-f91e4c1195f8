package com.tcm.device.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.io.Serializable;
import java.util.Date;

/**
 * 设备数据实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("device_data")
@EqualsAndHashCode(callSuper = false)
public class DeviceData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 数据ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备ID
     */
    private Long deviceId;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 数据值
     */
    private String dataValue;

    /**
     * 单位
     */
    private String unit;

    /**
     * 采集时间
     */
    private Date collectTime;

    /**
     * 状态（0正常 1异常）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;
}