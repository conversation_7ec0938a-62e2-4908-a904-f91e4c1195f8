package com.tcm.common.core.utils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期工具类
 */
public class DateUtils {

    public static final String YYYY = "yyyy";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static final String[] PARSE_PATTERNS = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"
    };

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd HH:mm:ss
     */
    public static String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取当前日期时间字符串, 默认格式为yyyyMMddHHmmss
     */
    public static String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    /**
     * 获取当前日期时间字符串, 按指定格式返回
     */
    public static String dateTimeNow(final String format) {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));
    }

    /**
     * 获取当前Date型日期
     */
    public static Date getNowDate() {
        return new Date();
    }

    /**
     * 获取日期路径, 按照yyyy/MM/dd格式返回
     */
    public static String datePath() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
    }

    /**
     * 获取日期路径, 按照yyyy/MM格式返回
     */
    public static String dateMonth() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM"));
    }

    /**
     * 格式化日期, 按照yyyy-MM-dd格式返回
     */
    public static String formatDate(final Date date) {
        return new SimpleDateFormat(YYYY_MM_DD).format(date);
    }

    /**
     * 格式化日期时间, 按照yyyy-MM-dd HH:mm:ss格式返回
     */
    public static String formatDateTime(final Date date) {
        return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(date);
    }

    /**
     * 格式化日期, 按照指定格式返回
     */
    public static String formatDate(final Date date, final String format) {
        return new SimpleDateFormat(format).format(date);
    }
}