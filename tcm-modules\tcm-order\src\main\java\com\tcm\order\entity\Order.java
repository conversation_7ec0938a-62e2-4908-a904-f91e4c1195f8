package com.tcm.order.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单实体类
 *
 * <AUTHOR> Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("tcm_order")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    @TableId(value = "order_id", type = IdType.AUTO)
    private Long orderId;

    /**
     * 订单编号（业务编号，用于前端展示）
     */
    private String orderNo;
    
    /**
     * 订单类型：1-门诊处方订单 2-住院处方订单 3-药材订单 4-远程会诊订单
     */
    private Integer orderType;
    
    /**
     * 患者ID
     */
    private Long patientId;
    
    /**
     * 患者姓名
     */
    private String patientName;
    
    /**
     * 医生ID
     */
    private Long doctorId;
    
    /**
     * 医生姓名
     */
    private String doctorName;
    
    /**
     * 处方ID
     */
    private Long prescriptionId;
    
    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;
    
    /**
     * 实付金额
     */
    private BigDecimal payAmount;
    
    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;
    
    /**
     * 支付方式：1-现金 2-医保 3-微信 4-支付宝 5-银行卡
     */
    private Integer paymentMethod;
    
    /**
     * 支付状态：0-未支付 1-已支付 2-已退款 3-部分退款
     */
    private Integer paymentStatus;
    
    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;
    
    /**
     * 支付交易号
     */
    private String transactionNo;
    
    /**
     * 订单状态：0-待支付 1-已支付 2-配药中 3-待取药 4-已完成 5-已取消 6-已退款
     */
    private Integer orderStatus;
    
    /**
     * 取药方式：1-医院自取 2-快递配送
     */
    private Integer deliveryMethod;
    
    /**
     * 配送地址ID
     */
    private Long addressId;
    
    /**
     * 收货人姓名
     */
    private String receiverName;
    
    /**
     * 收货人电话
     */
    private String receiverPhone;
    
    /**
     * 收货地址
     */
    private String receiverAddress;
    
    /**
     * 物流公司
     */
    private String expressCompany;
    
    /**
     * 物流单号
     */
    private String expressNo;
    
    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;
    
    /**
     * 收货时间
     */
    private LocalDateTime receiveTime;
    
    /**
     * 订单备注
     */
    private String remark;
    
    /**
     * 取消原因
     */
    private String cancelReason;
    
    /**
     * 退款原因
     */
    private String refundReason;
    
    /**
     * 退款时间
     */
    private LocalDateTime refundTime;
    
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    
    /**
     * 退款交易号
     */
    private String refundTransactionNo;
    
    /**
     * 逻辑删除：0-未删除 1-已删除
     */
    @TableLogic
    private Integer delFlag;
    
    /**
     * 创建人ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;
    
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    
    /**
     * 更新人ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
