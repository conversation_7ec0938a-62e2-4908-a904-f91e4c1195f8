package com.tcm.report.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType;

import javax.sql.DataSource;

/**
 * 数据源配置
 * 
 * <AUTHOR>
 */
@Configuration
public class DataSourceConfig {

    /**
     * 临时H2内存数据库配置，用于开发测试
     * 当没有配置真实数据源时使用
     */
    @Bean
    @ConditionalOnProperty(name = "spring.datasource.url", matchIfMissing = true)
    public DataSource dataSource() {
        return new EmbeddedDatabaseBuilder()
                .setType(EmbeddedDatabaseType.H2)
                .setName("tcm_report_temp")
                .build();
    }
}
