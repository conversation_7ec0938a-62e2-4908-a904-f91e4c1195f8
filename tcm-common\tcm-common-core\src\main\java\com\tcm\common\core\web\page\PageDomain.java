package com.tcm.common.core.web.page;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分页数据
 */
@Data
@Accessors(chain = true)
public class PageDomain {
    /**
     * 当前记录起始索引
     */
    private Integer pageNum;

    /**
     * 每页显示记录数
     */
    private Integer pageSize;

    /**
     * 排序列
     */
    private String orderByColumn;

    /**
     * 排序的方向desc或者asc
     */
    private String isAsc;

    /**
     * 分页参数合理化
     */
    private Boolean reasonable = true;

    public Integer getPageNum() {
        return pageNum == null ? 1 : pageNum;
    }

    public Integer getPageSize() {
        return pageSize == null ? 10 : pageSize;
    }

    public String getOrderBy() {
        if (orderByColumn != null && !orderByColumn.isEmpty()) {
            return orderByColumn + " " + (isAsc == null || "asc".equalsIgnoreCase(isAsc) ? "asc" : "desc");
        }
        return null;
    }
}