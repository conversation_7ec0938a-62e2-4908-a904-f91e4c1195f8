package com.tcm.schedule.controller;

import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tcm.common.core.annotation.Log;
import com.tcm.common.core.domain.PageVO;
import com.tcm.common.core.domain.Result;
import com.tcm.schedule.dto.SysLogOperationDTO;
import com.tcm.schedule.service.SysLogOperationService;
import com.tcm.schedule.vo.SysLogOperationVO;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

/**
 * 系统操作日志控制器
 */
@Tag(name = "操作日志接口")
@RestController
@RequestMapping("/operlog")
@RequiredArgsConstructor
public class SysLogOperationController {

    private final SysLogOperationService sysLogOperationService;

    /**
     * 获取操作日志列表
     */
    @Operation(summary = "获取操作日志列表")
    @GetMapping("/list")
    public Result<PageVO<SysLogOperationVO>> list(SysLogOperationDTO dto) {
        PageVO<SysLogOperationVO> pageVO = sysLogOperationService.selectLogPage(dto);
        return Result.success(pageVO);
    }

    /**
     * 获取操作日志详细信息
     */
    @Operation(summary = "获取操作日志详细信息")
    @GetMapping("/{id}")
    public Result<SysLogOperationVO> getInfo(@Parameter(description = "日志ID") @PathVariable Long id) {
        SysLogOperationVO sysLogOperation = sysLogOperationService.getLogById(id);
        return Result.success(sysLogOperation);
    }

    /**
     * 删除操作日志
     */
    @Operation(summary = "删除操作日志")
    @Log(module = "日志管理", operateType = 3, description = "删除操作日志")
    @DeleteMapping("/{ids}")
    public Result<?> remove(@Parameter(description = "日志ID串，多个以逗号分隔") @PathVariable Long[] ids) {
        return sysLogOperationService.deleteLogByIds(ids) ? Result.success() : Result.error("删除操作日志失败");
    }

    /**
     * 清空操作日志
     */
    @Operation(summary = "清空操作日志")
    @Log(module = "日志管理", operateType = 9, description = "清空操作日志")
    @DeleteMapping("/clean")
    public Result<?> clean() {
        return sysLogOperationService.cleanLog() ? Result.success() : Result.error("清空操作日志失败");
    }
}
