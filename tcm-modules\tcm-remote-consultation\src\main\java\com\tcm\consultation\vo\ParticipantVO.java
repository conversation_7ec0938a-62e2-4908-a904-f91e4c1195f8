package com.tcm.consultation.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 会诊参与人视图对象
 */
@Data
public class ParticipantVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参与记录ID
     */
    private Long participantId;

    /**
     * 会诊记录ID
     */
    private Long consultationId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 医生职称
     */
    private String doctorTitle;

    /**
     * 医生所属机构
     */
    private String doctorHospital;

    /**
     * 医生科室
     */
    private String doctorDepartment;

    /**
     * 参与类型（1：主诊医生，2：会诊医生，3：专家）
     */
    private Integer type;

    /**
     * 参与类型名称
     */
    private String typeName;

    /**
     * 参与状态（0：待响应，1：已接受，2：已拒绝，3：已参与）
     */
    private Integer status;

    /**
     * 参与状态名称
     */
    private String statusName;

    /**
     * 是否在线（0：离线，1：在线）
     */
    private Integer online;

    /**
     * 加入时间
     */
    private Date joinTime;

    /**
     * 离开时间
     */
    private Date leaveTime;

    /**
     * 会诊意见
     */
    private String opinion;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 获取类型名称
     */
    public String getTypeName() {
        if (type == null) {
            return "";
        }
        switch (type) {
            case 1:
                return "主诊医生";
            case 2:
                return "会诊医生";
            case 3:
                return "专家";
            default:
                return "未知类型";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (status == null) {
            return "";
        }
        switch (status) {
            case 0:
                return "待响应";
            case 1:
                return "已接受";
            case 2:
                return "已拒绝";
            case 3:
                return "已参与";
            default:
                return "未知状态";
        }
    }
}