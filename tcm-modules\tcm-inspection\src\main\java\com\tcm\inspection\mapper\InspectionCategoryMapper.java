package com.tcm.inspection.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tcm.inspection.domain.InspectionCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 检查分类Mapper接口
 */
public interface InspectionCategoryMapper extends BaseMapper<InspectionCategory> {

    /**
     * 查询检查分类列表
     *
     * @param inspectionCategory 检查分类信息
     * @return 检查分类集合
     */
    List<InspectionCategory> selectInspectionCategoryList(InspectionCategory inspectionCategory);

    /**
     * 分页查询检查分类列表
     *
     * @param page               分页信息
     * @param inspectionCategory 检查分类信息
     * @return 检查分类集合
     */
    IPage<InspectionCategory> selectInspectionCategoryPage(Page<InspectionCategory> page, @Param("category") InspectionCategory inspectionCategory);

    /**
     * 根据ID查询检查分类
     *
     * @param categoryId 检查分类ID
     * @return 检查分类信息
     */
    InspectionCategory selectInspectionCategoryById(Long categoryId);

    /**
     * 根据父分类ID查询检查分类列表
     *
     * @param parentId 父分类ID
     * @return 检查分类集合
     */
    List<InspectionCategory> selectInspectionCategoryByParentId(Long parentId);

    /**
     * 检查分类名称是否存在
     *
     * @param categoryName 检查分类名称
     * @return 结果
     */
    int checkCategoryNameUnique(String categoryName);

    /**
     * 检查分类编码是否存在
     *
     * @param categoryCode 检查分类编码
     * @return 结果
     */
    int checkCategoryCodeUnique(String categoryCode);
} 