<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.device.mapper.DeviceDataMapper">

    <!-- 基本结果集映射 -->
    <resultMap id="BaseResultMap" type="com.tcm.device.domain.DeviceData">
        <id column="id" property="id"/>
        <result column="device_id" property="deviceId"/>
        <result column="data_type" property="dataType"/>
        <result column="data_value" property="dataValue"/>
        <result column="unit" property="unit"/>
        <result column="collect_time" property="collectTime"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <!-- 基本列 -->
    <sql id="Base_Column_List">
        id, device_id, data_type, data_value, unit, collect_time, status, create_time
    </sql>
    
</mapper> 