package com.tcm.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tcm.log.domain.SysLog;

/**
 * 系统日志服务接口
 * 
 * <AUTHOR>
 */
public interface ISysLogService extends IService<SysLog> {
    
    /**
     * 保存系统日志
     * 
     * @param sysLog 系统日志信息
     * @return 结果
     */
    boolean saveLog(SysLog sysLog);
    
    /**
     * 清空系统日志
     * 
     * @return 结果
     */
    boolean clearLogs();
    
    /**
     * 删除系统日志
     * 
     * @param ids 需要删除的日志ID
     * @return 结果
     */
    boolean deleteLogs(Long[] ids);
} 