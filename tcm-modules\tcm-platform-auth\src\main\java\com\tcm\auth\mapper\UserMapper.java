package com.tcm.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tcm.auth.domain.User;
import org.apache.ibatis.annotations.Param;

/**
 * 用户Mapper接口
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 更新用户登录信息
     *
     * @param userId 用户ID
     * @param ip     登录IP
     * @return 影响行数
     */
    int updateLoginInfo(@Param("userId") Long userId, @Param("ip") String ip);
}