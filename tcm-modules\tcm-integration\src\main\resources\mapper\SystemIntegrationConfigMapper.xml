<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tcm.integration.mapper.SystemIntegrationConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tcm.integration.entity.SystemIntegrationConfig">
        <id column="id" property="id" />
        <result column="system_name" property="systemName" />
        <result column="system_code" property="systemCode" />
        <result column="system_type" property="systemType" />
        <result column="api_url" property="apiUrl" />
        <result column="auth_type" property="authType" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="token" property="token" />
        <result column="token_expire_time" property="tokenExpireTime" />
        <result column="connect_timeout" property="connectTimeout" />
        <result column="read_timeout" property="readTimeout" />
        <result column="description" property="description" />
        <result column="status" property="status" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 根据系统编码查询配置 -->
    <select id="selectBySystemCode" resultMap="BaseResultMap">
        SELECT *
        FROM sys_integration_config
        WHERE system_code = #{systemCode}
          AND status = 1
        LIMIT 1
    </select>

</mapper> 