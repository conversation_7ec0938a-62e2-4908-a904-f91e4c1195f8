package com.tcm.admin.controller;

import com.tcm.admin.dto.UserDTO;
import com.tcm.admin.service.UserService;
import com.tcm.admin.vo.UserVO;
import com.tcm.common.core.domain.PageVO;
import com.tcm.common.core.domain.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Tag(name = "用户管理接口", description = "提供用户的增删改查功能")
@RestController
@RequestMapping("/api/v1/users")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;

    @Operation(summary = "获取用户列表", description = "分页获取用户列表")
    @GetMapping
    public Result<PageVO<UserVO>> listUsers(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String username,
            @RequestParam(required = false) String realName) {
        return Result.success(userService.listUsers(pageNum, pageSize, username, realName));
    }

    @Operation(summary = "获取用户详情", description = "根据用户ID获取用户详情")
    @GetMapping("/{id}")
    public Result<UserVO> getUserDetail(@PathVariable Long id) {
        return Result.success(userService.getUserById(id));
    }

    @Operation(summary = "创建用户", description = "创建新用户")
    @PostMapping
    public Result<Long> createUser(@Valid @RequestBody UserDTO userDTO) {
        return Result.success(userService.createUser(userDTO));
    }

    @Operation(summary = "更新用户", description = "更新用户信息")
    @PutMapping("/{id}")
    public Result<Boolean> updateUser(@PathVariable Long id, @Valid @RequestBody UserDTO userDTO) {
        userService.updateUser(id, userDTO);
        return Result.success(true);
    }

    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @DeleteMapping("/{id}")
    public Result<Boolean> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return Result.success(true);
    }

    @Operation(summary = "修改用户状态", description = "启用或禁用用户")
    @PutMapping("/{id}/status")
    public Result<Boolean> updateUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        userService.updateUserStatus(id, status);
        return Result.success(true);
    }

    @Operation(summary = "重置密码", description = "重置用户密码")
    @PutMapping("/{id}/password")
    public Result<Boolean> resetPassword(@PathVariable Long id) {
        userService.resetPassword(id);
        return Result.success(true);
    }
}