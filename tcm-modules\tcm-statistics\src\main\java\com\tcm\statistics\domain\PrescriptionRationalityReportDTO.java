package com.tcm.statistics.domain;

import java.util.List;
import java.util.Map;

import lombok.Data;

/**
 * 处方合理性报告数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class PrescriptionRationalityReportDTO {
    
    /**
     * 统计时间段
     */
    private DateRangeDTO period;
    
    /**
     * 科室ID
     */
    private Long departmentId;
    
    /**
     * 抽样分析的处方数
     */
    private Integer sampleSize;
    
    /**
     * 各类问题的发生率
     */
    private Map<String, RationalityStatDTO> issueStats;
    
    /**
     * 不合理处方比例
     */
    private Double irrationalRate;
    
    /**
     * 问题类型分布
     */
    private List<IssueDistributionDTO> issueDistribution;
} 